//package com.globalsources.rfi;
//
//import cn.hutool.core.date.DateTime;
//import cn.hutool.core.date.DateUtil;
//import com.globalsources.framework.result.Result;
//import com.globalsources.rfi.agg.dto.inquiry.InquiryAllQueryDTO;
//import com.globalsources.rfi.agg.dto.inquiry.SupplierInquiryListExportDTO;
//import com.globalsources.rfi.agg.request.admin.AdminInquiryEDMDTO;
//import com.globalsources.rfi.agg.request.admin.InquiryLogRequestDTO;
//import com.globalsources.rfi.agg.response.rfi.InquireAllVO;
//import com.globalsources.rfi.controller.InquiryAdminController;
//import com.globalsources.rfi.controller.InquiryController;
//import com.globalsources.rfi.controller.InquiryLogController;
//import com.globalsources.rfi.data.dao.InquiryAllEmailDao;
//import com.globalsources.rfi.service.InquiryAllEmailAddressService;
//import com.globalsources.rfi.service.InquiryAllService;
//import com.globalsources.rfi.service.SupplierInquiryService;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.Date;
//import java.util.List;
//
///**
// * <AUTHOR> Chen
// * @date 2021/7/29 11:31
// */
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = GsolRfiAggApplication.class)
//public class RfiAggAppTest {
//
//    @Autowired
//    private InquiryController inquiryController;
//
//    @Autowired
//    private InquiryAllEmailAddressService inquiryAllEmailAddressService;
//
//    @Autowired
//    private InquiryAllEmailDao inquiryAllEmailDao;
//
//    @Autowired
//    private SupplierInquiryService supplierInquiryService;
//
//    @Autowired
//    private InquiryLogController logController;
//    @Autowired
//    private InquiryAdminController adminController;
//
//    @Autowired
//    private InquiryAllService inquiryAllService;
//    @Test
//    public void test() {
//        DateTime yesterday = DateUtil.yesterday();
//        Date beginOfDay = DateUtil.beginOfDay(yesterday);
//        Date endOfDay = DateUtil.endOfDay(yesterday);
//        InquiryAllQueryDTO pending = InquiryAllQueryDTO.builder().startDate(beginOfDay).endDate(endOfDay).eblockStatus("pending").tmxStatus("pending").build();
//        Result<List<InquireAllVO>> listResult = inquiryController.queryInquiryAll(pending);
//        System.out.println(listResult);
//    }
//
//    @Test
//    public void test3() {
//        InquiryLogRequestDTO dto = new InquiryLogRequestDTO();
//        dto.setPageNum(1);
//        dto.setPageSize(10);
//        System.out.println(logController.offlineEmailLogList(dto));
//    }
//
//
//    @Test
//    public void test4() {
//        AdminInquiryEDMDTO dto = new AdminInquiryEDMDTO();
//        dto.setStartDate(new DateTime("2023-01-01"));
//        dto.setEndDate(new DateTime("2024-01-01"));
//        adminController.edmAnalysis(dto);
//    }
//
//    @Test
//    public void test5() {
//        inquiryAllService.calcSupplierMatchScore();
//    }
//}
