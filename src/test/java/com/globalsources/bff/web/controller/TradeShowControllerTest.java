package com.globalsources.bff.web.controller;

import com.globalsources.bff.web.WebBffApp;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

@Slf4j
@SpringBootTest(classes = WebBffApp.class)
@RunWith(SpringRunner.class)
public class TradeShowControllerTest {
    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext wac;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(wac).build();
    }


    @Test
    public void geimsRegister() {
        try {
            String jsonString = "{\"language\":\"\",\"email\":\"a@qq.c\",\"title\":\"MR\",\"firstName\":\"a\",\"lastName\":\"a\",\"department\":\"PRO-MGT\"," +
                    "\"countryCode\":\"AX\",\"jobTitle\":\"asdad\",\"jobFunction\":\"ED19\",\"jobLevel\":\"MM028\",\"roleInPurchase\":\"SOLE\",\"contactCountryCode\":\"AD\"," +
                    "\"contactNumberCountryCode\":\"376\",\"contactNumberAreaCode\":\"123\",\"contactNumberPhoneNumber\":\"123213\",\"contactNumberExt\":\"3\"," +
                    "\"mobileCountryCode\":\"AS\",\"mobileCountry\":\"684\",\"mobileNumber\":\"1231231\",\"companyName\":\"dasdasd\"," +
                    "\"businessTypes\":[\"DIST\",\"EXP\"],\"totalEmployee\":\"S02\",\"yearEstablished\":\"1235\",\"annualSourcingValue\":\"V06\",\"annualSourcingAmountOfBusinessUnit\":\"V02\"," +
                    "\"sellingChannels\":[\"C09\",\"C08\",\"C07\"],\"countriesSource\":[\"EM05\",\"EM06\",\"EM13\",\"EM14\"],\"marketYouSellTo\":[\"EM13\",\"EM14\",\"EM08\",\"EM05\"]," +
                    "\"supplierLookingFor\":[\"WHO\",\"TC\",\"BRO\",\"OBM\",\"ODM\"]," +
                    "\"productCategoryList\":[[\"LASER-PROCESSING\",\"INJECTION-MOLDING\"],[\"NEW-RENEWABLE-ENERGY\"],[\"RESISTANCE\",\"SENSOR\",\"FILTER\",\"MANUFACTURING\",\"ELECTRONIC\"],[\"ELECTRIC-MOTORS\",\"INTEGRATED\"]]," +
                    "\"readPolicy\":true,\"from\":\"h5\"}";

            mockMvc.perform(MockMvcRequestBuilders.post("/trade-show/v1/user/geimsRegister")
                    .contentType(MediaType.APPLICATION_JSON)
                    .characterEncoding("UTF-8")
                    .accept(MediaType.APPLICATION_JSON)
                    .content(jsonString)
            )
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andDo(MockMvcResultHandlers.print());
        } catch (Exception e) {
            log.error("Error:", e);
        }
    }
}