package com.globalsources.admin.feign;

import com.globalsources.admin.AdminApplication;
import com.globalsources.admin.model.vo.seo.KeywordActionSettingPageVO;
import com.globalsources.admin.model.vo.seo.KeywordActionSettingRequestVO;
import com.globalsources.admin.model.vo.seo.KeywordActionSettingVO;
import com.globalsources.framework.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Title KeywordFeignTest
 * @date 2022/7/5 18:02
 * @Description:
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {AdminApplication.class})
public class KeywordFeignTest {

    @Autowired
    private KeywordFeign keywordFeign;

    @Test
    public void selectTest() {
        KeywordActionSettingRequestVO vo = new KeywordActionSettingRequestVO(1,6,2,null);
        Result<KeywordActionSettingPageVO> result = keywordFeign.selectSpecialKeywordList(vo);
        log.info("result : {}", result);
        log.info("size = " + result.getData().getKeywordList().size());
    }

    @Test
    public void insertTest(){
        KeywordActionSettingVO vo = new KeywordActionSettingVO(null,"sadsad","Protected","sex",10l);
        Result<String> result = keywordFeign.insertSpecialKeyword(vo);
        log.info("result : {}", result.getData());
    }

}
