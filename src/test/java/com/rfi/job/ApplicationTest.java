//package com.rfi.job;

//import com.globalsources.framework.result.Result;
//import com.globalsources.job.RfiJobApplication;
//import com.globalsources.job.handler.RfiAlertNoticeHandler;
//import com.globalsources.job.handler.RfiConvertRfqJobHandler;
//import com.globalsources.job.handler.RfiSupplierPotentialOpportunityHandler;
//import com.globalsources.job.service.OfflineSyncAmazonService;
//import com.globalsources.message.dto.MessageNoticeEntity;
//import com.globalsources.message.dto.NoticeMsgDataDTO;
//import com.globalsources.message.dto.NoticePushDTO;
//import com.globalsources.message.enums.MessageTypeEnum;
//import com.globalsources.message.enums.NoticeBizTypeEnum;
//import com.globalsources.message.enums.NoticeCopyTypeEnum;
//import com.globalsources.message.feign.MessageAggFeign;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.mail.MessagingException;
//import java.io.IOException;
//import java.util.Collections;
//
///**
// * <a>Title: ApplicationTest </a>
// * <a>Author: Levlin Li <a>
// * <a>Description：<a>
// *
// * <AUTHOR> Li
// * @date 2021/8/10-15:50
// */
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = RfiJobApplication.class)
//public class ApplicationTest {
//
//    @Autowired
//    private OfflineSyncAmazonService offlineSyncAmazonService;
//
//    @Autowired
//    private RfiAlertNoticeHandler rfiAlertNoticeHandler;
//
//    @Autowired
//    private RfiSupplierPotentialOpportunityHandler rfiSupplierPotentialOpportunityHandler;
//
//    @Autowired
//    private RfiConvertRfqJobHandler rfiConvertRfqJobHandler;
//
//    @Autowired
//    private MessageAggFeign messageAggFeign;
//
//    @Test
//    public void testRfiReplyOffline() throws IOException, MessagingException {
//        offlineSyncAmazonService.offlineScan();
//    }
//
//    @Test
//    public void RfiAlertNoticeHandler(){
//        String email = "<EMAIL>";
//        rfiAlertNoticeHandler.pendingRfiAlertNotice(email);
//    }
////    @Test
////    public void RfiAlertNoticeHandler(){
////        String email = "<EMAIL>";
////        rfiAlertNoticeHandler.pendingRfiAlertNotice(email);
////    }

//    @Test
//    public void testJob(){
//
//        rfiConvertRfqJobHandler.rfiConvertRfqJobHandler();
//        rfiSupplierPotentialOpportunityHandler.markPotentialOpportunity();
//    }

//    @Test
//    public void testMessage(){
//        MessageNoticeEntity messageNoticeEntity = MessageNoticeEntity.builder()
//                .businessId("1706557261314113536")
//                .messageType(MessageTypeEnum.RFI_POTENTIAL_OPPORTUNITY.getValue())
//                .roleType(2)
//                .supplierId(2008832656220L)
//                .userId(1306801809499L)
//                .build();
//
//        NoticePushDTO noticePushDTO = new NoticePushDTO();
//        noticePushDTO.setBizType(NoticeBizTypeEnum.RFI_POTENTIAL_OPPORTUNITY.name());
//        noticePushDTO.setMessageNoticeEntityList(Collections.singletonList(messageNoticeEntity));
//        noticePushDTO.setNoticeCopyType(NoticeCopyTypeEnum.RFI_POTENTIAL_OPPORTUNITY_NOTICE.name());
//        noticePushDTO.setNoticeMsgDataDTOList(Collections.singletonList(new NoticeMsgDataDTO()));
//        log.info("markPotentialOpportunity notice push dto:{}", noticePushDTO);
//
//        Result<Boolean> msgResult = messageAggFeign.insertMessage(noticePushDTO);
//        log.info("markPotentialOpportunity notice push result:{}", msgResult);
//    }
//}
