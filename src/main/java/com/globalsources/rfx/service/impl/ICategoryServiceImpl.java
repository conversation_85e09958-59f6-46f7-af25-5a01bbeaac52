package com.globalsources.rfx.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.model.common.constant.ConstantPool;
import com.globalsources.product.agg.api.dto.category.SuppProductCategoryLinkAggDTO;
import com.globalsources.product.agg.api.dto.request.category.MostProdCategoryQueryCoreDTO;
import com.globalsources.product.agg.api.feign.BuyerProductFeign;
import com.globalsources.product.agg.api.feign.ProductCategoryAggFeign;
import com.globalsources.product.agg.api.vo.ProductCategory;
import com.globalsources.product.agg.api.vo.ProductCategoryAttrVO;
import com.globalsources.product.agg.api.vo.ProductCategoryVO;
import com.globalsources.rfx.service.ICategoryService;
import com.globalsources.rfx.util.ResultConverter;
import io.vavr.control.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR> Sun
 * @since 2023/5/8
 */
@Slf4j
@Component
public class ICategoryServiceImpl implements ICategoryService {

    @Resource
    private BuyerProductFeign buyerProductFeign;

    @Resource
    private ProductCategoryAggFeign productCategoryAggFeign;

    @Override
    public String getCategoryDescEnById(Long categoryId) {
        ProductCategoryVO productCategory = this.getCategoryById(categoryId);
        if (Objects.isNull(productCategory)) {
            log.info("getCategoryDescEnById productCategory is null, will return null, categoryId:{}", categoryId);
            return "";
        }
        return productCategory.getDescEn();
    }

    @Override
    public ProductCategoryVO getCategoryById(Long categoryId) {
        if (Objects.isNull(categoryId)) {
            log.info("getCategoryById categoryId is null, will return null");
            return null;
        }
        List<ProductCategoryVO> productCategoryList = this.getCategoryListByIds(Collections.singletonList(categoryId));
        return CollectionUtils.isNotEmpty(productCategoryList) ? CollUtil.getFirst(productCategoryList) : null;
    }

    @Override
    public List<ProductCategoryVO> getCategoryListByIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            log.info("getCategoryListByIds categoryIds is null or empty, will return null");
            return Collections.emptyList();
        }
        List<ProductCategoryVO> productCategoryList = null;
        try {
            productCategoryList = ResultConverter.convertData(productCategoryAggFeign.queryCategoryListByIds(categoryIds));
            log.info("getCategoryListByIds productCategoryAggFeign.queryCategoryListByIds success, categoryIds:{}", JSON.toJSONString(categoryIds));
        } catch (Exception e) {
            log.error("getCategoryListByIds productCategoryAggFeign.queryCategoryListByIds error, categoryIds:{}, e:{}", JSON.toJSONString(categoryIds), e.getMessage(), e);
        }
        return productCategoryList;
    }

    @Override
    public ProductCategory queryCategoryListById(Long categoryId, String language) {
        if (Objects.isNull(categoryId)) {
            log.info("getCategoryById categoryId is null, will return null");
            return null;
        }
        List<ProductCategory> productCategoryList = this.queryCategoryListByIds(Collections.singletonList(categoryId), StringUtils.isNotBlank(language) ? language : "enus");
        return CollectionUtils.isNotEmpty(productCategoryList) ? CollUtil.getFirst(productCategoryList) : null;
    }

    @Override
    public List<ProductCategory> queryCategoryListByIds(List<Long> categoryIds, String language) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            log.info("getCategoryListByIds categoryIds is null or empty, will return null");
            return Collections.emptyList();
        }
        List<ProductCategory> productCategoryList = null;
        try {
            productCategoryList = ResultConverter.convertData(productCategoryAggFeign.queryCategoryListByIds(categoryIds, StringUtils.isNotBlank(language) ? language : "enus"));
            log.info("getCategoryListByIds productCategoryAggFeign.queryCategoryListByIds success, categoryIds:{}", JSON.toJSONString(categoryIds));
        } catch (Exception e) {
            log.error("getCategoryListByIds productCategoryAggFeign.queryCategoryListByIds error, categoryIds:{}, e:{}", JSON.toJSONString(categoryIds), e.getMessage(), e);
        }
        return productCategoryList;
    }

    @Override
    public List<ProductCategoryAttrVO> getProductCategoryAttr(Long l4CategoryId) {
        if (Objects.isNull(l4CategoryId)) {
            log.info("getProductCategoryAttr l4CategoryId is null or empty, will return null");
            return Collections.emptyList();
        }
        return getProductCategoryAttrList(Collections.singletonList(l4CategoryId));
    }

    @Override
    public List<ProductCategoryAttrVO> getProductCategoryAttrList(List<Long> l4CategoryIds) {
        if (CollectionUtils.isEmpty(l4CategoryIds)) {
            log.info("getProductCategoryAttrList l4CategoryIds is null or empty, will return null");
            return Collections.emptyList();
        }
        List<ProductCategoryAttrVO> productCategoryAttrList = null;
        try {
            productCategoryAttrList = ResultConverter.convertData(buyerProductFeign.getProductCategoryAttrs(l4CategoryIds));
            log.info("getProductCategoryAttrList buyerProductFeign.getProductCategoryAttrs success, l4CategoryIds:{}", JSON.toJSONString(l4CategoryIds));
        } catch (Exception e) {
            log.error("getProductCategoryAttrList buyerProductFeign.getProductCategoryAttrs error, l4CategoryIds:{}, e:{}", JSON.toJSONString(l4CategoryIds), e.getMessage(), e);
        }
        return productCategoryAttrList;
    }

    @Override
    public SuppProductCategoryLinkAggDTO getMostProdCategoryId(Long supplierId, int limit) {
        if (Objects.isNull(supplierId)) {
            log.info("getMostProdCategoryId supplierId is null or empty, will return null, supplierId:{}, limit:{}", supplierId, limit);
            return null;
        }
        List<SuppProductCategoryLinkAggDTO> suppProductCategoryLinkList = this.getMostProdCategoryIdList(Collections.singletonList(supplierId), limit);
        return CollectionUtils.isNotEmpty(suppProductCategoryLinkList) ? CollUtil.getFirst(suppProductCategoryLinkList) : null;
    }

    @Override
    public List<SuppProductCategoryLinkAggDTO> getMostProdCategoryIdList(List<Long> supplierIds, int limit) {
        if (CollectionUtils.isEmpty(supplierIds)) {
            log.info("getMostProdCategoryId supplierIds is null or empty, will return null, supplierIds:{}, limit:{}", supplierIds, limit);
            return Collections.emptyList();
        }
        List<SuppProductCategoryLinkAggDTO> suppProductCategoryLinkList = null;
        try {
            suppProductCategoryLinkList = ResultConverter.convertData(
                    productCategoryAggFeign.getMostProdCategoryIds(MostProdCategoryQueryCoreDTO.builder().highlightFlag(false).limit(Optional.ofNullable(limit).orElse(5)).supplierIds(supplierIds).build()));
            log.info("getMostProdCategoryId productCategoryAggFeign.getMostProdCategoryIds success, supplierIds:{}, limit:{}", JSON.toJSONString(supplierIds), limit);
        } catch (Exception e) {
            log.error("getMostProdCategoryId productCategoryAggFeign.getMostProdCategoryIds error, supplierIds:{}, limit:{}, e:{}", JSON.toJSONString(supplierIds), limit, e.getMessage(), e);
        }
        return suppProductCategoryLinkList;
    }

    @Override
    public Map<Long, ProductCategory> getCategoryIdNameMapWithDelete(List<Long> categoryIdList) {
        if (CollectionUtils.isEmpty(categoryIdList)) {
            return Collections.emptyMap();
        }
        Map<Long, ProductCategory> result = new HashMap<>();
        List<ProductCategory> productCategory = null;
        try {
            productCategory = ResultUtil.getData(productCategoryAggFeign.findCategoryByIdListWithDeleted(categoryIdList, ConstantPool.LANGUAGE_DEFAULT, true));
        } catch (Exception e) {
            log.error("getCategoryIdNameMapWithDelete error, category id list is : {}, error message is : {}", categoryIdList, JSON.toJSONString(e));
        }
        Option.of(productCategory).getOrElse(Collections.emptyList()).parallelStream().filter(Objects::nonNull).distinct().forEach(item -> result.put(item.getCategoryId(), item));

        return result;
    }

    @Override
    public String getCategoryNameWithDelete(Long categoryId) {
        if (Objects.isNull(categoryId)) {
            return StringUtils.EMPTY;
        }
        try {
            List<ProductCategory> productCategory = ResultUtil.getData(productCategoryAggFeign.findCategoryByIdListWithDeleted(Collections.singletonList(categoryId), ConstantPool.LANGUAGE_DEFAULT, true));
            if (CollectionUtils.isEmpty(productCategory) || Objects.isNull(productCategory.get(0))) {
                return StringUtils.EMPTY;
            }
            return productCategory.get(0).getCategoryName();
        } catch (Exception e) {
            log.error("getCategoryNameWithDelete, category id  is : {}, error message is : {}", categoryId, JSON.toJSONString(e));
        }

        return StringUtils.EMPTY;
    }
}
