package com.globalsources.rfx.service;

import com.globalsources.supplierconsole.agg.api.supplier.dto.rfi.SuppInfoForRfiReportAggDTO;

import java.util.List;

/**
 * <a>Title: ISupplierService </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: ISupplierService <a>
 *
 * <AUTHOR>
 * @date 2021/12/21 - 9:00
 */
public interface ISupplierConsoleService {

    /**
     * 查询公司信息(询盘报告)
     * @param supplierIds
     * @return
     */
    List<SuppInfoForRfiReportAggDTO> getSuppInfosForRfiReportBySupplierIds(List<Long> supplierIds);
}
