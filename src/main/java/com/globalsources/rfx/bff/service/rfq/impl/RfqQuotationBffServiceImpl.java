package com.globalsources.rfx.bff.service.rfq.impl;

import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.rfq.bff.api.feign.RfqAgg;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationChatRepliedRequestDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationCompareRequestDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationCreateRequestDTO;
import com.globalsources.rfq.bff.api.model.vo.RfqQuotationChatInfoVO;
import com.globalsources.rfq.bff.api.model.vo.RfqQuotationCompareVO;
import com.globalsources.rfq.bff.api.model.vo.RfqQuotationDetailVO;
import com.globalsources.rfq.bff.api.model.vo.RfqQuotationListVO;
import com.globalsources.rfq.bff.api.model.vo.app.BuyerAppQuotationCompareVO;
import com.globalsources.rfq.bff.api.model.vo.app.RfqBuyerAppQuotationDetailVO;
import com.globalsources.rfq.bff.api.model.vo.app.RfqBuyerAppQuotationVO;
import com.globalsources.rfq.bff.api.model.vo.seller.RfqQuotationImportVO;
import com.globalsources.rfq.bff.api.model.vo.seller.SellerQuotationCountVO;
import com.globalsources.rfq.core.api.model.dto.app.RfqAppInfoUnReadQuotationListRequestDTO;
import com.globalsources.rfx.bff.service.rfq.IRfqQuotationBffService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <a>Title: RfqQuotationBffServiceImpl </a>
 * <a>Author: Mike Chen <a>
 * <a>Description: RfqQuotationBffServiceImpl <a>
 *
 * <AUTHOR> Chen
 * @date 2021/11/26 13:58
 */
@Service
@RequiredArgsConstructor
public class RfqQuotationBffServiceImpl implements IRfqQuotationBffService {

    private final RfqAgg.RfqQuotationAggService rfqQuotationAggService;

    @Override
    public Result<RfqQuotationListVO> rfqQuotationList(String rfqId, Long userId) {
        return rfqQuotationAggService.rfqQuotationList(rfqId, userId);
    }

    @Override
    public Result<RfqQuotationDetailVO> rfqQuotationDetail(String quotationId, Long userId) {
        return rfqQuotationAggService.rfqQuotationDetailV2(quotationId, userId);
    }

    @Override
    public Result createQuotation(RfqQuotationCreateRequestDTO requestDTO) {
        return rfqQuotationAggService.createQuotation(requestDTO);
    }

    @Override
    public Result<SellerQuotationCountVO> supplierQuotationCount(Long supplierId) {
        return Result.success(rfqQuotationAggService.supplierQuotationCount(supplierId));
    }

    @Override
    public Result<RfqQuotationCompareVO> rfqQuotationCompare(RfqQuotationCompareRequestDTO requestDTO) {
        return rfqQuotationAggService.rfqQuotationCompare(requestDTO);
    }

    @Override
    public Result<Boolean> rfqQuotationChatReplied(RfqQuotationChatRepliedRequestDTO requestDTO) {
        return Result.success(rfqQuotationAggService.rfqQuotationChatReplied(requestDTO));
    }

    @Override
    public Result<RfqQuotationImportVO> importPreviousQuotation(Long supplierId, Long userId) {
        return Result.success(rfqQuotationAggService.importPreviousQuotation(supplierId, userId));
    }

    @Override
    public Result<String> importPreviousQuotationCheck(Long supplierId, Long userId) {
        return rfqQuotationAggService.importPreviousQuotationCheck(supplierId, userId);
    }

    @Override
    public PageResult<RfqBuyerAppQuotationVO> getBuyerAppUnreadQuotationList(RfqAppInfoUnReadQuotationListRequestDTO requestDTO) {
        return rfqQuotationAggService.getBuyerAppUnreadQuotationList(requestDTO);
    }

    @Override
    public Result<RfqBuyerAppQuotationDetailVO> rfqBuyerAppDetail(String quotationId, Long userId) {
        return Result.success(rfqQuotationAggService.rfqBuyerAppDetail(quotationId, userId));
    }

    @Override
    public Result<List<BuyerAppQuotationCompareVO>> appQuotationCompare(RfqQuotationCompareRequestDTO requestDTO) {
        return rfqQuotationAggService.appQuotationCompare(requestDTO);
    }

    @Override
    public Result<RfqQuotationChatInfoVO> rfqQuotationChatInfo(String quotationId) {
        return rfqQuotationAggService.rfqQuotationChatInfo(quotationId);
    }
}
