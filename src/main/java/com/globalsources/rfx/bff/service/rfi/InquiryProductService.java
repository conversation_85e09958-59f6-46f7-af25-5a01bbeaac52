package com.globalsources.rfx.bff.service.rfi;

import com.globalsources.product.agg.api.dto.product.ProductDetailVo;
import com.globalsources.rfi.agg.response.product.ProductCategoryAttributeDetailVO;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/12 11:10
 */
public interface InquiryProductService {
    List<ProductCategoryAttributeDetailVO> getProductCategoryAttributesByCategoryIds(@NonNull List<Long> l4CategoryIdList);

    Long getCategoryIdByProductDetailVoOfSearch(ProductDetailVo result);

    Long getCategoryIdByProductDetailVoOfAgg(ProductDetailVo result);

    Long getCategoryIdByProductDetailVo(ProductDetailVo result, Integer level);
}
