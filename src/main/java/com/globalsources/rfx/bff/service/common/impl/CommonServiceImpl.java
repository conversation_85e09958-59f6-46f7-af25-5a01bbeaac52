package com.globalsources.rfx.bff.service.common.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.crm.common.api.feign.CrmDataFeign;
import com.globalsources.crm.common.api.model.dto.repot.CrmReportDownloadRecordAggDTO;
import com.globalsources.crm.common.api.model.dto.repot.CrmReportParamDTO;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.vo.UserBaseProfileVO;
import com.globalsources.rfq.bff.api.feign.RfqAgg;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuerySimpleRequestDTO;
import com.globalsources.rfq.core.api.util.ResultUtil;
import com.globalsources.rfx.bff.model.common.ChatUserInfoVO;
import com.globalsources.rfx.bff.service.common.CommonService;
import com.globalsources.rfx.bff.util.DictCountryUtils;
import com.globalsources.rfx.service.IUserService;
import com.globalsources.rfx.util.ResultConverter;
import com.globalsources.user.api.feign.UserQueryFeign;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Objects;


@Slf4j
@Component
@AllArgsConstructor
public class CommonServiceImpl implements CommonService {

    private final UserQueryFeign userQueryFeign;
    private final DictCountryUtils dictCountryUtils;
    private final CrmDataFeign crmDataFeign;
    private final IUserService userService;
    private final RfqAgg.RfqInfoAggService rfqInfoAggService;

    @Override
    public ChatUserInfoVO getCharUserInfo(Long userId) {
        log.info("getCharUserInfo userId:{}", userId);

        ChatUserInfoVO result = new ChatUserInfoVO();
        try {
            result = OrikaMapperUtil.coverObject(userService.getChatUserInfo(userId,null), ChatUserInfoVO.class);

            //s70  businessType -> majorBusinessType

            UserBaseProfileVO user = getUserBaseProfile(userId);
            if(Objects.nonNull(user) && Objects.nonNull(user.getCompanyInfo())){
                result.setBusinessType(dictCountryUtils.convertMapForDictVal("BUSINESS_TYPE_V1", user.getCompanyInfo().getMajorBusinessType()));
            }
        } catch (Exception e) {
            log.error("getCharUserInfo userId:{}, e:{}", userId, JSON.toJSONString(e));
        }
        return result;

    }

    @Override
    public ChatUserInfoVO getChatUserInfo(Long userId, String rfqId, Long supplierId) {
        log.info("------ getCharUserInfo userId:{}, rfqId:{}, supplierId:{}", userId, rfqId, supplierId);

        ChatUserInfoVO result = new ChatUserInfoVO();
        try {
            if (Objects.isNull(userId) || Objects.isNull(rfqId) || Objects.isNull(supplierId)) {
                return result;
            }

            // 根据rfqId,userId,supplierId，检查rfq是否有对应类型记录
            RfqQuerySimpleRequestDTO dto = RfqQuerySimpleRequestDTO.builder()
                    .rfqId(rfqId)
                    .userId(userId)
                    .supplierId(supplierId)
                    .build();
            Boolean existFlag = ResultUtil.checkAndGet(rfqInfoAggService.checkRecordExist(dto));

            if (!Boolean.TRUE.equals(existFlag)) {
                return result;
            }

            return getCharUserInfo(userId);
        } catch (Exception e) {
            log.error("------ getChatUserInfo error, userId:{}, rfqId:{}, supplierId:{}, error:{}", userId, rfqId, supplierId, JSON.toJSONString(e));
        }
        return result;
    }

    public UserBaseProfileVO getUserBaseProfile(Long userId) {
        if (Objects.isNull(userId)) {
            log.info("getUserBaseProfile userId is null or empty, will return null");
            return null;
        }
        UserBaseProfileVO result = null;
        try {
            result = ResultConverter.convertData(userQueryFeign.getUserProfile(userId));
            log.info("getUserBaseProfile userQueryFeign.getUserProfile success, userId:{}", userId);
        } catch (Exception e) {
            log.error("getUserBaseProfile userQueryFeign.getUserProfile error, userId:{}, e:{}", userId, e.getMessage(), e);
        }
        return result;
    }

    @Async
    @Override
    public void reportDownloadRecordOfCrmReportPage(CrmReportParamDTO crmReportParamDto, String reportName) {
        //异步上报crm报告页报告下载记录
        try {
            CrmReportDownloadRecordAggDTO crmReportDownloadRecordAggDTO = OrikaMapperUtil.coverObject(crmReportParamDto, CrmReportDownloadRecordAggDTO.class);
            crmReportDownloadRecordAggDTO.setReportName(reportName);
            crmDataFeign.reportDownloadRecordOfCrmReportPage(crmReportDownloadRecordAggDTO);
        } catch (Exception e) {
            log.error("reportDownloadRecordOfCrmReportPage error, dto: {}", crmReportParamDto, e);
        }
    }

}
