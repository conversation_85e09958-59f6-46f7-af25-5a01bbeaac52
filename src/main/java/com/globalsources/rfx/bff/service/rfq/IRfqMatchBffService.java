package com.globalsources.rfx.bff.service.rfq;

import com.globalsources.framework.result.Result;
import com.globalsources.rfq.bff.api.model.dto.req.RfqMatchPreviewRequestDTO;
import com.globalsources.rfq.bff.api.model.vo.match.RfqMatchPageVO;

/**
 * <a>Title: IRfqMatchBffService </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: IRfqMatchBffService <a>
 *
 * <AUTHOR>
 * @date 2021/11/26 14:01
 */
public interface IRfqMatchBffService {

    Result<RfqMatchPageVO> rfqMatch(String matchId);

    Result<RfqMatchPageVO> preview(RfqMatchPreviewRequestDTO requestDTO);

}
