package com.globalsources.rfx.bff.service.report.impl;

import com.globalsources.common.api.dto.complain.SubmitComplainDTO;
import com.globalsources.common.api.feign.ComplainAggFeign;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCodeEnum;
import com.globalsources.rfx.bff.service.report.RfxComplainService;
import com.globalsources.rfx.util.ContentReplaceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> liu
 * @date 2022/1/25 16:03
 */
@Slf4j
@Service
public class RfxComplainServiceImpl implements RfxComplainService {

    @Autowired
    private ComplainAggFeign complainAggFeign;

    @Override
    public Result submitComplain(SubmitComplainDTO submitComplainDTO) {
        String complainComment = ContentReplaceUtil.filterHtmlInjectionContent(submitComplainDTO.getComplainComment());
        if(StringUtils.isBlank(complainComment)){
            return Result.failed(ResultCodeEnum.PARAMETER_VALIDATION_FAILED.getCode(), "Complain comment validation failed");
        }
        submitComplainDTO.setComplainComment(complainComment);
        return complainAggFeign.submitComplain(submitComplainDTO);
    }
}
