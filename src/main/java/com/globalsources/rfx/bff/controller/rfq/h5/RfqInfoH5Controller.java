package com.globalsources.rfx.bff.controller.rfq.h5;

import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqKeywordDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqMobileCreateRequestDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqMobileInfoRequestDTO;
import com.globalsources.rfq.bff.api.model.vo.CategorySuggestVO;
import com.globalsources.rfq.bff.api.model.vo.RfqMobileInfoVO;
import com.globalsources.rfq.bff.api.util.UserUtil;
import com.globalsources.rfx.bff.service.rfq.IRfqInfoBffService;
import com.globalsources.rfx.bff.struct.rfq.RfqInfoRequestStruct;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

/**
 * <a>Title: RfqInfoH5Controller </a>
 * <a>Author: Mike Chen <a>
 * <a>Description: RfqInfoH5Controller <a>
 *
 * <AUTHOR> Chen
 * @date 2021/12/1 16:03
 */
@Validated
@RestController
@AllArgsConstructor
@RequestMapping("rfq-h5-bff")
@Api(tags = {"H5 Site--rfq信息相关接口"})
public class RfqInfoH5Controller {

    private final IRfqInfoBffService rfqInfoBffService;
    private final RfqInfoRequestStruct rfqInfoRequestStruct;

    @Login
    @ApiOperation(value = "买家rfq--mobile的rfq列表", notes = "买家rfq--mobile的rfq列表")
    @PostMapping("v1/buyer/rfq-mobile-list")
    public Result<PageResult<RfqMobileInfoVO>> getRfqMobileInfoList(@ApiIgnore UserVO userVO, @Valid @RequestBody RfqMobileInfoRequestDTO requestDTO) {
        return Result.success(rfqInfoBffService.getRfqMobileInfoList(rfqInfoRequestStruct.toRfqMobileInfoRequest(userVO, requestDTO)));
    }

    @Login
    @ApiOperation(value = "买家rfq--mobile创建rfq", notes = "买家rfq--mobile创建rfq")
    @PostMapping("v1/buyer/rfq-mobile-create")
    public Result createMobileRfqInfo(@ApiIgnore UserVO userVO, @Valid @RequestBody RfqMobileCreateRequestDTO requestDTO,
                                      @RequestHeader(value = "lang",required = false,defaultValue = "enus")String lang) {
        requestDTO.setLangCode(lang);
        return rfqInfoBffService.createMobileRfqInfo(rfqInfoRequestStruct.toRfqMobileCreateRequest(userVO, requestDTO));
    }

    @Login
    @ApiOperation(value = "买家端rfq--mobile回复rfq未读数量", notes = "买家端rfq--mobile回复rfq未读数量")
    @GetMapping("v1/buyer/rfq-mobile-unread-count")
    public Result<Integer> rfqUnReadCount(@ApiIgnore UserVO userVO) {
        return rfqInfoBffService.rfqUnReadCount(UserUtil.getUserId(userVO));
    }

    @ApiOperation(value = "买家端rfq--mobile创建rfq类目联想", notes = "买家端rfq--mobile创建rfq类目联想")
    @PostMapping("v1/buyer/category-suggest")
    public Result<List<CategorySuggestVO>> categorySuggest(@RequestBody RfqKeywordDTO dto) {
        return rfqInfoBffService.categorySuggest(dto);
    }

}
