package com.globalsources.rfx.bff.controller.rfi.h5;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.globalsources.agg.supplier.api.feign.SupplierAggFeign;
import com.globalsources.agg.supplier.api.model.dto.SupplierCommonInfoDTO;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.page.BasePage;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCodeEnum;
import com.globalsources.framework.utils.HttpUtil;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.utils.TokenUtil;
import com.globalsources.framework.vo.InquiryNoticeVO;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.message.feign.MessageAggFeign;
import com.globalsources.product.agg.api.dto.product.ProductDetailVo;
import com.globalsources.product.agg.api.feign.BuyerProductFeign;
import com.globalsources.product.agg.api.vo.ProductLiteVO;
import com.globalsources.rfi.agg.dto.template.InquiryReplyTemplateAggDTO;
import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import com.globalsources.rfi.agg.enums.TemplateEnums;
import com.globalsources.rfi.agg.feign.BuyerInquiryFeign;
import com.globalsources.rfi.agg.feign.InquiryFeign;
import com.globalsources.rfi.agg.request.AttachmentAggDTO;
import com.globalsources.rfi.agg.request.EmailTemplateDTO;
import com.globalsources.rfi.agg.request.RequestInquiryDTO;
import com.globalsources.rfi.agg.request.RequestProductDTO;
import com.globalsources.rfi.agg.response.BuyerInquiryListTotalVO;
import com.globalsources.rfi.agg.response.H5InquiryVO;
import com.globalsources.rfi.agg.response.IdsVO;
import com.globalsources.rfi.agg.response.InquirySessionVO;
import com.globalsources.rfi.agg.response.product.ProductCategoryAttributeDetailVO;
import com.globalsources.rfi.agg.response.rfi.RecommendProductInfoVO;
import com.globalsources.rfx.bff.constanst.RedisCachePrefix;
import com.globalsources.rfx.bff.model.rfi.dto.*;
import com.globalsources.rfx.bff.model.rfi.vo.*;
import com.globalsources.rfx.bff.service.rfi.InquiryProductService;
import com.globalsources.rfx.bff.service.rfi.InquiryReplyTemplateService;
import com.globalsources.rfx.bff.service.rfi.MessageService;
import com.globalsources.rfx.bff.util.UserUtil;
import com.globalsources.search.api.feign.ISub;
import com.globalsources.user.api.feign.UserQueryFeign;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/14
 */
@Api(tags = {"H5 Site--Inquiry"})
@Slf4j
@RequestMapping("rfi-h5/buyer")
@RestController
@RefreshScope
public class InquiryController {

    @Autowired
    private UserUtil userUtil;

    @Autowired
    private InquiryFeign inquiryFeign;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private UserQueryFeign userQueryFeign;

    @Autowired
    private ISub.ProductClient productFeign;

    @Autowired
    private InquiryProductService inquiryProductService;

    @Autowired
    private ISub.SupplierClient supplierClient;

    @Autowired
    private BuyerInquiryFeign buyerInquiryFeign;

    @Autowired
    private BuyerProductFeign buyerProductFeign;

    @Autowired
    private SupplierAggFeign supplierAggFeign;

    @Autowired
    private MessageAggFeign messageAggFeign;

    @Value("${gsol.tmx.orgId}")
    public String orgId;

    @Autowired
    private MessageService messageService;

    //收藏产品数量
    private static final Integer ADD_COUNT = 100;

    private static final String DEFAULT_REDIS_VALUE = "DEFAULT_REDIS_VALUE";

    @Value("${gsol.rfx.upsell.profile}")
    public String rfiProfile;

    @Autowired
    private InquiryReplyTemplateService inquiryReplyTemplateService;

    @Value("${gsol.doi.h5.days:5}")
    public Integer doiDays;

    @Value("${gsol.doi.h5.doiFlag:false}")
    public Boolean doiFlag;

    @Login(validLogin = false)
    @ApiOperation(value = "一对多产品询盘信息", tags = "H5 Site--Inquiry")
    @GetMapping(value = "v1/prod-inquiry-info")
    public Result<MobileProductInquiryTotalVO> prodInquiryInfo(@ApiIgnore UserVO userVO) {
        MobileProductInquiryTotalVO mobileProductInquiryTotalVO = new MobileProductInquiryTotalVO();
        final Tuple2<List<MobileInquiryCompanyVO>, List<MobileInquiryProdVO>> listTuple = collectionsIds(userVO.getUserId());
        List<MobileInquiryCompanyVO> supplierIds = listTuple._1.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()
                -> new TreeSet<>(Comparator.comparing(MobileInquiryCompanyVO::getSupplierId))), ArrayList::new));

        List<MobileInquiryProdVO> productIds = listTuple._2;
        if (CollectionUtils.isNotEmpty(productIds) && CollectionUtils.isNotEmpty(supplierIds)) {
            mobileProductInquiryTotalVO.setProdCount(productIds.size());
            mobileProductInquiryTotalVO.setSupplierCount(supplierIds.size());
            InquirySessionVO inquirySessionVOResult = buyerInquiryFeign.inquirySession().getData();
            mobileProductInquiryTotalVO.setSupplierId(inquirySessionVOResult.getOrgId());
            mobileProductInquiryTotalVO.setSessionId(inquirySessionVOResult.getSessionId());
            EmailTemplateDTO emailTemplateDTO = new EmailTemplateDTO();
            emailTemplateDTO.setType(TemplateEnums.PRODUCT);
            emailTemplateDTO.setProductId(productIds.get(0).getProductId());
            mobileProductInquiryTotalVO.setMessage(inquiryFeign.messageTemp(emailTemplateDTO).getData());
            if (ObjectUtils.isNotEmpty(userVO)) {
                emailTemplateDTO.setUsername(userVO.getFirstName() + " " + userVO.getFirstName());
            }
            return Result.success(mobileProductInquiryTotalVO);
        }
        return Result.success();
    }

    private ProductLiteVO getProductInfoEn(Long productId) {
        List<ProductLiteVO> productList = buyerProductFeign.getLiteProductListByIds(Arrays.asList(productId), true, true, false, "enus").getData();
        if (CollectionUtils.isNotEmpty(productList)) {
            return productList.get(0);
        }
        return null;
    }

    @Login(validLogin = false)
    @ApiOperation(value = "一对多提交产品询盘", tags = "H5 Site--Inquiry")
    @PostMapping(value = "v1/prod-many-inquiry")
    public Result<List<String>> prodManyInquiry(@ApiIgnore UserVO userVO, @RequestBody ProdManyInquiryDTO prodManyInquiryDTO, @ApiIgnore HttpServletRequest request) {
        List<ProductManyDTO> productManyDTOList = new ArrayList<>();
        List<MobileInquiryProdVO> productIds = collectionsIds(userVO.getUserId())._2;
        for (MobileInquiryProdVO prodVO : productIds) {
            ProductLiteVO productDetailVo = getProductInfoEn(prodVO.getProductId());
            if (ObjectUtils.isEmpty(productDetailVo)){
                continue;
            }
            ProductManyDTO dto = new ProductManyDTO();
            dto.setInquiryProductId(productDetailVo.getProductId());
            dto.setProductNum(productDetailVo.getMinOrderQuantity());
            dto.setProductUnit(productDetailVo.getMinOrderSingleUnit());
            productManyDTOList.add(dto);
        }
        prodManyInquiryDTO.setProductManyDTOList(productManyDTOList);

        if (ObjectUtils.isNotEmpty(prodManyInquiryDTO) && CollectionUtils.isNotEmpty(prodManyInquiryDTO.getProductManyDTOList())) {
            List<RequestProductDTO> list = new ArrayList<>();
            for (ProductManyDTO productManyDTO : prodManyInquiryDTO.getProductManyDTOList()) {
                ProductLiteVO productDetailVo = getProductInfoEn(productManyDTO.getInquiryProductId());
                if (ObjectUtils.isNotEmpty(productDetailVo)) {
                    RequestProductDTO requestProductDTO = new RequestProductDTO();
                    requestProductDTO.setSupplierId(productDetailVo.getSupplierId());
                    requestProductDTO.setProductId(productDetailVo.getProductId());
                    requestProductDTO.setProductName(productDetailVo.getProductName());
                    requestProductDTO.setProductImage(productDetailVo.getProductPrimaryImage());
                    requestProductDTO.setModelNumber(productDetailVo.getModelNumber());
                    requestProductDTO.setInquiryCatDesc(productDetailVo.getCategoryName());
                    requestProductDTO.setProductNum(productDetailVo.getMinOrderQuantity());
                    requestProductDTO.setProductUnit(productDetailVo.getMinOrderUnit());
                    requestProductDTO.setInquiryType(InquiryTypeEnum.PRODUCT);
                    requestProductDTO.setProductCategoryAttrInfos(productManyDTO.getProductCategoryAttrInfos());
                    list.add(requestProductDTO);
                }
            }
            String sessionId = request.getSession().getId();
            RequestInquiryDTO requestInquiryDTO = new RequestInquiryDTO();
            requestInquiryDTO.setSessionId(prodManyInquiryDTO.getSessionId());
            requestInquiryDTO.setJsSessionId(sessionId);
            requestInquiryDTO.setIpAddr(HttpUtil.getUserIp(request));
            requestInquiryDTO.setRecommendFlag(false);
            requestInquiryDTO.setMessage(prodManyInquiryDTO.getMessage());
            requestInquiryDTO.setRfiSource(2);
            requestInquiryDTO.setInquiryType(InquiryTypeEnum.PRODUCT);
            requestInquiryDTO.setRegFlag(ObjectUtils.isNotEmpty(prodManyInquiryDTO.getRegFlag()) ? prodManyInquiryDTO.getRegFlag() : Boolean.FALSE);
            if (CollectionUtils.isNotEmpty(prodManyInquiryDTO.getAttachmentList())) {
                requestInquiryDTO.setAttachmentList(OrikaMapperUtil.coverList(prodManyInquiryDTO.getAttachmentList(), AttachmentAggDTO.class));
            }
            requestInquiryDTO.setUserProfile(userUtil.buildUserProfileDTO(userVO));
            requestInquiryDTO.setProductDTOList(list);
            requestInquiryDTO.setInquiryPath(prodManyInquiryDTO.getInquiryPath());
            return inquiryFeign.inquireNow(requestInquiryDTO);
        }
        return Result.success();
    }

    @Login
    @ApiOperation(value = "收藏询盘增加商品", tags = "H5 Site--Inquiry")
    @PostMapping(value = "v1/add-inquiry-prod/{productId}")
    public Result<String> addInquiryProd(@ApiIgnore UserVO userVO, @PathVariable Long productId) {
        MobileInquiryCompanyVO mobileInquiryCompanyVO = new MobileInquiryCompanyVO();
        ProductDetailVo productDetailVo = buyerProductFeign.productDetail(productId).getData();
        log.info("[==product detail=={}]", productDetailVo);
        int countNow = redisTemplate.opsForHash().keys(RedisCachePrefix.INQUIRY_PRODUCT_LIST + userVO.getUserId()).size();
        if (countNow >= ADD_COUNT) {
            return Result.success("");
        }
        if (ObjectUtils.isNotEmpty(productDetailVo)) {
            Long supplierId = productDetailVo.getProduct().getOrgId();
            Result<SupplierCommonInfoDTO> commonInfo = supplierAggFeign.getCommonInfo(supplierId);
            SupplierCommonInfoDTO supplierMainInfoDto = ResultUtil.getData(commonInfo);

            if (ObjectUtils.isNotEmpty(supplierMainInfoDto)) {
                mobileInquiryCompanyVO.setCompanyName(supplierMainInfoDto.getCompanyDisplayName());
                mobileInquiryCompanyVO.setSupplierId(supplierId);
                mobileInquiryCompanyVO.setSupplierType(supplierMainInfoDto.getSupplierType());
                mobileInquiryCompanyVO.setMaxContractLevel(supplierMainInfoDto.getMaxContractLevel());
                mobileInquiryCompanyVO.setO2oFlag(supplierMainInfoDto.getO2oFlag());
                mobileInquiryCompanyVO.setMemberSince(supplierMainInfoDto.getMemberSince());
                mobileInquiryCompanyVO.setVerifiedManufacturerFlag(supplierMainInfoDto.getVerifiedManufacturerFlag());
                mobileInquiryCompanyVO.setVerifiedSupplierFlag(supplierMainInfoDto.getVerifiedSupplierFlag());
                mobileInquiryCompanyVO.setSupplierShortName(supplierMainInfoDto.getSupplierShortName());

                List<MobileInquiryProdVO> list = new ArrayList<>();
                MobileInquiryProdVO mobileInquiryProdVO = new MobileInquiryProdVO();
                mobileInquiryProdVO.setProductId(productDetailVo.getProduct().getProductId());
                mobileInquiryProdVO.setProductImage(productDetailVo.getProductPrimaryImage());
                mobileInquiryProdVO.setProductName(productDetailVo.getProductInfoMultiLan().getProductName());
                mobileInquiryProdVO.setProductFOBMax(productDetailVo.getProduct().getProductFOBMax());
                mobileInquiryProdVO.setProductFOBMin(productDetailVo.getProduct().getProductFOBMin());
                mobileInquiryProdVO.setSampleUnit(productDetailVo.getProduct().getSampleUnit());
                //数据追踪
                mobileInquiryProdVO.setL1CategoryId(productDetailVo.getCategoryInfo().getL1CategoryVo().getCategoryId());
                mobileInquiryProdVO.setL2CategoryId(productDetailVo.getCategoryInfo().getL2CategoryVo().getCategoryId());
                mobileInquiryProdVO.setL3CategoryId(productDetailVo.getCategoryInfo().getL3CategoryVo().getCategoryId());
                mobileInquiryProdVO.setL4CategoryId(productDetailVo.getCategoryInfo().getL4CategoryVo().getCategoryId());

                mobileInquiryProdVO.setCreateDate(new Date());
                list.add(mobileInquiryProdVO);
                mobileInquiryCompanyVO.setMobileInquiryProdVOS(list);
                redisTemplate.opsForHash().put(RedisCachePrefix.INQUIRY_PRODUCT_LIST + userVO.getUserId(), productDetailVo.getProduct().getProductId().toString()
                        , JSON.toJSONString(mobileInquiryCompanyVO));
            }
        }
        return Result.success();
    }

    @Login
    @ApiOperation(value = "询盘删除商品", tags = "H5 Site--Inquiry")
    @PostMapping(value = "v1/del-inquiry-prod/{productId}")
    public Result<String> delInquiryProd(@ApiIgnore UserVO userVO, @PathVariable Long productId) {
        redisTemplate.opsForHash().delete(RedisCachePrefix.INQUIRY_PRODUCT_LIST + userVO.getUserId(), productId.toString());
        return Result.success();
    }

    @Login
    @ApiOperation(value = "收藏询盘列表", tags = "H5 Site--Inquiry")
    @GetMapping(value = "v1/inquiry-prod-list")
    public Result<List<MobileInquiryCompanyVO>> inquiryProdList(@ApiIgnore UserVO userVO) {
        List<MobileInquiryCompanyVO> dataList = getDataFromCache(userVO.getUserId());
        if (CollectionUtils.isEmpty(dataList)) {
            return Result.success();
        }
        List<MobileInquiryCompanyVO> supplierIds = dataList.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MobileInquiryCompanyVO::getSupplierId))), ArrayList::new));

        supplierIds.forEach(v ->
            dataList.forEach(mobileInquiryCompanyVO -> {
                if (mobileInquiryCompanyVO.getSupplierId().equals(v.getSupplierId())) {
                    List<MobileInquiryProdVO> inquiryProdVOS = v.getMobileInquiryProdVOS();
                    inquiryProdVOS.addAll(mobileInquiryCompanyVO.getMobileInquiryProdVOS());
                    //去重
                    inquiryProdVOS = inquiryProdVOS.stream().collect(
                            Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MobileInquiryProdVO::getCreateDate))), ArrayList::new));
                    //重排序
                    inquiryProdVOS = inquiryProdVOS.stream().sorted(Comparator.comparing(MobileInquiryProdVO::getCreateDate).reversed()).collect(Collectors.toList());
                    // 获取最大的时间
                    Optional<MobileInquiryProdVO> data = inquiryProdVOS.stream().max(Comparator.comparing(MobileInquiryProdVO::getCreateDate));
                    v.setCreateDate(data.get().getCreateDate());
                    v.setMobileInquiryProdVOS(inquiryProdVOS);
                }
            })
        );
        List<MobileInquiryCompanyVO> result = supplierIds.stream().sorted(Comparator.comparing(MobileInquiryCompanyVO::getCreateDate).reversed()).collect(Collectors.toList());
        return Result.success(result);
    }

    private List<MobileInquiryCompanyVO> getDataFromCache(Long userId) {
        try {
            Map<String, String> cache = redisTemplate.opsForHash().entries(RedisCachePrefix.INQUIRY_PRODUCT_LIST + userId);
            List<MobileInquiryCompanyVO> result = new ArrayList<>(cache.size());
            cache.entrySet().forEach(entry ->
                result.add(JSON.parseObject(entry.getValue(), MobileInquiryCompanyVO.class))
            );
            return result;
        } catch (Exception e) {
            log.error("InquiryController H5 getDataFromCache error,userId:{},error:{}", userId, JSON.toJSONString(e));
            return new ArrayList<>();
        }
    }

    @Login(validLogin = false)
    @ApiOperation(value = "供应商询盘模板", tags = "H5 Site--Inquiry")
    @GetMapping(value = "v1/supplier-temp")
    public Result<String> supplierTemplate(@ApiIgnore UserVO userVO) {
        EmailTemplateDTO emailTemplateDTO = new EmailTemplateDTO();
        emailTemplateDTO.setType(TemplateEnums.SUPPLIER);
        if (ObjectUtils.isNotEmpty(userVO)) {
            emailTemplateDTO.setUsername(userVO.getFirstName() + " " + userVO.getLastName());
        }
        return inquiryFeign.messageTemp(emailTemplateDTO);
    }

    @ApiOperation(value = "供应商信息接口", tags = "H5 Site--Inquiry")
    @GetMapping(value = "v1/supplier-detail/{supplierId}")
    public Result<SupplierDetailVO> supplierDetail(@PathVariable Long supplierId) {
        Result<SupplierCommonInfoDTO> commonInfo = supplierAggFeign.getCommonInfo(supplierId);
        SupplierCommonInfoDTO supplierMainInfoDto = ResultUtil.getData(commonInfo);
        SupplierDetailVO supplierDetailVO = SupplierDetailVO.builder()
                .supplierId(supplierId)
                .supplierType(supplierMainInfoDto.getSupplierType())
                .companyName(supplierMainInfoDto.getCompanyDisplayName())
                .maxContractLevel(supplierMainInfoDto.getMaxContractLevel())
                .o2oFlag(supplierMainInfoDto.getO2oFlag())
                .memberSince(supplierMainInfoDto.getMemberSince())
                .verifiedManufacturerFlag(supplierMainInfoDto.getVerifiedManufacturerFlag())
                .verifiedSupplierFlag(supplierMainInfoDto.getVerifiedSupplierFlag())
                .supplierShortName(supplierMainInfoDto.getSupplierShortName())
                .build();
        return Result.success(supplierDetailVO);
    }

    @Login(validLogin = false)
    @ApiOperation(value = "产品询盘模板", tags = "H5 Site--Inquiry")
    @GetMapping(value = "v1/prod-temp/{prodId}")
    public Result<String> prodTemp(@ApiIgnore UserVO userVO, @PathVariable Long prodId) {
        EmailTemplateDTO emailTemplateDTO = new EmailTemplateDTO();
        emailTemplateDTO.setProductId(prodId);
        emailTemplateDTO.setType(TemplateEnums.PRODUCT);
        if (ObjectUtils.isNotEmpty(userVO)) {
            emailTemplateDTO.setUsername(userVO.getFirstName() + " " + userVO.getLastName());
        }
        return inquiryFeign.messageTemp(emailTemplateDTO);
    }

    @ApiOperation(value = "产品一对一产品详情", tags = "H5 Site--Inquiry")
    @GetMapping(value = "v1/inquiry-prod-detail/{productId}")
    public Result<InquiryProductDetailBffVO> inquiryProductDetail(@PathVariable Long productId) {
        InquiryProductDetailBffVO inquiryProductDetailVO = new InquiryProductDetailBffVO();
        ProductDetailVo productDetailVo = buyerProductFeign.productDetail(productId).getData();

        if (ObjectUtils.isEmpty(productDetailVo)) {
            return Result.failed(ResultCodeEnum.THE_OBJECT_WAS_NOT_FOUND);
        }
        Long supplierId = productDetailVo.getProduct().getOrgId();
        Result<SupplierCommonInfoDTO> commonInfo = supplierAggFeign.getCommonInfo(supplierId);
        SupplierCommonInfoDTO supplierMainInfoDto = ResultUtil.getData(commonInfo);
        if (ObjectUtils.isNotEmpty(productDetailVo)) {
            inquiryProductDetailVO.setProductId(productId);

            inquiryProductDetailVO.setProductId(productId);
            inquiryProductDetailVO.setProductTitle(productDetailVo.getProductInfoMultiLan().getProductName());
            inquiryProductDetailVO.setProductDesc(productDetailVo.getProductInfoMultiLan().getProductDescription());
            inquiryProductDetailVO.setProductImage(productDetailVo.getProductPrimaryImage());
            inquiryProductDetailVO.setCategoryName(productDetailVo.getCategoryInfo().getL4CategoryVo().getCategoryName());
            inquiryProductDetailVO.setCompanyName(productDetailVo.getSupplierSnippetInfo().getCompanyName());
            inquiryProductDetailVO.setQuantity(productDetailVo.getProduct().getMinOrderQuantity());
            inquiryProductDetailVO.setUnit(productDetailVo.getProduct().getMinOrderUnit());

            inquiryProductDetailVO.setSupplierId(productDetailVo.getSupplierSnippetInfo().getOrgId());

            inquiryProductDetailVO.setProductSearchKeyword(productDetailVo.getProduct().getSearchKeywords());
            Long categoryId = productDetailVo.getCategoryInfo().getL4CategoryVo().getCategoryId();
            inquiryProductDetailVO.setCategoryId(categoryId);
            if (Objects.nonNull(categoryId)) {
                List<ProductCategoryAttributeDetailVO> productCategoryAttributes = inquiryProductService.getProductCategoryAttributesByCategoryIds(Lists.newArrayList(categoryId));
                inquiryProductDetailVO.setProductCategoryAttrInfos(productCategoryAttributes);
            }
            //数据追踪
            inquiryProductDetailVO.setL1CategoryId(productDetailVo.getCategoryInfo().getL1CategoryVo().getCategoryId());
            inquiryProductDetailVO.setL2CategoryId(productDetailVo.getCategoryInfo().getL2CategoryVo().getCategoryId());
            inquiryProductDetailVO.setL3CategoryId(productDetailVo.getCategoryInfo().getL3CategoryVo().getCategoryId());
            inquiryProductDetailVO.setL4CategoryId(productDetailVo.getCategoryInfo().getL4CategoryVo().getCategoryId());
        }
        if (ObjectUtils.isNotEmpty(supplierMainInfoDto)) {
            inquiryProductDetailVO.setMaxContractLevel(supplierMainInfoDto.getMaxContractLevel());
            inquiryProductDetailVO.setO2oFlag(supplierMainInfoDto.getO2oFlag());
            inquiryProductDetailVO.setMemberSince(supplierMainInfoDto.getMemberSince());
            inquiryProductDetailVO.setVerifiedManufacturerFlag(supplierMainInfoDto.getVerifiedManufacturerFlag());
            inquiryProductDetailVO.setVerifiedSupplierFlag(supplierMainInfoDto.getVerifiedSupplierFlag());
            //数据追踪
            inquiryProductDetailVO.setSupplierId(supplierMainInfoDto.getSupplierId());
            inquiryProductDetailVO.setSupplierType(supplierMainInfoDto.getSupplierType());
            inquiryProductDetailVO.setSupplierShortName(supplierMainInfoDto.getSupplierShortName());
            inquiryProductDetailVO.setAggTwoFlag(supplierMainInfoDto.getAggTwoFlag());
        }
        return Result.success(inquiryProductDetailVO);
    }

    @ApiOperation(value = "产品一对多", tags = "H5 Site--Inquiry")
    @PostMapping(value = "/prod-many-info")
    @Login(validLogin = false)
    public Result<List<SupplierManyInfoVO>> prodManyInfo(@RequestBody InquiryIdsDTO inquiryIdsDTO) {
        List<SupplierManyInfoVO> supplierManyInquiryVOResult = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(inquiryIdsDTO) && inquiryIdsDTO.getIds().length > 0) {
            for (Long str : inquiryIdsDTO.getIds()) {
                ProductDetailVo result = buyerProductFeign.productDetail(str).getData();
                if (ObjectUtils.isNotEmpty(result)) {
                    Long supplierId = result.getProduct().getOrgId();
                    //产品信息
                    SupplierProdInfoVO supplierProdInquiryVO = new SupplierProdInfoVO();
                    supplierProdInquiryVO.setProductId(result.getProduct().getProductId());
                    supplierProdInquiryVO.setProductImage(result.getProductPrimaryImage());
                    supplierProdInquiryVO.setProductName(result.getProductInfoMultiLan().getProductName());
                    supplierProdInquiryVO.setQuantity(result.getProduct().getMinOrderQuantity());
                    supplierProdInquiryVO.setUnit(result.getProduct().getMinOrderSingleUnit());

                    supplierProdInquiryVO.setL1CategoryId(result.getCategoryInfo().getL1CategoryVo().getCategoryId());
                    supplierProdInquiryVO.setL2CategoryId(result.getCategoryInfo().getL2CategoryVo().getCategoryId());
                    supplierProdInquiryVO.setL3CategoryId(result.getCategoryInfo().getL3CategoryVo().getCategoryId());
                    supplierProdInquiryVO.setL4CategoryId(result.getCategoryInfo().getL4CategoryVo().getCategoryId());

                    Long categoryId = inquiryProductService.getCategoryIdByProductDetailVoOfAgg(result);
                    if (Objects.nonNull(categoryId)) {
                        supplierProdInquiryVO.setCategoryId(categoryId);
                        List<ProductCategoryAttributeDetailVO> productCategoryAttributes = inquiryProductService.getProductCategoryAttributesByCategoryIds(Lists.newArrayList(categoryId));
                        supplierProdInquiryVO.setProductCategoryAttrInfos(productCategoryAttributes);
                    }
                    //公司信息
                    SupplierCommonInfoDTO supplierMainInfoDto = supplierAggFeign.getCommonInfo(supplierId).getData();
                    SupplierManyInfoVO supplierManyInquiryVO = new SupplierManyInfoVO();
                    supplierManyInquiryVO.setSupplierId(supplierId);
                    supplierManyInquiryVO.setCompanyName(supplierMainInfoDto.getCompanyDisplayName());
                    supplierManyInquiryVO.setMaxContractLevel(supplierMainInfoDto.getMaxContractLevel());
                    supplierManyInquiryVO.setO2oFlag(supplierMainInfoDto.getO2oFlag());
                    supplierManyInquiryVO.setMemberSince(supplierMainInfoDto.getMemberSince());
                    supplierManyInquiryVO.setVerifiedSupplierFlag(supplierMainInfoDto.getVerifiedSupplierFlag());
                    supplierManyInquiryVO.setVerifiedManufacturerFlag(supplierMainInfoDto.getVerifiedManufacturerFlag());
                    supplierManyInquiryVO.setSupplierType(supplierMainInfoDto.getSupplierType());
                    supplierManyInquiryVO.setSupplierShortName(supplierMainInfoDto.getSupplierShortName());

                    if (CollectionUtils.isNotEmpty(supplierManyInquiryVOResult)) {
                        AtomicBoolean flag = new AtomicBoolean(false);
                        supplierManyInquiryVOResult.stream().forEach(v -> {
                            if (v.getSupplierId().equals(supplierId)) {
                                List<SupplierProdInfoVO> supplierProdInquiryVOList = v.getProdInquiryVOList();
                                supplierProdInquiryVOList.add(supplierProdInquiryVO);
                                flag.set(true);
                            }
                        });

                        if (!flag.get()) {
                            List<SupplierProdInfoVO> supplierProdInquiryVOList = new ArrayList<>();
                            supplierProdInquiryVOList.add(supplierProdInquiryVO);
                            supplierManyInquiryVO.setProdInquiryVOList(supplierProdInquiryVOList);
                            supplierManyInquiryVOResult.add(supplierManyInquiryVO);
                        }
                    } else {
                        List<SupplierProdInfoVO> supplierProdInquiryVOList = new ArrayList<>();
                        supplierProdInquiryVOList.add(supplierProdInquiryVO);
                        supplierManyInquiryVO.setProdInquiryVOList(supplierProdInquiryVOList);
                        supplierManyInquiryVOResult.add(supplierManyInquiryVO);
                    }
                }
            }
        }
        return Result.success(supplierManyInquiryVOResult);
    }

    @Login(validLogin = false)
    @ApiOperation(value = "询盘提交", tags = "H5 Site--Inquiry")
    @PostMapping(value = "v1/h5InquiryNow")
    public Result<List<String>> h5InquiryNow(@ApiIgnore UserVO userVO, @Valid @RequestBody InquiryFromDTO inquireFromDTO, HttpServletRequest request) {
        if (StringUtils.isNotBlank(inquireFromDTO.getToken())) {
            Long userId = Long.valueOf(TokenUtil.getUserId(inquireFromDTO.getToken()));
            userVO = userQueryFeign.getUserByUserId(userId).getData();
            if (ObjectUtils.isEmpty(userVO)) {
                return Result.failed();
            }
        }
        RequestInquiryDTO requestInquiryDTO = new RequestInquiryDTO();
        String sessionId = request.getSession().getId();
        ProductLiteVO productDetailVo = getProductInfoEn(inquireFromDTO.getInquiryProductId());

        if (ObjectUtils.isEmpty(productDetailVo)) {
            return Result.failed(ResultCodeEnum.THE_OBJECT_WAS_NOT_FOUND);
        }
        //处理重复提交问题
        String duplicateValue = (String) redisTemplate.opsForValue().getAndSet(RedisCachePrefix.INQUIRY_DUPLICATE_DATA + inquireFromDTO.getSessionId(), DEFAULT_REDIS_VALUE);
        if (DEFAULT_REDIS_VALUE.equals(duplicateValue)) {
            return Result.failed(ResultCodeEnum.REQUEST_BUSY);
        }
        redisTemplate.expire(RedisCachePrefix.INQUIRY_DUPLICATE_DATA + inquireFromDTO.getSessionId(), 3, TimeUnit.SECONDS);
        requestInquiryDTO.setMessage(inquireFromDTO.getMessage());
        if (CollectionUtils.isNotEmpty(inquireFromDTO.getAttachmentList())) {
            requestInquiryDTO.setAttachmentList(OrikaMapperUtil.coverList(inquireFromDTO.getAttachmentList(), AttachmentAggDTO.class));
        }
        requestInquiryDTO.setSupplierIds(new Long[]{productDetailVo.getSupplierId()});
        requestInquiryDTO.setSessionId(inquireFromDTO.getSessionId());
        requestInquiryDTO.setJsSessionId(sessionId);
        requestInquiryDTO.setInquiryType(InquiryTypeEnum.PRODUCT);
        requestInquiryDTO.setRecommendFlag(false);
        RequestProductDTO requestProductDTO = new RequestProductDTO();
        if (Boolean.TRUE.equals(inquireFromDTO.getRecommendFlag())) {
            requestInquiryDTO.setInquiryType(InquiryTypeEnum.PRODUCT_UPSELL);
            requestInquiryDTO.setRecommendFlag(true);
        }
        requestProductDTO.setSupplierId(productDetailVo.getSupplierId());
        requestProductDTO.setProductId(productDetailVo.getProductId());
        requestProductDTO.setProductImage(productDetailVo.getProductPrimaryImage());
        requestProductDTO.setProductName(productDetailVo.getProductName());
        requestProductDTO.setInquiryCatDesc(productDetailVo.getCategoryName());
        requestProductDTO.setModelNumber(productDetailVo.getModelNumber());
        requestProductDTO.setProductNum(inquireFromDTO.getProductNum());
        requestProductDTO.setProductUnit(productDetailVo.getMinOrderUnit());
        requestInquiryDTO.setRegFlag(ObjectUtils.isNotEmpty(inquireFromDTO.getRegFlag()) ? inquireFromDTO.getRegFlag() : Boolean.FALSE);
        requestInquiryDTO.setConvertRfqFlag(ObjectUtils.isNotEmpty(inquireFromDTO.getConvertRfqFlag()) ? inquireFromDTO.getConvertRfqFlag() : Boolean.FALSE);

        if (CollectionUtils.isNotEmpty(inquireFromDTO.getProductCategoryAttrInfos())) {
            requestProductDTO.setProductCategoryAttrInfos(inquireFromDTO.getProductCategoryAttrInfos());
        }
        requestInquiryDTO.setProductDTOList(Collections.singletonList(requestProductDTO));
        requestInquiryDTO.setIpAddr(HttpUtil.getUserIp(request));
        requestInquiryDTO.setRfiSource(2);
        requestInquiryDTO.setUserProfile(userUtil.buildUserProfileDTO(userVO));
        requestInquiryDTO.setInquiryPath(inquireFromDTO.getInquiryPath());

        Boolean flag = inquireFromDTO.getRecommendFlag();
        if (Boolean.TRUE.equals(flag)) {
            requestInquiryDTO.setInquiryType(InquiryTypeEnum.getEnumByKey(rfiProfile));

            Boolean expireTime = userUtil.isMatchExpire(inquireFromDTO.getInquiryProductId(), userVO.getUserId());
            if (Boolean.TRUE.equals(expireTime)) {
                requestInquiryDTO.setMatchId(inquireFromDTO.getMatchId());
            } else {
                log.info("Changed user submitted within 30 minutes, userId = {}", userVO.getUserId());
                requestInquiryDTO.setInquiryType(InquiryTypeEnum.PRODUCT);
                requestInquiryDTO.setRecommendFlag(false);
            }
        } else {
            requestInquiryDTO.setInquiryType(InquiryTypeEnum.PRODUCT);
        }

        log.info("invoke inquiryFeign.inquireNow begin, userId = {}", requestInquiryDTO.getUserProfile().getUserId());
        Result<List<String>> ids = inquiryFeign.inquireNow(requestInquiryDTO);
        log.info("invoke inquiryFeign.inquireNow end, userId = {}, result data = {}", requestInquiryDTO.getUserProfile().getUserId(), ids);
        if (CollectionUtils.isNotEmpty(ids.getData())) {
            String[] resultIds = ids.getData().toArray(new String[0]);
            IdsVO idsVO = new IdsVO();
            idsVO.setInquiryIds(resultIds);
            return ids;
        }
        return Result.failed();
    }

    @Login
    @ApiOperation(value = "供应商询盘提交", tags = "H5 Site--Inquiry")
    @PostMapping(value = "v1/h5SupplierInquiryNow")
    public Result<List<String>> h5SupplierInquiryNow(@ApiIgnore UserVO userVO, @Valid @RequestBody SupplierInquiryFromDTO supplierInquireFromDTO, HttpServletRequest request) {
        RequestInquiryDTO requestInquiryDTO = new RequestInquiryDTO();
        requestInquiryDTO.setInquiryType(InquiryTypeEnum.SUPPLIER);
        String sessionId = request.getSession().getId();
        //查询公司
        requestInquiryDTO.setSupplierIds(new Long[]{supplierInquireFromDTO.getSupplierId()});
        requestInquiryDTO.setRfiSource(2);
        requestInquiryDTO.setUserProfile(userUtil.buildUserProfileDTO(userVO));
        requestInquiryDTO.setIpAddr(HttpUtil.getUserIp(request));
        requestInquiryDTO.setJsSessionId(sessionId);
        requestInquiryDTO.setSessionId(supplierInquireFromDTO.getSessionId());
        requestInquiryDTO.setMessage(supplierInquireFromDTO.getMessage());
        requestInquiryDTO.setRecommendFlag(false);
        requestInquiryDTO.setRegFlag(ObjectUtils.isNotEmpty(supplierInquireFromDTO.getRegFlag()) ? supplierInquireFromDTO.getRegFlag() : Boolean.FALSE);
        requestInquiryDTO.setInquiryPath(supplierInquireFromDTO.getInquiryPath());
        if (CollectionUtils.isNotEmpty(supplierInquireFromDTO.getAttachmentList())) {
            requestInquiryDTO.setAttachmentList(OrikaMapperUtil.coverList(supplierInquireFromDTO.getAttachmentList(), AttachmentAggDTO.class));
        }
        log.info("user id = {}", userVO.getUserId());
        return inquiryFeign.inquireNow(requestInquiryDTO);
    }

    @Login
    @ApiOperation(value = "询盘列表", tags = "H5 Site--Inquiry")
    @PostMapping(value = "v1/h5InquiryList")
    public Result<PageResult<H5InquiryVO>> h5InquiryList(@ApiIgnore UserVO userVO, @RequestBody BasePage basePage) {
        return buyerInquiryFeign.h5InquiryList(basePage, userVO.getUserId());
    }

    public Tuple2<List<MobileInquiryCompanyVO>, List<MobileInquiryProdVO>> collectionsIds(Long userId) {
        List<MobileInquiryCompanyVO> dataList = getDataFromCache(userId);
        if (CollectionUtils.isEmpty(dataList)) {
            return Tuple.of(new ArrayList<>(), new ArrayList<>());
        }

        List<MobileInquiryProdVO> productIds = new ArrayList<>(dataList.size());
        List<MobileInquiryCompanyVO> supplierIds = new ArrayList<>(dataList.size());

        dataList.stream().forEach(mobileInquiryCompanyVO -> {
            supplierIds.add(mobileInquiryCompanyVO);
            for (MobileInquiryProdVO mobileInquiryProdVO : mobileInquiryCompanyVO.getMobileInquiryProdVOS()) {
                productIds.add(mobileInquiryProdVO);
            }
        });
        return Tuple.of(supplierIds, productIds);
    }

    @Login
    @ApiOperation(value = "买家列表数统计", tags = "H5 Site--Inquiry")
    @GetMapping(value = "v1/buyerInquiryListTotal")
    public Result<BuyerInquiryListTotalVO> buyerInquiryListTotal(@ApiIgnore UserVO userVO) {
        return buyerInquiryFeign.inquiryStatusCount(userVO.getUserId(), 1);
    }

    @Login
    @GetMapping(value = "v1/notice")
    @ApiOperation(value = "Mobile Buyer 通知消息", tags = "H5 Site--Inquiry")
    public Result<List<InquiryNoticeVO>> notice(@ApiIgnore UserVO userVO, HttpServletRequest request) {
        return Result.success(messageAggFeign.notice(userVO.getUserId(), 0L, 1));
    }

    @Login
    @GetMapping(value = "v1/notice-total")
    @ApiOperation(value = "通知消息总数", tags = "H5 Site--Inquiry")
    public Result<Integer> noticeTotal(@ApiIgnore UserVO userVO, HttpServletRequest request) {
        return Result.success(messageAggFeign.notice(userVO.getUserId(), 0L, 1).size());
    }

    @GetMapping(value = "v1/inquirySession")
    @ApiOperation(value = "生成gsol客户端session", tags = "H5 Site--Inquiry")
    public Result<InquirySessionVO> inquirySession(HttpServletRequest request) {
        Snowflake snowflake = IdUtil.createSnowflake(1, 1);
        InquirySessionVO inquirySessionVO = InquirySessionVO.builder()
                .sessionId(snowflake.nextId())
                .orgId(orgId).build();
        return Result.success(inquirySessionVO);
    }

    @Login
    @GetMapping(value = "/query-unread-count")
    @ApiOperation(value = "查询未读数量")
    public Result<Integer> queryUnreadCount(@ApiIgnore UserVO userVO) {
        Integer num = messageService.queryUnreadCount(userVO);
        return Result.success(num);
    }

    @Login
    @ApiOperation(value = "买家回复模板列表(有content)", notes = "卖家回复模板列表(有content)", tags = {"Web Site--Inquiry"})
    @GetMapping("v1/reply-template/detail-list")
    public Result<List<InquiryReplyTemplateAggDTO>> getReplyTemplateDetailList(@ApiIgnore UserVO userVO) {
        List<InquiryReplyTemplateAggDTO> replyTemplateList = inquiryReplyTemplateService.getReplyTemplateDetailList(userVO, false);
        return Result.success(replyTemplateList);
    }

    @Login
    @ApiOperation(value = "doi days", tags = "Web Site--Inquiry")
    @GetMapping(value = "v1/doi-days")
    public Result<Integer> getDoiDays() {
        return Result.success(doiDays);
    }

    @ApiOperation(value = "doi settings", tags = "Web Site--Inquiry")
    @GetMapping(value = "v1/doi-settings")
    public Result<DoiSettingVO> doiSettings() {
        return Result.success(DoiSettingVO.builder().days(doiDays).doiFlag(doiFlag).build());
    }
    
    @ApiOperation(value = "邮件一对多询盘提交", tags = "H5 Site--Inquiry")
    @GetMapping(value = "v1/email-upsell-submit")
    public Result<Integer> emailUpsellSubmit(@RequestParam String threadId, @RequestParam String token) {
        log.info("emailUpsellSubmit threadId:{},token:{}",threadId,token);
        if(StringUtils.isBlank(threadId) || StringUtils.isBlank(token)){
            return Result.failed(ResultCodeEnum.PARAMETER_VALIDATION_FAILED);
        }
        Long userId = null;
        try {
            userId =Long.valueOf(TokenUtil.getUserId(token));
        }catch (Exception e){
            log.warn("emailUpsellSubmit token error,token:{}",token);
            return Result.failed(ResultCodeEnum.PARAMETER_VALIDATION_FAILED);
        }
        return inquiryFeign.convertUpsell(threadId,userId);
    }

    @ApiOperation(value = "获取推荐产品")
    @GetMapping("/recommend-product")
    public Result<List<RecommendProductInfoVO>> recommendProduct(@RequestParam Long productId){
        return inquiryFeign.recommendProduct(productId);
    }

}
