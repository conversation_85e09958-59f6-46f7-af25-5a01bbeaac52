package com.globalsources.rfx.bff.controller.rfq.web;

import com.globalsources.framework.result.Result;
import com.globalsources.rfq.bff.api.model.dto.req.RfqMatchReserveRequestDTO;
import com.globalsources.rfq.bff.api.model.vo.match.RfqMatchReserveVO;
import com.globalsources.rfx.bff.service.rfq.IRfqMatchMeetingBffService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <a>Title: RfqMatchMeetingWebController </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: RfqMatchMeetingWebController <a>
 *
 * <AUTHOR>
 * @date 2021/11/26 9:32
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("rfq-bff/match-meeting")
@Api(tags = {"Web Site--rfq match 预约相关接口"})
public class RfqMatchMeetingWebController {

    private final IRfqMatchMeetingBffService rfqMatchMeetingAggService;

    @ApiOperation(value = "rfq--match-meeting 预约", notes = "rfq--match-meeting 预约")
    @PostMapping("v1/reserve")
    public Result<RfqMatchReserveVO> reserve(@RequestBody RfqMatchReserveRequestDTO requestDTO) {
        return rfqMatchMeetingAggService.reserve(requestDTO);
    }
}
