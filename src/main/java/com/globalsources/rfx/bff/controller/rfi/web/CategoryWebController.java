package com.globalsources.rfx.bff.controller.rfi.web;

import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.TokenUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfi.agg.dto.inquiry.InquiryUpsellMatchDTO;
import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import com.globalsources.rfi.agg.feign.BuyerInquiryFeign;
import com.globalsources.rfi.agg.response.product.MatchProductInfo;
import com.globalsources.rfi.agg.response.product.MatchRfiThreadVO;
import com.globalsources.rfx.bff.util.UserUtil;
import com.globalsources.user.api.feign.UserQueryFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <a>Title: CategoryWebController </a>
 * <a>Author: Levlin Li <a>
 * <a>Description：L4 匹配接口<a>
 *
 * <AUTHOR> Li
 * @date 2022/4/6-11:38
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("rfi-web/category")
@Api(tags = {"Web Site--Category Buyer RFI"})
@RefreshScope
public class CategoryWebController {

    @Value("${gsol.rfx.upsell.profile}")
    public String upsellProfile;

    @Value("${gsol.rfx.category.expire.minute:30}")
    public Integer minute;

    private final UserUtil userUtil;
    private final UserQueryFeign userQueryFeign;
    private final BuyerInquiryFeign buyerInquiryFeign;

    @ApiOperation(value = "获取L4，upsell的配置", tags = "Web Site--Category Buyer RFI")
    @GetMapping("v1/match-profile")
    public Result<String> matchProfile() {
        return Result.success(upsellProfile);
    }

    @Login(validLogin = false)
    @ApiOperation(value = "用户指定时间内发送询盘")
    @GetMapping("v1/category-expire/{productId}")
    public Result<Boolean> categoryExpire(@ApiIgnore UserVO userVO, @PathVariable Long productId, @RequestParam(required = false, defaultValue = "") String token) {
        //Redis key规则
        if (StringUtils.isNotEmpty(token)) {
            //通过token获取用户信息
            Long userId = Long.valueOf(TokenUtil.getUserId(token));
            userVO = userQueryFeign.getUserByUserId(userId).getData();
            if (ObjectUtils.isEmpty(userVO)) {
                log.error("invoke category expire fail, userQueryFeign.getUserByUserId empty, token = {}", token);
                return Result.success(false);
            }
        }

        if (ObjectUtils.isNotEmpty(userVO)) {
            return Result.success(userUtil.isCacheKey(productId, userVO.getUserId()));
        }
        return Result.success(false);
    }

    @Login(validLogin = false)
    @ApiOperation(value = "根据参数获取L4，upsell匹配数据", tags = "Web Site--Category Buyer RFI")
    @PostMapping("v1/match-rfi")
    public Result<MatchRfiThreadVO> matchRfi(@ApiIgnore UserVO userVO, @RequestBody InquiryUpsellMatchDTO dto) {
        log.info("matchRfi dto:{}",dto);
        Long userId = 0L;
        if (ObjectUtils.isNotEmpty(userVO)) {
            userId = userVO.getUserId();
        }
        if (StringUtils.isNotEmpty(dto.getToken())) {
            userId = Long.valueOf(TokenUtil.getUserId(dto.getToken()));
        }
        MatchRfiThreadVO data = null;
        //L4,upsell
        if (InquiryTypeEnum.CATEGORY.getKey().equals(upsellProfile)) {
            //L4 是否已经匹配过了
            data = buyerInquiryFeign.l4Upsell(dto.getProductId(), userId).getData();
        } else {
            //upsell 匹配结果
            data = buyerInquiryFeign.matchUpsellRfi(dto).getData();
        }
        if (ObjectUtils.isNotEmpty(data) && CollectionUtils.isNotEmpty(data.getProductInfoList())) {
            List<Long> suppCnt = data.getProductInfoList().stream().map(MatchProductInfo::getSupplierId).distinct().collect(Collectors.toList());
            data.setSupplierCnt(suppCnt.size());
            data.setMatchName(upsellProfile.equals(InquiryTypeEnum.PRODUCT_UPSELL.getKey()) ? dto.getQuery() : data.getProductInfoList().get(0).getCategoryName());
            return Result.success(data);
        }
        return Result.success(ResultCode.CommonResultCode.SUCCESS.getCode(), ResultCode.RfiResultCode.RFI_MATCH_EMPTY_DATA.getMsg(), null);
    }
}
