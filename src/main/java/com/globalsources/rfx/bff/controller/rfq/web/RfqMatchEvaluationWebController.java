package com.globalsources.rfx.bff.controller.rfq.web;

import com.globalsources.framework.result.Result;
import com.globalsources.rfq.bff.api.model.dto.req.RfqMatchEvaluationRequestDTO;
import com.globalsources.rfx.bff.service.rfq.IRfqMatchEvaluationBffService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <a>Title: RfqMatchEvaluationWebController </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: RfqMatchEvaluationWebController <a>
 *
 * <AUTHOR>
 * @date 2021/11/26 9:32
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("rfq-bff/match-evaluation")
@Api(tags = {"Web Site--rfq match 评价相关接口"})
public class RfqMatchEvaluationWebController {

    private final IRfqMatchEvaluationBffService rfqMatchEvaluationService;

    @ApiOperation(value = "rfq match evaluation 评论rfq match", notes = "rfq match evaluation 评论rfq match")
    @PostMapping("v1/evaluation")
    public Result<Boolean> evaluation(@Valid @RequestBody RfqMatchEvaluationRequestDTO requestDTO) {
        return rfqMatchEvaluationService.evaluation(requestDTO);
    }
}
