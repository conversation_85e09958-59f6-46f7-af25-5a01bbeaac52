package com.globalsources.rfx.bff.controller.rfi.web;

import com.globalsources.framework.result.Result;
import com.globalsources.rfi.agg.dto.inquiry.MonitorInquiryQueryDTO;
import com.globalsources.rfi.agg.feign.InquiryAggFeign;
import com.globalsources.rfi.agg.response.rfi.MonitorInquiryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2023-4-19
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("rfi-web/monitor")
@Api(tags = {"rfi monitor api"})
public class MonitorController {

    private final InquiryAggFeign inquiryAggFeign;

    @ApiOperation(value = "获取最新询盘记录", tags = "rfi monitor api")
    @PostMapping("v1/monitor-inquiry")
    public Result<List<MonitorInquiryVO>> monitorInquiry(@RequestBody MonitorInquiryQueryDTO monitorInquiryQueryDTO) {
        return inquiryAggFeign.queryMonitorInquiry(monitorInquiryQueryDTO);
    }
}
