package com.globalsources.rfx.bff.controller.rfq.app;

import com.alibaba.fastjson.JSON;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.model.bff.dto.quotImport.ImportCommonDTO;
import com.globalsources.model.bff.vo.quotImport.CategoryChooseVO;
import com.globalsources.model.bff.vo.quotImport.ImportProductListVO;
import com.globalsources.model.bff.vo.quotImport.ImportProductVO;
import com.globalsources.rfq.bff.api.feign.QuotImportAggService;
import com.globalsources.rfq.bff.api.model.dto.req.*;
import com.globalsources.rfq.bff.api.model.vo.app.BuyerAppQuotationCompareVO;
import com.globalsources.rfq.bff.api.model.vo.app.RfqBuyerAppQuotationDetailVO;
import com.globalsources.rfq.bff.api.model.vo.app.RfqBuyerAppQuotationVO;
import com.globalsources.rfq.bff.api.model.vo.seller.RfqQuotationImportVO;
import com.globalsources.rfq.bff.api.model.vo.seller.SellerQuotationCountVO;
import com.globalsources.rfq.bff.api.util.UserUtil;
import com.globalsources.rfq.core.api.model.dto.app.RfqAppInfoUnReadQuotationListRequestDTO;
import com.globalsources.rfx.bff.service.rfq.IRfqQuotationBffService;
import com.globalsources.rfx.bff.struct.rfq.RfqInfoRequestStruct;
import com.globalsources.rfx.bff.struct.rfq.RfqQuotationRequestStruct;
import com.globalsources.rfx.service.ISupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

/**
 * <a>Title: RfqQuotationAppController </a>
 * <a>Author: Mike Chen <a>
 * <a>Description: RfqQuotationAppController <a>
 *
 * <AUTHOR> Chen
 * @date 2021/11/26 17:10
 */
@Validated
@RestController
@RequiredArgsConstructor
@RefreshScope
@Api(tags = {"App Site--rfq报价相关接口"})
@RequestMapping("rfq-app-bff/quotation")
public class RfqQuotationAppController {

    private final ISupplierService iSupplierService;
    private final QuotImportAggService quotImportAggService;
    private final RfqInfoRequestStruct rfqInfoRequestStruct;
    private final IRfqQuotationBffService rfqQuotationBffService;
    private final RfqQuotationRequestStruct rfqQuotationRequestStruct;

    @Value("${prohibit_quotation_supplier}")
    private Long prohibitQuotationSupplier;

    @Value("${prohibit_quotation_user}")
    private String prohibitQuotationUser;

    @Login
    @PostMapping(value = "v1/unread-quotation-list")
    @ApiOperation(value = "买家rfq--未读报价列表", notes = "买家rfq--未读报价列表")
    public Result<PageResult<RfqBuyerAppQuotationVO>> unReadQuotationList(@ApiIgnore UserVO userVO, @Valid RfqAppInfoUnReadQuotationListRequestDTO requestDTO) {
        return Result.success(rfqQuotationBffService.getBuyerAppUnreadQuotationList(rfqInfoRequestStruct.toAppInfoUnReadQuotationListRequest(userVO, requestDTO)));
    }

    @Login
    @GetMapping(value = "v1/quotation-detail")
    @ApiOperation(value = "买家rfq--rfq报价详情", notes = "买家rfq--rfq报价详情")
    public Result<RfqBuyerAppQuotationDetailVO> getBuyerRfqQuotationDetail(@ApiIgnore UserVO userVO,@RequestParam String quotationId) {
        return rfqQuotationBffService.rfqBuyerAppDetail(quotationId,userVO.getUserId());
    }

    @Login(validSwitch = true)
    @PostMapping("v1/create-quotation")
    @ApiOperation(value = "卖家rfq--报价", notes = "卖家rfq--报价")
    public Result createQuotation(@ApiIgnore UserVO userVO, @Valid SupplierAppRfqQuotationCreateRequestDTO requestDTO) {

        requestDTO.setUserId(UserUtil.getUserId(userVO));
        requestDTO.setSupplierId(UserUtil.getSupplierId(userVO));
        requestDTO.setEmail(UserUtil.getEmail(userVO));

        if (ObjectUtils.isNotEmpty(prohibitQuotationSupplier) && userVO.getCurrentSupplier().getSupplierId().equals(prohibitQuotationSupplier)
                && prohibitQuotationUser.contains(String.valueOf(userVO.getUserId()))){
            return Result.failed("You have no access to quote, please contact your Super Administrator.");
        }

        List<RfqAttachmentRequestDTO> attachList = JSON.parseArray(requestDTO.getAttachmentList(), RfqAttachmentRequestDTO.class);
        List<RfqQuotationPriceRequestDTO> priceList = JSON.parseArray(requestDTO.getRfqQuotationPriceList(), RfqQuotationPriceRequestDTO.class);

        requestDTO.setAttachmentList(null);
        requestDTO.setRfqQuotationPriceList(null);
        RfqQuotationCreateRequestDTO dto = OrikaMapperUtil.coverObject(requestDTO, RfqQuotationCreateRequestDTO.class);
        dto.setProductCretification(requestDTO.getProductCertification());
        dto.setAttachmentList(attachList);
        dto.setRfqQuotationPriceList(priceList);
        return rfqQuotationBffService.createQuotation(dto);
    }

    @Login
    @PostMapping("v1/quotation-compare")
    @ApiOperation(value = "买家rfq--报价对比", notes = "买家rfq--报价对比")
    public Result<List<BuyerAppQuotationCompareVO>> quotationCompare(@ApiIgnore UserVO userVO, @Valid @RequestBody RfqQuotationCompareRequestDTO requestDTO) {
        return rfqQuotationBffService.appQuotationCompare(rfqQuotationRequestStruct.toRfqQuotationCompareRequest(userVO, requestDTO));
    }

    @Login(validSwitch = true)
    @ApiOperation(value = "卖家rfq--导入上次报价信息确认", notes = "卖家rfq--导入上次报价信息二次确认(返回true代表有上次报价信息,可以导入上次报价/false无上次报价信息 弹出toast提示 : 你暂时没有报价记录)")
    @GetMapping("v1/rfq-quotation-import-check")
    public Result<Boolean> importPreviousQuotationCheck(@ApiIgnore UserVO userVO) {
        Result<String> result = rfqQuotationBffService.importPreviousQuotationCheck(UserUtil.getSupplierId(userVO), UserUtil.getUserId(userVO));
        return Result.success(StringUtils.isNotBlank(ResultUtil.getData(result)));
    }

    @Deprecated
    @Login(validSwitch = true)
    @ApiOperation(value = "卖家rfq--导入上次报价信息", notes = "卖家rfq--导入上次报价信息")
    @GetMapping("v1/rfq-quotation-import")
    public Result<RfqQuotationImportVO> importPreviousQuotation(@ApiIgnore UserVO userVO) {
        return rfqQuotationBffService.importPreviousQuotation(UserUtil.getSupplierId(userVO), UserUtil.getUserId(userVO));
    }

    @Login(validSwitch = true)
    @ApiOperation(value = "卖家rfq--供应商报价数量", notes = "卖家rfq--供应商报价数量")
    @GetMapping("v1/rfq-quotation-count")
    public Result<SellerQuotationCountVO> supplierQuotationCount(@ApiIgnore UserVO userVO) {
        return rfqQuotationBffService.supplierQuotationCount(UserUtil.getSupplierId(userVO));
    }

    @Login(validSwitch = true)
    @ApiOperation(value = "卖家rfq--导入产品信息搜索接口", notes = "卖家rfq--导入产品信息搜索接口")
    @PostMapping("v1/import-product-list")
    public Result<List<ImportProductListVO>> importProductList(@ApiIgnore UserVO userVO, ImportCommonDTO importCommonDTO) {
        importCommonDTO.setMainAccount(iSupplierService.hasSupplierRfqAndRfiViewPermissions(userVO));
        return quotImportAggService.importProductList(rfqQuotationRequestStruct.toImportCommonRequest(userVO, importCommonDTO));
    }

    @Login(validSwitch = true)
    @ApiOperation(value = "卖家rfq--导入产品信息搜索类目", notes = "卖家rfq--导入产品信息搜索类目")
    @GetMapping({"v1/category-choose"})
    public Result<List<CategoryChooseVO>> categoryChoose(@ApiIgnore UserVO userVO) {
        return quotImportAggService.categoryChoose(rfqQuotationRequestStruct.toCategoryChooseRequest(userVO, iSupplierService.hasSupplierRfqAndRfiViewPermissions(userVO)));
    }

    @Login(validSwitch = true)
    @ApiOperation(value = "卖家rfq--导入产品信息", notes = "卖家rfq--导入产品信息")
    @GetMapping("v1/import-product")
    public Result<ImportProductVO> importProduct(@RequestParam("productId") Long productId) {
        return quotImportAggService.importProduct(productId);
    }

    @Login
    @ApiOperation(value = "买家rfq--聊天修改报价已回复状态", notes = "买家rfq--聊天修改报价已回复状态")
    @GetMapping("v1/rfq-quotation-chat-replied")
    public Result<Boolean> rfqQuotationChatReplied(@ApiIgnore UserVO userVO, @RequestParam("quotationId") String quotationId) {
        return rfqQuotationBffService.rfqQuotationChatReplied(rfqQuotationRequestStruct.toRfqQuotationChatRepliedRequest(quotationId, userVO));
    }
}
