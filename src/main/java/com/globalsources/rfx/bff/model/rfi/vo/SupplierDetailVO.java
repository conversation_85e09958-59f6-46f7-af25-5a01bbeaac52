package com.globalsources.rfx.bff.model.rfi.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: InquirySupplierDetailVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/10/13-18:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SupplierDetailVO implements Serializable {

    @ApiModelProperty("supplierId")
    private Long supplierId;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("supplierType: ADV AGG FL")
    private String supplierType;

    @ApiModelProperty("供应商最大等级, supplier package, 为null/-1/-2 标识不存在, -2无合同，-1 agg免费合同， 0-6: p0-p6")
    private Integer maxContractLevel;

    @ApiModelProperty("是否有o2o标志")
    private Boolean o2oFlag;

    @ApiModelProperty("与GS合作年限")
    private Integer memberSince;

    @ApiModelProperty("是否已认证制造商")
    private Boolean verifiedManufacturerFlag;

    @ApiModelProperty("是否为已认证供应商")
    private Boolean verifiedSupplierFlag;

    @ApiModelProperty("SEO, 供应商自定义域名")
    private String supplierShortName;

}
