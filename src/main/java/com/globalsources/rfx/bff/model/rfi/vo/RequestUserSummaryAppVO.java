package com.globalsources.rfx.bff.model.rfi.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RequestUserSummaryAppVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "总数")
    private String reqCnt="0";

    @ApiModelProperty(value = "回复数")
    private String reqRespondCnt="0";

    @ApiModelProperty(value = "回复率")
    private String responseRate="0";

    @ApiModelProperty(value = "买家回复数")
    private String buyerResponseCnt="0";

    @ApiModelProperty(value = "买家回复率")
    private String buyerResponseRate="0";

    @ApiModelProperty(value = "供应商删除数量")
    private String inqDeleteCnt="0";

    @ApiModelProperty(value = "平均回复时间")
    private String avgResponseTimeHrs="0";

    @ApiModelProperty(value = "买家已读数")
    private String buyerViewCnt="0";

    @ApiModelProperty(value = "买家已读率")
    private String buyerViewRate="0";

    @ApiModelProperty(value = "数据汇总时段，全部/最近30天/最近7天，ALL/LAST_30/LAST_7")
    private String summaryPeriod;

    @ApiModelProperty(value = "询盘类型，全部/一对一/一对多，ALL/ONE_TO_ONE/ONE_TO_ALL")
    private String inquiryTypePath;

}
