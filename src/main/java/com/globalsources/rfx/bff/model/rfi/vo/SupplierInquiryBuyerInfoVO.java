package com.globalsources.rfx.bff.model.rfi.vo;

import com.globalsources.rfx.bff.model.common.ChatUserInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/7/27 16:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SupplierInquiryBuyerInfoVO implements Serializable {

    private static final long serialVersionUID = 2913170034465949710L;

    @ApiModelProperty(value = "chat user info")
    private ChatUserInfoVO userInfo;

    @ApiModelProperty(value = "关注时间")
    private Date buyerFollowDate;

    @ApiModelProperty("买家类型")
    private Integer[] buyerType;

    @ApiModelProperty("ip")
    private String ipAddress;

    @ApiModelProperty("rfiSource")
    private Integer rfiSource;

    @ApiModelProperty(value = "最近回复询盘时间")
    private Date lastReplyDate;

    @ApiModelProperty(value = "buyer user id")
    private Long userId;

    @ApiModelProperty(value = "countryCode")
    private String countryCode;

    @ApiModelProperty(value = "ip 对应日期")
    private Date ipDate;

    @ApiModelProperty(value = "ip 时区")
    private String ipDifference;
}
