/**
 * <a>Title: InquirySessionVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/29-8:38
 */
package com.globalsources.rfx.bff.model.rfi.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class InquirySessionVO implements Serializable {

    @ApiModelProperty(value = "生成tmxsessionId")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long sessionId;

    @ApiModelProperty("tmx分配的orgId")
    private String orgId;
}
