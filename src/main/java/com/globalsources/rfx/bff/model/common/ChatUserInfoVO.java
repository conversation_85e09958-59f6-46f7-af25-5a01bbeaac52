package com.globalsources.rfx.bff.model.common;

import com.globalsources.chat.dto.ChatUserDetailInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/1
 */
@Data
public class ChatUserInfoVO {

    @ApiModelProperty("姓名")
    private String firstName;
    @ApiModelProperty("姓氏")
    private String lastName;
    @ApiModelProperty("头像")
    private String avatar;
    @ApiModelProperty("最近登录时间")
    private Date lastLoginDate;
    @ApiModelProperty("公司地址")
    private String companyUrl;
    @ApiModelProperty("职位名 ,")
    private String jobTitle;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("电话国家区号")
    private String telCountryCode;
    @ApiModelProperty("电话区码")
    private String telAreaCode;
    @ApiModelProperty("联系电话")
    private String phone;
    @ApiModelProperty("注册时间")
    private String regDate;
    @ApiModelProperty("国家代号，如CN , Country")
    private String countryCode;
    @ApiModelProperty("VIP flag")
    private Boolean vipFlag;
    @ApiModelProperty("DOI flag, 是否verify 用户")
    private Boolean doiFlag;
    @ApiModelProperty("verified Flag")
    private Boolean verifiedFlag;
    @ApiModelProperty("是否展示个人信息")
    private Boolean privateDataShowFlag;
    @ApiModelProperty("国家名 ")
    private String countryName;
    @ApiModelProperty("国旗图片地址")
    private String countryFlagUrl;
    @ApiModelProperty("公司地址")
    private String companyAddress;
    @ApiModelProperty("公司名字")
    private String companyName;
    @ApiModelProperty("员工总数 StaffRange")
    private String totalEmployee;
    @ApiModelProperty("公司简介")
    private String introduction;
    @ApiModelProperty("公司类型  BusinessType")
    private String businessType;
    @ApiModelProperty("买家chat被拉黑的次数")
    private Integer chatBlackedCount;
    @ApiModelProperty("RFQ 提交数量")
    private Integer rfqSubmitCount;
    @ApiModelProperty("RFI 提交数量")
    private Integer rfiSubmitCount;
    @ApiModelProperty("聊天联系人数量")
    private Long chatContactCount;
    @ApiModelProperty("交易笔数")
    private Long totalTransaction;
    @ApiModelProperty("交易金额")
    private Double transactionVolume;
    @ApiModelProperty("采购意向列表")
    private List<String> sourcingPreference;
    @ApiModelProperty("最近搜索关键词")
    private List<String> keywordList;
    @ApiModelProperty("近期参展列表")
    private List<String> tradeShowAttendList;
    @ApiModelProperty("近期预定展会列表")
    private List<String> tradeShowRreRegList;
    @ApiModelProperty("近期参展列表")
    private List<ChatUserDetailInfo.TradeShowVO> tsAttendList;
    @ApiModelProperty("近期预定展会列表")
    private List<ChatUserDetailInfo.TradeShowVO> tsRreRegList;

    public static class TradeShowVO {
        @ApiModelProperty("参展时间")
        private Date joinDate;
        @ApiModelProperty("开展时间")
        private Date tsStartDate;
        @ApiModelProperty("闭展时间")
        private Date tsEndDate;
        @ApiModelProperty("展会名称")
        private String name;
        
        public TradeShowVO() {
        }
    }
}
