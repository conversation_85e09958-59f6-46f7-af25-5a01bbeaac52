/**
 * <a>Title: InquiryFromDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/20-19:30
 */
package com.globalsources.rfx.bff.model.rfi.dto;

import com.globalsources.rfi.agg.request.AttachmentAggDTO;
import com.globalsources.rfi.agg.request.product.ProductCategoryAttributeRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value="询盘提交对象", description="")
public class InquiryFromDTO implements Serializable {

    @ApiModelProperty(value = "matchId")
    private String matchId;

    @ApiModelProperty(value = "token")
    private String token;

    @ApiModelProperty(value = "商品编号")
    @NotNull(message = "inquiryProductId is not null")
    private Long inquiryProductId;

    @ApiModelProperty(value = "message")
    @NotNull(message = "message is not null")
    @Length(max = 5000)
    private String message;

    @ApiModelProperty(value = "附件")
    private List<AttachmentAggDTO> attachmentList;

    @ApiModelProperty(value = "商品數量")
    @NotNull(message = "productNum is not null")
    @Range(min = 1, max = 999999999, message = "商品數量")
    private Integer productNum;

    @ApiModelProperty(value = "商品单位")
    private String productUnit;

    @ApiModelProperty(value = "sessionId")
    @NotNull(message = "sessionId is not null")
    private String sessionId;

    @ApiModelProperty(value = "recommend matching suppliers and send this inquiry to them")
    private Boolean recommendFlag;

    @ApiModelProperty(value = "产品类别属性信息")
    private List<ProductCategoryAttributeRequestDTO> productCategoryAttrInfos;

    @ApiModelProperty(value = "inquiryPath")
    private String inquiryPath;

    @ApiModelProperty(value = "是否新注册用户")
    private Boolean regFlag;

    @ApiModelProperty(value = "勾选转rfq标记")
    private Boolean convertRfqFlag;
}
