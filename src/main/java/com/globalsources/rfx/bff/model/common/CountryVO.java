package com.globalsources.rfx.bff.model.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: SupplierUserInfoVO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: SupplierUserInfoVO <a>
 *
 * <AUTHOR>
 * @date 2021/11/30 9:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class CountryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String countryName;

    private String countryCode;

}
