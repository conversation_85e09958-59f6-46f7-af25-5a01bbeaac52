package com.globalsources.rfx.bff.struct.rfq;

import com.globalsources.framework.vo.UserVO;
import com.globalsources.model.bff.dto.quotImport.ImportCommonDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationChatRepliedRequestDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationCompareRequestDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationCreateRequestDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <a>Title: RfqQuotationRequestStruct </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: RfqQuotationRequestStruct <a>
 *
 * <AUTHOR>
 * @date 2021/11/26 15:42
 */
@Mapper(componentModel = "spring")
public interface RfqQuotationRequestStruct {

    @Mapping(target = "userId", expression = "java(com.globalsources.rfq.bff.api.util.UserUtil.getUserId(userVO))")
    RfqQuotationCompareRequestDTO toRfqQuotationCompareRequest(UserVO userVO, RfqQuotationCompareRequestDTO requestDTO);

    @Mapping(target = "email", expression = "java(com.globalsources.rfq.bff.api.util.UserUtil.getEmail(userVO))")
    @Mapping(target = "userId", expression = "java(com.globalsources.rfq.bff.api.util.UserUtil.getUserId(userVO))")
    @Mapping(target = "supplierId", expression = "java(com.globalsources.rfq.bff.api.util.UserUtil.getSupplierId(userVO))")
    RfqQuotationCreateRequestDTO toRfqQuotationCreateRequest(UserVO userVO, RfqQuotationCreateRequestDTO requestDTO);

    @Mapping(target = "quotationId", expression = "java(quotationId)")
    @Mapping(target = "userId", expression = "java(com.globalsources.rfq.bff.api.util.UserUtil.getUserId(userVO))")
    RfqQuotationChatRepliedRequestDTO toRfqQuotationChatRepliedRequest(String quotationId, UserVO userVO);

    @Mapping(target = "userId", expression = "java(com.globalsources.rfq.bff.api.util.UserUtil.getUserId(userVO))")
    @Mapping(target = "supplierId", expression = "java(com.globalsources.rfq.bff.api.util.UserUtil.getSupplierId(userVO))")
    ImportCommonDTO toImportCommonRequest(UserVO userVO, ImportCommonDTO importCommonDTO);

    @Mapping(source = "viewPermission", target = "mainAccount")
    @Mapping(target = "userId", expression = "java(com.globalsources.rfq.bff.api.util.UserUtil.getUserId(userVO))")
    @Mapping(target = "supplierId", expression = "java(com.globalsources.rfq.bff.api.util.UserUtil.getSupplierId(userVO))")
    ImportCommonDTO toCategoryChooseRequest(UserVO userVO, Boolean viewPermission);
}
