/**
 * <a>Title: SupplierInfoUtils </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/5/21-17:10
 */
package com.globalsources.rfx.bff.utils;

import com.globalsources.agg.supplier.api.model.dto.SupplierCommonInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Objects;

@Slf4j
public class SupplierInfoUtils {

    private SupplierInfoUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 根据orgId 获取供应商下面所有的用户
     * @param orgId
     * @return
     */
    public static final String SUPPLIER_URL = "http://psc.api.qa.globalsources.com/pscapi/user/getOrgUserIdsService.api?orgId=";

    public static String getSupplierInfoList(Long orgId, Boolean proxyEnable) {
        RestTemplate restTemplate = new RestTemplate();
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        String eBlockUrl = SUPPLIER_URL + orgId;
        //加入代理
        boolean flag = proxyEnable;
        if (Boolean.TRUE.equals(flag)) {
            requestFactory.setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("proxy.globalsources.com", 3333)));
        }
        restTemplate.setRequestFactory(requestFactory);

        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        headers.add("Accept", MediaType.ALL_VALUE);

        String resultSupplierId = "";
        try{
            resultSupplierId = restTemplate.getForObject(eBlockUrl, String.class, headers);
            log.info("orgId ={}", resultSupplierId);
        } catch (Exception exception) {
            log.error("get supplier list url fail ={}", exception.getStackTrace());
        }
        return resultSupplierId;
    }

    public static Boolean getVrFlag(SupplierCommonInfoDTO supplierInfo) {
        return Objects.nonNull(supplierInfo) && (StringUtils.isNotEmpty(supplierInfo.getBannerVRUrl()) || StringUtils.isNotEmpty(supplierInfo.getCompleteTourVRURL()));
    }
}
