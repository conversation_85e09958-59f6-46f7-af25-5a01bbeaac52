package com.globalsources.rfx.util;

import com.globalsources.agg.supplier.api.model.dto.organization.OrganizationDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <a>Title: SupplierUtil </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: SupplierUtil <a>
 *
 * <AUTHOR>
 * @date 2021/12/21 - 10:55
 */
public abstract class SupplierUtil {

    public static Boolean isSupplierCso(Long userId, List<OrganizationDTO> organizationDTOList) {
        boolean result = false;
        if (Objects.nonNull(userId) && CollectionUtils.isNotEmpty(organizationDTOList)) {
            result = organizationDTOList.stream().map(OrganizationDTO::getCsoUserId).filter(Objects::nonNull).distinct().anyMatch(id -> Objects.equals(id, userId));
        }
        return result;
    }

    public static Long findCsoUserId(List<OrganizationDTO> organizationDTOList) {
        if (CollectionUtils.isEmpty(organizationDTOList)) {
            return null;
        }
        return organizationDTOList.parallelStream().map(OrganizationDTO::getCsoUserId).filter(Objects::nonNull).findAny().orElseGet(null);
    }

}
