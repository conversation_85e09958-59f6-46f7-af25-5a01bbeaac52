package com.globalsources.rfx.exception;

import com.globalsources.framework.result.IResultCode;

/**
 * <a>Title: CategorySuggestException </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: CategorySuggestException <a>
 *
 * <AUTHOR>
 * @date 2021/12/7 13:59
 */
public class CategorySuggestException extends RuntimeException {

    private static final long serialVersionUID = 2201800548165425255L;

    private String code;
    private String message;
    private Object data;

    public CategorySuggestException(String code) {
        this.code = code;
    }

    public CategorySuggestException(IResultCode resultCode) {
        this.code = resultCode.getCode();
        this.message = resultCode.getMsg();
    }

    public CategorySuggestException(String code, String message) {
        this.message = message;
        this.code = code;
    }

    public CategorySuggestException(String code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    public Object getData() {
        return this.data;
    }
}
