package com.globalsources.rfx.query;

import com.globalsources.rfx.enums.RfxCommonEnum;
import com.globalsources.rfx.enums.TermEnum;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <a>Title: CollapseQuery </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: CollapseQuery <a>
 *
 * <AUTHOR>
 * @date 2021/12/3 14:18
 */
public final class CollapseQuery implements QueryBuilder<Map<String, Object>> {

    private final Map<String, Object> paramMap;
    private final List<Map<String, Object>> sortList;

    public CollapseQuery() {
        paramMap = new HashMap<>(2);
        sortList = new ArrayList<>();
        paramMap.put(RfxCommonEnum.PARAM_SORT_FIELDS.key(), sortList);
    }

    public CollapseQuery removeEmptySort() {
        this.paramMap.remove(RfxCommonEnum.PARAM_SORT_FIELDS.key());
        return this;
    }

    public CollapseQuery innerName(String innerName) {
        Assert.hasLength(innerName, "inner name can not be null");
        paramMap.put(RfxCommonEnum.PARAM_INNER_NAME.key(), innerName);
        return this;
    }

    public CollapseQuery fieldName(String fieldName) {
        Assert.hasLength(fieldName, "field name can not be null");
        paramMap.put(RfxCommonEnum.PARAM_BASE_FILTER_NAME.key(), fieldName);
        return this;
    }

    public CollapseQuery termEnum(TermEnum termEnum) {
        Assert.notNull(termEnum, "term enum can not be null");
        paramMap.put(RfxCommonEnum.PARAM_BASE_FILTER_TERM_ENUM.key(), termEnum.name());
        return this;
    }

    public CollapseQuery size(Integer size) {
        paramMap.put(RfxCommonEnum.PARAM_INNER_SIZE.key(), size);
        return this;
    }

    public CollapseQuery sort(SortBuilder sortBuilder) {
        sortList.add(sortBuilder.build());
        return this;
    }

    @Override
    public Map<String, Object> build() {
        return paramMap;
    }
}
