package com.globalsources.rfx.query;

import com.globalsources.rfx.enums.RfxCommonEnum;
import com.globalsources.rfx.enums.SortModel;

import java.util.HashMap;
import java.util.Map;

/**
 * <a>Title: SortBuilder </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: SortBuilder <a>
 *
 * <AUTHOR>
 * @date 2021/12/3 14:21
 */
public final class SortBuilder implements QueryBuilder<Map<String, Object>> {

    private final Map<String, Object> param;

    public SortBuilder() {
        param = new HashMap<>(2);
    }

    public SortBuilder sort(SortModel sortModel) {
        param.put(RfxCommonEnum.PARAM_SORT.key(), sortModel.name());
        return this;
    }

    public SortBuilder sortField(String fieldName) {
        param.put(RfxCommonEnum.PARAM_SORT_FIELD.key(), fieldName);
        return this;
    }

    @Override
    public Map<String, Object> build() {
        return param;
    }
}
