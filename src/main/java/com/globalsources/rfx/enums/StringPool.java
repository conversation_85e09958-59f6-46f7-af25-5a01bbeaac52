package com.globalsources.rfx.enums;

/**
 * <a>Title: StringPool </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: StringPool <a>
 *
 * <AUTHOR>
 * @date 2021/12/23 16:39
 */
public enum StringPool implements ConstantEnum<String> {

    /**
     * get request ip address
     */
    HTTP_REQUEST_UN_KNOWN("unknown", "unknown"),
    HTTP_REQUEST_X_FOR_WARDED_FOR("x-forwarded-for", "x-forwarded-for"),
    HTTP_REQUEST_PROXY_CLIENT_IP("Proxy-Client-IP", "Proxy-Client-IP"),
    HTTP_REQUEST_WL_PROXY_CLIENT_IP("WL-Proxy-Client-IP", "WL-Proxy-Client-IP"),
    HTTP_REQUEST_HTTP_CLIENT_IP("HTTP_CLIENT_IP", "HTTP_CLIENT_IP"),
    HTTP_REQUEST_HTTP_X_FORWARDED_FOR("HTTP_X_FORWARDED_FOR", "HTTP_X_FORWARDED_FOR"),

    /**
     * Dictionary key
     */
    DICTIONARY_FOB_PORT("FOBPort", "supplier fob port key"),
    DICTIONARY_PAYMENT_TERM("PaymentTerm", "supplier payment key"),
    DICTIONARY_UNIT("Unit", "supplier unit key"),
    DICTIONARY_REGION("Region", "region unit key"),
    DICTIONARY_COUNTRY("Country", "supplier country key"),
    DICTIONARY_SAMPLE_POLICY("samplepolicy", "supplier sample policy key"),
    DICTIONARY_EXPORT_MARKET("ExportMarket", "supplier export market key"),
    DICTIONARY_BUSINESS_TYPE("BusinessType", "supplier business type key"),
    DICTIONARY_SOURCING_TYPE("sourcingType", "supplier sourcing type key"),
    DICTIONARY_MONEY_UNIT("moneyUnit", "supplier money unit key"),
    DICTIONARY_SHIPMENT_TERMS("shipmentTerms", "supplier shipment terms key"),
    DICTIONARY_PAYMENT_TERMS("PAYMENTTERMS", "supplier payment terms key"),
    DICTIONARY_SIZE_UNIT_ARR("MRT,CMT,FOT,INH,YRD", "supplier size unit key"),
    DICTIONARY_WEIGHT_UNIT_ARR("GRM,KGM,MTN,LBR,LTN,LTS", "supplier weight unit key"),
    DICTIONARY_I18N_EN("en", "language en"),
    DICTIONARY_I18N_ES("es", "language es"),

    DEFAULT_LANGUAGE("enus", "default language"),

    SERVER_NAME("spring.application.name", "application name"),
    ;

    private String description;
    private String value;

    StringPool(String value, String description) {
        this.description = description;
        this.value = value;
    }

    @Override
    public String value() {
        return this.value;
    }

}
