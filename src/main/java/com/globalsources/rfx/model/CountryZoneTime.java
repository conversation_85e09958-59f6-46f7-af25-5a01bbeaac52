package com.globalsources.rfx.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <a>Title: CountryZoneTime </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2022/3/25-11:05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CountryZoneTime implements Serializable {

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家全称
     */
    private String countryEn;

    /**
     * 国家当前时间
     */
    private Date date;

    /**
     * 时间差
     */
    private String difference;

}
