package com.globalsources.rfx.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <a>Title: CategorySuggestBO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: CategorySuggestBO <a>
 *
 * <AUTHOR>
 * @date 2021/12/7 14:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class CategorySuggestBO {

    private String descEn;

    private Long categoryId;

    List<CategoriesBO> categories;

}
