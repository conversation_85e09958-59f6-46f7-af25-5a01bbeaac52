package com.globalsources.rfx.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <a>Title: RfiThreadInfoBO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: RfiThreadInfoBO <a>
 *
 * <AUTHOR>
 * @date 2021/12/7 10:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class RfiThreadInfoBO {

    List<RfiThreadBO> rfiThreadList;

    private Integer totalCount;

}
