package com.globalsources.admin.operation.model.dto.campaignproductregister;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ProductRegisterAuditDTO")
public class CampaignProductRegisterAuditReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "campaignId is null")
    @ApiModelProperty(value = "活动ID")
    private Long campaignId;

    @ApiModelProperty("供应商id")
    private List<Long> supplierIds;

    @ApiModelProperty("产品id")
    private List<Long> productIds;

    @NotNull(message = "auditStatus is null, Confirmed/Rejected")
    @ApiModelProperty("审批状态：Confirmed/Rejected")
    private String auditStatus;

}
