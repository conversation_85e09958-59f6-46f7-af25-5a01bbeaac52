package com.globalsources.admin.operation.model.dto.pagetemplate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/11/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "PageTemplateSaveReqDTO")
public class PageTemplateSaveReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @NotBlank(message = "templateName is blank")
    @Size(max = 20, message = "templateName max length is 20")
    @ApiModelProperty(value = "模板名称")
    private String templateName;

}
