package com.globalsources.admin.operation.model.dto.campaigncomponent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/12/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CommonComponentSaveReqDTO")
public class CommonComponentSaveReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "campaignId is null")
    @ApiModelProperty(value = "活动ID")
    private Long campaignId;

    @ApiModelProperty(value = "公共组件ID-Public Config ID")
    private Long commonComponentId;

    @NotBlank(message = "componentName is blank")
    @Size(max = 50, message = "componentName max length is 50")
    @ApiModelProperty(value = "组件名称-Public Config Name")
    private String componentName;

    @NotBlank(message = "componentType is blank")
    @Size(max = 50, message = "componentType max length is 50")
    @ApiModelProperty(value = "组件类型-Purposes: TOP_TAB(Top TAB)、SLIDE_TAB(Slide TAB)、IMAGE_TEXT(Image & Text)、FAQ(FAQ)")
    private String componentType;

    @NotBlank(message = "desktopContent is blank")
    @ApiModelProperty(value = "组件内容-desktop")
    private String desktopContent;

    @NotBlank(message = "mobileContent is blank")
    @ApiModelProperty(value = "组件内容-mobile")
    private String mobileContent;

    @NotBlank(message = "appContent is blank")
    @ApiModelProperty(value = "组件内容-app")
    private String appContent;
}
