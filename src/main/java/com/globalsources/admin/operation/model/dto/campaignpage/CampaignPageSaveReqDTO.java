package com.globalsources.admin.operation.model.dto.campaignpage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/11/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CampaignPageSaveReqDTO")
public class CampaignPageSaveReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "页面id")
    private Long pageId;

    @NotNull(message = "pageId is null")
    @ApiModelProperty(value = "活动ID")
    private Long campaignId;

    @NotBlank(message = "pageName is blank")
    @Size(max = 100, message = "pageName max length is 100")
    @ApiModelProperty(value = "页面名称")
    private String pageName;

    @NotBlank(message = "pageUrlType is blank")
    @Size(max = 20, message = "pageUrlType max length is 20")
    @ApiModelProperty(value = "URL类型")
    private String pageUrlType;

    @Size(max = 300, message = "pageUrlSuffix max length is 300")
    @ApiModelProperty(value = "页面URL后缀")
    private String pageUrlSuffix;

    @Size(max = 100, message = "pageUrl max length is 100")
    @ApiModelProperty(value = "页面URL")
    private String pageUrl;

    @NotNull(message = "templateId is null")
    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @NotBlank(message = "templateName is blank")
    @Size(max = 100, message = "templateName max length is 100")
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @NotBlank(message = "platformType is blank")
    @Size(max = 30, message = "platformType max length is 30")
    @ApiModelProperty(value = "平台类型")
    private String platformType;
}
