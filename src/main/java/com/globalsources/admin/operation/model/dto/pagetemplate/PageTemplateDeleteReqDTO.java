package com.globalsources.admin.operation.model.dto.pagetemplate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/1/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "PageTemplateDeleteReqDTO")
public class PageTemplateDeleteReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "templateId is null")
    @ApiModelProperty(value = "模板id")
    private Long templateId;

}
