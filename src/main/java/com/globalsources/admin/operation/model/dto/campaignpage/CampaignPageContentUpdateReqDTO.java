package com.globalsources.admin.operation.model.dto.campaignpage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/11/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CampaignPageContentUpdateReqDTO")
public class CampaignPageContentUpdateReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "pageId is null")
    @ApiModelProperty(value = "页面id")
    private Long pageId;

    @NotBlank(message = "pageContent is blank")
    @ApiModelProperty(value = "页面内容")
    private String pageContent;

    @NotBlank(message = "platformType is blank")
    @ApiModelProperty(value = "平台类型(Desktop, Mobile, App)")
    private String platformType;

    @ApiModelProperty(value = "模板草稿")
    private String pageTemplateDraft;
}
