package com.globalsources.admin.operation.model.dto.campaigncomponent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/6/1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CampaignComponentSaveReqDTO")
public class CampaignComponentSaveReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "campaignId is null")
    @ApiModelProperty("活动ID")
    private Long campaignId;

    @NotNull(message = "pageId is null")
    @ApiModelProperty("页面ID")
    private Long pageId;

    @NotBlank(message = "platformType is blank")
    @Size(max = 30, message = "platformType max length is 30")
    @ApiModelProperty(value = "平台类型")
    private String platformType;

    @NotBlank(message = "componentKey is blank")
    @Size(max = 200, message = "componentKey max length is 200")
    @ApiModelProperty(value = "组件标识")
    private String componentKey;

    @NotBlank(message = "componentContent is blank")
    @ApiModelProperty("组件内容")
    private String componentContent;

    @Size(max = 100, message = "componentType max length is 100")
    @ApiModelProperty("组件类型")
    private String componentType;

    @ApiModelProperty("公共组件标记")
    private Boolean commonFlag = false;
}
