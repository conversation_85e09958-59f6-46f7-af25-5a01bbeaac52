package com.globalsources.admin.operation.model.dto.campaignpage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/11/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CampaignPageTrackingCodeEditReqDTO")
public class CampaignPageTrackingCodeEditReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "pageId is null")
    @ApiModelProperty(value = "页面id")
    private Long pageId;

    @Size(max = 500, message = "trackingCode max length is 500")
    @ApiModelProperty(value = "Tracking Code")
    private String trackingCode;
}
