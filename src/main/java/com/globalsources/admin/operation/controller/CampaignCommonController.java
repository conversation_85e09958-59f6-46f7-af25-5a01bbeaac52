package com.globalsources.admin.operation.controller;

import com.globalsources.framework.result.Result;
import com.globalsources.supplierconsole.agg.api.supplier.dto.tradeshow.AllTradeShowSearchAggDTO;
import com.globalsources.supplierconsole.agg.api.supplier.dto.tradeshow.TradeshowSimpleAggDTO;
import com.globalsources.supplierconsole.agg.api.supplier.feign.OriginalSectionFeign;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> Sun
 * @since 2023/6/14
 */
@Slf4j
@Api(tags = "运营-其它公共接口")
@RestController
@RequestMapping("/campaign-common")
public class CampaignCommonController {

    @Autowired
    private OriginalSectionFeign originalSectionFeign;

    @GetMapping("/v1/select-trade-show")
    public Result<List<TradeshowSimpleAggDTO>> selectTradeShow(){
        AllTradeShowSearchAggDTO dto = new AllTradeShowSearchAggDTO();
        DateTime dt = new DateTime();
        dto.setStartDate(dt.dayOfYear().withMinimumValue().withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).toDate());
        dto.setEndDate(dt.dayOfYear().withMaximumValue().withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate());
        return originalSectionFeign.queryAllTradeShows(dto);
    }
}
