package com.globalsources.admin.operation.controller;

import com.globalsources.admin.operation.model.dto.campaigngroup.CampaignGroupProductDeleteReqDTO;
import com.globalsources.admin.util.FileUtil;
import com.globalsources.common.api.feign.CommonAggFeign;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.annotation.AutoIdempotent;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCodeEnum;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.operation.agg.api.dto.CampaignGroupProductDeleteDTO;
import com.globalsources.operation.agg.api.dto.CampaignGroupProductFilterDTO;
import com.globalsources.operation.agg.api.dto.CampaignGroupProductSupplierListDTO;
import com.globalsources.operation.agg.api.dto.GroupProductDeleteAllDTO;
import com.globalsources.operation.agg.api.dto.ProductConfirmDTO;
import com.globalsources.operation.agg.api.feign.CampaignGroupFeign;
import com.globalsources.operation.agg.api.feign.CampaignGroupProductFeign;
import com.globalsources.operation.agg.api.vo.CampaignGroupEditVO;
import com.globalsources.operation.agg.api.vo.CampaignGroupProductListVO;
import com.globalsources.operation.agg.api.vo.SimpleSupplierInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Sun
 * @since 2022/11/11
 */

@Slf4j
@Api(tags = "运营-圈品")
@RestController
@RequestMapping("/campaign-group-product")
@AllArgsConstructor
public class CampaignGroupProductController {

    private final CampaignGroupProductFeign groupProductFeign;

    private final CommonAggFeign commonAggFeign;

    private final CampaignGroupFeign campaignGroupFeign;

    private static final String SUPPLIER_LIST_TEMPLATE_PATH = "template/operationProductDataSupplierListDownloadTemplate.xlsx";

    private static SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");

    @AdminLogin
    @AutoIdempotent
    @ApiOperation(value = "确认圈品", notes = "确认圈品")
    @PostMapping("/v1/confirm-group-product")
    public Result<Boolean> confirmGroupProduct(@ApiIgnore UserVO userInfo, @ApiParam("数据集id") @RequestParam("groupId") Long groupId, @RequestBody ProductConfirmDTO dto) {
        return groupProductFeign.confirmGroupProduct(groupId, dto);
    }

    @AdminLogin
    @ApiOperation(value = "圈品列表", notes = "圈品列表")
    @PostMapping("/v1/select-group-product")
    public Result<PageResult<CampaignGroupProductListVO>> selectGroupProduct(@RequestBody CampaignGroupProductFilterDTO dto) {
        return groupProductFeign.selectGroupProduct(dto);
    }

    @AdminLogin
    @ApiOperation(value = "圈品数量", notes = "圈品数量")
    @PostMapping("/v1/get-groupSupplierCount")
    public Result<Long> getCampaignGroupSupplierCount(@RequestBody CampaignGroupProductFilterDTO dto) {
        return groupProductFeign.getCampaignGroupSupplierCount(dto);
    }

    @AdminLogin
    @ApiOperation(value = "删除圈品", notes = "删除圈品")
    @PostMapping("/v1/delete-group-product")
    public Result<Boolean> deleteGroupProduct(@ApiIgnore UserVO userInfo, @RequestBody CampaignGroupProductDeleteReqDTO reqDTO) {
        CampaignGroupProductDeleteDTO dto = OrikaMapperUtil.coverObject(reqDTO, CampaignGroupProductDeleteDTO.class);
        dto.setUserId(userInfo.getUserId());
        return groupProductFeign.deleteGroupProduct(dto);
    }

    @AdminLogin
    @ApiOperation(value = "圈品全部删除", notes = "圈品全部删除")
    @PostMapping("/v1/delete-all")
    public Result<Boolean> deleteAll(@ApiIgnore UserVO userInfo, @RequestBody CampaignGroupProductFilterDTO reqDTO) {
        if (Objects.isNull(reqDTO) || Objects.isNull(reqDTO.getGroupId())) {
            log.error("deleteAll error, reqDTO is null or groupId is null");
            throw new BusinessException(ResultCodeEnum.PARAMETER_VALIDATION_FAILED);
        }
        GroupProductDeleteAllDTO dto = OrikaMapperUtil.coverObject(reqDTO, GroupProductDeleteAllDTO.class);
        dto.setUserId(userInfo.getUserId());
        return groupProductFeign.deleteAll(reqDTO.getGroupId(), dto);
    }

    @ApiOperation(value = "获取businessType")
    @GetMapping("/v1/get-businessType")
    public Result<Map<String, String>> getBusinessType() {
        return commonAggFeign.getDicKeyValueMap("BusinessType", LanguageDicEnum.EN_US.getValue());
    }


    @ApiOperation(value = "圈品供应商列表下载", notes = "圈品供应商列表下载")
    @GetMapping("/v1/group-product-supplier-list-download")
    public void supplierListDownload(@ApiParam("数据集id") @RequestParam("groupId") Long groupId, HttpServletRequest request, HttpServletResponse response) {
        log.info("supplierListDownload group id:{}", groupId);
        CampaignGroupEditVO detail = ResultUtil.getData(campaignGroupFeign.getGroup(groupId));
        log.info("supplierListDownload group data:{}", detail);

        List<SimpleSupplierInfoVO> allDataList = new ArrayList<>(detail.getSupplierNum().intValue());
        CampaignGroupProductSupplierListDTO dto = CampaignGroupProductSupplierListDTO.builder().groupId(groupId).lastSupplierId(null).limit(1000L).build();
        List<SimpleSupplierInfoVO> list = ResultUtil.getData(groupProductFeign.supplierListDownload(dto));
        while (CollectionUtils.isNotEmpty(list)) {
            allDataList.addAll(list);
            dto.setLastSupplierId(getLastSupplierId(list));
            list = ResultUtil.getData(groupProductFeign.supplierListDownload(dto));
        }
        log.info("supplierListDownload total count :{}", allDataList.size());

        try (InputStream is = getClass().getClassLoader().getResourceAsStream(SUPPLIER_LIST_TEMPLATE_PATH);
             ServletOutputStream os = response.getOutputStream();) {

            response.setContentType("application/vnd.ms-excel");
            response.setHeader("content-disposition", "attachment;filename=" + FileUtil.encodeFileName("[" + detail.getGroupName() + "]+" + sf.format(new Date()) + ".xlsx", request));
            processTemplate(allDataList, is, os);
            log.info("download succ");
        } catch (Exception e) {
            log.error("download supplier-list error,{}", e);
        }
    }

    private Long getLastSupplierId(List<SimpleSupplierInfoVO> list) {
        return list.get(list.size() - 1).getSupplierId();
    }

    private void processTemplate(List<SimpleSupplierInfoVO> dataList, InputStream is, ServletOutputStream os) throws IOException {
        Map<String, Object> value = new HashMap<>();
        value.put("dataList", dataList);
        Context context = new Context(value);
        JxlsHelper.getInstance().processTemplate(is, os, context);
    }
}
