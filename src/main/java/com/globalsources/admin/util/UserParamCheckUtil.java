package com.globalsources.admin.util;

import com.globalsources.framework.result.Result;
import com.globalsources.user.api.enums.UserErrorEnum;
import lombok.experimental.UtilityClass;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

@UtilityClass
public class UserParamCheckUtil {


    public static Result<Void> checkPasswd(String password) {
        int strong = getPasswordLevel(password, 6);
        if (strong <= 1) {
            return Result.failed(UserErrorEnum.ILLEGAL_INPUT_PASSWORD);
        } else if (strong == 2) {
            return Result.failed(UserErrorEnum.NOT_ENOUGH_LENGTH_PASSWORD);
        } else {
            return strong < 4 ? Result.failed(UserErrorEnum.UNSECURITY_PASSWORD) : Result.success();
        }
    }

    public static int getPasswordLevel(String password, int minLength) {
        if (StringUtils.isEmpty(password)) {
            return 0;
        } else {
            Pattern pattern = Pattern.compile("[A-Za-z0-9\\x21-\\x2F\\x3A-\\x40\\x5B-\\x60\\x7B-\\x7E]+");
            if (!pattern.matcher(password).matches()) {
                return 1;
            } else if (password.length() < minLength) {
                return 2;
            } else {
                int strong = 2;
                pattern = Pattern.compile("[a-z]+", 2);
                if (pattern.matcher(password).find()) {
                    ++strong;
                }

                pattern = Pattern.compile("[\\d]+");
                if (pattern.matcher(password).find()) {
                    ++strong;
                }

                pattern = Pattern.compile("[\\x21-\\x2F\\x3A-\\x40\\x5B-\\x60\\x7B-\\x7E]+");
                if (pattern.matcher(password).find()) {
                    ++strong;
                }

                return strong;
            }
        }
    }
}
