package com.globalsources.admin.util;

import com.alibaba.fastjson.JSON;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.auth.WebIdentityTokenCredentialsProvider;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.cloudfront.AmazonCloudFrontClient;
import com.amazonaws.services.cloudfront.model.CreateInvalidationRequest;
import com.amazonaws.services.cloudfront.model.CreateInvalidationResult;
import com.amazonaws.services.cloudfront.model.InvalidationBatch;
import com.amazonaws.services.cloudfront.model.Paths;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.util.EC2MetadataUtils;
import com.globalsources.framework.constants.CoreConstants;
import com.globalsources.framework.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Date;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> Sun
 * @since 2024/7/11
 */
@Slf4j
@Component
public class AmazonS3Storage implements InitializingBean {

    @Value("${proxy.switch}")
    private Boolean enableProxy;
    @Value("${https.proxyHost}")
    private String proxyHost;
    @Value("${https.proxyPort}")
    private Integer proxyPort;

    @Value("${video.cdn.s3.region}")
    private String region;
    @Value("${video.cdn.s3.accessKey:}")
    private String cdnAccessKey;
    @Value("${video.cdn.s3.secretKey:}")
    private String cdnSecretKey;
    @Value("${video.cdn.s3.bucket}")
    private String cdnBucket;
    @Value("${video.cdn.prefix}")
    private String cdnPrefix;
    @Value("${video.cdn.s3.baseUrl}")
    private String cdnBaseUrl;

    @Value("${op.cdn.s3.region}")
    private String opRegion;
    @Value("${op.cdn.s3.accessKey:}")
    private String opCdnAccessKey;
    @Value("${op.cdn.s3.secretKey:}")
    private String opCdnSecretKey;
    @Value("${op.cdn.s3.bucket}")
    private String opCdnBucket;
    @Value("${op.cdn.prefix}")
    private String opCdnPrefix;
    @Value("${op.cdn.s3.baseUrl}")
    private String opCdnBaseUrl;

    @Value("${aws.endpointUrlFlag:true}")
    private Boolean endpointUrlFlag;
    @Value("${aws.endpointUrl:http://bucket.vpce-0c93e078f94a11c62-uasorx6n%s.s3.ap-east-1.vpce.amazonaws.com}")
    private String endpointUrlPattern;

    //cdn supplier level
    @Value("${cdn.s3.region:}")
    private String cdnRegion;
    @Value("${cdn.s3.s.bucket:}")
    private String cdnBucketS;
    // 图片
    @Value("${cdn.s3.domain.s:}")
    private String cdnDomainS;
    @Value("${cdn.s3.a.bucket:}")
    private String cdnBucketA;
    // 视频
    @Value("${cdn.s3.domain.a:}")
    private String cdnDomainA;

    @Value("${cdn.s3.openAkSk:false}")
    private Boolean openAkSk;
    @Value("${cdn.s3.accessKey:}")
    private String cdnAk;
    @Value("${cdn.s3.secretKey:}")
    private String cdnSk;

    @Value("${cdn.cf.distribution.id:}")
    private String distributionId;

    private AmazonS3 client;
    private AmazonS3 opClient;

    private AmazonS3 sClient;
    private AmazonS3 aClient;

    /**
     * 初始化
     */
    @Override
    public void afterPropertiesSet() {
        client = initClient(cdnAccessKey, cdnSecretKey, region, false);
        if (Boolean.TRUE.equals(openAkSk)) {
            opClient = initClient(opCdnAccessKey, opCdnSecretKey, opRegion, false);
            aClient = initClient(cdnAk, cdnSk, cdnRegion, false);
            sClient = initClient(cdnAk, cdnSk, cdnRegion, false);
        } else {
            opClient = initClient(null, null, opRegion, endpointUrlFlag);
            aClient = initClient(null, null, cdnRegion, endpointUrlFlag);
            sClient = initClient(null, null, cdnRegion, endpointUrlFlag);
        }
    }

    /**
     * 返回S3客户端
     *
     * @param accessKey
     * @param secretKey
     * @return
     */
    private AmazonS3 initClient(String accessKey, String secretKey, String region, boolean endpointFlag) {
        AWSCredentialsProvider credentialsProvider;
        if (StringUtils.isBlank(accessKey) || StringUtils.isBlank(secretKey)) {
            // IAM Role
            log.info("========region={}, accessKey={}, secretKey={}, use IAM Role ", region, accessKey, secretKey);
            credentialsProvider = WebIdentityTokenCredentialsProvider.builder().build();
        } else {
            AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
            credentialsProvider = new AWSStaticCredentialsProvider(credentials);
        }

        ClientConfiguration config = new ClientConfiguration();
        log.info("------ proxy.switch:{}", enableProxy);
        config.setProtocol(Protocol.HTTPS);
        if (Boolean.TRUE.equals(enableProxy)) {
            config.setProxyHost(proxyHost);
            config.setProxyPort(proxyPort);
        }
        config.setMaxErrorRetry(3);
        config.setMaxConnections(100);
        config.setConnectionTimeout(6000);
        config.setSocketTimeout(30000);
        config.setConnectionMaxIdleMillis(6000);

        AmazonS3ClientBuilder builder = AmazonS3ClientBuilder.standard()
                .withCredentials(credentialsProvider)
                .withClientConfiguration(config);

        // 是否使用PrivateLink，开关
        if (endpointFlag) {
            // 获取当前可用区（ap-east-1a，ap-east-1b）
            String availabilityZone = EC2MetadataUtils.getAvailabilityZone();
            // endpointUrlPattern = http://bucket.vpce-0c93e078f94a11c62-uasorx6n%s.s3.ap-east-1.vpce.amazonaws.com
            String endpointUrl = String.format(endpointUrlPattern, Strings.isBlank(availabilityZone) ? "" : "-" + availabilityZone);
            log.info("========region={}, availabilityZone={}, endpointUrl={}", region, availabilityZone, endpointUrl);
            builder.withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpointUrl, region));
        } else {
            builder.withRegion(region);
        }
        return builder.build();
    }

    public String genFileKey(String suffix) {
        Date curDate = new Date();
        Random random = new Random(System.currentTimeMillis());

        String filename = DateUtil.date2String(curDate, DateUtil.DATETIMESTRING_YYYYMMDDHHMMSSS) + random.nextInt(1000);
        if (!StringUtils.isEmpty(suffix)) {
            filename += "." + suffix;
        }

        return filename;
    }

    public String uploadFile(Integer bucketType, String cdnRootDir, File file) {

        String bucketName = getBucket(bucketType);
        String prefix = getPrefix(bucketType);
        String filename = cdnRootDir + CoreConstants.SLASH + file.getName();
        log.info("------ uploadFile start, bucketType:{}, cdnRootDir:{}", bucketType, cdnRootDir);
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, prefix + filename, file)
                .withCannedAcl(CannedAccessControlList.BucketOwnerFullControl);
        //开始执行文件上传
        try {
            log.info("------ uploadFile start, getClient:{}, putObjectRequest:{}", getClient(bucketType), JSON.toJSONString(putObjectRequest));
            PutObjectResult putObjectResult = getClient(bucketType).putObject(putObjectRequest);
            log.info("------ uploadFile end, putObjectResult:{}", JSON.toJSONString(putObjectResult));
        } catch (Exception e) {
            log.error("------ uploadFile exception, bucketName:{}, cdnRootDir:{}, filename:{}, fileSize:{}, detail:{}", bucketName, cdnRootDir, filename, file.length(), e.getMessage(), e);
            return null;
        }

        //获取文件url地址
        return getBaseUrl(bucketType) + filename;
    }

    /**
     * 获得文件后缀
     *
     * @param filename
     * @return 匹配失败返回空字符串
     */
    public String getFileSuffix(String filename) {
        Pattern p = Pattern.compile("\\.([a-zA-Z0-9]{1,5})$");
        Matcher matcher = p.matcher(filename);
        if (!matcher.find()) {
            return "";
        }

        return matcher.group(1);
    }

    public Boolean deleteObject(Integer bucketType, String fileKey, boolean isVideo) {
        if (StringUtils.isBlank(fileKey)) {
            return false;
        }

        if (Boolean.FALSE.equals(isVideo)) {
            fileKey = fileKey.replaceFirst("IMAGES/", "images/");
        }

        String bucketName = getBucket(bucketType);
        try {
            DeleteObjectRequest deleteObjectRequest = new DeleteObjectRequest(bucketName, fileKey);
            log.info("-------- deleteObject start, bucketName:{}, isVideo:{}, fileKey:{}, deleteObjectRequest:{}", bucketName, isVideo, fileKey, deleteObjectRequest);
            getClient(bucketType).deleteObject(deleteObjectRequest);
            log.info("-------- deleteObject end, bucketName:{}, fileKey:{}", bucketName, fileKey);
            return true;
        } catch (Exception e) {
            log.error("------ deleteObject exception, , bucketName:{}, fileKey:{}, detail:{}", bucketName, fileKey, e.getMessage(), e);
            return false;
        }
    }

    public String purgeVideo(String... fileKey) {
        try {
            log.info("-------- purgeVideo start fileKey:{}", fileKey);
            AmazonCloudFrontClient cloudFrontClient = getAmazonCloudFrontClient();

            // 设置无效化的路径,请求的唯一标识符
            Paths paths = new Paths().withItems(fileKey).withQuantity(fileKey.length);
            InvalidationBatch invalidationBatch = new InvalidationBatch(paths, "invalidation-" + System.currentTimeMillis());
            // 创建无效化请求, 分布ID（你的CloudFront分布的唯一标识符）和无效的路径
            CreateInvalidationRequest invalidationRequest = new CreateInvalidationRequest()
                    .withDistributionId(distributionId)
                    .withInvalidationBatch(invalidationBatch);

            // 发送请求并获取结果
            CreateInvalidationResult invalidationResult = cloudFrontClient.createInvalidation(invalidationRequest);

            // 输出无效化的ID
            log.info("-------- purgeVideo end result:{}", invalidationResult.getInvalidation().getId());
            return "SUCCESS";
        } catch (Exception e) {
            log.error("------ purgeVideo error", e);
        }
        return "FAIL";
    }

    private AmazonCloudFrontClient getAmazonCloudFrontClient() {
        ClientConfiguration config = new ClientConfiguration();

        if (Boolean.TRUE.equals(enableProxy)) {
            config.setProxyHost(proxyHost);
            config.setProxyPort(proxyPort);
        }

        AmazonCloudFrontClient cloudFrontClient ;
        if (Boolean.TRUE.equals(openAkSk)) {
            AWSCredentials credentials = new BasicAWSCredentials(cdnAk, cdnSk);
            cloudFrontClient = new AmazonCloudFrontClient(credentials, config);
        } else {
            cloudFrontClient = new AmazonCloudFrontClient();
        }
        return cloudFrontClient;
    }

    public String getFileKey(String fileUrl) {
        return getFileKey(fileUrl, false);
    }

    public String getFileKey(String fileUrl, boolean isCDNDomainA) {
        String regex = "^" + cdnDomainS;
        if (fileUrl.toLowerCase().endsWith(".mp4") || isCDNDomainA) {
            regex = "^" + cdnDomainA;
        }

        log.info("-------- getFileKey regex:{}, fileUrl:{}", regex, fileUrl);
        return fileUrl.replaceFirst(regex, "");
    }

    private AmazonS3 getClient(Integer type) {

        AmazonS3 s3Client;
        switch (type) {
            case 0:
                s3Client = client;
                break;
            case 1:
                s3Client = opClient;
                break;
            case 2:
                s3Client = opClient;
                break;
            case 3:
                s3Client = aClient;
                break;
            case 4:
                s3Client = sClient;
                break;
            default:
                s3Client = client;
        }

        return s3Client;
    }

    /**
     * 获取桶名字
     *
     * @param type
     * @return
     */
    private String getBucket(Integer type) {
        String bucket;

        switch (type) {
            case 0:
                bucket = cdnBucket;
                break;
            case 1:
                bucket = opCdnBucket;
                break;
            case 2:
                bucket = opCdnBucket;
                break;
            case 3:
                bucket = cdnBucketA;
                break;
            case 4:
                bucket = cdnBucketS;
                break;
            default:
                bucket = "";
        }

        return bucket;
    }

    /**
     * 获取prefix
     *
     * @param type
     * @return
     */
    private String getPrefix(Integer type) {
        String prefix;

        switch (type) {
            case 0:
                prefix = cdnPrefix;
                break;
            case 1:
                prefix = opCdnPrefix;
                break;
            case 2:
                prefix = opCdnPrefix;
                break;
            case 3:
                prefix = "";
                break;
            case 4:
                prefix = "";
                break;
            default:
                prefix = "";
        }

        return prefix;
    }

    private String getBaseUrl(Integer type) {
        String baseUrl;

        switch (type) {
            case 0:
                baseUrl = cdnBaseUrl;
                break;
            case 1:
                baseUrl = opCdnBaseUrl;
                break;
            case 2:
                baseUrl = opCdnBaseUrl;
                break;
            case 3:
                baseUrl = cdnDomainA;
                break;
            case 4:
                baseUrl = cdnDomainS;
                break;
            default:
                baseUrl = "";
        }

        return baseUrl;
    }
}
