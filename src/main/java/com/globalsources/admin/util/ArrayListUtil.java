package com.globalsources.admin.util;

import java.util.ArrayList;
import java.util.List;

public class ArrayListUtil {
    private ArrayListUtil() {
    }

    /**
     * 比较两个数组的键和值，并返回差集
     * @param arr1
     * @param arr2
     * @return
     */
    public static List<Long> arrayDiffAssoc(Long[] arr1, Long[] arr2) {
        List<Long> temp = new ArrayList<>();
        for (int i = 0; i < arr1.length; i++) {
            // 如果当前索引 已经超过 被比较数组的最大索引，则直接添加至差异数组中
            if (i > arr2.length-1 || !arr1[i].equals(arr2[i])) {
                temp.add(arr1[i]);
            }
        }
        return temp;
    }

    /**
     * 创建一个指定长度值递增的数组
     * @param size
     * @return
     */
    public static Integer[] initArray(int size) {
        Integer[] arr = new Integer[size];
        for (int i = 0; i < arr.length; i++) {
            arr[i] = i;
        }
        return arr;
    }

    public static <T> void moveToFirst(List<T> list, T target) {
        if (list == null || target == null || list.isEmpty() || !list.contains(target)) {
            return;
        }

        list.remove(target);
        list.add(0, target);
    }
}
