package com.globalsources.admin.util;

import com.alibaba.fastjson.JSON;
import com.globalsources.file.api.dto.FileReqDTO;
import com.globalsources.file.api.enums.StoreType;
import com.globalsources.file.api.feign.FileFeign;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.CipherUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/18 4:48 下午
 * @description
 */
@Slf4j
@Component
public class FileUtil {
    @Value("${third.sso.dataKey}")
    private String signKey;
    @Autowired
    private FileFeign fileFeign;

    /**
     * 获取文件url
     * @param fileKey
     * @return
     */
    public String getKeyUrl(String fileKey) {
        if(StringUtils.isEmpty(fileKey)){
            return "";
        }

        if(fileKey.toLowerCase().startsWith("http://") || fileKey.toLowerCase().startsWith("https://")){
            return fileKey;
        }

        FileReqDTO reqDTO = new FileReqDTO();
        reqDTO.setFileKey(fileKey);
        String sign = getSign(signKey);
        reqDTO.setSign(sign);
        reqDTO.setType(StoreType.ADMIN.getCode());
        Result<String> fileUrl = fileFeign.getFileUrl(reqDTO);
        String data = fileUrl.getData();
        if (StringUtils.isEmpty(data)) {
            log.warn("fileFeign batchGetFileUrl is null , sign:{}, key:{}", sign, fileKey);
            return null;
        }

        return data;
    }

    private String getSign(String signKey) {
        String sign = "";
        try {
            sign = CipherUtil.getSign(signKey, "gsol-user-agg");
        } catch (Exception e) {
            log.warn("get sign fail! signKey:{}", signKey);
        }
        return sign;
    }

    /**
     * 删除文件
     * @param file
     * @return
     */
    public static boolean deleteFile(File file){
        //删除文件
        if(file==null || !file.exists()) {
            return false;
        }

        try {
            Files.delete(file.getAbsoluteFile().toPath());
            return true;
        } catch (IOException e) {
            log.warn("delete file occur exception, file:{},detail:{}",file.getAbsoluteFile(),e.getMessage());
            return false;
        }
    }

    /**
     * 获取当前工作目录
     * @param currentObj
     * @return
     */
    public static String getWorkPath(Object currentObj) {
        ApplicationHome home = new ApplicationHome(currentObj.getClass());
        File jarFile = home.getSource();
        if (Objects.isNull(jarFile)) {
            jarFile = home.getDir();
        }
        String workPath = jarFile.getParentFile().getPath();

        if (workPath == null || "/".equals(workPath)) {
            throw new BusinessException(ResultCode.CommonResultCode.SYSTEM_ERROR);
        }

        return workPath;
    }

    public static String encodeFileName(String fileNames, HttpServletRequest request) {
        String codedFilename = null;
        try {
            String agent = request.getHeader("USER-AGENT");
            if (null != agent && -1 != agent.indexOf("MSIE") || null != agent && -1 != agent.indexOf("Trident") || null != agent && -1 != agent.indexOf("Edge")) {// ie浏览器及Edge浏览器
                String name = java.net.URLEncoder.encode(fileNames, "UTF-8");
                codedFilename = name;
            } else if (null != agent && -1 != agent.indexOf("Mozilla")) {
                // 火狐,Chrome等浏览器
                codedFilename = new String(fileNames.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
            }
        } catch (Exception e) {
            log.error("encodeFileName error:{}", JSON.toJSONString(e));
        }
        return codedFilename;
    }
}

