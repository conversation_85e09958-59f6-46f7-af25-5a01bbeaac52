package com.globalsources.admin.util;

/**
 * <p>
 * VideoConstants
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
public class VideoConstants {
	public static final String DOT = ".";

	public static final String PRODUCT_TYPE = "PROD";
	public static final String SECTION_TYPE = "MAIN";
	public static final String TS_TYPE = "TRADESHOW";
	public static final String GSTS_TYPE = "gsTradeShowVideo";
	public static final String SUPPLIER_APP_TYPE = "supplierAPP";

	/** video decoder */
	public static final String VIDEO_DECODER = "h264";

	/** mp4 format */
	public static final String VIDEO_FORMATE_MP4 = "mp4";

	/** allow upload file list */
	public static final String[] FORMAT_LIST = new String[] {DOT + VIDEO_FORMATE_MP4};

	/** video resolution */
	public static final String[] VIDEO_RESOLUTION = new String[] {"16:9","4:3","1:1"};

	/**Allow to upload video count*/
	public static final int MAX_COMP_VIDEO_NUM = 1;

	/** video resolution */
	public static final String[] VIDEO_COVER_RESOLUTION = new String[] {"4:3"};

	/**
	 * video process status
	 */
	public enum ProcessStatus {
		NEW("New"),
		APPROVED("Approved"),
		REJECTED("Rejected"),
		EXPIRED("Expired"),
		ONLINE("Online"),
		DELETED("Deleted"),
		UNPUBLISHED("Unpublished"),
		FAILED("Failed");

		private ProcessStatus(String status) {
			this.status = status;
		}

		String status = "";

		/**
		 * To string
		 */
		public String getString() {
			return status;
		}

	}

	public interface VideoProperty {

		/** frame height */
		String FRAMEHEIGHT = "FrameHeight";

		/** frame width */
		String FRAMEWIDTH = "FrameWidth";

		/** video resolution */
		String VIDEORESOLUTION = "VideoResolution";

		/** video size */
		String VIDEOSIZE = "VideoSize";

		/** play time */
		String PLAYTIME = "PlayTime";

		/** total bit rate */
		String TOTALBITRATE = "TotalBitRate";

		/** video decoder */
		String VIDEODECODER = "VideoDecoder";

		/** video audio decoder */
		String AUDIODECODER = "AudioDecoder";
	}
}
