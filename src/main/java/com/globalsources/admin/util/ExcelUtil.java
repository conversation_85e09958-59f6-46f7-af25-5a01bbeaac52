package com.globalsources.admin.util;

import cn.hutool.core.io.FileUtil;
import com.globalsources.framework.annotation.ExcelColumn;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Sun
 * @date 2021/8/1
 */
@Slf4j
public class ExcelUtil {

    private static final String EXCEL_XLS = "xls";
    private static final String EXCEL_XLSX = "xlsx";

    private static final String DATESTRING_HHMM = "HH:mm";
    private static final String DATESTRING = "yyyy-MM-dd";
    private static final String DATETIMESTRING = "yyyy-MM-dd HH:mm:ss";

    //默认读取第一个sheet
    private static final Integer SHEET_NUM = 0;

    private ExcelUtil() {
    }

    public static <T> List<T> readExcel(Class<T> cls, MultipartFile batchPointFile) {

        String fileName = batchPointFile.getOriginalFilename();

        List<T> dataList = new ArrayList<>();
        Workbook workbook = null;
        try (InputStream is = batchPointFile.getInputStream();) {

            if (Objects.nonNull(fileName) && fileName.endsWith(EXCEL_XLS)) {
                workbook = new HSSFWorkbook(is);
            }
            if (Objects.nonNull(fileName) && fileName.endsWith(EXCEL_XLSX)) {
                workbook = new XSSFWorkbook(is);
            }
            if (workbook != null) {
                readWorkbook(cls, dataList, workbook);
            }
        } catch (Exception e) {
            log.error("parse excel exception:{},e:{}", e.getMessage(), e);
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    log.error("parse excel exception:{},e:{}", e.getMessage(), e);
                }
            }
        }
        return dataList;
    }

    public static <T> List<T> readExcel(Class<T> cls, InputStream inputStream,String fileName) {


        List<T> dataList = new ArrayList<>();
        Workbook workbook = null;
        try {

            if (Objects.nonNull(fileName) && fileName.endsWith(EXCEL_XLS)) {
                workbook = new HSSFWorkbook(inputStream);
            }
            if (Objects.nonNull(fileName) && fileName.endsWith(EXCEL_XLSX)) {
                workbook = new XSSFWorkbook(inputStream);
            }
            if (workbook != null) {
                readWorkbook(cls, dataList, workbook);
            }
        } catch (Exception e) {
            log.error("parse excel exception:{},e:{}", e.getMessage(), e);
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    log.error("parse excel exception:{},e:{}", e.getMessage(), e);
                }
            }
        }
        return dataList;
    }

    private static <T> void readWorkbook(Class<T> cls, List<T> dataList, Workbook workbook) throws InstantiationException, IllegalAccessException {
        //类映射  注解 value-->bean columns
        Map<String, List<Field>> classMap = new HashMap<>();
        List<Field> fields = Stream.of(cls.getDeclaredFields()).collect(Collectors.toList());
        fields.forEach(
                field -> {
                    ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
                    if (annotation != null) {
                        String value = annotation.value();
                        if (StringUtils.isBlank(value)) {
                            return;
                        }
                        if (!classMap.containsKey(value)) {
                            classMap.put(value, new ArrayList<>());
                        }
                        classMap.get(value).add(field);
                    }
                }
        );
        //索引-->columns
        Map<Integer, List<Field>> reflectionMap = new HashMap<>(16);
        //默认读取第一个sheet
        Sheet sheet = workbook.getSheetAt(SHEET_NUM);
        readSheet(cls, dataList, classMap, reflectionMap, sheet);
    }

    private static <T> void readSheet(Class<T> cls, List<T> dataList, Map<String, List<Field>> classMap, Map<Integer, List<Field>> reflectionMap, Sheet sheet) throws InstantiationException, IllegalAccessException {
        boolean firstRow = true;
        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            //首行  提取注解
            if (firstRow) {
                for (int j = row.getFirstCellNum(); j <= row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    String cellValue = getCellValue(cell);
                    if (classMap.containsKey(cellValue)) {
                        reflectionMap.put(j, classMap.get(cellValue));
                    }
                }
                firstRow = false;
            } else {
                buildDataList(cls, dataList, reflectionMap, i, row);
            }
        }
    }

    private static <T> void buildDataList(Class<T> cls, List<T> dataList, Map<Integer, List<Field>> reflectionMap, int i, Row row) throws InstantiationException, IllegalAccessException {
        //忽略空白行
        if (row == null) {
            return;
        }
        T t = cls.newInstance();
        //判断是否为空白行
        boolean allBlank = true;
        for (int j = row.getFirstCellNum(); j <= row.getLastCellNum(); j++) {
            if (reflectionMap.containsKey(j)) {
                Cell cell = row.getCell(j);
                String cellValue = getCellValue(cell);
                if (StringUtils.isNotBlank(cellValue)) {
                    allBlank = false;
                }
                List<Field> fieldList = reflectionMap.get(j);
                fieldList.forEach(
                        x -> {
                            try {
                                setValue(t, x, cellValue);
                            } catch (Exception e) {

                                log.error("reflect field:{} value:{} exception:{}", x.getName(), cellValue, e);
                            }
                        }
                );
            }
        }
        if (!allBlank) {
            dataList.add(t);
        } else {
            log.info("row:{} is blank ignore!", i);
        }
    }

    /**
     * 通过反射动态将Excel读取的信息设置到对应的bean中
     *
     * @param object-存储对象bean
     * @param field-属性名
     * @param value-属性值
     * @throws Exception
     */
    @SneakyThrows
    public static void setValue(Object object, Field field, String value) {
        String key = field.getName();
        String methodName;
        Method[] methods;
        Class<?> clazz = object.getClass();
        methods = clazz.getDeclaredMethods();

        for (Method method : methods) {
            methodName = method.getName();

            if (methodName.startsWith("set") && methodName.toLowerCase().equals("set" + key.toLowerCase())) {
                // 根据参数类型转化value，并进行set操作
                excuteInvokeSetvalue(object, method, field, value);

                // 该属性已经执行setValue操作，无需循环
                break;
            }
        }
    }

    public static void excuteInvokeSetvalue(Object t, Method method, Field field, String value) {
        try {
            Class<?> type = field.getType();
            if (type == void.class || StringUtils.isBlank(value)) {
                return;
            }
            if (type == Object.class) {
                method.invoke(t, value);
                //数字类型
            } else if (type.getSuperclass() == null || type.getSuperclass() == Number.class) {
                invokeNumberType(t, method, value, type);
            } else if (type == boolean.class || type == Boolean.class) {
                method.invoke(t, BooleanUtils.toBoolean(value));
            } else if (type == Date.class) {
                method.invoke(t, new SimpleDateFormat(DATETIMESTRING).parse(value));
            } else if (type == String.class) {
                method.invoke(t, value);
            } else {
                Constructor<?> constructor = type.getConstructor(String.class);
                method.invoke(t, constructor.newInstance(value));
            }
        } catch (Exception e) {
            log.error("handleField error:{}", e.getMessage(), e);
        }
    }

    private static void invokeNumberType(Object t, Method method, String value, Class<?> type) throws IllegalAccessException, InvocationTargetException {
        if (type == int.class || type == Integer.class) {
            method.invoke(t, NumberUtils.toInt(value));
        } else if (type == long.class || type == Long.class) {
            method.invoke(t, NumberUtils.toLong(value));
        } else if (type == double.class || type == Double.class) {
            method.invoke(t, NumberUtils.toDouble(value));
        } else if (type == BigDecimal.class) {
            method.invoke(t, new BigDecimal(value));
        }
    }

    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        if (cell.getCellType() == CellType.NUMERIC) {
            if (DateUtil.isCellDateFormatted(cell)) {
                return coverDate(cell);
            } else {
                return BigDecimal.valueOf(cell.getNumericCellValue()).toString();
            }
        } else if (cell.getCellType() == CellType.STRING) {
            return StringUtils.trimToEmpty(cell.getStringCellValue());
        } else if (cell.getCellType() == CellType.FORMULA) {
            return StringUtils.trimToEmpty(cell.getCellFormula());
        } else if (cell.getCellType() == CellType.BLANK) {
            return "";
        } else if (cell.getCellType() == CellType.BOOLEAN) {
            return String.valueOf(cell.getBooleanCellValue());
        } else if (cell.getCellType() == CellType.ERROR) {
            return "ERROR";
        } else {
            return cell.toString().trim();
        }
    }

    private static String coverDate(Cell cell) {
        short format = cell.getCellStyle().getDataFormat();
        String cellValue = "";
        SimpleDateFormat sdf = null;
        if (format == 20 || format == 32) {
            sdf = new SimpleDateFormat(DATESTRING_HHMM);
        } else if (format == 14 || format == 31 || format == 57 || format == 58) {
            // 处理自定义日期格式：m月d日(通过判断单元格的格式id解决，id的值是58)
            sdf = new SimpleDateFormat(DATESTRING);
            double value = cell.getNumericCellValue();
            Date date = org.apache.poi.ss.usermodel.DateUtil
                    .getJavaDate(value);
            cellValue = sdf.format(date);
        } else {// 日期
            sdf = new SimpleDateFormat(DATETIMESTRING);
        }
        try {
            cellValue = sdf.format(cell.getDateCellValue());// 日期
        } catch (Exception e) {
            log.error("coverDate error:{}", e.getMessage(), e);
        }
        return cellValue;
    }

    /**
     * Download
     *
     * @param path
     * @param response
     */
    public static void download(String path, HttpServletResponse response) {
        try {
            // path是指欲下载的文件的路径。
            ClassPathResource classPathResource = new ClassPathResource(path);
            File file = FileUtil.file(classPathResource.getPath());
            // 取得文件名。
            String filename = file.getName();

            //设置要下载的文件的名称
            response.setHeader("Content-Disposition", "attachment;fileName=" + filename);
            //通知客户端文件的MIME类型
            response.setContentType("application/vnd.ms-template;charset=UTF-8");
            //获取文件的路径
            extracted(path, response);
        } catch (Exception ex) {
            log.warn("Failed to download file, path {}" , path, ex);
        }
    }

    private static void extracted(String path, HttpServletResponse response) {
        try (InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(path)) {
            //读取excel模板
            assert inputStream != null;
            XSSFWorkbook wb = new XSSFWorkbook(inputStream);
            OutputStream os = new BufferedOutputStream(response.getOutputStream());
            wb.write(os);
            os.flush();
            os.close();
        } catch (IOException ex) {
            log.warn("Failed to download file :{}" , path, ex);
        }
    }
}
