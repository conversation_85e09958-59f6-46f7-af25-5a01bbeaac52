package com.globalsources.admin.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/23
 */
@Slf4j
@UtilityClass
public class CookieUtil {
    /**
     * 设置cookie
     * @param response
     * @param cookieName
     * @param cookieValue
     * @param domain cookie的域
     * @param cookieMaxAge 最大生命周期
     */
    public static void setCookie(HttpServletResponse response, String cookieName, String cookieValue, String domain, int cookieMaxAge){
        try {
            if (StringUtils.isEmpty(cookieValue)) {
                cookieValue = "";
            } else {
                cookieValue = URLEncoder.encode(cookieValue, "UTF-8");
            }

            Cookie cookie = new Cookie(cookieName, cookieValue);
            if (cookieMaxAge > 0) {
                cookie.setMaxAge(cookieMaxAge);
            }

            // 设置域名的cookie
            if (!StringUtils.isEmpty(domain)) {
                cookie.setDomain(domain);
            }

            cookie.setPath("/");
            cookie.setHttpOnly(true);
            cookie.setSecure(false);
            response.addCookie(cookie);

        } catch (Exception e) {
            log.error("设置cookie发生异常,cookieName:{}, cookieValue:{}",cookieName,cookieValue, e);
        }
    }
}
