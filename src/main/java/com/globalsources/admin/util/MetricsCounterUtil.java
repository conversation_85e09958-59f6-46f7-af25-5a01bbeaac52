package com.globalsources.admin.util;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Maps;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @since 2024/4/19
 */
@Slf4j
@Component
public class MetricsCounterUtil {

    @Resource
    private MeterRegistry meterRegistry;
    private final Map<String, AtomicLong> metricMap = Maps.newHashMap();

    public void extractMetrics(String counterName, String type, String name, String group, String desc, Integer value) {
        log.info("------- {} -> {}, {}, {}, {}, {}", counterName, type, name, group, desc, value);
        String key = type + "_" + name + "_" + group;
        metricMap.computeIfAbsent(key, k -> metricMap.put(key,
                meterRegistry.gauge(counterName, CollUtil.newArrayList(Tag.of("type", type), Tag.of("name", name), Tag.of("group", group), Tag.of("desc", desc)), new AtomicLong(0))));
        metricMap.get(key).set(value);
    }
}
