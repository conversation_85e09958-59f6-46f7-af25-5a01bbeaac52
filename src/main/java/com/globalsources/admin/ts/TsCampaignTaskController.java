package com.globalsources.admin.ts;

import com.globalsources.admin.model.pojo.AcUser;
import com.globalsources.admin.service.IAcUserService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.ts.api.enums.TradeShowCampaignTaskTypeEnums;
import com.globalsources.ts.api.feign.TsCampaignTaskFeign;
import com.globalsources.ts.api.model.dto.desktop.TsCampaignTaskCreateDTO;
import com.globalsources.ts.api.model.dto.desktop.TsCampaignTaskListDTO;
import com.globalsources.ts.api.model.vo.desktop.TsCampaignTaskDetailVO;
import com.globalsources.ts.api.model.vo.desktop.TsCampaignTaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "展会活动任务")
@Validated
@RequestMapping("/ts/campaign/task")
@RestController
@RequiredArgsConstructor
public class TsCampaignTaskController {

    private final TsCampaignTaskFeign tsCampaignTaskFeign;
    private final IAcUserService userService;
    @ApiOperation("任务类型列表")
    @GetMapping("/type-list")
    public Result<List<Map<String,String>>> typeList() {
        List<Map<String,String>> result = new ArrayList<>();
        for(TradeShowCampaignTaskTypeEnums type: TradeShowCampaignTaskTypeEnums.values()){
            Map<String,String> typeMap = new HashMap<>();
            typeMap.put("key",type.getCode());
            typeMap.put("value",type.getValue());
            result.add(typeMap);
        }
        return Result.success(result);
    }

    @ApiOperation("保存任务")
    @AdminLogin
    @PostMapping("/save")
    public Result<Boolean> save(@ApiIgnore UserVO user, @Valid @RequestBody TsCampaignTaskCreateDTO dto) {
        dto.setAdminUserId(user.getUserId());
        return tsCampaignTaskFeign.save(dto);
    }

    @ApiOperation("获取任务信息")
    @AdminLogin
    @GetMapping("/detail")
    public Result<TsCampaignTaskDetailVO> detail(@ApiParam("活动id") @RequestParam Integer campaignId) {
        return tsCampaignTaskFeign.detail(campaignId);
    }

    @ApiOperation("查询任务列表")
    @AdminLogin
    @PostMapping("/list")
    public Result<PageResult<TsCampaignTaskVO>> list(@ApiIgnore UserVO user, @RequestBody TsCampaignTaskListDTO dto) {
        Result<PageResult<TsCampaignTaskVO>> result = tsCampaignTaskFeign.list(dto);
        if(CollectionUtils.isNotEmpty(result.getData().getList())){
            List<Long> userId= result.getData().getList().stream().map(TsCampaignTaskVO::getCreateBy).collect(Collectors.toList());
            userId.addAll(result.getData().getList().stream().map(TsCampaignTaskVO::getLUpdBy).collect(Collectors.toList()));
            Map<Long,String> userMap = userService.getUserByIds(userId).stream().collect(Collectors.toMap(AcUser::getUserId,AcUser::getEmail));
            result.getData().getList().forEach(item->{
                item.setCreater(userMap.get(item.getCreateBy()));
                item.setUpdater(userMap.get(item.getLUpdBy()));
            });
        }
        return result;
    }


}
