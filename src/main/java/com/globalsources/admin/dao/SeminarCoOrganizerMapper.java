package com.globalsources.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.globalsources.admin.seminar.model.dto.SeminarCoOrganizerQueryDto;
import com.globalsources.admin.seminar.model.entity.SeminarCoOrganizer;
import org.apache.ibatis.annotations.Param;

public interface SeminarCoOrganizerMapper extends BaseMapper<SeminarCoOrganizer> {


    IPage<SeminarCoOrganizer> listPage(IPage<SeminarCoOrganizer> page,
                                       @Param("query") SeminarCoOrganizerQueryDto query);

}
