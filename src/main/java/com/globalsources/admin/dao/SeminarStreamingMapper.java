package com.globalsources.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.globalsources.admin.seminar.model.dto.SeminarStreamingQueryDto;
import com.globalsources.admin.seminar.model.entity.SeminarStreaming;
import org.apache.ibatis.annotations.Param;

public interface SeminarStreamingMapper extends BaseMapper<SeminarStreaming> {


    IPage<SeminarStreaming> listPage(IPage<SeminarStreaming> page,
                                       @Param("query") SeminarStreamingQueryDto query);

}
