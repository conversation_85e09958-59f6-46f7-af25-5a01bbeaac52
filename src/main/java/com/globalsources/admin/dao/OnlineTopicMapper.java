package com.globalsources.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.admin.model.pojo.OnlineTopic;
import com.globalsources.admin.model.vo.helpcenter.OnlineTopicSCVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/3
 */
public interface OnlineTopicMapper extends BaseMapper<OnlineTopic> {

    Integer getDisplaySeqMax(Long topicPid, Boolean homeFlag, String sourceCode);

    Long getTopicPid(Long topicId);

    Integer updateDisplaySeq(Long topicId, Integer displaySeq);

    Integer updateAcrossCategoriesDisplaySeq(Long topicId,Long topicPid, Integer displaySeq);

    List<OnlineTopicSCVO> selectAll(Boolean homeFlag, String sourceCode);

    List<OnlineTopicSCVO> selectAllSCMenu(long topicPid, String sourceCode);
}
