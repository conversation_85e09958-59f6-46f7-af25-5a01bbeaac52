package com.globalsources.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.admin.model.dto.resource.SimpleResourceContractLevelLinkDTO;
import com.globalsources.admin.model.pojo.AcResourceContractLevelLink;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-14
 */
public interface AcResourceContractLevelLinkMapper extends BaseMapper<AcResourceContractLevelLink> {

    List<SimpleResourceContractLevelLinkDTO> selectChildrenResourceContractLinkSimpleDto(@Param("resourceId") Long resourceId);

    List<SimpleResourceContractLevelLinkDTO> selectParentResourceContractLinkSimpleDto(@Param("resourceId") Long resourceId);

    List<SimpleResourceContractLevelLinkDTO> selectResourceContractLinkSimpleDtoByAppName(@Param("resourceIds") List<Long> resourceIds, @Param("appName") String appName);

    SimpleResourceContractLevelLinkDTO selectResourceContractLinkSimpleDtoById(@Param("resourceId") Long resourceId);

}
