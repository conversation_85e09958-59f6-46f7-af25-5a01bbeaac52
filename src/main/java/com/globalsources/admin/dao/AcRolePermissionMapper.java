package com.globalsources.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.admin.model.pojo.AcRolePermission;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 角色权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-28
 */
public interface AcRolePermissionMapper extends BaseMapper<AcRolePermission> {

    int deleteRolePermission(@Param("roleId") Long roleId);

    int batchLogicDeleteRolePermission(@Param("ids") List<Long> ids, @Param("userId") Long userId, @Param("updateDate") Date updateDate);
}
