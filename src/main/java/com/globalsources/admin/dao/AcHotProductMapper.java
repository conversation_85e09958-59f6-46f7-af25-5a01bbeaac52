package com.globalsources.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.admin.model.dto.recommend.HotProdBasicParamDTO;
import com.globalsources.admin.model.pojo.AcHotProduct;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * admin console平台Hot product表的Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
public interface AcHotProductMapper extends BaseMapper<AcHotProduct> {

    /**
     * seq重复时，根据id seq upFlag，批量把相应记录的seq+1, 使记录排序上移或下移 (倒序排列)
     *
     * @param id         hot_product_id
     * @param currentSeq id->seq
     * @param upFlag     upFlag
     * @param param      param
     * @return int
     */
    int increaseSeqBatchForDuplicateSeq(@Param("id") Long id, @Param("currentSeq") Integer currentSeq, @Param("upFlag") boolean upFlag, @Param("param") HotProdBasicParamDTO param);
}
