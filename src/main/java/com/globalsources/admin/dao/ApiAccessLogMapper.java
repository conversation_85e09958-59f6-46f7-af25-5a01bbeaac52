package com.globalsources.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.admin.model.pojo.supplier.ApiAccessLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface ApiAccessLogMapper extends BaseMapper<ApiAccessLog> {

    Page<ApiAccessLog> searchApiAccessLog(Page<ApiAccessLog> page, @Param("email") String email, @Param("supplierId") Long supplierId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("sort") String sort);

}
