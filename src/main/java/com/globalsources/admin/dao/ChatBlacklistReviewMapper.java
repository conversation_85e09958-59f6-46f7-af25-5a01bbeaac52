package com.globalsources.admin.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.admin.model.pojo.ChatBlacklistReview;
import org.apache.ibatis.annotations.Param;

public interface ChatBlacklistReviewMapper extends BaseMapper<ChatBlacklistReview> {
    /**
     * 查询待审核列表,聊天记录超过30天忽略
     * @param keyword
     * @param orderField
     * @param order
     * @return
     */
    Page<ChatBlacklistReview> selectReviewList(Page<ChatBlacklistReview> page,@Param("keyword") String keyword, @Param("orderField") String orderField, @Param("sort") String sort);

}
