package com.globalsources.admin.user.controller;


import cn.hutool.core.text.csv.CsvWriter;
import com.globalsources.admin.util.HttpHeadUtil;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.DateUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.user.api.dto.TmxResultDetailDeleteAggDTO;
import com.globalsources.user.api.dto.TmxResultDetailQueryAggDTO;
import com.globalsources.user.api.feign.TmxResultDetailFeign;
import com.globalsources.user.api.vo.TmxResultDetailAggVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.OutputStreamWriter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * TMX返回结果
 * </p>
 *
 * <AUTHOR> Sun
 * @since 2024-10-14
 */
@Slf4j
@Api(tags = "TMX返回结果")
@RequestMapping("/tmx-result-detail")
@RestController
public class TmxResultDetailController {

    @Resource
    private TmxResultDetailFeign tmxResultDetailFeign;

    @AdminLogin
    @ApiOperation("查询")
    @PostMapping("/v1/select-tmx-result-detail")
    public Result<PageResult<TmxResultDetailAggVO>> selectTmxResultDetail(@RequestBody @Valid TmxResultDetailQueryAggDTO dto) {
        return tmxResultDetailFeign.selectTmxResultDetail(dto);
    }

    @AdminLogin
    @ApiOperation("删除")
    @PostMapping("/v1/delete-tmx-result-detail")
    public Result<Boolean> deleteTmxResultDetail(@ApiIgnore UserVO user, @RequestBody @Valid TmxResultDetailDeleteAggDTO dto) {
        dto.setUserId(user.getUserId());
        return tmxResultDetailFeign.deleteTmxResultDetail(dto);
    }

    @ApiOperation("下载")
    @GetMapping("/v1/download-tmx-result-detail")
    public void downloadTmxResultDetail(HttpServletResponse response,
                                        @RequestParam(required = false) String keyword,
                                        @RequestParam(required = false) String exactId,
                                        @RequestParam(required = false) String smartId,
                                        @RequestParam(required = false) String startDate,
                                        @RequestParam(required = false) String endDate) {

        String fileName = "Blacklist Device ID-" + DateUtil.data2str(new Date(), DateUtil.PATTERN_YYYYMMDD) + ".csv";

        TmxResultDetailQueryAggDTO queryDTO = TmxResultDetailQueryAggDTO.builder()
                .keyword(keyword)
                .exactId(exactId)
                .smartId(smartId)
                .startDate(startDate)
                .endDate(endDate)
                .pageNum(1L)
                .pageSize(200L)
                .build();

        PageResult<TmxResultDetailAggVO> pageResult = ResultUtil.getData(tmxResultDetailFeign.selectTmxResultDetail(queryDTO), "selectTmxResultDetail return error, dto:{}" + queryDTO);

        try (CsvWriter writer = new CsvWriter(new OutputStreamWriter(response.getOutputStream()))) {
            HttpHeadUtil.setDownHeader(response, fileName, true);

            //写入下载表头
            writer.writeLine("Smart ID",
                    "Exact ID",
                    "Buyer ID",
                    "Buyer Email",
                    "Added Date",
                    "Reason");

            // 遍历分页数据写入内容
            Integer currentPageNum = 1;
            while (Objects.nonNull(pageResult.getTotalPage()) && currentPageNum <= pageResult.getTotalPage()) {
                writeCsvRowData(writer, pageResult.getList());
                // 查询下一页
                currentPageNum++;
                queryDTO.setPageNum(Long.valueOf(currentPageNum));
                pageResult = ResultUtil.getData(tmxResultDetailFeign.selectTmxResultDetail(queryDTO), "selectTmxResultDetail return error, dto:{}" + queryDTO);
                if (CollectionUtils.isEmpty(pageResult.getList())) {
                    writer.flush();
                }
            }
            // 写完刷新
            writer.flush();
        } catch (Exception e) {
            log.error("new CsvWriter error,", e);
        }
    }

    private void writeCsvRowData(CsvWriter writer, List<TmxResultDetailAggVO> list) {
        String dateFormat = "yyyy/MM/dd HH:mm:ss";

        for (TmxResultDetailAggVO vo : list) {
            String accessTime = "";
            if (vo.getCreateDate() != null) {
                accessTime = DateUtil.data2str(vo.getCreateDate(), dateFormat);
            }

            writer.writeLine(vo.getSmartId(), vo.getExactId(), Optional.ofNullable(vo.getUserId()).map(Object::toString).orElse(StringUtils.EMPTY), vo.getEmailAddr(), accessTime, vo.getPolicy());
        }
    }
}
