package com.globalsources.admin.annotation;

import com.globalsources.admin.enums.MenuOperationEnum;
import com.globalsources.admin.enums.OperationEnum;
import com.globalsources.admin.enums.PageOperationEnum;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * admin 用户登录检查以及登录信息注入
 *
 * <AUTHOR>
 */
@Documented
@Target(ElementType.METHOD)
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Component
public @interface AdminOperationLoginLog {

    MenuOperationEnum menuId();

    PageOperationEnum pageId();

    OperationEnum method();

}
