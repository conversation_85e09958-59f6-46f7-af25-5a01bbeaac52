package com.globalsources.admin.handler;

import com.alibaba.fastjson.JSON;
import com.globalsources.admin.model.pojo.AcSupplierVr;
import com.globalsources.admin.service.AcSupplierVrService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/12 17:17
 */
@Slf4j
@Component
public class SuppVrHandler {

    @Autowired
    private AcSupplierVrService acSupplierVrService;

    @XxlJob(value = "postSupplierVr")
    public ReturnT<String> postSupplierVr() {
        log.info("begin postSupplierVr");
        List<AcSupplierVr> newSupplierVrList = null;
        try {
            newSupplierVrList = acSupplierVrService.getNewSupplierVr();
        } catch (Exception e) {
            log.error("failed to getNewSupplierVr, errorMsg: " + e.getMessage(), e);
        }
        if (CollectionUtils.isNotEmpty(newSupplierVrList)) {
            for (AcSupplierVr vr :newSupplierVrList) {
                boolean b = false;
                try {
                    b = acSupplierVrService.postSupplierVr(vr);
                } catch (Exception e) {
                    log.error("failed to postSupplierVr , error:" + e.getMessage() +  ", vr:" + JSON.toJSONString(vr), e);
                }
                if (!b) {
                    log.warn("failed to postSupplierVr, supplierVr: " + vr);
                }
            }
        }
        log.info("postSupplierVr end");
        return ReturnT.SUCCESS;
    }
}
