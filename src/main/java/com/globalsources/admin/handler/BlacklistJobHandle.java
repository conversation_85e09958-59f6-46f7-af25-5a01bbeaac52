package com.globalsources.admin.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.globalsources.admin.dao.RfqBlackListMapper;
import com.globalsources.admin.model.pojo.RfqBlackListPO;
import com.globalsources.admin.service.ChatBlacklistReviewService;
import com.globalsources.admin.service.RfqBlackListService;
import com.globalsources.eblock.agg.api.enums.EblockTypeEnum;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfx.service.IUserService;
import com.opencsv.CSVReader;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class BlacklistJobHandle {

    private final RfqBlackListService blackListService;
    private final ChatBlacklistReviewService blacklistReviewService;
    private final RfqBlackListMapper rfqBlackListMapper;
    private final IUserService userService;

    @XxlJob(value = "forceSyncNoSpeaking")
    public ReturnT<String> forceSyncNoSpeaking(){
        try {
            log.info("start force sync no speaking task");
            int handleCount = blackListService.forceSyncNoSpeaking();
            log.info("execute force sync no speaking, success:{}", handleCount);
            return ReturnT.SUCCESS;

        }catch (InterruptedException ie){
            Thread.currentThread().interrupt();
            log.error("execute force sync no speaking occur interrupt exception,detail:{}",ie.getMessage(),ie);
            return ReturnT.FAIL;
        }catch (Exception e){
            log.error("execute force sync no speaking occur exception,detail:{}",e.getMessage(),e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "initImportData")
    public ReturnT<String> initNoSpeakingData(){
        try {
            log.info("start init import no speaking data task");
            int handleCount = blackListService.initImportData();
            log.info("execute init import no speaking data, success:{}", handleCount);
            return ReturnT.SUCCESS;

        }catch (Exception e){
            log.error("execute init import no speaking data occur exception,detail:{}",e.getMessage(),e);
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "initBlacklistSource")
    public ReturnT<String> initBlacklistSource(){
        try {
            log.info("start init blacklist source data");
            blacklistReviewService.initBlacklistSource();
            log.info("complete init blacklist source data");
            return ReturnT.SUCCESS;
        }catch (Exception e){
            log.error("execute init blacklist source data occur exception,detail:{}",e.getMessage(),e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "addBlackListUserByEmail")
    public ReturnT<String> addBlackListUserByEmail(){
        String param = XxlJobHelper.getJobParam();
        if(StringUtils.isBlank(param)){
            return ReturnT.FAIL;
        }
        try {
            log.info("start addBlackListUserByEmail");
            String[] emails = param.split(",");
            for(String email:emails){
                blackList(email);
            }
            log.info("complete addBlackListUserByEmail");
            return ReturnT.SUCCESS;
        }catch (Exception e){
            log.error("execute addBlackListUserByEmail exception,detail:{}",e.getMessage(),e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "addBlackListUserByFile")
    public ReturnT<String> addBlackListUserByFile() {
        String param = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(param)) {
            return ReturnT.FAIL;
        }
        log.info("start addBlackListUserByFile");

        try (CSVReader csvReader = new CSVReader(new InputStreamReader(Objects.requireNonNull(BlacklistJobHandle.class.getClassLoader().getResourceAsStream(param)), "UTF8"))) {
            String[] strArr = null;
            while ((strArr = csvReader.readNext()) != null) {
                blackList(strArr[0]);
            }
        } catch (IOException e) {
            log.error("execute addBlackListUserByEmail exception,detail:{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("complete addBlackListUserByFile");
        return ReturnT.SUCCESS;
    }

    public void blackList(String email) {
        try{
            if(StringUtils.isBlank(email)){
                return;
            }
            UserVO user = userService.getUserByUserEmail(email.trim());
            if(Objects.isNull(user)){
                log.info("addBlackListUserByEmail email :{} without user",email);
                return;
            }
            boolean result = blackListService.add(user.getUserId(),1300000000001L,null,"Add Blacklist","RU sanctioned buyer");
            log.info("addBlackListUserByEmail email : {} userId :{} result :{}",email,user.getUserId(),result);
        }catch (Exception e) {
            log.error("blackList exception, email :{} detail:{}",email,e.getMessage(),e);
        }
    }

    /**
     * 移除黑名单用户订阅
     * @return
     */
    @XxlJob(value = "unSubscribeBlackListUser")
    public ReturnT<String> unSubscribeBlackListUser(){
        try {
            log.info("start unSubscribeBlackListUserEmail");
            List<RfqBlackListPO> blackList = getBlacklistUser(0L);
            while(CollectionUtils.isNotEmpty(blackList)) {
                blackList.forEach(blackListPO -> {
                    boolean result = blackListService.unsubscribeAll(blackListPO.getBuyerId());
                    log.info("unsubscribeAll userId :{} result :{}",blackListPO.getBuyerId(),result);
                });
                blackList = getBlacklistUser(blackList.get(blackList.size() - 1).getBuyerId());
            }
            log.info("complete unSubscribeBlackListUserEmail");
            return ReturnT.SUCCESS;
        }catch (Exception e){
            log.error("execute unSubscribeBlackListUserEmail exception,detail:{}",e.getMessage(),e);
            return ReturnT.FAIL;
        }
    }

    private List<RfqBlackListPO> getBlacklistUser(Long lastUserId){
        return rfqBlackListMapper.selectList(new LambdaQueryWrapper<RfqBlackListPO>()
                .gt(RfqBlackListPO::getBuyerId,lastUserId)
                .eq(RfqBlackListPO::getType, EblockTypeEnum.black.getType())
                .eq(RfqBlackListPO::getDeleteFlag,false)
                .orderByAsc(RfqBlackListPO::getBuyerId)
                .last("limit 100")
        );

    }
}
