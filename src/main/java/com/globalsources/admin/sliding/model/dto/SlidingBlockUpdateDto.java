package com.globalsources.admin.sliding.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
@Accessors(chain = true)
@ApiModel(value="Sliding-block update 对象", description="Sliding-block update 对象")
@RequiredArgsConstructor
public class SlidingBlockUpdateDto implements Serializable {

    private static final long serialVersionUID = 4261653809124853914L;

    @ApiModelProperty(value = "slidingId", notes = "slidingId")
    @NotNull(message = "slidingId is required")
    private Long slidingId;


    @ApiModelProperty(value = "slidingValue", notes = "sliding value")
    @NotBlank(message = "slidingValue is required")
    private String slidingValue;

    @ApiModelProperty(value = "slidingEnabled", notes = "true/false")
    private Boolean slidingEnabled;

}
