package com.globalsources.admin.mq;

import com.globalsources.framework.rabbit.RabbitTemplateFactory;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.Connection;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class BlacklistMessageConsumer {
    private String exchange="GSOL.BLACKLIST";
    public static final String QUEUE_NAME="buyer.blacklist.max";

    @Resource
    private RabbitTemplateFactory rabbitTemplateFactory;
    @Resource
    private BlacklistDeliver dataDeliver;
    @Resource
    private BlacklistSubscriberCallback subscriberCallback;

    /**
     * 设置rabbit工厂
     *
     * @param rabbitTemplateFactory
     */
    public void setRabbitTemplateFactory(RabbitTemplateFactory rabbitTemplateFactory) {
        this.rabbitTemplateFactory = rabbitTemplateFactory;
    }

    /**
     * 初始化mq
     */
    public void init(){
        rabbitTemplateFactory.createQueue("direct",exchange,QUEUE_NAME,QUEUE_NAME);
    }


    public void startConsume() {
        log.info("start receive buyer blacklist msg");
        //创建一个mq连接
        try(Connection connection = rabbitTemplateFactory.getConnection().createConnection();
            Channel channel = connection.createChannel(false))  {

            log.info("==start receive buyer blacklist,consumer status, connection open:{}, channel open:{}", connection.isOpen(), channel.isOpen());
            //启动mq监听 note:(consumerTag 回调方法会收到这个tag)
            channel.basicConsume(QUEUE_NAME, true, "buyerBlacklistMaxTag", dataDeliver, subscriberCallback, subscriberCallback);
        } catch (Exception e) {
            log.error("start receive buyer blacklist occur exception,detail:{}",e.getMessage(), e);
        }
    }
}
