package com.globalsources.admin.report.service;

import com.globalsources.admin.report.model.dto.OrderReportListQueryDTO;
import com.globalsources.admin.report.model.vo.OrderSummaryReportVO;
import com.globalsources.admin.report.model.vo.OrderSupplierReportVO;
import com.globalsources.admin.report.model.vo.SummaryReportDetailVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;

import javax.servlet.http.HttpServletResponse;

public interface OrderReportService {
    Result<PageResult<OrderSupplierReportVO>> getSupplierReportList(OrderReportListQueryDTO queryDTO);

    Result<PageResult<OrderSummaryReportVO>> getSummaryReportList(OrderReportListQueryDTO queryDTO);

    Result<SummaryReportDetailVO> getSummaryReportDetail(String orderId);

    void exportOrderSummaryReport(Long startDate, Long endDate, HttpServletResponse response);
}
