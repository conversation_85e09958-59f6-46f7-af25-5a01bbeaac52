package com.globalsources.admin.report.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SummaryReportDetailVO {

    private OrderAttributeVO orderAttributeVO;

    private OrderAmountVO orderAmountVO;

    private OrderStatusVO orderStatusVO;

    private List<OrderProductVO> orderProductVO;

    private OrderShippingVO orderShippingVO;

    private BuyerProfileVO buyerProfileVO;

    private SupplierContactVO supplierContactVO;

    @Data
    @Builder
    public static class OrderAttributeVO {
        private String orderId;
        private Long orderCreateDate;
        private String currentOrderStatus;
        private String OrderType;
        private String referenceId;
        private String paymentMethod;
        private String cancelReason;
    }

    @Data
    @Builder
    public static class  OrderAmountVO {
        private BigDecimal totalOrderAmount;
        private BigDecimal totalProductAmount;
        private BigDecimal totalShippingAmount;
        private BigDecimal discountAmount;
        private Integer couponId;
        private Integer campaignId;
        private BigDecimal paidToPaypal;
        private BigDecimal paidToGS;
        private BigDecimal paidToSupplier;
        private Boolean refundRequest;
        private BigDecimal refundAmount;
        private String currency;
        private Double amount;
        private Double thresholdAmount;
    }

    @Data
    @Builder
    public static class OrderStatusVO {
        private Long orderCreateDate;
        private Long quoteDate;
        private Long revisedQuoteDate;
        private Long paymentDate;
        private Long shipmentDate;
        private Long refundRequestDate;
        private Long refundAcceptedDate;
        private Long refundRejectedDate;
        private Long submitDisputeDate;
        private Long disputeSolvedDate;
        private Long cancellationDate;
        private Long completionDate;
    }

    @Data
    @Builder
    public static class OrderProductVO {
        private Integer productTotalCount;
        private String modelNumber;
        private String orderQuantity;
        private String L4categoryName;
        private Long productId;
        private String ppSpd;
        private String shippingNotes;
        private String unitPrice;
        private String reviseUnitPrice;
        private String currency;
    }

    @Data
    @Builder
    public static class OrderShippingVO {
        private String shippingTrackingNumber;
        private String shippingCourierName;
        private String recipientName;
        private String recipientContactNumber;
        private String shippingAddress;
        private String leadTimeToShip;
    }

    @Data
    @Builder
    public static class BuyerProfileVO {
        private String buyerName;
        private String buyerEmail;
        private String contactNumber;
        private String buyerCountry;
        private String buyerAddress;
        private String buyerCompanyName;
    }

    @Data
    @Builder
    public static class SupplierContactVO {
        private Long supplierId;
        private String supplierName;
        private String supplierOrderContactPerson;
        private String supplierRegion;
        private String supplierCity;
        private String supplierContactNumber;
        private String supplierPaypalAccount;
        private String supplierPaypalAccountSnap;
        private String supplierCSO;
    }
}
