package com.globalsources.admin.service;

import com.globalsources.admin.model.dto.TrafficDistSeetingDTO;
import com.globalsources.admin.model.vo.TrafficDistSettingVO;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-10-17
 */
public interface TrafficDistSettingService {

    Result<List<TrafficDistSettingVO>> getTrafficDistributionSettingInfo();

    boolean updateTrafficDistSettingInfo(UserVO userInfo, TrafficDistSeetingDTO trafficDistSeetingDto);

    boolean updateDistUserTagSettingInfo(UserVO userInfo,Boolean hongkongShowFlag,Boolean virtualShowFlag,Boolean gsolFlag);

    boolean updateAllTrafficDistSettingInfo(UserVO userInfo,List<TrafficDistSeetingDTO> trafficDistDtoList);

}
