package com.globalsources.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.admin.model.dto.AcRoleCreateDTO;
import com.globalsources.admin.model.dto.AcRoleEditDTO;
import com.globalsources.admin.model.dto.AcRoleStatusDTO;
import com.globalsources.admin.model.dto.AcRoleSupplierDTO;
import com.globalsources.admin.model.dto.AcRoleSupplierInfoDTO;
import com.globalsources.admin.model.dto.AcRoleSupplierPageReqDTO;
import com.globalsources.admin.model.dto.QuerySupplierRangeResDTO;
import com.globalsources.admin.model.dto.role.RoleDispListQueryDTO;
import com.globalsources.admin.model.pojo.AcRole;
import com.globalsources.admin.model.vo.role.AcRoleBaseVO;
import com.globalsources.admin.model.vo.role.AcRoleDetailVO;
import com.globalsources.admin.model.vo.role.RoleChooseBaseVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.vo.UserVO;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> Chen
 * @since 2021-06-28
 */
public interface IAcRoleService extends IService<AcRole> {

    PageResult<AcRoleBaseVO> acRoleList(RoleDispListQueryDTO basePage, UserVO userVO);

    boolean acRoleDelete(List<Long> ids, @NonNull Long userId, String appName);

    boolean acRoleCreate(AcRoleCreateDTO acRoleCreateDTO, @NonNull Long userId);

    boolean acRoleStatus(AcRoleStatusDTO acRoleStatusDTO, @NonNull Long userId);

    List<RoleChooseBaseVO> roleChooseList(UserVO userVO, String appName);

    AcRoleDetailVO acRoleDetail(Long id, UserVO userVO);

    boolean acRoleEdit(AcRoleEditDTO acRoleEditDTO, @NonNull Long userId);

    AcRoleSupplierDTO acRoleSupplierDetail(Long roleId, UserVO userVO);

    PageResult<AcRoleSupplierInfoDTO> acRoleSupplierPageInfo(AcRoleSupplierPageReqDTO reqDTO, Long userId);

    QuerySupplierRangeResDTO querySupplierRangeByUserId(Long userId);

    boolean existRoleName(String roleName, String appName);

    List<Long> getRoleIdsByUserId(Long userId, String appName, Long supplierId);

    List<Long> getRoleIdsByRoleIdsAndAppName(List<Long> roleIds, String appName);

    List<Long> selectCsoRoleIdsByUserIdAndSupplierId(Long userId, Long supplierId);

    List<String> getRoleNamesByRoleIds(List<Long> roleIds);

}
