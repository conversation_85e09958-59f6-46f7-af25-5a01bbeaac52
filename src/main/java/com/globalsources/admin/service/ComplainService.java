package com.globalsources.admin.service;

import com.globalsources.admin.model.dto.complain.SelectComplainBuyerListDTO;
import com.globalsources.admin.model.vo.complain.ComplainBuyerDetailVO;
import com.globalsources.admin.model.vo.complain.ComplainBuyerVO;
import com.globalsources.admin.model.vo.complain.RfiComplainDetailVO;
import com.globalsources.admin.model.vo.complain.RfqComplainDetailVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.operation.agg.api.dto.CampaignFileDeleteDTO;
import com.globalsources.operation.agg.api.vo.CampaignFileUploadVO;
import org.springframework.web.multipart.MultipartFile;

public interface ComplainService {
    PageResult<ComplainBuyerVO> selectComplainBuyerList(SelectComplainBuyerListDTO selectComplainBuyerListDTO);

    ComplainBuyerDetailVO selectComplainListByUserId(Long userId);

    Result postComplainResult(Long userId, String result, Long reviewerId, String reviewerEmail);

    Result cancelComplain(Long userId,Long reviewerId);

    Integer queryPendingBuyerNum();

    RfqComplainDetailVO requestDetailRfq(Long userId, Long supplierId, String requestId);

    RfiComplainDetailVO requestDetailRfi(Long userId, Long supplierId, String requestId);

    Result<CampaignFileUploadVO> uploadCampaignFile(Long userId, Long campaignId, MultipartFile file);

    Result<Boolean> deleteCampaignFile(CampaignFileDeleteDTO fileDeleteDTO);
}
