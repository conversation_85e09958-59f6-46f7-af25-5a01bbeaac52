package com.globalsources.admin.service;

import com.globalsources.activity.agg.api.vo.LiveStreamReportSwitchVO;
import com.globalsources.admin.model.dto.LivestreamReportSwitchDTO;
import com.globalsources.admin.model.dto.MakeLiveCampaignReportDTO;
import com.globalsources.admin.model.dto.report.LiveCampaignSuppReport;
import com.globalsources.admin.model.vo.QueryLiveCampaignReportStatusVO;
import com.globalsources.framework.result.Result;

import java.util.List;

public interface LiveCampaignReportService {
    QueryLiveCampaignReportStatusVO queryReportStatus();

    Result<Void> makeCampaignReport(MakeLiveCampaignReportDTO dto);

    /**
     * 查询campaign供应商报告最多65535条
     * @return
     */
    List<LiveCampaignSuppReport> queryCampaignSupplierReport();

    void resetReportStatus();

    LiveStreamReportSwitchVO reportSwitch();

    LiveStreamReportSwitchVO reportSwitchUpdate(LivestreamReportSwitchDTO dto);

    Result<Boolean> reportNotifySend(Long lcId);
}
