package com.globalsources.admin.service;

import com.globalsources.activity.agg.api.vo.JoinLiveVO;
import com.globalsources.activity.agg.api.vo.StartLiveVO;
import com.globalsources.activity.agg.api.vo.WsReplayCtlVO;
import com.globalsources.framework.result.Result;

public interface LivestreamService {
    /**
     * 加入直播频道
     * @param userId
     * @param activityId
     * @return
     */
    Result<JoinLiveVO> join(String userId, Long activityId);

    /**
     * 创建直播频道
     * @param activityId
     * @param userId
     * @return
     */
    Result<StartLiveVO> createChannel(Long activityId, Long userId);

    /**
     * 开始直播
     * @param activityId
     * @param userId
     * @param width
     * @param height
     * @return
     */
    Result<StartLiveVO> startLive(Long activityId, Long userId,Integer width,Integer height);

    /**
     * 结束直播
     * @param activityId
     * @param userId
     * @return
     */
    Result<Void> closeLive(Long activityId, Long userId);

    /**
     * 切换供应商
     * @param userId
     * @param activityId
     * @param supplierId
     * @return
     */
    Result<Void> switchSupplier(Long userId,Long activityId,Long supplierId);

    /**
     * 取消供应商切换
     * @param userId
     * @param activityId
     * @return
     */
    Result<Void> cancelSwitchSupplier(Long userId, Long activityId);

    /**
     * 制作进入直播工作台签名
     * @param activityId
     * @param time
     * @return
     */
    String makeAccessSign(Long activityId,Long time);

    /**
     * 制作进入工作台签名
     * @param activityId
     * @param userId 用户id
     * @param time
     * @return
     */
    String makeAccessSignV1(Long activityId, Long userId, Long time);

    /**
     * 暂停直播
     * @param activityId
     * @param userId
     * @param replayId
     * @return
     */
    Result<Void> pauseLive(Long activityId, Long userId,Long replayId);

    /**
     * 获取工作台回放信息
     * @param activityId
     * @return
     */
    Result<WsReplayCtlVO> getWsReplayCtlInfo(Long activityId);

    /**
     * 插播视频
     * @param systemUserId
     * @param activityId
     * @param replayId
     * @param trigger
     * @return
     */
    Result<Void> playVideo(long systemUserId, Long activityId, Long replayId, Boolean trigger);

    /**
     * 修改cdn分辨率
     * @param activityId
     * @param userId 登录用户id
     * @param width 分辨率宽度
     * @param height 分辨率高度
     * @return
     */
    Result<Void> switchCdnResolution(Long activityId, Long userId, Integer width, Integer height);

    /**
     * 发送开始直播信令
     * @param activityId
     * @param userId
     * @return
     */
    Result<Void> sendStartSignaling(Long activityId, Long userId);
}
