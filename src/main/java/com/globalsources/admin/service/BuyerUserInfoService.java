package com.globalsources.admin.service;

import com.globalsources.admin.model.dto.user.BuyerSearchDTO;
import com.globalsources.admin.model.vo.user.BuyerListVO;
import com.globalsources.admin.model.vo.user.BuyerProfileVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;

public interface BuyerUserInfoService {
    /**
     * 搜索用户列表
     * @param query
     * @return
     */
    Result<PageResult<BuyerListVO>> searchUser(BuyerSearchDTO query);
    /**
     * 获取用户profile信息
     * @param buyerId
     * @return
     */
    Result<BuyerProfileVO> getUserProfile(Long buyerId);
}
