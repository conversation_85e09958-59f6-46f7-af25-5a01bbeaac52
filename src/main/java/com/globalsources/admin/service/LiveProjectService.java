package com.globalsources.admin.service;

import com.globalsources.activity.agg.api.dto.LiveChannelPhaseDTO;
import com.globalsources.activity.agg.api.dto.LiveProjectDTO;
import com.globalsources.activity.agg.api.vo.LiveChannelPhaseVO;
import com.globalsources.activity.agg.api.vo.LiveChannelSimpleVO;
import com.globalsources.activity.agg.api.vo.LiveChannelVO;
import com.globalsources.activity.agg.api.vo.PavilionVO;
import com.globalsources.admin.model.dto.AcLiveChannelPhaseDTO;
import com.globalsources.admin.model.dto.LiveBannerDTO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface LiveProjectService {
    Result<PageResult<LiveChannelVO>> queryListPage(Long pageNum, Long pageSize);

    Result<Long> saveLiveProject(Long userId, LiveProjectDTO dto);

    Result<Void> removeLiveProject(Long userId, Long projectId);

    Result<PageResult<LiveChannelPhaseVO>> queryPhasePage(Long projectId, Long pageNum, Long pageSize);

    Result<Void> switchPhaseEnabled(Long userId, Long phaseId, Boolean enabled);

    Result<Void> saveProjectPhase(Long userId, AcLiveChannelPhaseDTO dto);

    Result<LiveChannelPhaseDTO> getProjectPhaseInfo(Long phaseId);

    Result<Void> removePhase(Long userId, Long phaseId);

    Result<Boolean> uploadLiveBanner(Long userId, Long lcpId, MultipartFile file);

    List<LiveBannerDTO> downloadLiveBanner(Long lcpId);

    Result<Boolean> deleteLiveBanner(Long lcpId);

    Result<List<PavilionVO>> queryPhasePavilionList(Long phaseId);

    Result<LiveChannelVO> getLiveProjectInfo(Long lcId);

    Result<Void> openReportTrigger(Long userId,Long projectId, Boolean triggerVal);

    Result<List<LiveChannelSimpleVO>> queryList();
}
