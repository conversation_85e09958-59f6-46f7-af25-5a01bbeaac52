package com.globalsources.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.admin.model.dto.hotel.AcHotelDTO;
import com.globalsources.admin.model.dto.hotel.HotelCtaDTO;
import com.globalsources.admin.model.pojo.AcHotel;
import com.globalsources.framework.result.PageResult;

/**
 * <p>
 * Tradeshow hotels  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
public interface AcHotelService extends IService<AcHotel> {

    PageResult<AcHotel> pageHotelList(String searchHotelName, Integer pageNum, Integer pageSize);

    Boolean edit(AcHotelDTO acHotel);

    HotelCtaDTO findCtaOne(Integer hotelId, Integer phase);

    Boolean editCta(HotelCtaDTO hotelCtaDTO, Long userId);
}
