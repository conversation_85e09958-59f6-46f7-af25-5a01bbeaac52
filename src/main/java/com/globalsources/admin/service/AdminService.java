package com.globalsources.admin.service;

import com.globalsources.admin.model.dto.AcResetPwdDTO;
import com.globalsources.admin.model.dto.UserAdminConsoleDTO;
import com.globalsources.admin.model.pojo.AcUser;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.user.api.dto.UserLoginDTO;
import com.globalsources.user.api.vo.UserAuthorizationVO;

import javax.servlet.http.HttpServletResponse;

public interface AdminService {
    /**
     * 用户登录
     *
     * @param userLoginDTO
     * @param response
     * @return
     */
    UserAuthorizationVO login(UserLoginDTO userLoginDTO, HttpServletResponse response);

    /**
     * 制作密码密文
     * @param email 账号
     * @param password 密码
     * @return
     */
    String makeCipherPassword(String email, String password);

    /**
     * 查找admin用户
     * @param email
     * @return
     */
    AcUser findAcUser(String email);

    /**
     * 注册admin用户
     * @param userId 用户id
     * @param parentId 创建人id
     * @param userDTO
     * @return
     */
    boolean registerUserAdmin(Long userId, Long parentId, UserAdminConsoleDTO userDTO);

    /**
     * 登出
     * @param token
     */
    void logout(Long userId,String token);

    /**
     * 修改密码
     * @param user
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return
     */
    Result<Void> changePassword(UserVO user, String oldPassword, String newPassword);

    /**
     * 通过email查询用户
     * @param email
     * @return
     */
    AcUser getAcUserByEmail(String email);

    /**
     * 发送忘记密码邮件
     * @param user
     * @return
     */
    Result<Void> sendForgetPwdEmail(AcUser user);

    /**
     * 检查重置密码token
     * @param token
     * @return
     */
    Result<Void> checkResetPwdToken(String token);

    /**
     * 重置密码
     * @param dto
     * @return
     */
    Result<Void> resetPwd(AcResetPwdDTO dto);
}
