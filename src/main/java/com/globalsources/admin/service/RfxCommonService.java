package com.globalsources.admin.service;

import com.globalsources.admin.model.vo.RfxBuyerInfoVO;
import com.globalsources.admin.model.vo.RfxBuyerTradeshowInfoVO;
import com.globalsources.admin.model.vo.inquiry.RfiConvertRfqDataViewVO;
import com.globalsources.framework.result.Result;
import com.globalsources.product.agg.api.vo.ProductLiteVO;
import com.globalsources.rfi.agg.request.admin.AdminInquiryQueryDTO;
import com.globalsources.supplierconsole.agg.api.supplier.dto.tradeshow.TradeshowSimpleAggDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-07-12
 */
public interface RfxCommonService {

    RfxBuyerInfoVO buyerDetailInfo(Long buyerId);

    RfxBuyerInfoVO buyerDetailInfoByEmail(String email);

    Result<ProductLiteVO> productInfo(Long productId);

    RfxBuyerTradeshowInfoVO buyerTradeshowInfo(String param);

    Result<RfxBuyerInfoVO> buyerDetailInfoByParam(String param);

    List<TradeshowSimpleAggDTO> selectTradeShowInfo(String keyword, String sortType, String tsType);

    List<RfiConvertRfqDataViewVO> getRfiConvertRfqCountList(AdminInquiryQueryDTO dto);
}
