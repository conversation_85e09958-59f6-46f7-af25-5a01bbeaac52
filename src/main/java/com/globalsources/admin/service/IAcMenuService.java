package com.globalsources.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.admin.model.dto.route.RouterBaseDTO;
import com.globalsources.admin.model.pojo.AcMenu;
import com.globalsources.admin.model.vo.AcMenuTreeBaseVO;
import com.globalsources.framework.vo.UserVO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-28
 */
public interface IAcMenuService extends IService<AcMenu> {

    boolean importMenu(AcMenu acMenu);

    boolean updateMenu(AcMenu acMenu);

    List<AcMenu> getAllMenuInfo();

    List<AcMenuTreeBaseVO> acMenuTree(UserVO userVO);

    List<RouterBaseDTO> acUserMenuTree(Long userId);
}
