package com.globalsources.admin.service;

import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.ts.api.model.dto.desktop.PastConferenceVideoDTO;
import com.globalsources.ts.api.model.vo.desktop.PastConferenceVideoVO;

public interface SeminarConferenceService {
    Result<PageResult<PastConferenceVideoVO>> getConferenceVideoList(Long pageNum, Long pageSize);

    Result<PastConferenceVideoVO> getLatestModifyConferenceVideo();

    Result<Void> modifySortNumOfConferenceVideo(Long currentId, String direction);

    Result<Void> removeConferenceVideo(Long userId,Long spcId);

    Result<PastConferenceVideoVO> getConferenceVideo(Long spcId);

    Result<Void> editConferenceVideo(Long userId, PastConferenceVideoDTO dto);

    Result<Void> addConferenceVideo(Long userId, PastConferenceVideoDTO dto);
}
