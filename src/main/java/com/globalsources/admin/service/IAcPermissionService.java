package com.globalsources.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.admin.model.pojo.AcPermission;
import com.globalsources.admin.model.pojo.AcUser;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-28
 */
public interface IAcPermissionService extends IService<AcPermission> {

    boolean importPermission(AcPermission acPermission);

    boolean updatePermission(AcPermission acPermission);

    List<AcPermission> getAllPermissionInfo();

    List<AcPermission> getPermissionInfoByKey(List<String> permissionKey);

    List<AcPermission> selectAcPermissionListByUserId(AcUser userVO);
}
