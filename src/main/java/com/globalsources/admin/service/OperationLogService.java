package com.globalsources.admin.service;

import com.globalsources.supplierconsole.agg.api.constant.EntityType;
import com.globalsources.supplierconsole.agg.api.constant.OperationEnum;

import javax.servlet.http.HttpServletRequest;

public interface OperationLogService {
    void saveOperationLog(EntityType entityType, OperationEnum operation, String operationDesc, String websiteTypeListStr, String entityId, Long orgId, Long userId, HttpServletRequest request);

}
