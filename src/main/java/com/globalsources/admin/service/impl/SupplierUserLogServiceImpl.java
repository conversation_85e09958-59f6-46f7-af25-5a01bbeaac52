package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.admin.dao.ApiAccessLogMapper;
import com.globalsources.admin.model.dto.supplier.LogSearchDTO;
import com.globalsources.admin.model.pojo.supplier.ApiAccessLog;
import com.globalsources.admin.service.SupplierUserLogService;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Service
public class SupplierUserLogServiceImpl implements SupplierUserLogService {
    @Resource
    private ApiAccessLogMapper apiAccessLogMapper;

    @Override
    public PageResult<ApiAccessLog> search(Long pageNum, Long pageSize, LogSearchDTO dto) {
        Date latestDate= DateUtil.addDay(new Date(),-1);
        Date queryStratDate = null;
        Date queryEndDate;

        //数据只能查询昨天的数据
        if(dto.getStartDate()!=null && dto.getEndDate()!=null){
            //超过最大时间直接返回
            if(dto.getStartDate().getTime()>latestDate.getTime()){
                return PageResult.init(pageNum,pageSize);
            }
            queryEndDate=dto.getEndDate();
            if(queryEndDate.getTime()>latestDate.getTime()){
                queryEndDate=latestDate;
            }
            queryStratDate = dto.getStartDate();
        }else{
            //最大查询时间限制
            queryEndDate = latestDate;

        }

        String email = dto.getEmail();
        if (Objects.nonNull(email)) {
            email = email.trim().toLowerCase();
        }

        Page<ApiAccessLog> page= apiAccessLogMapper.searchApiAccessLog(new Page<>(pageNum,pageSize), email, dto.getSupplierId(), queryStratDate, queryEndDate,dto.getSort());

        return PageResult.restPage(page);
    }
}
