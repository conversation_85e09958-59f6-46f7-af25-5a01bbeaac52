package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.dao.AcJobQueueMapper;
import com.globalsources.admin.model.pojo.AcJobQueue;
import com.globalsources.admin.service.AcJobQueueService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Service
public class AcJobQueueServiceImpl extends ServiceImpl<AcJobQueueMapper, AcJobQueue> implements AcJobQueueService {

    @Override
    public List<AcJobQueue> getListByTypeAndStatus(String type, String status) {
        return baseMapper.selectListByTypeAndStatus(type, status);
    }

    @Override
    public Long getIdByIdAndStatus(Long id, String status) {
        return baseMapper.selectIdByIdAndStatus(id, status);
    }

    @Override
    public AcJobQueue getEntityById(Long id) {
        return this.baseMapper.selectById(id);
    }

}
