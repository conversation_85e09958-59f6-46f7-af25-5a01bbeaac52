package com.globalsources.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.admin.service.DictService;
import com.globalsources.common.api.feign.CommonAggFeign;
import com.globalsources.common.api.vo.DictionaryItemVO;
import com.globalsources.common.api.vo.QueryDictListRequestVO;
import com.globalsources.common.api.vo.QueryDictListResultVO;
import com.globalsources.framework.enums.DicGroupNameEnum;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.LangUtil;
import com.globalsources.user.api.vo.CountryVO;
import com.globalsources.user.api.vo.LocaleCountryListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class DictServiceImpl implements DictService {

    @Autowired
    private CommonAggFeign dictFeign;

    @Value("${instantmsg.EU.countrycode}")
    private String euCountryCode;

    @Value("${website.hot.country:}")
    private String hotCountryCodeStr;

    @Override
    public Result<LocaleCountryListVO> listCountry(HttpServletRequest request) {
        LocaleCountryListVO listVO = new LocaleCountryListVO();
        LanguageDicEnum lang= LangUtil.getLanguage(request);
        Map<String, String> countryDialCodeMap = convertDicMap(DicGroupNameEnum.COUNTRY_DIAL_CODE.getValue(), lang.getValue());
        String euCountryStr = euCountryCode+",";

        //热门国家
        List<String> hotCountryCodes= Arrays.asList(hotCountryCodeStr.split(","));

        // 获取国家列表
        List<CountryVO> countryVOS = new ArrayList<>();
        List<CountryVO> hotCountryList= new ArrayList<>();

        //查询字典
        QueryDictListRequestVO dictDto=new QueryDictListRequestVO();
        dictDto.setGroupNameArr(Arrays.asList(DicGroupNameEnum.COUNTRY.getValue()));
        dictDto.setLanguage(lang.getValue());
        Result<QueryDictListResultVO> resp = dictFeign.queryDictList(dictDto);
        if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
            log.error("query country dict info return error, lang:{},resp:{}",lang,resp);
            return Result.error();
        }

        QueryDictListResultVO data = resp.getData();
        if(data==null || data.getDictMap()==null || !data.getDictMap().containsKey(DicGroupNameEnum.COUNTRY.getValue())){
            log.error("query country dict info return empty, lang:{},resp:{}",lang,resp);
            return Result.success();
        }

        List<DictionaryItemVO> sysI18nItems = data.getDictMap().get(DicGroupNameEnum.COUNTRY.getValue());
        Map<String, String> countryMap = new HashMap<>();
        sysI18nItems.forEach(sysI18nItem -> countryMap.put(sysI18nItem.getI18nKey(), sysI18nItem.getI18nValue()));

        for(DictionaryItemVO sysI18nItem:sysI18nItems) {

            String countryName = countryMap.get(sysI18nItem.getI18nKey());
            String telCountryCode = countryDialCodeMap.get(sysI18nItem.getI18nKey());
            CountryVO countryVO = new CountryVO();
            countryVO.setCountryId(sysI18nItem.getId());
            countryVO.setCountryCode(sysI18nItem.getI18nKey());
            countryVO.setCountryName(countryName);
            countryVO.setTelCountryCode(telCountryCode);
            countryVO.setEuFlag(euCountryStr.contains(countryVO.getCountryCode() + ","));

            countryVOS.add(countryVO);

            //热门国家列表
            if (CollectionUtils.isNotEmpty(hotCountryCodes) && hotCountryCodes.contains(countryVO.getCountryCode())) {
                hotCountryList.add(countryVO);
            }
        }

        listVO.setList(countryVOS);
        listVO.setHots(hotCountryList);

        //默认中国
        String countryCode = "CN";
        CountryVO localeCountry = new CountryVO(18088L, "China", countryCode,"86",false);
        listVO.setLocale(localeCountry);

        return Result.success(listVO);
    }

    public Map<String, String> convertDicMap(String groupName, String lang){
        Map<String, String> map = new HashMap<>();
        Result<String> queryI18n = dictFeign.queryI18n(groupName, lang);
        String data = queryI18n.getData();
        List<DictionaryItemVO> sysI18nItems = JSON.parseArray(data, DictionaryItemVO.class);
        sysI18nItems.forEach(sysI18nItem -> map.put(sysI18nItem.getI18nKey(), sysI18nItem.getI18nValue()));
        return map;
    }


}
