package com.globalsources.admin.service.impl;

import com.globalsources.activity.agg.api.dto.LiveSupplierPlayNodeDTO;
import com.globalsources.activity.agg.api.feign.SupplierPlayNodeFeign;
import com.globalsources.activity.agg.api.vo.LiveSupplierPlayNodeVO;
import com.globalsources.admin.service.LiveSupplierPlayNodeService;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class LiveSupplierPlayNodeServiceImpl implements LiveSupplierPlayNodeService {
    @Resource
    private SupplierPlayNodeFeign supplierPlayNodeFeign;

    private long getSecond(long microsecond){
        return (long)Math.ceil((float)microsecond/1000);
    }

    @Override
    public Result<List<LiveSupplierPlayNodeVO>> queryReplayNode(Long replayId) {
        Result<List<LiveSupplierPlayNodeVO>> resp=supplierPlayNodeFeign.queryReplayNodeList(replayId);
        if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
            log.error("query supplier play node list return error, replayId:{},resp:{}",replayId,resp);
            return Result.error();
        }

        if(CollectionUtils.isEmpty(resp.getData())){
           return resp;
        }

        //将毫秒转成秒
        resp.getData().forEach(e->{
            if(e.getStartTime()!=null) {
                e.setStartTime(getSecond(e.getStartTime()));
            }
            if(e.getEndTime()!=null) {
                e.setEndTime(getSecond(e.getEndTime()));
            }
        });

        return resp;
    }

    @Override
    public Result<LiveSupplierPlayNodeVO> getPlayNodeInfo(String nodeId) {
        Result<LiveSupplierPlayNodeVO> resp=supplierPlayNodeFeign.getPlayNodeInfo(nodeId);
        if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
            log.error("get supplier play node info return error, nodeId:{},resp:{}",nodeId,resp);
            return Result.error();
        }

        if(resp.getData()==null){
            return resp;
        }

        //将毫秒转成秒
        LiveSupplierPlayNodeVO data=resp.getData();
        data.setStartTime(getSecond(data.getStartTime()));
        data.setEndTime(getSecond(data.getEndTime()));

        return resp;
    }

    @Override
    public Result<Void> editPlayNode(LiveSupplierPlayNodeDTO dto) {
        //将秒转成毫秒
        dto.setStartTime(dto.getStartTime()*1000);
        dto.setEndTime(dto.getEndTime()*1000);

        return supplierPlayNodeFeign.editPlayNode(dto);
    }

    @Override
    public Result<Void> removePlayNode(String nodeId) {
        return supplierPlayNodeFeign.removePlayNode(nodeId);
    }
}
