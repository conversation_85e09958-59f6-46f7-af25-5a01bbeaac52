package com.globalsources.admin.service.impl;

import com.globalsources.admin.model.dto.TradeshowSearchDTO;
import com.globalsources.admin.model.dto.TradeshowUpdateDTO;
import com.globalsources.admin.model.vo.TradeshowPageResultVO;
import com.globalsources.admin.model.vo.TradeshowVO;
import com.globalsources.admin.service.TradeshowService;
import com.globalsources.agg.supplier.api.feign.TradeshowAdminConsoleAggFeign;
import com.globalsources.agg.supplier.api.model.dto.tradeshow.TradeshowAdminConsoleAggDTO;
import com.globalsources.agg.supplier.api.model.dto.tradeshow.TradeshowAggSearchDTO;
import com.globalsources.agg.supplier.api.model.dto.tradeshow.TradeshowAggUpdateDTO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


/**
 * <AUTHOR> Chen
 * @since 2021-08-12
 */
@Slf4j
@Service
public class TradeshowServiceImpl implements TradeshowService {

    @Autowired
    private TradeshowAdminConsoleAggFeign tradeshowAdminConsoleAggFeign;

    @Override
    public TradeshowPageResultVO getTradeshowList(Integer pageNum, Integer pageSize, TradeshowSearchDTO tradeshowSearchDTO) {
        TradeshowAggSearchDTO tradeshowAggSearchDTO = new TradeshowAggSearchDTO();
        if (tradeshowSearchDTO != null) {
            BeanUtils.copyProperties(tradeshowSearchDTO, tradeshowAggSearchDTO);
        }

        TradeshowPageResultVO tradeshowPageResultVO = new TradeshowPageResultVO();

        // set last update user email and last update date
        PageResult<TradeshowAdminConsoleAggDTO> latestTradeshowAggVOPageResult = ResultUtil.getData(tradeshowAdminConsoleAggFeign.search(1, 1, new TradeshowAggSearchDTO()));
        if (!CollectionUtils.isEmpty(latestTradeshowAggVOPageResult.getList())) {
            TradeshowAdminConsoleAggDTO latestTradeshowAggVO = latestTradeshowAggVOPageResult.getList().get(0);
            tradeshowPageResultVO.setLastUpdateUserEmail(latestTradeshowAggVO.getLastUpdateByEmail());
            tradeshowPageResultVO.setLastUpdateDate(latestTradeshowAggVO.getLastUpdateDate());
        }

        PageResult<TradeshowAdminConsoleAggDTO> tradeshowAggVOPageResult = ResultUtil.getData(tradeshowAdminConsoleAggFeign.search(pageNum, pageSize, tradeshowAggSearchDTO));
        PageResult<TradeshowVO> tradeshowVOPageResult = new PageResult<>();
        BeanUtils.copyProperties(tradeshowAggVOPageResult, tradeshowVOPageResult);
        tradeshowVOPageResult.setList(OrikaMapperUtil.coverList(tradeshowAggVOPageResult.getList(), TradeshowVO.class));

        tradeshowPageResultVO.setTradeshowVOPageResult(tradeshowVOPageResult);

        return tradeshowPageResultVO;
    }

    @Override
    public void update(TradeshowUpdateDTO tradeshowUpdateDTO, UserVO userVO) {
        TradeshowAggUpdateDTO tradeshowAggUpdateDTO = new TradeshowAggUpdateDTO();
        BeanUtils.copyProperties(tradeshowUpdateDTO, tradeshowAggUpdateDTO);
        tradeshowAggUpdateDTO.setLastUpdateBy(userVO.getUserId());

        tradeshowAdminConsoleAggFeign.update(tradeshowAggUpdateDTO);
    }

}
