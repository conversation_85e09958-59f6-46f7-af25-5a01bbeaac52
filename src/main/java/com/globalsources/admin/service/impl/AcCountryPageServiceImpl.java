package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.constants.DictionaryCode;
import com.globalsources.admin.dao.AcCpCountryMapper;
import com.globalsources.admin.dao.AcCpRecomSupplierMapper;
import com.globalsources.admin.model.dto.*;
import com.globalsources.admin.model.pojo.AcCpCountry;
import com.globalsources.admin.model.pojo.AcCpRecomSupplier;
import com.globalsources.admin.model.pojo.AcUser;
import com.globalsources.admin.model.vo.AcCpCountryVO;
import com.globalsources.admin.model.vo.AcCpRecSupplierVO;
import com.globalsources.admin.service.AcCountryPageService;
import com.globalsources.admin.service.IAcUserService;
import com.globalsources.admin.service.SupplierService;
import com.globalsources.common.api.feign.CommonAggFeign;
import com.globalsources.common.api.vo.DictionaryItemVO;
import com.globalsources.common.api.vo.QueryDictListRequestVO;
import com.globalsources.common.api.vo.QueryDictListResultVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Chen
 * @date 2021/8/23 18:44
 */
@Slf4j
@Service
public class AcCountryPageServiceImpl extends ServiceImpl<AcCpCountryMapper, AcCpCountry> implements AcCountryPageService {

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private CommonAggFeign commonAggFeign;

    @Autowired
    private IAcUserService userService;

    @Autowired
    private AcCpRecomSupplierMapper acCpRecomSupplierMapper;

    @Override
    public PageResult<AcCpCountryVO> getCpCountryList(Integer pageNum, Integer pageSize) {
        PageResult<AcCpCountryVO> pageResult = null;

        pageNum = Optional.ofNullable(pageNum).orElse(BigInteger.ONE.intValue());
        pageSize = Optional.ofNullable(pageSize).orElse(BigInteger.ONE.intValue());
        Page<AcCpCountry> page = new Page<>(pageNum, pageSize);
        Page<AcCpCountry> acCpCountryPage = this.baseMapper.selectPage(page, Wrappers.lambdaQuery(AcCpCountry.class).orderByDesc(AcCpCountry::getLUpdBy));
        List<AcCpCountryDTO> list = OrikaMapperUtil.coverList(acCpCountryPage.getRecords(), AcCpCountryDTO.class);
        PageResult<AcCpCountryDTO> data = PageResult.restPage(list, acCpCountryPage);

        if (Objects.nonNull(data)) {
            PageResult<AcCpCountryAggDTO> result = new PageResult<>();
            List<AcCpCountryAggDTO> resultList = OrikaMapperUtil.coverList(Optional.ofNullable(data.getList()).orElse(Lists.newArrayList()), AcCpCountryAggDTO.class);
            for (AcCpCountryAggDTO dto : resultList) {
                String countryCode = dto.getCountryCode();
                String country = getCountryValueByCode(countryCode);
                dto.setCountry(country);
                AcUser user = userService.getUserById(dto.getLUpdBy());
                String email = Optional.ofNullable(user).map(AcUser::getEmail).orElse(StringUtils.EMPTY);
                dto.setUpdateUserEmail(email);
            }
            BeanUtils.copyProperties(data, result, "list");
            result.setList(resultList);

            pageResult = new PageResult<>();
            BeanUtils.copyProperties(data, pageResult, "list");
            pageResult.setList(OrikaMapperUtil.coverList(result.getList(), AcCpCountryVO.class));

        }
        return pageResult;
    }

    public String getCountryValueByCode(String countryCode) {
        if (StringUtils.isEmpty(countryCode)) {
            return StringUtils.EMPTY;
        }
        QueryDictListRequestVO queryDictListRequestVO = new QueryDictListRequestVO();
        queryDictListRequestVO.setGroupNameArr(ImmutableList.of(DictionaryCode.COUNTRY));
        queryDictListRequestVO.setLanguage(Locale.ENGLISH.getLanguage());
        Result<QueryDictListResultVO> queryDictListResultVoResult = commonAggFeign.queryDictList(queryDictListRequestVO);
        String country = StringUtils.EMPTY;

        try {
            QueryDictListResultVO data = ResultUtil.getData(queryDictListResultVoResult, "failed to queryDictList, dto:" + queryDictListRequestVO);
            HashMap<String, List<DictionaryItemVO>> dictMap = Optional.ofNullable(data.getDictMap()).orElse(Maps.newHashMap());
            List<DictionaryItemVO> dictionaryItemVoList = dictMap.get(DictionaryCode.COUNTRY);
            if (CollectionUtils.isNotEmpty(dictionaryItemVoList)) {
                country = dictionaryItemVoList.stream().filter(dto -> Objects.nonNull(dto) && dto.getI18nKey().equals(countryCode)).findFirst().map(DictionaryItemVO::getI18nValue).orElse(StringUtils.EMPTY);
            }
        } catch (Exception e) {
            log.warn("failed to getCountryValueByCode, countryCode:" + countryCode + ", result:" + queryDictListResultVoResult);
        }
        return country;
    }

    @Override
    public List<AcCpRecSupplierVO> getCpRecSupplierListByCountryId(Integer cpCountryId) {
        List<AcCpRecSupplierVO> result = new ArrayList<>();
        if (Objects.nonNull(cpCountryId)) {
            List<AcCpRecomSupplier> acCpRecomSuppliers = acCpRecomSupplierMapper.selectList(Wrappers.lambdaQuery(AcCpRecomSupplier.class).eq(AcCpRecomSupplier::getCpCountryId, cpCountryId).orderByAsc(AcCpRecomSupplier::getDisplayPos));
            result = OrikaMapperUtil.coverList(acCpRecomSuppliers, AcCpRecSupplierVO.class);
        }
        return result;
    }

    @Override
    public List<AcCpRecSupplierVO> saveCpRecSupplierList(AcCpRecSupplierSaveBffDTO dto, UserVO userInfo) {
        List<AcCpRecSupplierVO> result = new ArrayList<>();
        Long userId = userInfo.getUserId();
        if (Objects.nonNull(dto)) {
            AcCpRecSupplierSaveDTO saveDto = OrikaMapperUtil.coverObject(dto, AcCpRecSupplierSaveDTO.class);
            List<Long> supplierIds = Optional.ofNullable(saveDto.getSupplierIds()).orElse(Lists.newArrayList())
                    .stream().filter(Objects::nonNull).distinct().limit(10).collect(Collectors.toList());
            supplierIds.forEach(id -> supplierService.checkOnlineSupplier(id));
            saveDto.setSupplierIds(supplierIds);

            List<AcCpRecomSupplier> resultList = new ArrayList<>();
            AcCpCountry country = this.baseMapper.selectById(saveDto.getCpCountryId());
            if (Objects.isNull(saveDto.getCpCountryId()) || Objects.isNull(country)) {
                log.info("saveRecSupplierOfCountry fail, cpCountryId is null or not exist, cpCountryId:{}", saveDto.getCpCountryId());
                return result;
            }
            acCpRecomSupplierMapper.delete(Wrappers.lambdaQuery(AcCpRecomSupplier.class).eq(AcCpRecomSupplier::getCpCountryId, saveDto.getCpCountryId()));

            log.info("saveRecSupplierOfCountry, delete cpRecSupplier by cpCountryId:{}", saveDto.getCpCountryId());

            if (CollectionUtils.isNotEmpty(supplierIds)) {
                int i = 0;
                for (Long supplierId : supplierIds) {
                    if (Objects.nonNull(supplierId)) {
                        AcCpRecomSupplier acCpRecomSupplier = new AcCpRecomSupplier();
                        acCpRecomSupplier.setSupplierId(supplierId);
                        acCpRecomSupplier.setCpCountryId(saveDto.getCpCountryId());
                        acCpRecomSupplier.setDisplayPos(i);
                        int insert = acCpRecomSupplierMapper.insert(acCpRecomSupplier);
                        if (insert > 0) {
                            resultList.add(acCpRecomSupplier);
                        }
                        i++;
                    }
                }
            }

            Date now = new Date();
            country.setLUpdDate(now);
            country.setLUpdBy(userId);
            this.updateById(country);

            log.info("saveRecSupplierOfCountry success, recSupplierIds:" + supplierIds);
            result = OrikaMapperUtil.coverList(resultList, AcCpRecSupplierVO.class);
        }
        return result;
    }

    @Override
    public List<Long> getRecSupplierDtoListByCpCountryCode(String countryCode) {
        Integer cpCountryId = getCpCountryIdByCountryCode(countryCode);
        List<AcCpRecSupplierDTO> data = getRecSupplierDtoListByCpCountryId(cpCountryId);
        return Optional.ofNullable(data).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).map(AcCpRecSupplierDTO::getSupplierId).collect(Collectors.toList());
    }

    public Integer getCpCountryIdByCountryCode(String countryCode) {
        Integer cpCountryId = null;
        if (StringUtils.isNotEmpty(countryCode)) {
            Page<AcCpCountry> page = new Page<>(BigInteger.ONE.intValue(), BigInteger.ONE.intValue());
            Page<AcCpCountry> acCpCountryPage = this.baseMapper.selectPage(page, Wrappers.lambdaQuery(AcCpCountry.class).eq(AcCpCountry::getCountryCode, countryCode).orderByDesc(AcCpCountry::getLUpdBy));
            cpCountryId = Optional.ofNullable(acCpCountryPage)
                    .map(Page::getRecords)
                    .map(list -> CollectionUtils.isNotEmpty(list) ? list.get(0) : null)
                    .map(AcCpCountry::getCpCountryId)
                    .orElse(null);
        }
        return cpCountryId;
    }

    public List<AcCpRecSupplierDTO> getRecSupplierDtoListByCpCountryId(Integer cpCountryId) {
        List<AcCpRecomSupplier> acCpRecomSuppliers = acCpRecomSupplierMapper.selectList(Wrappers.lambdaQuery(AcCpRecomSupplier.class).eq(AcCpRecomSupplier::getCpCountryId, cpCountryId).orderByAsc(AcCpRecomSupplier::getDisplayPos));
        return OrikaMapperUtil.coverList(acCpRecomSuppliers, AcCpRecSupplierDTO.class);
    }

}
