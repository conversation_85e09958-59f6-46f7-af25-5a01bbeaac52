package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.dao.AppParameterMapper;
import com.globalsources.admin.model.dto.AppParameterDTO;
import com.globalsources.admin.model.pojo.AppParameter;
import com.globalsources.admin.model.vo.AppParameterVO;
import com.globalsources.admin.service.AppParameterService;
import com.globalsources.framework.utils.OrikaMapperUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/8/22
 */
@Service
public class AppParameterServiceImpl extends ServiceImpl<AppParameterMapper, AppParameter> implements AppParameterService {


    @Override
    public AppParameterVO getByParamName(String paramName) {
        LambdaQueryWrapper<AppParameter> queryWrapper = Wrappers.lambdaQuery(AppParameter.class).eq(AppParameter::getParamName, paramName);
        AppParameter appParameter = getOne(queryWrapper, false);
        return Optional.ofNullable(appParameter).map(item -> OrikaMapperUtil.coverObject(item, AppParameterVO.class)).orElse(null);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateByParamName(AppParameterDTO dto) {
        LambdaUpdateWrapper<AppParameter> lambdaUpdateWrapper = new LambdaUpdateWrapper<AppParameter>()
                .set(AppParameter::getParamValue, dto.getParamValue())
                .set(StringUtils.isNotEmpty(dto.getDescription()), AppParameter::getDescription, dto.getDescription())
                .set(AppParameter::getLUpdDate, new Date())
                .eq(AppParameter::getParamName, dto.getParamName());
        update(lambdaUpdateWrapper);
    }

}
