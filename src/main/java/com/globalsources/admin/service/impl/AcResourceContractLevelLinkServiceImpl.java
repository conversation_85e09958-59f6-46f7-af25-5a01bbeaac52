package com.globalsources.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.constants.AdminConstants;
import com.globalsources.admin.dao.AcResourceContractLevelLinkMapper;
import com.globalsources.admin.dao.AcResourceMapper;
import com.globalsources.admin.model.dto.resource.SimpleResourceContractLevelLinkDTO;
import com.globalsources.admin.model.pojo.AcResourceContractLevelLink;
import com.globalsources.admin.service.AcResourceContractLevelLinkService;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.inject.UserRoleRedis;
import com.globalsources.framework.result.ResultCode;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> Chen
 * @since 2022-10-14
 */
@Slf4j
@Service
public class AcResourceContractLevelLinkServiceImpl extends ServiceImpl<AcResourceContractLevelLinkMapper, AcResourceContractLevelLink> implements AcResourceContractLevelLinkService {

    @Resource
    private UserRoleRedis userRoleRedis;
    
    @Resource
    private AcResourceMapper acResourceMapper;

    @Override
    @Transactional
    public List<Long> updateResourceContractLevelLinkAndChildren(Long resourceId, List<String> contractCodeList, Boolean validParent, Long userId, Boolean updateChildren) {
        List<Long> updateResourceIds = Lists.newArrayList();

        contractCodeList = Optional.ofNullable(contractCodeList).map(list -> list.stream()
                .filter(StringUtils::isNotEmpty).distinct()
                .collect(Collectors.toList())).orElse(Lists.newArrayList());
        validContractCodeList(contractCodeList);
        if (Boolean.TRUE.equals(validParent) && CollectionUtils.isNotEmpty(contractCodeList)) {
            List<SimpleResourceContractLevelLinkDTO> parentContractLevelDtoList = getParentSimpleResourceContractLevelLinkListByResourceId(resourceId);
            Set<String> parentContractCodeList = Optional.ofNullable(parentContractLevelDtoList)
                    .map(list -> list.stream().filter(Objects::nonNull).map(SimpleResourceContractLevelLinkDTO::getContractCodeList).flatMap(List::stream).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toSet()))
                    .orElse(Sets.newHashSet());
            if (!parentContractCodeList.containsAll(contractCodeList)) {
                throw new BusinessException(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED.getCode(), "contract code Invalid, " + CollectionUtils.subtract(contractCodeList, parentContractCodeList));
            }
        }
        boolean updateFlag = updateResourceContractCodeLink(resourceId, contractCodeList, userId);
        if (updateFlag) {
            updateResourceIds.add(resourceId);
        }
        if (Boolean.TRUE.equals(updateChildren)) {
            //减少选中的合同等级时，更新子节点
            List<Long> changeChildrenIds = updateChildrenResourceWhenDeselect(resourceId, contractCodeList, userId);
            updateResourceIds.addAll(changeChildrenIds);
        }
        return updateResourceIds;
    }

    @Override
    public List<SimpleResourceContractLevelLinkDTO> getParentSimpleResourceContractLevelLinkListByResourceId(Long resourceId) {
        return this.baseMapper.selectParentResourceContractLinkSimpleDto(resourceId);
    }

    private List<Long> updateChildrenResourceWhenDeselect(Long resourceId, List<String> contractCodelList, Long userId) {
        if (Objects.isNull(contractCodelList)) {
            contractCodelList = Lists.newArrayList();
        }
        List<Long> updateResourceIds = Lists.newArrayList();
        //所有子节点的合同等级信息
        List<SimpleResourceContractLevelLinkDTO> childrenLinkDtoList = this.baseMapper.selectChildrenResourceContractLinkSimpleDto(resourceId);
        if (CollectionUtils.isNotEmpty(childrenLinkDtoList)) {
            Set<String> contractCodeSet = Optional.ofNullable(contractCodelList).map(HashSet::new).orElse(Sets.newHashSet());
            for (SimpleResourceContractLevelLinkDTO dto : childrenLinkDtoList) {
                if (!contractCodeSet.containsAll(dto.getContractCodeList())) {
                    List<String> oldCodeList = dto.getContractCodeList();
                    //resource与当前子节点 contract level 交集。 下级菜单可选合同等级范围不能超过商机菜单已选合同等级。
                    List<String> newCodeList = (List<String>) CollectionUtils.intersection(contractCodelList, oldCodeList);
                    boolean updateFlag = updateResourceContractLevelLink(dto.getResourceId(), newCodeList, userId, oldCodeList);
                    if (updateFlag) {
                        updateResourceIds.add(dto.getResourceId());
                    }
                }
            }
        }
        return updateResourceIds;
    }

    @Override
    @Transactional
    public List<Long> updateAllChildrenContractLevelLink(Long resourceId, List<String> contractCodeList, Long userId) {
        List<Long> resultIds = Lists.newArrayList();
        if (Objects.isNull(resourceId)) {
            return resultIds;
        }
        validContractCodeList(contractCodeList);
        List<Long> childrenResourceIds = acResourceMapper.selectAllChildrenResourceIdByResourceId(resourceId);
        if (CollectionUtils.isNotEmpty(childrenResourceIds)) {
            for (Long childResourceId : childrenResourceIds) {
                boolean updateFlag = deleteAllAndInsertLinks(childResourceId, contractCodeList, userId);
                if (updateFlag) {
                    resultIds.add(childResourceId);
                }
            }
        }
        return resultIds;
    }

    public boolean deleteAllAndInsertLinks(Long resourceId, List<String> contractCodeList, Long userId) {
        if (Objects.isNull(resourceId)) {
            return false;
        }
        boolean result = true;
        deleteByResourceId(resourceId);
        if (CollectionUtils.isNotEmpty(contractCodeList)) {
            Date now = new Date();
            List<AcResourceContractLevelLink> insertBeanList = contractCodeList.stream()
                    .map(code -> {
                AcResourceContractLevelLink bean = new AcResourceContractLevelLink();
                bean.setResourceId(resourceId);
                bean.setContractCode(code);
                bean.setCreateDate(now);
                bean.setLUpdDate(now);
                bean.setLUpdBy(userId);
                return bean;
            }).collect(Collectors.toList());
            result = saveBatch(insertBeanList);
        }
        return result;
    }

    public boolean updateResourceContractCodeLink(Long resourceId, List<String> contractCodeList, Long userId) {
        if (Objects.isNull(resourceId)) {
            return false;
        }
        List<AcResourceContractLevelLink> oldLinks = this.baseMapper.selectList(Wrappers.lambdaQuery(AcResourceContractLevelLink.class)
                .eq(AcResourceContractLevelLink::getResourceId, resourceId)
                .select(AcResourceContractLevelLink::getResourceId, AcResourceContractLevelLink::getContractCode)
        );
        List<String> oldCodeList = Optional.ofNullable(oldLinks).map(list -> list.stream().filter(Objects::nonNull)
                        .map(AcResourceContractLevelLink::getContractCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
        return updateResourceContractLevelLink(resourceId, contractCodeList, userId, oldCodeList);
    }

    private boolean updateResourceContractLevelLink(Long resourceId, List<String> contractCodelList, Long userId, List<String> oldCodeList) {
        if (Objects.isNull(resourceId)) {
            return false;
        }
        boolean updateFlag = false;
        if (CollectionUtils.isEmpty(contractCodelList)) {
            if (CollectionUtils.isNotEmpty(oldCodeList)) {
                int deleteNum = deleteByResourceId(resourceId);
                updateFlag = deleteNum > 0;
            }
        } else {
            //差集
            List<String> deleteCodeList = (List<String>) CollectionUtils.subtract(oldCodeList, contractCodelList);
            List<String> insertCodeList = (List<String>) CollectionUtils.subtract(contractCodelList, oldCodeList);
            if (CollectionUtils.isNotEmpty(deleteCodeList)) {
                deleteByResourIdAndCodeList(resourceId, deleteCodeList);
                updateFlag = true;
            }
            if (CollectionUtils.isNotEmpty(insertCodeList)) {
                Date now = new Date();
                List<AcResourceContractLevelLink> insertBeanList = insertCodeList.stream().map(code -> {
                    AcResourceContractLevelLink bean = new AcResourceContractLevelLink();
                    bean.setResourceId(resourceId);
                    bean.setContractCode(code);
                    bean.setCreateDate(now);
                    bean.setLUpdDate(now);
                    bean.setLUpdBy(userId);
                    return bean;
                }).collect(Collectors.toList());
                saveBatch(insertBeanList);
                updateFlag = true;
            }
        }
        if (updateFlag) {
            log.info("updateResourceContractLevelLink, resourceId: {}, levelList: {}, userId: {}", resourceId, contractCodelList, userId);
        }
        return updateFlag;
    }

    private int deleteByResourceId(Long resourceId) {
        return this.baseMapper.delete(Wrappers.lambdaQuery(AcResourceContractLevelLink.class).eq(AcResourceContractLevelLink::getResourceId, resourceId));
    }

    private int deleteByResourIdAndCodeList(Long resourceId, List<String> deleteCodeList) {
        return this.baseMapper.delete(Wrappers.lambdaQuery(AcResourceContractLevelLink.class)
                .eq(AcResourceContractLevelLink::getResourceId, resourceId)
                .in(AcResourceContractLevelLink::getContractCode, deleteCodeList));
    }

    @Override
    public Long deleteContractCodeListByResourceKey(String appName, List<String> resourceKeys) {
        log.info("deleteRolesByResourceKey, appName: {}, resourceKeys: {}", appName, resourceKeys);
        return userRoleRedis.deleteContractCodeListByResourceKey(appName, resourceKeys);
    }

    @Override
    public void refreshResourceContractLevelLinkCache(String appName) {
        refreshResourceContractLevelLinkCache(appName, null, true);
    }

    @Override
    public void refreshResourceContractLevelLinkCache(String appName, List<Long> resourceIds, Boolean clearFlag) {
        //计划用于接口权限校验。由于目前动态菜单-合同配置 只做到一二级菜单，只控制入口显示。这里的缓存还没用上
        if (!AdminConstants.PERMISSION_APP_NAME_SET.contains(appName)) {
            throw new BusinessException(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED.getCode(), "appName Invalid");
        }
        if (Boolean.TRUE.equals(clearFlag)) {
            resourceIds = null;
        }
        List<SimpleResourceContractLevelLinkDTO> acResourceLinkList = getSimpleResourceContractLevelLinkDtoListByAppName(appName, resourceIds);
        Map<String, List<String>> resourceContractLevelLinkMap = Optional.ofNullable(acResourceLinkList).orElse(Lists.newArrayList())
                .stream().filter(Objects::nonNull)
                .filter(dto -> StringUtils.isNotEmpty(userRoleRedis.getResourceKey(dto.getReqUri(), dto.getResourceCode())))
                .collect(Collectors.toMap(dto -> userRoleRedis.getResourceKey(dto.getReqUri(), dto.getResourceCode()), dto -> Optional.ofNullable(dto.getContractCodeList()).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).collect(Collectors.toList()), (oldValue, newValue) -> oldValue));
        if (Boolean.TRUE.equals(clearFlag)) {
            log.info("refreshResourceContractLevelLinkCache clear cache, appName: {}", appName);
            userRoleRedis.clearContractCodeByResourceKeyMap(appName);
        }
        userRoleRedis.setContractCodeListByResourceKeyBatch(appName, resourceContractLevelLinkMap);
        log.info("refreshResourceContractLevelLinkCache, appName: {}, clearFlag: {}, resourceIds:{}, setContractLevelListByResourceKeyBatch, resourceContractLevelLinkMap: {}",
                appName, clearFlag, resourceIds, JSON.toJSONString(resourceContractLevelLinkMap));
    }

    @Override
    public List<SimpleResourceContractLevelLinkDTO> getSimpleResourceContractLevelLinkDtoListByAppName(String appName, List<Long> resourceIds) {
        List<SimpleResourceContractLevelLinkDTO> links = this.baseMapper.selectResourceContractLinkSimpleDtoByAppName(resourceIds, appName);
        return Optional.ofNullable(links).orElse(Lists.newArrayList());
    }

    @Override
    public SimpleResourceContractLevelLinkDTO getSimpleResourceContractLevelLinkDtoById(Long resourceId) {
        return this.baseMapper.selectResourceContractLinkSimpleDtoById(resourceId);
    }

    public void validContractCode(String contractCode) {
        if (!AdminConstants.RESOURCE_CONTRACT_CODE_SET.contains(contractCode)) {
            log.warn("failed to validContractCode, code: {}", contractCode);
            throw new BusinessException(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED.getCode(), "contract code error");
        }
    }



    @Override
    public void validContractCodeList(List<String> contractCodeList) {
        if (CollectionUtils.isNotEmpty(contractCodeList)) {
            for (String code : contractCodeList) {
                validContractCode(code);
            }
        }
    }


}
