package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.dao.RegionalOnlineShowSupplierMapper;
import com.globalsources.admin.model.pojo.RegionalOnlineShowSupplier;
import com.globalsources.admin.service.RegionalOnlineShowSupplierService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-29
 */
@Service
public class RegionalOnlineShowSupplierServiceImpl extends ServiceImpl<RegionalOnlineShowSupplierMapper, RegionalOnlineShowSupplier> implements RegionalOnlineShowSupplierService {


    @Override
    public List<Long> getSupplierListByRegion(String region) {
        if (StringUtils.isEmpty(region)) {
            return Lists.newArrayList();
        }
        List<RegionalOnlineShowSupplier> regionalOnlineShowSuppliers = this.baseMapper.selectList(Wrappers.lambdaQuery(RegionalOnlineShowSupplier.class)
                .eq(RegionalOnlineShowSupplier::getRegion, region)
        );
        return Optional.ofNullable(regionalOnlineShowSuppliers).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull)
                .map(RegionalOnlineShowSupplier::getSupplierId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

}
