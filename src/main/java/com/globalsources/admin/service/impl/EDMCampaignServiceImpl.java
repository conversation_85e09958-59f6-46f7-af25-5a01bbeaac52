package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.dao.EDMCampaignMapper;
import com.globalsources.admin.enums.EDMCampaignTypeEnum;
import com.globalsources.admin.enums.EDMDisplayStyleEnum;
import com.globalsources.admin.enums.EDMSourceTypeEnum;
import com.globalsources.admin.model.dto.EDMCampaignDTO;
import com.globalsources.admin.model.dto.EDMEnumDTO;
import com.globalsources.admin.model.dto.EDMEnumListDTO;
import com.globalsources.admin.model.pojo.EDMCampaignPO;
import com.globalsources.admin.model.pojo.EDMCampaignProductPO;
import com.globalsources.admin.model.vo.EDMCampaignVO;
import com.globalsources.admin.service.EDMCampaignService;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.user.api.feign.UserQueryFeign;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.opencsv.bean.CsvToBeanBuilder;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * <AUTHOR> Chen
 * @since 2021-08-12
 */
@Slf4j
@Service
public class EDMCampaignServiceImpl extends ServiceImpl<EDMCampaignMapper, EDMCampaignPO> implements EDMCampaignService {

    @Autowired
    private UserQueryFeign userQueryFeign;

    @Autowired
    private FreeMarkerConfigurer freeMarkerConfigurer;

    @Override
    @Transactional
    public Long create(EDMCampaignDTO edmCampaignDTO, UserVO userVO) {
        EDMCampaignPO edmCampaignPO = OrikaMapperUtil.coverObject(edmCampaignDTO, EDMCampaignPO.class);
        if (EDMCampaignTypeEnum.CUSTOM.getCode().equals(edmCampaignDTO.getCampaignType())) {
            edmCampaignPO.setFileName(getUploadFileName(edmCampaignDTO.getCsvFile()));
        }
        edmCampaignPO.setCreateBy(userVO.getUserId());
        edmCampaignPO.setLUpdBy(userVO.getUserId());
        edmCampaignPO.setLUpdDate(new Date());
        baseMapper.insert(edmCampaignPO);

        Long campaignId = edmCampaignPO.getCampaignId();
        if (EDMCampaignTypeEnum.CUSTOM.getCode().equals(edmCampaignDTO.getCampaignType())) {
            processCustomDataFile(edmCampaignDTO.getCsvFile(), campaignId, userVO);
        }

        return campaignId;
    }

    private String getUploadFileName(MultipartFile csvFile) {
        String fileName = "";
        if (Objects.nonNull(csvFile)) {
            fileName = csvFile.getOriginalFilename();
        }

        return fileName;
    }

    private void processCustomDataFile(MultipartFile csvFile, Long campaignId, UserVO userVO) {
        if (Objects.isNull(csvFile)) {
            throw new BusinessException(ResultCode.UploadResultCode.UPLOAD_FILE_NULL);
        }

        String fileName = csvFile.getOriginalFilename();
        if (Objects.isNull(fileName)) {
            throw new BusinessException(ResultCode.UploadResultCode.UPLOAD_FILE_NAME_NULL);
        }

        if (!fileName.toLowerCase().endsWith(".csv")) {
            throw new BusinessException(ResultCode.UploadResultCode.FORMAT_NOT_SUPPORT);
        }

        List<EDMCampaignProductPO> edmCampaignProductPOList = null;
        try {
            BufferedReader br = new BufferedReader(new InputStreamReader(csvFile.getInputStream(), "GBK"));
            edmCampaignProductPOList = new CsvToBeanBuilder<EDMCampaignProductPO>(br)
                    .withType(EDMCampaignProductPO.class).build().parse();
        } catch (Exception e) {
            throw new BusinessException(ResultCode.CommonResultCode.UPLOAD_FAILED.getCode(), "Please check the upload files before uploading");
        }

        if (CollectionUtils.isEmpty(edmCampaignProductPOList)) {
            throw new BusinessException(ResultCode.CommonResultCode.DATA_NON_EXISTENT.getCode(), "Please check the upload files before uploading");
        }

        batchSaveCampaignProduct(edmCampaignProductPOList, userVO, campaignId);
    }

    @Override
    public EDMCampaignVO getEDMCampaign(Long campaignId) {
        EDMCampaignPO edmCampaignPO = baseMapper.selectById(campaignId);

        EDMCampaignVO edmCampaignVO = OrikaMapperUtil.coverObject(edmCampaignPO, EDMCampaignVO.class);

        Set<Long> userIdSet = Sets.newHashSet();
        if (edmCampaignPO.getCreateBy() != null) {
            userIdSet.add(edmCampaignPO.getCreateBy());
        }
        if (edmCampaignPO.getLUpdBy() != null) {
            userIdSet.add(edmCampaignPO.getLUpdBy());
        }
        Map<Long, UserVO> userInfoMap = this.getUserInfoMap(Lists.newArrayList(userIdSet));

        edmCampaignVO.setCreateByEmail(Optional.ofNullable(userInfoMap.get(edmCampaignVO.getCreateBy())).map(UserVO::getEmail).orElse(""));
        edmCampaignVO.setLUpdByEmail(Optional.ofNullable(userInfoMap.get(edmCampaignVO.getLUpdBy())).map(UserVO::getEmail).orElse(""));

        return edmCampaignVO;
    }

    @Override
    public PageResult<EDMCampaignVO> getEDMCampaignList(Integer pageNum, Integer pageSize) {
        Page<EDMCampaignPO> pageInfo = new Page<>(pageNum, pageSize);
        IPage<EDMCampaignPO> iPage = baseMapper.queryEDMCampaignList(pageInfo);

        // transfer to EDMCampaignVO PageResult
        Page<EDMCampaignVO> edmCampaignVOPage = new Page<>();
        BeanUtils.copyProperties(iPage, edmCampaignVOPage);

        List<EDMCampaignPO> edmCampaignPOList = iPage.getRecords();
        if (!CollectionUtils.isEmpty(edmCampaignPOList)) {
            Set<Long> userIdSet = Sets.newHashSet();
            edmCampaignPOList.forEach(edmCampaignPO -> {
                if (edmCampaignPO.getCreateBy() != null) {
                    userIdSet.add(edmCampaignPO.getCreateBy());
                }
                if (edmCampaignPO.getLUpdBy() != null) {
                    userIdSet.add(edmCampaignPO.getLUpdBy());
                }
            });
            Map<Long, UserVO> userInfoMap = this.getUserInfoMap(Lists.newArrayList(userIdSet));

            List<EDMCampaignVO> edmCampaignVOList = Lists.newArrayList();
            edmCampaignPOList.forEach(edmCampaignPO -> {
                EDMCampaignVO edmCampaignVO = OrikaMapperUtil.coverObject(edmCampaignPO, EDMCampaignVO.class);
                edmCampaignVO.setCampaignTypeDesc(EDMCampaignTypeEnum.getDescription(edmCampaignPO.getCampaignType()));
                edmCampaignVO.setDisplayStyleDesc(EDMDisplayStyleEnum.getDescription(edmCampaignPO.getDisplayStyle()));
                edmCampaignVO.setSourceTypeDesc(EDMSourceTypeEnum.getDescription(edmCampaignPO.getSourceType()));

                edmCampaignVO.setCreateByEmail(Optional.ofNullable(userInfoMap.get(edmCampaignVO.getCreateBy())).map(UserVO::getEmail).orElse(""));
                edmCampaignVO.setLUpdByEmail(Optional.ofNullable(userInfoMap.get(edmCampaignVO.getLUpdBy())).map(UserVO::getEmail).orElse(""));

                edmCampaignVOList.add(edmCampaignVO);
            });
            edmCampaignVOPage.setRecords(edmCampaignVOList);
        }

        return PageResult.restPage(edmCampaignVOPage);
    }

    private Map<Long, UserVO> getUserInfoMap(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyMap();
        }

        try {
            List<UserVO> userVOList = userQueryFeign.findUsersByUserId(userIdList).getData();
            if (!CollectionUtils.isEmpty(userVOList)) {
                return userVOList.stream().collect(Collectors.toMap(UserVO::getUserId, userVO -> userVO));
            }
        } catch (Exception e) {
            log.warn("get User Info error, ", e);
        }

        return Collections.emptyMap();
    }

    @Override
    @Transactional
    public int update(EDMCampaignDTO edmCampaignDTO, UserVO userVO) {
        EDMCampaignPO edmCampaignPO = OrikaMapperUtil.coverObject(edmCampaignDTO, EDMCampaignPO.class);
        edmCampaignPO.setLUpdBy(userVO.getUserId());
        edmCampaignPO.setLUpdDate(new Date());
        if (EDMCampaignTypeEnum.CUSTOM.getCode().equals(edmCampaignDTO.getCampaignType()) && Objects.nonNull(edmCampaignDTO.getCsvFile())) {
            edmCampaignPO.setFileName(getUploadFileName(edmCampaignDTO.getCsvFile()));
        }
        int count = baseMapper.updateById(edmCampaignPO);

        if (count > 0 && EDMCampaignTypeEnum.CUSTOM.getCode().equals(edmCampaignDTO.getCampaignType()) && Objects.nonNull(edmCampaignDTO.getCsvFile())) {
            processCustomDataFile(edmCampaignDTO.getCsvFile(), edmCampaignDTO.getCampaignId(), userVO);
        }

        return count;
    }

    @Override
    public EDMEnumListDTO getAllEDMEnum() {
        EDMEnumListDTO edmEnumListDTO = new EDMEnumListDTO();

        List<EDMEnumDTO> campaignTypeList = Lists.newArrayList();
        for (EDMCampaignTypeEnum edmCampaignTypeEnum : EDMCampaignTypeEnum.values()) {
            EDMEnumDTO enumDTO = new EDMEnumDTO();
            enumDTO.setCode(edmCampaignTypeEnum.getCode());
            enumDTO.setDesc(edmCampaignTypeEnum.getDescription());

            campaignTypeList.add(enumDTO);
        }
        edmEnumListDTO.setCampaignTypeList(campaignTypeList);

        List<EDMEnumDTO> sourceTypeList = Lists.newArrayList();
        for (EDMSourceTypeEnum edmSourceTypeEnum : EDMSourceTypeEnum.values()) {
            EDMEnumDTO enumDTO = new EDMEnumDTO();
            enumDTO.setCode(edmSourceTypeEnum.getCode());
            enumDTO.setDesc(edmSourceTypeEnum.getDescription());

            sourceTypeList.add(enumDTO);
        }
        edmEnumListDTO.setSourceTypeList(sourceTypeList);

        List<EDMEnumDTO> displayStyleList = Lists.newArrayList();
        for (EDMDisplayStyleEnum edmDisplayStyleEnum : EDMDisplayStyleEnum.values()) {
            EDMEnumDTO enumDTO = new EDMEnumDTO();
            enumDTO.setCode(edmDisplayStyleEnum.getCode());
            enumDTO.setDesc(edmDisplayStyleEnum.getDescription());

            displayStyleList.add(enumDTO);
        }
        edmEnumListDTO.setDisplayStyleList(displayStyleList);

        return edmEnumListDTO;
    }

    @Value("${gsol.edm.init.url}")
    private String initUrl;
    @Value("${gsol.edm.image.url}")
    private String imageUrl;
    @Value("${gsol.edm.name.url}")
    private String nameUrl;
    @Value("${gsol.edm.detail.url}")
    private String detailUrl;
    @Value("${gsol.edm.inquiry.url}")
    private String inquiryUrl;

    @Value("${gsol.edm.eloqua.emailfield.email:{email}}")
    private String eloquaEmail;
    @Value("${gsol.edm.eloqua.emailfield.userId}")
    private String eloquaUserId;
    @Value("${gsol.edm.eloqua.emailfield.l4CategoryId}")
    private String eloquaL4CategoryId;

    @Override
    public String getHTMLTemplate(Long campaignId) {
        String htmlTemplate = "";
        try {
            EDMCampaignPO edmCampaignPO = baseMapper.selectById(campaignId);

            Map<String, Object> params = new HashMap<>();
            if (EDMSourceTypeEnum.NA.getCode().equals(edmCampaignPO.getSourceType())) {
                // use PP data in Eloqua directly
                params.put("useEloquaData", true);

            } else {
                int productNum = 6;
                String contentId = UUID.randomUUID().toString().replace("-", "");
                StringBuilder initURLSB = new StringBuilder(initUrl);
                initURLSB.append("?email=").append(eloquaEmail);
                initURLSB.append("&campaignId=").append(edmCampaignPO.getCampaignId());
                initURLSB.append("&contentId=").append(contentId);
                initURLSB.append("&campaignType=").append(edmCampaignPO.getCampaignType());
                initURLSB.append("&sourceType=").append(edmCampaignPO.getSourceType());
                initURLSB.append("&categoryId=").append(EDMSourceTypeEnum.USER_PREF.getCode().equals(edmCampaignPO.getSourceType()) ? "0" : eloquaL4CategoryId);
                initURLSB.append("&productNum=").append(productNum);
                params.put("init_url", initURLSB.toString());

                StringBuilder paramsSB = new StringBuilder("?");
                paramsSB.append("email=").append(eloquaEmail);
                paramsSB.append("&contentId=").append(contentId);

                StringBuilder detailURLSB = new StringBuilder(detailUrl);
                detailURLSB.append(paramsSB);
                params.put("detail_url", detailURLSB.toString());

                StringBuilder inquiryURLSB = new StringBuilder(inquiryUrl);
                inquiryURLSB.append(paramsSB);
                params.put("inquiry_url", inquiryURLSB.toString());

                StringBuilder imageURLSB = new StringBuilder(imageUrl);
                imageURLSB.append(paramsSB);
                params.put("image_url", imageURLSB.toString());

                StringBuilder nameURLSB = new StringBuilder(nameUrl);
                nameURLSB.append(paramsSB);
                params.put("name_url", nameURLSB.toString());
            }

            Template template = freeMarkerConfigurer.getConfiguration().getTemplate("edm-product.html");

            htmlTemplate = FreeMarkerTemplateUtils.processTemplateIntoString(template, params);
        } catch (Exception e) {
            log.error("getHTMLTemplate error, ", e);
        }

        return htmlTemplate;
    }

    @Override
    @Transactional
    public int batchSaveCampaignProduct(List<EDMCampaignProductPO> edmCampaignProductPOList, UserVO userVO, Long campaignId) {
        log.info("batchSaveCampaignProduct, edmCampaignProductPOList.size()={}", edmCampaignProductPOList.size());

        int batchCount = 1000;

        baseMapper.deleteCampaignProducts(campaignId);

        AtomicInteger insertCount = new AtomicInteger();
        List<EDMCampaignProductPO> insertList = Lists.newArrayList();
        edmCampaignProductPOList.forEach(edmCampaignProductPO -> {
            edmCampaignProductPO.setCampaignId(campaignId);
            edmCampaignProductPO.setCreateBy(userVO.getUserId());
            insertList.add(edmCampaignProductPO);

            // batch insert
            if (insertList.size() == batchCount) {
                int count = baseMapper.batchInsertCampaignProduct(insertList);
                insertCount.getAndAdd(count);

                insertList.clear();
            }
        });

        if (!CollectionUtils.isEmpty(insertList)) {
            // batch insert
            int count = baseMapper.batchInsertCampaignProduct(insertList);
            insertCount.getAndAdd(count);
        }

        return insertCount.get();
    }
}
