package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.constants.AdminConstants;
import com.globalsources.admin.dao.AcAnnouncementMapper;
import com.globalsources.admin.dao.AcAnnouncementMessageMapper;
import com.globalsources.admin.model.dto.AcAnnouncementDTO;
import com.globalsources.admin.model.pojo.AcAnnouncement;
import com.globalsources.admin.model.pojo.AcAnnouncementMessage;
import com.globalsources.admin.model.vo.AcAnnouncementMessageVO;
import com.globalsources.admin.model.vo.AcAnnouncementVO;
import com.globalsources.admin.model.vo.AnnouncementKeyWordVO;
import com.globalsources.admin.service.AcAnnouncementService;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.result.PageResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 走马灯公告 服务实现类
 * </p>
 *
 * <AUTHOR> Li
 * @since 2022-06-20
 */
@Slf4j
@Service
@AllArgsConstructor
public class AcAnnouncementServiceImpl extends ServiceImpl<AcAnnouncementMapper, AcAnnouncement> implements AcAnnouncementService {

    private final AcAnnouncementMapper acAnnouncementMapper;
    private final AcAnnouncementMessageMapper acAnnouncementMessageMapper;

    @Override
    public PageResult<AcAnnouncementVO> findAll(Integer pageNum, Integer pageSize, String roleType, String announcementType) {
        Page page = new Page(pageNum, pageSize);
        IPage<AcAnnouncementVO> list = acAnnouncementMapper.findAll(page, roleType, announcementType);
        if (list != null) {
            List<AcAnnouncementVO> result = list.getRecords().stream().map(item -> {
                item.setOnlineFlag(AdminConstants.OnlineStatus.ONLINE.equals(item.getOnlineStatus()));
                return item;
            }).collect(Collectors.toList());
            PageResult<AcAnnouncementVO> pageResult = new PageResult<>();
            pageResult.setList(result);
            pageResult.setTotal(list.getTotal());
            pageResult.setPageNum(list.getCurrent());
            pageResult.setPageSize(list.getSize());
            pageResult.setTotalPage(list.getPages());
            return pageResult;
        }
        return new PageResult<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public Integer insert(AcAnnouncementDTO acAnnouncementDTO, Long userId, String roleType, String announcementType) {
        AcAnnouncement acAnnouncement = AcAnnouncement.builder()
                .createBy(userId)
                .roleType(roleType)
                .announcementType(announcementType)
                .startDate(acAnnouncementDTO.getStartDate())
                .endDate(acAnnouncementDTO.getEndDate())
                .lUpdDate(new Date()).build();
        int count = acAnnouncementMapper.insert(acAnnouncement);
        if (count == 1) {

            acAnnouncementMapper.update(AcAnnouncement.builder().displaySeq(acAnnouncement.getAnnouncementId()).build(),
                    new LambdaQueryWrapper<AcAnnouncement>().eq(AcAnnouncement::getAnnouncementId, acAnnouncement.getAnnouncementId()));

            if (StringUtils.isNotEmpty(acAnnouncementDTO.getMessageEnUs())) {
                acAnnouncementMessageMapper.insert(AcAnnouncementMessage.builder()
                        .announcementId(acAnnouncement.getAnnouncementId()).langCode(LanguageDicEnum.EN_US.getValue()).description(acAnnouncementDTO.getMessageEnUs()).build());
            }
            if (StringUtils.isNotEmpty(acAnnouncementDTO.getMessageZhCn())) {
                acAnnouncementMessageMapper.insert(AcAnnouncementMessage.builder().langCode(LanguageDicEnum.ZH_CN.getValue()).announcementId(acAnnouncement.getAnnouncementId()).description(acAnnouncementDTO.getMessageZhCn()).build());
            }

            if (StringUtils.isNotEmpty(acAnnouncementDTO.getMessageZhTw())) {
                acAnnouncementMessageMapper.insert(AcAnnouncementMessage.builder().langCode(LanguageDicEnum.ZH_TW.getValue()).announcementId(acAnnouncement.getAnnouncementId()).description(acAnnouncementDTO.getMessageZhTw()).build());

            }

        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public Integer edit(AcAnnouncementDTO acAnnouncementDTO, Long userId, String roleType, String announcementType) {
        AcAnnouncement acAnnouncement = AcAnnouncement.builder()
                .lUpdBy(userId)
                .startDate(acAnnouncementDTO.getStartDate())
                .endDate(acAnnouncementDTO.getEndDate())
                .lUpdDate(new Date()).build();
        int count = acAnnouncementMapper.update(acAnnouncement, new LambdaQueryWrapper<AcAnnouncement>().eq(AcAnnouncement::getAnnouncementId, acAnnouncementDTO.getAnnouncementId()));

        if (count == 1) {
            if (StringUtils.isNotEmpty(acAnnouncementDTO.getMessageEnUs())) {
                acAnnouncementMessageMapper.update(AcAnnouncementMessage.builder().langCode(LanguageDicEnum.EN_US.getValue()).description(acAnnouncementDTO.getMessageEnUs()).build(),
                        new LambdaQueryWrapper<AcAnnouncementMessage>()
                                .eq(AcAnnouncementMessage::getAnnouncementId, acAnnouncementDTO.getAnnouncementId()).eq(AcAnnouncementMessage::getLangCode, LanguageDicEnum.EN_US.getValue()));
            }
            if (StringUtils.isNotEmpty(acAnnouncementDTO.getMessageZhCn())) {
                acAnnouncementMessageMapper.update(AcAnnouncementMessage.builder().langCode(LanguageDicEnum.ZH_CN.getValue()).description(acAnnouncementDTO.getMessageZhCn()).build(),
                        new LambdaQueryWrapper<AcAnnouncementMessage>()
                                .eq(AcAnnouncementMessage::getAnnouncementId, acAnnouncementDTO.getAnnouncementId()).eq(AcAnnouncementMessage::getLangCode, LanguageDicEnum.ZH_CN.getValue()));
            }
            if (StringUtils.isNotEmpty(acAnnouncementDTO.getMessageZhTw())) {
                acAnnouncementMessageMapper.update(AcAnnouncementMessage.builder().langCode(LanguageDicEnum.ZH_TW.getValue()).description(acAnnouncementDTO.getMessageZhTw()).build(),
                        new LambdaQueryWrapper<AcAnnouncementMessage>()
                                .eq(AcAnnouncementMessage::getAnnouncementId, acAnnouncementDTO.getAnnouncementId()).eq(AcAnnouncementMessage::getLangCode, LanguageDicEnum.ZH_TW.getValue()));
            }
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public Integer downAnnouncement(Integer announcementId, Long userId, String roleType, String announcementType) {
        AcAnnouncement acAnnouncement = acAnnouncementMapper.selectOne(new LambdaQueryWrapper<AcAnnouncement>().eq(AcAnnouncement::getAnnouncementId, announcementId));
        if (ObjectUtils.isEmpty(acAnnouncement)) {
            return 0;
        }
        //下移
        AnnouncementKeyWordVO keyWordVO = acAnnouncementMapper.selectNextId(announcementId, roleType, announcementType, acAnnouncement.getDisplaySeq());
        if (ObjectUtils.isNotEmpty(keyWordVO)) {
            acAnnouncementMapper.update(AcAnnouncement.builder().displaySeq(keyWordVO.getDisplaySeq()).lUpdBy(userId).lUpdDate(new Date()).build()
                    , new LambdaQueryWrapper<AcAnnouncement>().eq(AcAnnouncement::getAnnouncementId, announcementId));

            return acAnnouncementMapper.update(AcAnnouncement.builder().displaySeq(acAnnouncement.getDisplaySeq()).lUpdBy(userId).lUpdDate(new Date()).build()
                    , new LambdaQueryWrapper<AcAnnouncement>().eq(AcAnnouncement::getAnnouncementId, keyWordVO.getAnnouncementId()));
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public Integer upAnnouncement(Integer announcementId, Long userId, String roleType, String announcementType) {
        AcAnnouncement acAnnouncement = acAnnouncementMapper.selectOne(new LambdaQueryWrapper<AcAnnouncement>().eq(AcAnnouncement::getAnnouncementId, announcementId));
        if (ObjectUtils.isEmpty(acAnnouncement)) {
            return 0;
        }
        //上移
        AnnouncementKeyWordVO keyWordVO = acAnnouncementMapper.selectPreviousId(announcementId, roleType, announcementType, acAnnouncement.getDisplaySeq());
        if (ObjectUtils.isNotEmpty(keyWordVO)) {
            acAnnouncementMapper.update(AcAnnouncement.builder().displaySeq(keyWordVO.getDisplaySeq()).lUpdBy(userId).lUpdDate(new Date()).build()
                    , new LambdaQueryWrapper<AcAnnouncement>().eq(AcAnnouncement::getAnnouncementId, announcementId));

            return acAnnouncementMapper.update(AcAnnouncement.builder().displaySeq(acAnnouncement.getDisplaySeq()).lUpdBy(userId).lUpdDate(new Date()).build()
                    , new LambdaQueryWrapper<AcAnnouncement>().eq(AcAnnouncement::getAnnouncementId, keyWordVO.getAnnouncementId()));
        }
        return 0;
    }

    @Override
    public List<AcAnnouncementMessageVO> notice(String roleType, String announcementType) {
        Date currentDate = new Date();
        return acAnnouncementMapper.notice(roleType, announcementType, currentDate);
    }
}
