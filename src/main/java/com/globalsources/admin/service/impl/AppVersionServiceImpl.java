package com.globalsources.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.dao.AcAppVersionMapper;
import com.globalsources.admin.model.dto.AppVersionDTO;
import com.globalsources.admin.model.pojo.AppVersion;
import com.globalsources.admin.service.AppVersionService;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.BeanUtil;
import com.globalsources.framework.utils.OrikaMapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class AppVersionServiceImpl extends ServiceImpl<AcAppVersionMapper, AppVersion> implements AppVersionService {
    @Autowired
    private AcAppVersionMapper appVersionMapper;

    @Override
    public Result<PageResult<AppVersion>> getPageList(String appName, Long pageNum, Long pageSize) {
        Page<AppVersion> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<AppVersion> queryWrapper = Wrappers.lambdaQuery(AppVersion.class);
        queryWrapper.eq(AppVersion::getAppName, appName);
        queryWrapper.orderByDesc(AppVersion::getCreateDate);

        Page<AppVersion> iPage;
        try {
            iPage = page(page, queryWrapper);
        }catch (Exception e){
            log.error("query app version list occur exception, appName:{},pageNum:{},pageSize:{}",appName,pageNum,pageSize,e);
            return Result.error();
        }

        PageResult<AppVersion> data = new PageResult<>();
        data.setTotal(iPage.getTotal());
        data.setPageNum(iPage.getCurrent());
        data.setPageSize(iPage.getSize());
        data.setTotalPage(iPage.getPages());
        data.setList(iPage.getRecords());

        return Result.success(data);
    }

    @Override
    public Result<Void> delete(Long appVersionId) {
        try {
            appVersionMapper.deleteById(appVersionId);
        }catch (Exception e){
            log.error("delete app version occur exception, appVersionId:{}",appVersionId,e);
            return Result.error();
        }

        return Result.success();
    }

    @Override
    public Result<Void> editAppVersion(Long userId,AppVersionDTO dto) {
        try{
            AppVersion appVersion=getById(dto.getAppVersionId());
            //将dto的属性拷贝至实体
            BeanUtil.copyProperties(dto,appVersion);
            appVersion.setLUpdBy(userId);
            appVersion.setLUpdDate(appVersion.getCreateDate());

            if(appVersion.getFileSize()==null){
                appVersion.setFileSize(0l);
            }

            if(StringUtils.isEmpty(appVersion.getFileName())){
                appVersion.setFileName("NA");
            }

            //更新
            updateById(appVersion);

            return Result.success();
        }catch (Exception e){
            log.error("update app version occur exception,userId:{} dto:{}",userId, JSON.toJSONString(dto),e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> addAppVersion(Long userId,AppVersionDTO dto) {
        try {
            if(dto.getFileSize()==null){
                dto.setFileSize(0l);
            }
            if(StringUtils.isEmpty(dto.getFileName())){
                dto.setFileName("NA");
            }

            AppVersion appVersion = OrikaMapperUtil.coverObject(dto, AppVersion.class);
            appVersion.setCreateBy(userId);
            appVersion.setStatusCode("NOREV");
            appVersion.setLUpdBy(userId);
            appVersion.setCreateDate(new Date());
            appVersion.setLUpdDate(appVersion.getCreateDate());
            appVersion.setFileName(dto.getFileName());
            appVersion.setFileSize(dto.getFileSize());

            //新增
            save(appVersion);

            return Result.success();
        }catch (Exception e){
            log.error("add app version occur exception,userId:{} dto:{}",userId, JSON.toJSONString(dto),e);
            return Result.error();
        }
    }
}
