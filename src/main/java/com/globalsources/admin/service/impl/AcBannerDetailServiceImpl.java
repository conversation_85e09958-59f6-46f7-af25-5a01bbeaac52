package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.constants.AdminConstants;
import com.globalsources.admin.dao.AcBannerDetailMapper;
import com.globalsources.admin.model.dto.banner.AcBannerI18nDetailDTO;
import com.globalsources.admin.model.pojo.AcBannerDetail;
import com.globalsources.admin.service.AcBannerDetailService;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> Chen
 * @since 2022-09-07
 */
@Slf4j
@Service
@Validated
public class AcBannerDetailServiceImpl extends ServiceImpl<AcBannerDetailMapper, AcBannerDetail> implements AcBannerDetailService {


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveAcBannerDetailBatchByBannerId(List<AcBannerI18nDetailDTO> saveDtoList, @NotNull Long bannerId) {
        if (Objects.isNull(saveDtoList)) {
            throw new BusinessException(ResultCode.CommonResultCode.FAILED.getCode(), "banner i18n detail is null");
        }
        deleteByBannerId(bannerId);
        List<AcBannerDetail> acBannerDetails = OrikaMapperUtil.coverList(saveDtoList, AcBannerDetail.class);
        for (AcBannerDetail bean : acBannerDetails) {
            bean.setBannerId(bannerId);
            bean.setEnableFlag(Boolean.TRUE.equals(bean.getEnableFlag()));
        }
        return saveBatch(acBannerDetails);
    }

    @Override
    public List<AcBannerDetail> getAcBannerDetailListByBannerId(Long bannerId) {
        if (Objects.isNull(bannerId)) {
            return Lists.newArrayList();
        }
        List<AcBannerDetail> acBannerDetails = this.baseMapper.selectList(Wrappers.lambdaQuery(AcBannerDetail.class)
                .eq(AcBannerDetail::getBannerId, bannerId));
        return Optional.ofNullable(acBannerDetails).orElse(Lists.newArrayList());
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer deleteByBannerId(Long bannerId) {
        if (Objects.isNull(bannerId)) {
            return 0;
        }
        return this.baseMapper.delete(Wrappers.lambdaQuery(AcBannerDetail.class).eq(AcBannerDetail::getBannerId, bannerId));
    }


    @Override
    public List<String> getLangStrListByBannerId(Long bannerId) {
        List<AcBannerDetail> acBannerDetails = this.baseMapper.selectList(Wrappers.lambdaQuery(AcBannerDetail.class)
                .eq(AcBannerDetail::getBannerId, bannerId)
                .select(AcBannerDetail::getLangCode, AcBannerDetail::getEnableFlag));
        return Optional.ofNullable(acBannerDetails)
                .map(list -> list.stream()
                        .filter(bean -> Objects.nonNull(bean) && Boolean.TRUE.equals(bean.getEnableFlag()))
                        .map(AcBannerDetail::getLangCode)
                        .filter(StringUtils::isNotBlank)
                        .sorted(Comparator.comparingInt(langCode ->
                                AdminConstants.SUPPLIER_APP_BANNER_LANG_SEQ_MAP.getOrDefault(langCode, Integer.MAX_VALUE)))
                        .distinct().collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
    }


    @Override
    public List<AcBannerDetail> getAcBannerDetailListByBannerIdsAndLang(List<Long> bannerIds, String langCode) {
        if (CollectionUtils.isEmpty(bannerIds)) {
            return Lists.newArrayList();
        }
        List<AcBannerDetail> acBannerDetails = this.baseMapper.selectList(Wrappers.lambdaQuery(AcBannerDetail.class)
                .in(AcBannerDetail::getBannerId, bannerIds)
                .eq(AcBannerDetail::getLangCode, langCode));
        return Optional.ofNullable(acBannerDetails).orElse(Lists.newArrayList());
    }

}
