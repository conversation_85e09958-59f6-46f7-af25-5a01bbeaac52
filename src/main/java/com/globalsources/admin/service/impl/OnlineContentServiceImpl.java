package com.globalsources.admin.service.impl;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.constants.HelpCenterConstant;
import com.globalsources.admin.dao.OnlineContentMapper;
import com.globalsources.admin.dao.OnlineTopicMapper;
import com.globalsources.admin.model.dto.helpcenter.ContentBatchImportDTO;
import com.globalsources.admin.model.dto.helpcenter.ContentBatchUpdateStatusDTO;
import com.globalsources.admin.model.dto.helpcenter.ContentBatchUpdateTopicIdDTO;
import com.globalsources.admin.model.dto.helpcenter.OnlineContentSCDTO;
import com.globalsources.admin.model.dto.helpcenter.OnlineContentSaveDTO;
import com.globalsources.admin.model.dto.helpcenter.OperationLogEncapsulationDTO;
import com.globalsources.admin.model.dto.helpcenter.SortDTO;
import com.globalsources.admin.model.dto.helpcenter.TopicCommonPageDTO;
import com.globalsources.admin.model.dto.helpcenter.TopicContentCommonPageDTO;
import com.globalsources.admin.model.pojo.AcUser;
import com.globalsources.admin.model.pojo.OnlineContent;
import com.globalsources.admin.model.pojo.OnlineTopic;
import com.globalsources.admin.model.vo.OnlineContentVO;
import com.globalsources.admin.model.vo.helpcenter.BffChildrenOnlineContentVO;
import com.globalsources.admin.model.vo.helpcenter.BffContentListVO;
import com.globalsources.admin.model.vo.helpcenter.BffHomepagetListVO;
import com.globalsources.admin.model.vo.helpcenter.BffMenuListVO;
import com.globalsources.admin.model.vo.helpcenter.BffOnlineContentVO;
import com.globalsources.admin.model.vo.helpcenter.OnlineContentEditVO;
import com.globalsources.admin.model.vo.helpcenter.OnlineContentSCVO;
import com.globalsources.admin.model.vo.helpcenter.OnlineContentTopicVO;
import com.globalsources.admin.model.vo.helpcenter.OnlineTopicPidTreeVo;
import com.globalsources.admin.model.vo.helpcenter.OnlineTopicSCVO;
import com.globalsources.admin.service.FeedBackService;
import com.globalsources.admin.service.IAcUserService;
import com.globalsources.admin.service.OnlineContentService;
import com.globalsources.admin.service.OnlineTopicService;
import com.globalsources.admin.util.ArrayListUtil;
import com.globalsources.admin.util.FileUtil;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.result.ResultCodeEnum;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.supplierconsole.agg.api.constant.OperationEnum;
import com.google.common.collect.Maps;
import com.opencsv.CSVReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR> Sun
 * @date 2021/11/3
 */
@Service
@Slf4j
public class OnlineContentServiceImpl extends ServiceImpl<OnlineContentMapper, OnlineContent> implements OnlineContentService {

    private static final String FILE_WRONG_TYPE = "Wrong file format";
    public static final String BC = HelpCenterConstant.SourceCode.BC.getName();

    @Resource
    private FileUtil fileUtil;

    @Resource
    private IAcUserService iAcUserService;

    @Resource
    private OnlineTopicService onlineTopicService;

    @Resource
    private OnlineTopicMapper onlineTopicMapper;

    @Resource
    private FeedBackService feedBackService;

    @Override
    public Result<PageResult<OnlineContentVO>> getOnlineContent(Integer topicId, Integer pageNum, Integer pageSize) {
        PageResult<OnlineContentVO> pageResult = new PageResult<>();
        try {
            pageNum = Optional.ofNullable(pageNum).orElse(BigInteger.ONE.intValue());
            pageSize = Optional.ofNullable(pageSize).orElse(BigInteger.ONE.intValue());
            Page<OnlineContent> data = this.baseMapper.selectPage(new Page<>(pageNum, pageSize),
                    Wrappers.lambdaQuery(OnlineContent.class)
                            .eq(OnlineContent::getTopicId, topicId)
                            .orderByAsc(OnlineContent::getOcId));

            List<OnlineContent> onlineContentList = data.getRecords();
            if (CollectionUtils.isNotEmpty(data.getRecords())) {
                pageResult.setList(CollectionUtils.isEmpty(onlineContentList) ? Collections.emptyList() : OrikaMapperUtil.coverList(onlineContentList, OnlineContentVO.class));
                pageResult.setTotal(data.getTotal());
                pageResult.setPageNum(data.getCurrent());
                pageResult.setPageSize(data.getSize());
                pageResult.setTotalPage((long) PageUtil.totalPage((int) data.getTotal(), (int) data.getSize()));
            }
        } catch (Exception e) {
            log.error("-----------------getOnlineContent error, topicId:{},pageNum:{},pageSize:{}", topicId, pageNum, pageSize);
        }
        return Result.success(pageResult);
    }

    /******************************** 后台相关  ********************************/

    /**
     * 新增 Content Article 内容
     * @param userVO
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> saveContent(UserVO userVO, OnlineContentSaveDTO dto) {
        OnlineContent sc = OnlineContent.builder()
                .topicId(dto.getTopicId())
                .homepageTopicId(dto.getHomepageTopicId())
                .subject(dto.getSubject())
                .subjectZh(dto.getSubjectZh())
                .contentText(dto.getContentText())
                .contentTextZh(dto.getContentTextZh())
                .displaySeq(getDisplaySeqMax(dto.getTopicId()))
                .createBy(userVO.getUserId())
                .createDate(new Date())
                .build();

        if (BC.equals(dto.getSourceCode())) {
            sc.setDisplayFlag(false);
        }

        int insert = baseMapper.insert(sc);
        if (insert > 0) {

            OperationLogEncapsulationDTO buildDto = OperationLogEncapsulationDTO.builder()
                    .ocId(Long.valueOf(sc.getOcId()))
                    .homeFlag(dto.getHomeFlag())
                    .operationName(OperationEnum.CREATE.getOperationName())
                    .userId(userVO.getUserId())
                    .build();
            onlineTopicService.saveOperationLog(buildDto);
        }
        return Result.success(insert > 0) ;
    }

    /**
     * 更新 Content Article 内容
     * @param userVO
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> updateContent(UserVO userVO, OnlineContentSaveDTO dto) {
        OnlineContent onlineContent = baseMapper.selectById(dto.getOcId());
        if (Objects.isNull(onlineContent)) {
            return Result.failed();
        }

        BeanUtils.copyProperties(dto, onlineContent);
        onlineContent.setLUpdDate(new Date());
        onlineContent.setLUpdBy(userVO.getUserId());
        int i = baseMapper.alwaysUpdateSomeColumnById(onlineContent);
        if ( i > 0) {
            OperationLogEncapsulationDTO buildDto = OperationLogEncapsulationDTO.builder()
                    .ocId(Long.valueOf(onlineContent.getOcId()))
                    .homeFlag(dto.getHomeFlag())
                    .operationName(OperationEnum.UPDATE.getOperationName())
                    .userId(userVO.getUserId())
                    .build();
            onlineTopicService.saveOperationLog(buildDto);
        }
        return Result.success(i>0) ;
    }

    /**
     * 新增 Homepage Settings 内容
     * @param userVO
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> saveHomepage(UserVO userVO, OnlineContentSaveDTO dto) {
        if (StringUtils.isEmpty(dto.getWebUrl())
                || StringUtils.isEmpty(dto.getWebUrlZh())
                || StringUtils.isEmpty(dto.getImageUrl())) {
            return Result.failed(ResultCode.CommonResultCode.METHOD_ARGUMENT_TYPE_MISMATCH);
        }

        OnlineContent sc = OnlineContent.builder()
                .topicId(dto.getTopicId())
                .subject(dto.getSubject())
                .subjectZh(dto.getSubjectZh())
                .webUrl(dto.getWebUrl())
                .webUrlZh(dto.getWebUrlZh())
                .imageUrl(dto.getImageUrl())
                .displaySeq(getDisplaySeqMax(dto.getTopicId()))
                .createBy(userVO.getUserId())
                .createDate(new Date())
                .build();

        int insert = baseMapper.insert(sc);
        if (insert > 0) {

            OperationLogEncapsulationDTO buildDto = OperationLogEncapsulationDTO.builder()
                    .ocId(Long.valueOf(sc.getOcId()))
                    .homeFlag(dto.getHomeFlag())
                    .operationName(OperationEnum.CREATE.getOperationName())
                    .userId(userVO.getUserId())
                    .build();
            onlineTopicService.saveOperationLog(buildDto);
        }
        return Result.success(insert>0) ;
    }

    /**
     * 更新 Homepage Settings 内容
     * @param userVO
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> updateHomepage(UserVO userVO, OnlineContentSaveDTO dto) {
        if (Objects.isNull(dto.getOcId())
                || StringUtils.isEmpty(dto.getWebUrl())
                || StringUtils.isEmpty(dto.getWebUrlZh())
                || StringUtils.isEmpty(dto.getImageUrl())) {
            return Result.failed(ResultCode.CommonResultCode.METHOD_ARGUMENT_TYPE_MISMATCH);
        }

        OnlineContent sc = OnlineContent.builder()
                .ocId(dto.getOcId())
                .topicId(dto.getTopicId())
                .subject(dto.getSubject())
                .subjectZh(dto.getSubjectZh())
                .webUrl(dto.getWebUrl())
                .webUrlZh(dto.getWebUrlZh())
                .imageUrl(dto.getImageUrl())
                .lUpdBy(userVO.getUserId())
                .lUpdDate(new Date())
                .build();

        int i = baseMapper.updateById(sc);
        if ( i > 0) {
            OperationLogEncapsulationDTO buildDto = OperationLogEncapsulationDTO.builder()
                    .ocId(Long.valueOf(sc.getOcId()))
                    .homeFlag(dto.getHomeFlag())
                    .operationName(OperationEnum.UPDATE.getOperationName())
                    .userId(userVO.getUserId())
                    .build();
            onlineTopicService.saveOperationLog(buildDto);
        }
        return Result.success(i > 0) ;
    }

    private Map<Long, OnlineTopic> getTopicMap(String sourceCode) {
        List<OnlineTopic> allTopics = onlineTopicMapper.selectList(new LambdaQueryWrapper<OnlineTopic>().eq(OnlineTopic::getSourceCode, sourceCode));
        if (CollectionUtils.isEmpty(allTopics)) {
            return Maps.newHashMap();
        }
        return allTopics.stream().filter(Objects::nonNull).collect(Collectors.toMap(OnlineTopic::getTopicId, Function.identity()));
    }

    private void buildParentTopic(OnlineContentTopicVO contentTopicVO, Map<Long, OnlineTopic> topicMap) {
        Integer l3TopicId = contentTopicVO.getTopicId();
        if (Objects.isNull(l3TopicId)) {
            return;
        }
        OnlineTopic l3Topic = onlineTopicMapper.selectById(contentTopicVO.getTopicId());
        if (Objects.nonNull(l3Topic) && BC.equals(l3Topic.getSourceCode())) {
            OnlineTopic l2Topic = null;
            OnlineTopic l1Topic = null;
            if (Objects.nonNull(l3Topic.getTopicPid()) && l3Topic.getTopicPid() != 0) {
                l2Topic = topicMap.get(l3Topic.getTopicPid());
                if (Objects.nonNull(l2Topic) && Objects.nonNull(l2Topic.getTopicPid())  && l2Topic.getTopicPid() != 0) {
                    l1Topic = topicMap.get(l2Topic.getTopicPid());
                }
            }
            if (Objects.nonNull(l1Topic)) {
                contentTopicVO.setL3TopicId(l3TopicId);
                contentTopicVO.setL2TopicId(l2Topic.getTopicId().intValue());
                contentTopicVO.setL1TopicId(l1Topic.getTopicId().intValue());
                contentTopicVO.setL3TopicName(l3Topic.getTopic());
                contentTopicVO.setL2TopicName(l2Topic.getTopic());
                contentTopicVO.setL1TopicName(l1Topic.getTopic());
            } else if (Objects.nonNull(l2Topic)) {
                contentTopicVO.setL2TopicId(l3TopicId);
                contentTopicVO.setL1TopicId(l2Topic.getTopicId().intValue());
                contentTopicVO.setL2TopicName(l3Topic.getTopic());
                contentTopicVO.setL1TopicName(l2Topic.getTopic());
            } else {
                contentTopicVO.setL1TopicId(l3TopicId);
                contentTopicVO.setL1TopicName(l3Topic.getTopic());
            }
        }
    }

    private void buildParentTopic(OnlineContentTopicVO contentTopicVO) {
        Integer l3TopicId = contentTopicVO.getTopicId();
        if (Objects.isNull(l3TopicId)) {
            return;
        }
        OnlineTopic l3Topic = onlineTopicMapper.selectById(contentTopicVO.getTopicId());
        if (Objects.nonNull(l3Topic) && BC.equals(l3Topic.getSourceCode())) {
            Map<Long, OnlineTopic> topicMap = getTopicMap(BC);
            OnlineTopic l2Topic = null;
            OnlineTopic l1Topic = null;
            if (Objects.nonNull(l3Topic.getTopicPid()) && l3Topic.getTopicPid() != 0) {
                l2Topic = topicMap.get(l3Topic.getTopicPid());
                if (Objects.nonNull(l2Topic) && Objects.nonNull(l2Topic.getTopicPid())  && l2Topic.getTopicPid() != 0) {
                    l1Topic = topicMap.get(l2Topic.getTopicPid());
                }
            }
            if (Objects.nonNull(l1Topic)) {
                contentTopicVO.setL3TopicId(l3TopicId);
                contentTopicVO.setL2TopicId(l2Topic.getTopicId().intValue());
                contentTopicVO.setL1TopicId(l1Topic.getTopicId().intValue());
                contentTopicVO.setL3TopicName(l3Topic.getTopic());
                contentTopicVO.setL2TopicName(l2Topic.getTopic());
                contentTopicVO.setL1TopicName(l1Topic.getTopic());
            } else if (Objects.nonNull(l2Topic)) {
                contentTopicVO.setL2TopicId(l3TopicId);
                contentTopicVO.setL1TopicId(l2Topic.getTopicId().intValue());
                contentTopicVO.setL2TopicName(l3Topic.getTopic());
                contentTopicVO.setL1TopicName(l2Topic.getTopic());
            } else {
                contentTopicVO.setL1TopicId(l3TopicId);
                contentTopicVO.setL1TopicName(l3Topic.getTopic());
            }
        }
    }

    /**
     * 查询所有内容 -- 分页返回  -- 通用
     * @param topicCommonPageDTO
     * @return
     */
    @Override
    public Result<PageResult<OnlineContentSCVO>> selectContent(TopicCommonPageDTO topicCommonPageDTO) {
        if (Objects.isNull(topicCommonPageDTO)) {
            return Result.failed(ResultCode.CommonResultCode.REQUIRED_REQUEST_PARAMETERS_ARE_MISSING);
        }
        List<Long> allTopicId = getAllTopicId(topicCommonPageDTO);
        List<OnlineContentSCVO> result = getAllContentForTopics(topicCommonPageDTO, allTopicId);
        // 手动处理分页
        PageResult<OnlineContentSCVO> pageResult = new PageResult<>();

        // 取得总页数
        int totalPage = PageUtil.totalPage(result.size(), topicCommonPageDTO.getPageSize());
        // 0,1 为第一页
        PageUtil.setFirstPageNo(1);
        // 获取起始位置
        int start = PageUtil.getStart(topicCommonPageDTO.getPageNum(), topicCommonPageDTO.getPageSize());

        // 过滤
        List<OnlineContentSCVO> currentList = result.stream().skip(start).limit(topicCommonPageDTO.getPageSize()).collect(Collectors.toList());


        pageResult.setPageNum(Long.valueOf(topicCommonPageDTO.getPageNum()));
        pageResult.setPageSize(Long.valueOf(topicCommonPageDTO.getPageSize()));
        pageResult.setTotal(Long.valueOf(result.size()));
        pageResult.setTotalPage((long) totalPage);
        pageResult.setList(currentList);
        return Result.success(pageResult);
    }

    private List<OnlineContentSCVO> getAllContentForTopics(TopicCommonPageDTO topicCommonPageDTO, List<Long> allTopicId) {
        if (CollectionUtils.isEmpty(allTopicId)) {
            return Lists.newArrayList();
        }
        if (Objects.isNull(topicCommonPageDTO)) {
            return Lists.newArrayList();
        }

        // 根据菜单id 查询该菜单下的所有文章
        List<OnlineContentSCVO> result = new ArrayList<>();
        allTopicId.forEach(id -> {
            List<OnlineContentSCDTO> onlineContentSCDTOS;

            // 判断搜索传入的是否是一个合格的邮箱
            if (Objects.nonNull(topicCommonPageDTO.getSearchKey()) && Boolean.TRUE.equals(checkEmail(topicCommonPageDTO.getSearchKey()))) {

                AcUser userByUserEmail = iAcUserService.getUserByUserEmail(topicCommonPageDTO.getSearchKey());

                if (Objects.nonNull(userByUserEmail)) {
                    Long userId = userByUserEmail.getUserId();
                    // 通过 topicId(所属分类id) 下的所有文章
                    onlineContentSCDTOS = baseMapper.getSearchResult(id, topicCommonPageDTO.getStatus(), topicCommonPageDTO.getSearchKey(), userId);
                } else {
                    onlineContentSCDTOS = baseMapper.getSearchResult(id, topicCommonPageDTO.getStatus(), topicCommonPageDTO.getSearchKey(), null);
                }

            } else {
                // 通过 topicId(所属分类id) 下的所有文章
                onlineContentSCDTOS = baseMapper.getSearchResult(id, topicCommonPageDTO.getStatus(), topicCommonPageDTO.getSearchKey(), null);
            }

            result.addAll(buildOnlineContentVOList(onlineContentSCDTOS));
        });
        return result;
    }

    private List<OnlineContentSCVO> buildOnlineContentVOList(List<OnlineContentSCDTO> onlineContentSCDTOS) {
        List<OnlineContentSCVO> onlineContentSCVOS = new ArrayList<>();
        onlineContentSCDTOS.forEach(onlineContentSCDTO -> {
            // 转换成出参对象
            OnlineContentSCVO onlineContentSCVO = OrikaMapperUtil.coverObject(onlineContentSCDTO, OnlineContentSCVO.class);

            // 通过用户id 拿到用户邮箱
            if (Objects.nonNull(onlineContentSCDTO.getCreateBy())) {
                onlineContentSCVO.setCreateBy(iAcUserService.getUserById(onlineContentSCDTO.getCreateBy()).getEmail());
            }

            if (Objects.nonNull(onlineContentSCDTO.getLUpdBy())) {
                onlineContentSCVO.setLUpdBy(iAcUserService.getUserById(onlineContentSCDTO.getLUpdBy()).getEmail());
            }

            // 拿到层级菜单信息
            if (!onlineContentSCDTO.getTopicId().equals(-1)) {
                OnlineTopicPidTreeVo menuNameRecursivelyTopLevel = onlineTopicService.getMenuNameRecursivelyTopLevel(Long.valueOf(onlineContentSCDTO.getTopicId()));
                onlineContentSCVO.setTopicIdTreeVo(menuNameRecursivelyTopLevel);
            }

            // 拿到反馈信息
            onlineContentSCVO.setLikeNum(feedBackService.getFeedbackCountByArticleId(onlineContentSCDTO.getOcId().longValue(), Boolean.TRUE));
            onlineContentSCVO.setDislikeNum(feedBackService.getFeedbackCountByArticleId(onlineContentSCDTO.getOcId().longValue(), Boolean.FALSE));


            onlineContentSCVOS.add(onlineContentSCVO);
        });
        return onlineContentSCVOS;
    }

    private List<Long> getAllTopicId(TopicCommonPageDTO topicCommonPageDTO) {
        // 调用前台内容页菜单接口 -- 拿到以指定id为目录根的 菜单目录树。 如果 id=0 则是查所有，会拿到所有菜单id
        Result<List<BffMenuListVO>> listResult = onlineTopicService.selectAllMenuBff(
                topicCommonPageDTO.getTopicId(),
                "en", // 这里随便填中英文，返回的菜单id都一样,只是返回的语言不同。
                topicCommonPageDTO.getSourceCode(),
                topicCommonPageDTO.getHomeFlag()
        );

        if (listResult.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {

            // 获取所有菜单树结构
            List<BffMenuListVO> allMenu = listResult.getData();

            // 将菜单树中的id根据菜单树顺序取出
            List<Long> allTopicId = new ArrayList<>();

            // 如果菜单id=0 ，则还需查询文章分类被删除的内容 （被删除的文章的分类id=-1）
            if (topicCommonPageDTO.getTopicId().equals(0L)) {
                allTopicId.add(-1L); // 手动将没有分类的放在第一位
            }

            // 递归遍历拿到菜单树结构顺序的id数组
            allMenu.stream().forEach(bffMenuListVO -> {
                List<Long> temp = new ArrayList<>();
                temp.add(bffMenuListVO.getId());
                allTopicId.addAll(temp);
                allTopicId.addAll(getChildrenIds(bffMenuListVO.getChildren()));
            });
            return allTopicId;
        }
        return Lists.newArrayList();
    }

    private Boolean checkEmail(String email) {
        boolean isMatched = Validator.isEmail(email);
        if (isMatched) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 查询内容详情 -- 通用
     * @param ocId
     * @return
     */
    @Override
    public Result<OnlineContentEditVO> contentDetails(Integer ocId) {
        OnlineContent onlineContent = baseMapper.selectById(ocId);
        OnlineContentEditVO editVO = OrikaMapperUtil.coverObject(onlineContent, OnlineContentEditVO.class);
        OnlineContentTopicVO contentTopicVO = new OnlineContentTopicVO();
        contentTopicVO.setTopicId(onlineContent.getTopicId());
        buildParentTopic(contentTopicVO);
        editVO.setContentTopicVO(contentTopicVO);
        editVO.setHomePageTopicList(onlineTopicService.getTopicSimpleList(BC, true));
        return Result.success(editVO);
    }

    /**
     * 删除内容 -- 通用
     * @param userId
     * @param ocId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> deleteContent(Long userId,Integer ocId) {
        OnlineContent onlineContent = baseMapper.selectById(ocId);
        if (Objects.isNull(onlineContent)) {
            throw new BusinessException(ResultCodeEnum.THE_OBJECT_WAS_NOT_FOUND);
        }
        onlineContent.setLUpdBy(userId);
        onlineContent.setLUpdDate(new Date());
        onlineContent.setDeleteFlag(Boolean.TRUE);

        int i = baseMapper.updateById(onlineContent);

        if (i > 0) {
            // 通过文章的所属菜单id，去菜单表查询该文章属于 首页菜单 还是 内容页菜单
            OnlineTopic onlineTopic = onlineTopicMapper.selectById(onlineContent.getTopicId());
            OperationLogEncapsulationDTO buildDto = OperationLogEncapsulationDTO.builder()
                    .ocId(Long.valueOf(onlineContent.getOcId()))
                    .homeFlag(onlineTopic.getHomeFlag())
                    .operationName(OperationEnum.DELETE.getOperationName())
                    .userId(userId)
                    .build();
            onlineTopicService.saveOperationLog(buildDto);
        }
        return Result.success(i > 0) ;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Integer> contentBatchUpdateStatus(UserVO userVO,ContentBatchUpdateStatusDTO dto) {

        if (dto.getOcIdList().isEmpty()) {
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }

        // 记录日志
        Long topicId = baseMapper.getTopicId(Long.valueOf(dto.getOcIdList().get(0)));
        // 通过拖动元素的所属菜单id，去菜单表查询该文章属于 首页菜单 还是 内容页菜单
        OnlineTopic onlineTopic = onlineTopicMapper.selectById(topicId);

        // 如果是批量隐藏，则不需要校验，直接隐藏即可。
        if (dto.getStatus().equals(Boolean.FALSE)) {
            int i = baseMapper.batchUpdateDisplayFlag(dto.getOcIdList(), dto.getStatus());

            // 记录日志
            OperationLogEncapsulationDTO buildDto = OperationLogEncapsulationDTO.builder()
                    .ocIdList(dto.getOcIdList())
                    .homeFlag(onlineTopic.getHomeFlag())
                    .operationName(OperationEnum.INACTIVE.getOperationName())
                    .userId(userVO.getUserId())
                    .build();
            onlineTopicService.saveOperationLog(buildDto);

            return Result.success(i);
        }

        // 检验 Articles 的必填项完整性
        if (Boolean.TRUE.equals(batchUpdateStatusCheck(dto.getOcIdList()))) {
            int i = baseMapper.batchUpdateDisplayFlag(dto.getOcIdList(), dto.getStatus());

            // 记录日志
            OperationLogEncapsulationDTO buildDto = OperationLogEncapsulationDTO.builder()
                    .ocIdList(dto.getOcIdList())
                    .homeFlag(onlineTopic.getHomeFlag())
                    .operationName(OperationEnum.ACTIVE.getOperationName())
                    .userId(userVO.getUserId())
                    .build();
            onlineTopicService.saveOperationLog(buildDto);

            return Result.success(i);
        }
        return Result.failed(ResultCode.CommonResultCode.UPDATE_FAILED);
    }

    /**
     * 检验文章的必填项完整性
     * @param ocIds
     * @return
     */
    private Boolean batchUpdateStatusCheck(List<Integer> ocIds) {

        for (Integer ocId : ocIds) {
            OnlineContent onlineContent = baseMapper.selectById(ocId);
            if (onlineContent.getTopicId().equals(-1)
                    || StringUtils.isEmpty(onlineContent.getSubject())
                    || StringUtils.isEmpty(onlineContent.getSubjectZh())
                    || StringUtils.isEmpty(onlineContent.getContentText())
                    || StringUtils.isEmpty(onlineContent.getContentTextZh())) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Integer> contentBatchUpdateTopicId(UserVO userVO,ContentBatchUpdateTopicIdDTO dto) {
        if (dto.getOcIdList().isEmpty()) {
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }
        int i = baseMapper.batchUpdateTopicIdByIds(dto.getOcIdList(), dto.getTopicId(),userVO.getUserId(),new Date());

        // 记录日志
        Long topicId = baseMapper.getTopicId(Long.valueOf(dto.getOcIdList().get(0)));
        // 通过拖动元素的所属菜单id，去菜单表查询该文章属于 首页菜单 还是 内容页菜单
        OnlineTopic onlineTopic = onlineTopicMapper.selectById(topicId);

        OperationLogEncapsulationDTO buildDto = OperationLogEncapsulationDTO.builder()
                .ocIdList(dto.getOcIdList())
                .homeFlag(onlineTopic.getHomeFlag())
                .operationName(OperationEnum.UPDATE.getOperationName())
                .userId(userVO.getUserId())
                .build();
        onlineTopicService.saveOperationLog(buildDto);

        return Result.success(i);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Integer> contentBatchImportByCsvFile(UserVO userVO, MultipartFile csvFile) throws IOException {
        // 文件是否为空
        String fileName = csvFile.getOriginalFilename();
        if (csvFile.isEmpty() || StringUtils.isEmpty(fileName)) {
            return Result.failed("3003", FILE_WRONG_TYPE);
        }
        // 后缀是否合法
        fileName=fileName.trim();
        if (!fileName.endsWith("csv")) {
            return Result.failed("3001", FILE_WRONG_TYPE);
        }
        // 编码是否为 GBK
        String fileCharset = getFileCharset(csvFile.getInputStream());
        if (!fileCharset.equalsIgnoreCase("GBK")) {
            return Result.failed("3002",FILE_WRONG_TYPE);
        }

        List<Integer> errorLine = new ArrayList<>();
        List<ContentBatchImportDTO> contentBatchImportDTOList= new ArrayList<>();
        try (CSVReader csvReader = new CSVReader(new InputStreamReader(csvFile.getInputStream(),"GBK"))) {
            String[] titles = csvReader.readNext(); // 先读标题
            if (titles.length != 4 || !titles[0].equalsIgnoreCase("CN Article Title")
                    || !titles[1].equalsIgnoreCase("EN Article Title")
                    || !titles[2].equalsIgnoreCase("CN Article Content")
                    || !titles[3].equalsIgnoreCase("EN Article Content")) {
                errorLine.add(1);
            }

            // 定义每行读到的字符串数组
            String[] strArr = null;
            // 行数
            int index = 2;
            // 开始循环读取
            while((strArr =csvReader.readNext())!=null)
            {
                // 获取 csv 每一行的数据并校验是否符合原有规定
                ContentBatchImportDTO csvData = getCsvData(strArr, errorLine, index);
                contentBatchImportDTOList.add(csvData);
                index++;
            }
        } catch (IOException e) {
            log.error("csv File upload error,{}", e);
            return Result.failed();
        }
        if (!errorLine.isEmpty()) {
            return Result.failed("3004",errorLine.toString());
        }

        // 导入数据
        if (!contentBatchImportDTOList.isEmpty()) {
            int i = baseMapper.batchInsertOnlineContent(contentBatchImportDTOList, userVO.getUserId(), new Date());

            // 记录日志
            OperationLogEncapsulationDTO buildDto = OperationLogEncapsulationDTO.builder()
                    .operationName(OperationEnum.CREATE.getOperationName())
                    .userId(userVO.getUserId())
                    .build();
            onlineTopicService.saveOperationLog(buildDto);


            return Result.success(i);
        }
        return Result.failed("3005",FILE_WRONG_TYPE);
    }

    @Override
    public Result<PageResult<OnlineContentSCVO>> selectContentForAdmin(TopicCommonPageDTO topicCommonPageDTO) {
        IPage<OnlineContent> contentIPage = baseMapper.selectContentPageList(new Page<>(topicCommonPageDTO.getPageNum(), topicCommonPageDTO.getPageSize()), topicCommonPageDTO.getSourceCode());
        if (CollectionUtils.isEmpty(contentIPage.getRecords())) {
            return null;
        }

        PageResult<OnlineContentSCVO> pageResult = new PageResult<>();
        List<OnlineContent> onlineContents = contentIPage.getRecords();
        if (CollectionUtils.isNotEmpty(onlineContents)) {
            Map<Long, OnlineTopic> topicMap = getTopicMap(topicCommonPageDTO.getSourceCode());
            List<OnlineContentSCVO> contentSCVOS = OrikaMapperUtil.coverList(onlineContents, OnlineContentSCVO.class);
            if (CollectionUtils.isNotEmpty(contentSCVOS)) {
                contentSCVOS.forEach(item -> {
                    OnlineContentTopicVO contentTopicVO = new OnlineContentTopicVO();
                    contentTopicVO.setTopicId(item.getTopicId());
                    buildParentTopic(contentTopicVO, topicMap);
                    item.setContentTopicVO(contentTopicVO);
                    Integer homepageTopicId = item.getHomepageTopicId();
                    if (Objects.nonNull(homepageTopicId)) {
                        OnlineTopic homePageTopic = onlineTopicMapper.selectById(homepageTopicId);
                        item.setHomepageTopicName(homePageTopic.getTopic());
                    }
                });
            } else {
                contentSCVOS = Collections.emptyList();
            }

            pageResult.setList(contentSCVOS);
            pageResult.setTotal(contentIPage.getTotal());
            pageResult.setPageNum(contentIPage.getCurrent());
            pageResult.setPageSize(contentIPage.getSize());
            pageResult.setTotalPage((long) PageUtil.totalPage((int) contentIPage.getTotal(), (int) contentIPage.getSize()));
        }
        return Result.success(pageResult);
    }

    /**
     * 获取文件编码
     * @param ins
     * @return
     */
    private String getFileCharset(InputStream ins) {
        BufferedInputStream bin = new BufferedInputStream(ins);
        int p = 0;
        try {
            p = (bin.read() << 8) + bin.read();
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
        String code = null;

        switch (p) {
            case 0xefbb:
                code = "UTF-8";
                break;
            case 0xfffe:
                code = "Unicode";
                break;
            case 0xfeff:
                code = "UTF-16BE";
                break;
            default:
                code = "GBK";
        }
        return code;
    }


    /**
     * 获取 csv 每一行的数据并校验是否符合原有规定
     * @param csvLineData 每行数据，csv 模板文件中只有四列数据
     * @param errorLine 出现错误的行数
     * @param index 行数
     * @return 批量新增的数据
     */
    private ContentBatchImportDTO getCsvData(String[] csvLineData, List<Integer> errorLine, int index){
        String subject = csvLineData[1];
        String subjectZh = csvLineData[0];
        String contentText = csvLineData[3];
        String contentTextZh = csvLineData[2];

        // 不能为空，字符限制（中文标题 100个字符，英文标题 200个字符）。英文标题中不允许有中文字符
        if (csvLineData.length != 4 || subject.isEmpty()
                || subjectZh.isEmpty()
                || contentText.isEmpty()
                || contentTextZh.isEmpty()
                || subject.length() > 200
                || subjectZh.length() > 100
                || Boolean.TRUE.equals(isContainChinese(subject))) {
            errorLine.add(index);
        }

        if (!contentText.contains("<p>")) {
            contentText = "<p>" + contentText + "</p>";
        }

        if (!contentTextZh.contains("<p>")) {
            contentTextZh = "<p>" + contentTextZh + "</p>";
        }
        contentText = contentText.replaceAll("(\\r\\n|\\n|\\n\\r)","<br/>");

        contentTextZh = contentTextZh.replaceAll("(\\r\\n|\\n|\\n\\r)","<br/>");

        ContentBatchImportDTO contentBatchImportDTO = new ContentBatchImportDTO();
        contentBatchImportDTO.setSubject(subject);
        contentBatchImportDTO.setSubjectZh(subjectZh);
        contentBatchImportDTO.setContentText(contentText);
        contentBatchImportDTO.setContentTextZh(contentTextZh);

        return contentBatchImportDTO;
    }

    /**
     * 根据 Unicode编码 判断中文汉字和符号
     * @param c
     * @return
     */
    private static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION;
    }

    /**
     * 判断是否包含中文字符和符号
     * @param strName
     * @return
     */
    public static boolean isContainChinese(String strName) {
        char[] ch = strName.toCharArray();
        for (int i = 0; i < ch.length; i++) {
            char c = ch[i];
            if (isChinese(c)) {
                return true;
            }
        }
        return false;
    }


    /******************************** 前台相关  ********************************/

    /**
     * 前台：获取指定菜单下的所有文章，包含该菜单下的子菜单的文章，且文章顺序按菜单顺序排序 --分页返回  `内容页面所有文章`
     * @param dto
     * @return
     */
    @Override
    public Result<PageResult<BffContentListVO>> selectAllContentBff(TopicContentCommonPageDTO dto) {

        // 调用前台内容页菜单接口 -- 拿到所有菜单
        Result<List<BffMenuListVO>> listResult = onlineTopicService.selectAllMenuBff(dto.getTopicId(), dto.getLang(), dto.getSourceCode(), dto.getHomeFlag());

        List<Long> allTopicId = new ArrayList<>();

        if (listResult.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
            // 获取所有菜单树结构
            List<BffMenuListVO> allMenu = listResult.getData();

            // 递归遍历拿到菜单树结构顺序的id数组
            allMenu.stream().forEach(bffMenuListVO -> {
                if (bffMenuListVO.getId().equals(dto.getTopicId())) {
                    List<Long> temp = new ArrayList<>();
                    temp.add(bffMenuListVO.getId());
                    allTopicId.addAll(temp);
                    allTopicId.addAll(getChildrenIds(bffMenuListVO.getChildren()));
                }
            });

            List<BffContentListVO> result = new ArrayList<>();

            // 遍历菜单树结构顺序的id数组，依次拿到该分类下的所有文章内容
            allTopicId.forEach(id -> {
                // 先拿到当前 topicId(所属分类id) 下的所有文章
                List<OnlineContent> onlineContents = getOnlineContentsByTopicId(id);

                // 将文章对象转换为 BffContentListVO 对象
                List<BffContentListVO> bffContentListVOS = convertBffContentListVO(onlineContents, dto.getLang());

                result.addAll(bffContentListVOS);
            });

            resetContentDisplayPosition(result, dto.getOcId());

            // 开始处理分页
            PageResult<BffContentListVO> pageResult = new PageResult<>();

            // 取得总页数
            int totalPage = PageUtil.totalPage(result.size(), dto.getPageSize());
            // 0,1 为第一页
            PageUtil.setFirstPageNo(1);
            // 获取起始位置
            int start = PageUtil.getStart(dto.getPageNum(), dto.getPageSize());
            // 过滤
            List<BffContentListVO> currentList = result.stream().skip(start).limit(dto.getPageSize()).collect(Collectors.toList());
            Map<Long, OnlineTopic> topicMap = getTopicMap(dto.getSourceCode());
            currentList.forEach(content -> {
                OnlineContentTopicVO contentTopicVO = new OnlineContentTopicVO();
                contentTopicVO.setTopicId(content.getPid());
                buildParentTopic(contentTopicVO, topicMap);
                content.setContentTopicVO(contentTopicVO);
            });

            pageResult.setPageNum(Long.valueOf(dto.getPageNum()));
            pageResult.setPageSize(Long.valueOf(dto.getPageSize()));
            pageResult.setTotal(Long.valueOf(result.size()));
            pageResult.setTotalPage((long) totalPage);
            pageResult.setList(currentList);
            return Result.success(pageResult);
        } else {
            return Result.failed(ResultCode.CommonResultCode.REQUIRED_REQUEST_PARAMETERS_ARE_MISSING);
        }

    }

    private void resetContentDisplayPosition(List<BffContentListVO> contentListVOS, Integer ocId) {
        //若指定文章查看，将文章置于第一条
        if (CollectionUtils.isNotEmpty(contentListVOS) && Objects.nonNull(ocId)) {
            BffContentListVO listVO = contentListVOS.stream().filter(item -> ocId.equals(item.getId())).findFirst().orElse(null);
            ArrayListUtil.moveToFirst(contentListVOS, listVO);
        }
    }

    /**
     * 递归遍历拿到所有分类id下的所有子分类id
     * @param all
     * @return
     */
    private List<Long> getChildrenIds(List<BffMenuListVO> all) {

        List<Long> allId = new ArrayList<>();
        all.stream().forEach(bffMenuListVO -> {
            allId.add(bffMenuListVO.getId());
            allId.addAll(getChildrenIds( bffMenuListVO.getChildren()));
        });
        return allId;
    }

    /**
     * 前台：首页接口 拿到首页菜单下的所有菜单与菜单下的文章
     * @param lang
     * @param sourceCode
     * @param homeFlag
     * @return
     */
    @Override
    public Result<List<BffHomepagetListVO>> selectHomepageListBff(String lang, String sourceCode, Boolean homeFlag) {

        // 拿到首页菜单id
        OnlineTopic onlineTopic = onlineTopicMapper.selectOne(Wrappers.lambdaQuery(OnlineTopic.class)
                .eq(OnlineTopic::getHomeFlag, homeFlag)
                .eq(OnlineTopic::getSourceCode, sourceCode)
                .eq(OnlineTopic::getTopicPid, 0L)
                .orderByAsc(OnlineTopic::getDisplaySeq).last("limit 1"));
        if (Objects.isNull(onlineTopic)) {
            return Result.failed(ResultCode.CommonResultCode.DATA_NON_EXISTENT);
        }
        Long topicId = onlineTopic.getTopicId();

        // 拿到所有菜单
        List<OnlineTopicSCVO> onlineTopicSCVOS = onlineTopicMapper.selectAll(homeFlag, sourceCode);

        if (Objects.isNull(onlineTopicSCVOS)) {
            return Result.failed(ResultCode.CommonResultCode.DATA_NON_EXISTENT);
        }

        // 转化为 BffHomepagetListVO 对象
        List<BffHomepagetListVO> newBffHomepagetListVO = onlineTopicSCVOS.stream().map(onlineTopicSCVO -> {
            BffHomepagetListVO temp = new BffHomepagetListVO();
            temp.setTopicId(onlineTopicSCVO.getTopicId());
            temp.setTopicPId(onlineTopicSCVO.getTopicPid());
            if (HelpCenterConstant.Lang.ZH.getName().equals(lang)) {
                temp.setName(onlineTopicSCVO.getTopicZh());
            } else {
                temp.setName(onlineTopicSCVO.getTopic());
            }
            temp.setQuickNavigationFlag(onlineTopicSCVO.getQuickNavigationFlag());
            return temp;

        }).collect(Collectors.toList());
        // 递归生成树结构 BffHomepagetListVO 对象
        List<BffHomepagetListVO> test = recursionListGetTreeHomepageBff(topicId, newBffHomepagetListVO, lang);
        return Result.success(test);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> switchContentStatus(UserVO userVO,Integer ocId) {
        OnlineContent onlineContent = baseMapper.selectById(ocId);
        if (Objects.nonNull(onlineContent)) {
            Integer integer = baseMapper.switchContentStatus(ocId, onlineContent.getDisplayFlag());


            // 记录日志
            // 通过拖动元素的所属菜单id，去菜单表查询该文章属于 首页菜单 还是 内容页菜单
            OnlineTopic onlineTopic = onlineTopicMapper.selectById(onlineContent.getTopicId());

            String operationName = "";
            if (onlineContent.getDisplayFlag().equals(Boolean.TRUE)) {
                operationName = OperationEnum.INACTIVE.getOperationName();
            } else {
                operationName = OperationEnum.ACTIVE.getOperationName();
            }

            OperationLogEncapsulationDTO buildDto = OperationLogEncapsulationDTO.builder()
                    .ocId(Long.valueOf(ocId))
                    .homeFlag(onlineTopic.getHomeFlag())
                    .operationName(operationName)
                    .userId(userVO.getUserId())
                    .build();
            onlineTopicService.saveOperationLog(buildDto);

            return Result.success(integer>0);
        }
        return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
    }

    /**
     * 前台内容页文章详情
     * @param ocId
     * @param lang
     * @return
     */
    @Override
    public Result<BffOnlineContentVO> contentDetailsBff(Integer ocId, String lang) {
        OnlineContent onlineContent = baseMapper.selectById(ocId);
        if (Objects.nonNull(onlineContent)) {

            BffOnlineContentVO result = OrikaMapperUtil.coverObject(onlineContent, BffOnlineContentVO.class);

            if (HelpCenterConstant.Lang.ZH.getName().equals(lang)) {
                result.setSubject(onlineContent.getSubjectZh());
                result.setContentText(onlineContent.getContentTextZh());
                result.setWebUrl(onlineContent.getWebUrlZh());
            }

            OnlineContentTopicVO contentTopicVO = new OnlineContentTopicVO();
            contentTopicVO.setTopicId(result.getTopicId());
            buildParentTopic(contentTopicVO);
            result.setContentTopicVO(contentTopicVO);

            Long topId = onlineTopicService.recursionGetTopId(Long.valueOf(result.getTopicId()));
            result.setTopId(topId.intValue());
            return Result.success(result);
        }
        return Result.failed(ResultCode.CommonResultCode.DATA_NON_EXISTENT);
    }

    /**
     * 前台：递归获取菜单下的子菜单和所有文章，且文章顺序按菜单顺序排序 --  前台首页接口
     * @param topicPid                  菜单父id
     * @param bffHomepagetListVOList    所有菜单
     * @param lang                      语言类型
     * @return 返回菜单树，以及该菜单下的所有文章
     */
    private List<BffHomepagetListVO> recursionListGetTreeHomepageBff(Long topicPid, List<BffHomepagetListVO> bffHomepagetListVOList,String lang) {
        List<BffHomepagetListVO> result = new ArrayList<>();
        for (BffHomepagetListVO bffHomepagetListVO : bffHomepagetListVOList) {
            // 定义出口 每个元素的pid = 传入的pid 则添加
            if (bffHomepagetListVO.getTopicPId().equals(topicPid)) {

                // 所有菜单过滤掉 每个元素的pid = 传入的pid
                List<BffHomepagetListVO> newBffHomepagetListVOList = bffHomepagetListVOList.stream()
                        .filter(bffHomepagetListVO1 -> !bffHomepagetListVO1.getTopicPId().equals(topicPid))
                        .collect(Collectors.toList());
                // 获取子菜单
                bffHomepagetListVO.setChildren(recursionListGetTreeHomepageBff(bffHomepagetListVO.getTopicId(), newBffHomepagetListVOList,lang));

                // 拿到当前 topicId(所属分类id) 下的所有文章
                List<OnlineContent> onlineContents = getOnlineContentsByTopicId(bffHomepagetListVO.getTopicId());

                // 将拿到的文章转化为 BffChildrenOnlineContentVO 对象
                List<BffChildrenOnlineContentVO> bffChildrenOnlineContentVOList = convertBffChildrenOnlineContentVO(onlineContents, lang);

                // 设置 BffChildrenOnlineContentVOS 属性
                bffHomepagetListVO.setBffChildrenOnlineContentVOS(bffChildrenOnlineContentVOList);
                result.add(bffHomepagetListVO);
            }
        }
        return result;
    }

    /**
     * 拿到指定分类id下的所有文章
     * @param topicId   分类id
     * @return          排好顺序的文章列表
     */
    private List<OnlineContent> getOnlineContentsByTopicId(Long topicId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(OnlineContent.class)
                .eq(OnlineContent::getTopicId, topicId)
                .eq(OnlineContent::getDisplayFlag, Boolean.TRUE)
                .eq(OnlineContent::getDeleteFlag, Boolean.FALSE)
                .orderByAsc(OnlineContent::getDisplaySeq));
    }

    /**
     * 前台：将拿到的文章(OnlineContent)转化为 BffContentListVO 对象
     * BffContentListVO 对象会被用于前台文章列表
     * @param onlineContents
     * @param lang
     * @return
     */
    private List<BffContentListVO> convertBffContentListVO(List<OnlineContent> onlineContents, String lang) {
        return onlineContents.stream().map(onlineContent -> {
            BffContentListVO bffContentListVO = new BffContentListVO();

            bffContentListVO.setId(onlineContent.getOcId());
            bffContentListVO.setPid(onlineContent.getTopicId());

            if (HelpCenterConstant.Lang.ZH.getName().equals(lang)) {
                bffContentListVO.setName(onlineContent.getSubjectZh());
            } else {
                bffContentListVO.setName(onlineContent.getSubject());
            }

            return bffContentListVO;
        }).collect(Collectors.toList());
    }

    /**
     * 前台：将拿到的文章(OnlineContent)转化为 BffChildrenOnlineContentVO 对象
     * @param onlineContents
     * @param lang
     * @return
     */
    private List<BffChildrenOnlineContentVO> convertBffChildrenOnlineContentVO(List<OnlineContent> onlineContents,String lang) {
        return onlineContents.stream().map(onlineContent -> {
            BffChildrenOnlineContentVO bffChildrenOnlineContentVO = new BffChildrenOnlineContentVO();

            bffChildrenOnlineContentVO.setId(onlineContent.getOcId());

            if (HelpCenterConstant.Lang.ZH.getName().equals(lang)) {
                bffChildrenOnlineContentVO.setName(onlineContent.getSubjectZh());
                bffChildrenOnlineContentVO.setWebUrl(onlineContent.getWebUrlZh());
            } else {
                bffChildrenOnlineContentVO.setName(onlineContent.getSubject());
                bffChildrenOnlineContentVO.setWebUrl(onlineContent.getWebUrl());
            }

            bffChildrenOnlineContentVO.setImageUrl(onlineContent.getImageUrl());

            return bffChildrenOnlineContentVO;
        }).collect(Collectors.toList());
    }


    /******************************** 拖拽排序相关   ********************************/

    /**
     * 拖拽排序
     * @param sortDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> dragSort(Long userId,SortDTO sortDTO) {
        // 注意：拖动元素一定是一个文章了了。
        // 如果数组个数有且仅有 1 个, 则一定是跨类别拖动。
        if (sortDTO.getIds().size() == 1) {
            return acrossCategoriesDragSort(Boolean.TRUE,sortDTO,userId);
        }else if (Boolean.TRUE.equals(checkAcrossCategoriesDragSort(sortDTO.getChangeId(), sortDTO.getIds()))) { // 或者分类变更了也是跨类别拖动
            return acrossCategoriesDragSort(Boolean.FALSE,sortDTO,userId);
        }
        // 至此,就是同分类下移动
        List<Long> newIdsArray = sortDTO.getIds();
        List<Long> oldIdsArray = getIdsDBSort(sortDTO.getIds());
        Integer[] weighArray = ArrayListUtil.initArray(newIdsArray.size());
        List<Long> diffIdsArray  = ArrayListUtil.arrayDiffAssoc(newIdsArray.toArray(new Long[0]), oldIdsArray.toArray(new Long[0]));

        updateDiffIdsSort(newIdsArray,diffIdsArray,weighArray);
        return Result.success(Boolean.TRUE);
    }

    /**
     * 获取指定分类下的 display_seq 的最大值
     * @param topicId
     * @return
     */
    private Integer getDisplaySeqMax(Integer topicId) {
        Integer displaySeqMax = baseMapper.getDisplaySeqMax(topicId);

        Integer displaySeq = 0;
        if (Objects.nonNull(displaySeqMax)) {
            displaySeq = displaySeqMax + 1;
        }
        return displaySeq;
    }

    /**
     * 跨类别拖动
     * @param idsNumOnlyOne 前端传入的ids 数组中个数是否只有一个
     * @param sortDTO
     */
    private Result<Boolean> acrossCategoriesDragSort(Boolean idsNumOnlyOne,SortDTO sortDTO,Long userId) {
        // 获取拖动元素的父id
        Long origalChangeIdTopicId = baseMapper.getTopicId(sortDTO.getChangeId());

        // 只要是跨分类了，就需要更新拖动元素所在的原始数组
        updateChangeIdOriginSort(sortDTO);

        Integer integer = 0;
        if (Boolean.TRUE.equals(idsNumOnlyOne)) {
            integer = baseMapper.updateAcrossCategoriesDisplaySeq(sortDTO.getChangeId(), sortDTO.getIds().get(0), 0);
        } else {

            List<Long> newIdsArray = sortDTO.getIds();
            List<Long> oldIdsArray = getIdsDBSort(sortDTO.getIds().stream()
                    .filter(id -> !id.equals(sortDTO.getChangeId()))
                    .collect(Collectors.toList())
            );
            Integer[] weighArray = ArrayListUtil.initArray(newIdsArray.size());
            List<Long> diffIdsArray  = ArrayListUtil.arrayDiffAssoc(newIdsArray.toArray(new Long[0]), oldIdsArray.toArray(new Long[0]));

            // 先更新拖动元素的分类 更新时，排序取默认值0
            Long changeIdTopicId = baseMapper.getTopicId(oldIdsArray.get(0)); // 随便从原有数组中取的一个id 就行。
            integer = baseMapper.updateAcrossCategoriesDisplaySeq(sortDTO.getChangeId(), changeIdTopicId, 0);

            // 再更新排序
            updateDiffIdsSort(newIdsArray,diffIdsArray,weighArray);
        }
        // 记录日志
        // 通过拖动元素的所属菜单id，去菜单表查询该文章属于 首页菜单 还是 内容页菜单
        OnlineTopic onlineTopic = onlineTopicMapper.selectById(origalChangeIdTopicId);
        OperationLogEncapsulationDTO buildDto = OperationLogEncapsulationDTO.builder()
                .ocId(sortDTO.getChangeId())
                .homeFlag(onlineTopic.getHomeFlag())
                .operationName(OperationEnum.UPDATE.getOperationName())
                .userId(userId)
                .build();
        onlineTopicService.saveOperationLog(buildDto);
        return Result.success(integer>0);
    }

    /**
     * 更新拖动元素所在的原始位置id排序 -- 仅用于跨分类拖动
     * @param sortDTO
     */
    private void updateChangeIdOriginSort(SortDTO sortDTO) {

        // 拿到拖动元素的原始父id，根据这个原始父id拿到原始数组
        Long topicPid = baseMapper.getTopicId(sortDTO.getChangeId());

        // 如果不为0，则不需要处理，父id就已经自带了  SourceCode 和 HomeFlag
        List<OnlineContent> onlineContents = baseMapper.selectList(Wrappers.lambdaQuery(OnlineContent.class)
                .eq(OnlineContent::getTopicId, topicPid)
                .orderByAsc(OnlineContent::getDisplaySeq)
                .select(OnlineContent::getOcId));

        // 取得原始旧数组id列表
        List<Long> beOldIdsArray = onlineContents.stream().map(onlineContent -> onlineContent.getOcId().longValue()).collect(Collectors.toList());
        // 剔除掉 changeId 得到新列表
        List<Long> beNewIdsArray = beOldIdsArray.stream().filter(id -> !id.equals(sortDTO.getChangeId())).collect(Collectors.toList());

        Integer[] weighArray = ArrayListUtil.initArray(beNewIdsArray.size());
        List<Long> diffIdsArray  = ArrayListUtil.arrayDiffAssoc(beNewIdsArray.toArray(new Long[0]), beOldIdsArray.toArray(new Long[0]));
        updateDiffIdsSort(beNewIdsArray,diffIdsArray,weighArray);

    }

    /**
     * 检查是否是跨分类拖动
     * @param changeId 被拖动的元素id
     * @param ids   拖动后，新id组成的数组
     * @return true=跨分类,false=没有跨分类
     */
    private Boolean checkAcrossCategoriesDragSort(Long changeId,List<Long> ids) {
        Long changePid = baseMapper.getTopicId(changeId);

        Optional<Long> anyId = ids.stream()
                .filter(id -> !id.equals(changeId))
                .findAny();
        if (anyId.isPresent()) {
            Long anyPid = baseMapper.getTopicId(anyId.get());
            return !changePid.equals(anyPid);
        }
        return Boolean.FALSE;
    }

    /**
     * 查找 ids 在DB中的排序,返回一个数组
     *
     * @param ids
     * @return
     */
    private List<Long> getIdsDBSort(List<Long> ids) {
        List<OnlineContent> onlineContents = baseMapper.selectList(Wrappers.lambdaQuery(OnlineContent.class)
                .in(OnlineContent::getOcId, ids)
                .orderByAsc(OnlineContent::getDisplaySeq)
                .select(OnlineContent::getOcId));
        return onlineContents.stream().map(onlineContent -> onlineContent.getOcId().longValue()).collect(Collectors.toList());
    }

    /**
     * 更改拖拽前后受影响的id数组中的 display_seq 值
     * @param newIdsArray        拖拽后生成新的顺序的id组成的数组
     * @param diffIdsArray      拖拽前后受影响的id数组
     * @param weighArray        权重数组
     */
    private void updateDiffIdsSort(List<Long> newIdsArray,List<Long> diffIdsArray,Integer[] weighArray) {
        for (Long topicId : diffIdsArray) {
            baseMapper.updateDisplaySeq(topicId, weighArray[newIdsArray.indexOf(topicId)]);
        }
    }

    @Override
    public List<BffContentListVO> getHomePageContentList(Integer homepageTopicId) {
        List<OnlineContent> contents = baseMapper.selectList(new LambdaQueryWrapper<OnlineContent>()
                .eq(OnlineContent::getHomepageTopicId, homepageTopicId)
                .eq(OnlineContent::getDeleteFlag, false)
                .eq(OnlineContent::getDisplayFlag, true)
                .orderByDesc(OnlineContent::getCreateDate)
                .last("limit 8"));
        List<BffContentListVO> listVOS = Lists.newArrayList();
        if (CollectionUtils.isEmpty(contents)) {
            return listVOS;
        }
        Map<Long, OnlineTopic> topicMap = getTopicMap(BC);
        contents.forEach(content -> {
            BffContentListVO listVO = new BffContentListVO();
            listVO.setId(content.getOcId());
            listVO.setName(content.getSubject());
            listVO.setPid(content.getTopicId());
            OnlineTopic topic3 = onlineTopicMapper.selectById(listVO.getPid());
            if (Objects.nonNull(topic3) && BC.equals(topic3.getSourceCode())) {
                OnlineContentTopicVO contentTopicVO = new OnlineContentTopicVO();
                contentTopicVO.setTopicId(content.getTopicId());
                buildParentTopic(contentTopicVO, topicMap);
                listVO.setContentTopicVO(contentTopicVO);
            }
            listVOS.add(listVO);
        });
        return listVOS;
    }
}

