package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.dao.AcRolePermissionMapper;
import com.globalsources.admin.model.pojo.AcRolePermission;
import com.globalsources.admin.service.IAcRolePermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 角色权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-28
 */
@Slf4j
@Service
public class AcRolePermissionServiceImpl extends ServiceImpl<AcRolePermissionMapper, AcRolePermission> implements IAcRolePermissionService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRolePermission(List<AcRolePermission> rolePermissionList) {
        rolePermissionList.forEach(r -> this.baseMapper.insert(r));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteRolePermission(Long roleId) {
        return this.baseMapper.deleteRolePermission(roleId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchLogicDeleteRolePermission(List<Long> ids, Long userId) {
        return this.baseMapper.batchLogicDeleteRolePermission(ids, userId, new Date()) > 0;
    }
}
