package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.dao.VideoContentReportMapper;
import com.globalsources.admin.model.pojo.VideoContentReport;
import com.globalsources.admin.service.VideoContentReportService;
import com.globalsources.core.supplier.api.feign.VideoCoreFeign;
import com.globalsources.core.supplier.api.model.dto.request.video.VideoCntQueryCoreDTO;
import com.globalsources.core.supplier.api.model.dto.request.video.VideoSuppCntQueryCoreDTO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.DateUtil;
import com.globalsources.framework.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.Date;
import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> Chen
 * @since 2021-11-02
 */
@Service
@Slf4j
public class VideoContentReportServiceImpl extends ServiceImpl<VideoContentReportMapper, VideoContentReport> implements VideoContentReportService {

    @Autowired
    private VideoCoreFeign videoCoreFeign;

    @Override
    public PageResult<VideoContentReport> getVideoContentReportPage(Long pageNum, Long pageSize) {
        Page<VideoContentReport> page = new Page<>(pageNum, pageSize);
        Page<VideoContentReport> videoContentReportPage = this.baseMapper.selectPage(page, Wrappers.lambdaQuery(VideoContentReport.class).orderByDesc(VideoContentReport::getReportDate));
        return Optional.ofNullable(videoContentReportPage).map(PageResult::restPage).orElse(new PageResult<>());
    }

    @Override
    public VideoContentReport generateVideoReport(Date startWeek) {
        Date now = new Date();
        Date date = Optional.ofNullable(startWeek).orElse(now);

        LocalDate sunDay = DateUtil.dateToLocalDateTime(date).toLocalDate().with(DayOfWeek.SUNDAY);
        if (date.getTime() < DateUtil.valueOf(sunDay.atStartOfDay()).getTime()) {
            //时间小于所在周周日
            log.info("generateVideoReport failed, 时间小于所在周周日, date: {}", date);
            return null;
        }
        LocalDate saturday = DateUtil.dateToLocalDateTime(date).toLocalDate().with(DayOfWeek.SATURDAY);
        int insertNum = 0;
        VideoContentReport bean = null;
        try {
            Result<Integer> feedCntResult = videoCoreFeign.countVideo(VideoCntQueryCoreDTO.builder().videoType("supplierAPP").endDate(DateUtil.valueOf(sunDay.atStartOfDay())).build());
            Result<Integer> prodCntResult = videoCoreFeign.countVideo(VideoCntQueryCoreDTO.builder().videoType("PROD").endDate(DateUtil.valueOf(sunDay.atStartOfDay())).build());
            Result<Integer> feedSuppCntResult = videoCoreFeign.countSupplierWithVideo(VideoSuppCntQueryCoreDTO.builder().videoType("supplierAPP").endDate(DateUtil.valueOf(sunDay.atStartOfDay())).build());
            Result<Integer> prodSuppCntResult = videoCoreFeign.countSupplierWithVideo(VideoSuppCntQueryCoreDTO.builder().videoType("PROD").endDate(DateUtil.valueOf(sunDay.atStartOfDay())).build());
            Integer feedCnt = Optional.ofNullable(ResultUtil.getData(feedCntResult, "failed to get feedCnt")).orElse(BigInteger.ZERO.intValue());
            Integer prodCnt = Optional.ofNullable(ResultUtil.getData(prodCntResult, "failed to get prodCnt")).orElse(BigInteger.ZERO.intValue());
            Integer feedSuppCnt = Optional.ofNullable(ResultUtil.getData(feedSuppCntResult, "failed to get feedSuppCnt")).orElse(BigInteger.ZERO.intValue());
            Integer prodSuppCnt = Optional.ofNullable(ResultUtil.getData(prodSuppCntResult, "failed to get prodSuppCnt")).orElse(BigInteger.ZERO.intValue());
            bean = VideoContentReport.builder()
                    .feedVideoCnt(feedCnt)
                    .productVideoCnt(prodCnt)
                    .feedVideoSuppCnt(feedSuppCnt)
                    .productVideoSuppCnt(prodSuppCnt)
                    .reportDate(DateUtil.valueOf(saturday.atStartOfDay()))
                    .createDate(new Date())
                    .createBy(0L)
                    .build();
            insertNum = this.baseMapper.insert(bean);
        } catch (Exception e) {
            log.error("failed to generateVideoReport, errorMsg: " + e.getMessage(), e);
        }
        if (insertNum > 0) {
            log.info("generateVideoReport success, report: {}", bean);
        }
        log.info("generateVideoReport end");
        return bean;
    }

}
