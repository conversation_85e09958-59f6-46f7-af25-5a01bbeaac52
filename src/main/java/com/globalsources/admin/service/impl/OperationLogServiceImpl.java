package com.globalsources.admin.service.impl;

import com.globalsources.admin.model.pojo.AcUser;
import com.globalsources.admin.service.IAcUserService;
import com.globalsources.admin.service.OperationLogService;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.supplierconsole.agg.api.constant.ApplicationCode;
import com.globalsources.supplierconsole.agg.api.constant.EntityType;
import com.globalsources.supplierconsole.agg.api.constant.OperationEnum;
import com.globalsources.supplierconsole.agg.api.log.dto.OperationLogDTO;
import com.globalsources.supplierconsole.agg.api.log.feign.OperationLogFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> Chen
 * @date 2024/10/23 21:51
 */
@Slf4j
@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Autowired
    private OperationLogFeign operationLogFeign;

    @Autowired
    private IAcUserService acUserService;

    @Override
    public void saveOperationLog(EntityType entityType, OperationEnum operation, String operationDesc, String websiteTypeListStr, String entityId, Long orgId, Long userId, HttpServletRequest request) {
        try {
            String userIp = Objects.isNull(request.getHeader("real-client-ip")) ? "127.0.0.1" : request.getHeader("real-client-ip");
            String serverName = Objects.isNull(request.getServerName()) ? "local" : request.getServerName();
            String operationSource = request.getRequestURL().toString();
            OperationLogDTO operationLogDTO = OperationLogDTO.builder().operationName(operation.getOperationName())
                    .entityType(entityType.name())
                    .operationDesc(operationDesc)
                    .orgId(orgId)
                    .appName(ApplicationCode.ADMIN_CONSOLE.name())
                    .operationDate(new Date())
                    .entityStrId(entityId)
                    .entityId(Optional.ofNullable(entityId).filter(NumberUtils::isDigits).filter(item -> item.length() <= 13).map(Long::valueOf).orElse(0L))
                    .userId(userId)
                    .websiteType(StringUtils.isEmpty(websiteTypeListStr) ? null : websiteTypeListStr)
                    .operationSource(operationSource)
                    .serverName(serverName)
                    .userIpAddr(userIp)
                    .build();
            if (Objects.nonNull(userId) && userId > 0) {
                AcUser acUser = acUserService.getUserById(userId);
                if (Objects.nonNull(acUser)) {
                    operationLogDTO.setUserEmail(acUser.getEmail());
                }
            }
            Result<Integer> integerResult = operationLogFeign.saveOperationLog(operationLogDTO);
            if (!ResultCode.CommonResultCode.SUCCESS.getCode().equals(integerResult.getCode())) {
                log.error("save operation log error, result:{}  , entity:{}", integerResult, operationLogDTO);
            }
        } catch (Exception e) {
            log.error("Failed save operation log: ", e);
        }
    }


}
