package com.globalsources.admin.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.constants.AdminConstants;
import com.globalsources.admin.constants.FieldFilterConstants;
import com.globalsources.admin.constants.SearchConstants;
import com.globalsources.admin.dao.AcHotProductMapper;
import com.globalsources.admin.model.dto.AcHotProductTempDTO;
import com.globalsources.admin.model.dto.QueryAcHotProductDTO;
import com.globalsources.admin.model.dto.recommend.AcHotProductSaveDTO;
import com.globalsources.admin.model.dto.recommend.DisplayHotProdEntityQueryDTO;
import com.globalsources.admin.model.dto.recommend.HotProdBasicParamDTO;
import com.globalsources.admin.model.dto.recommend.HotProductVideoLandingQueryDTO;
import com.globalsources.admin.model.dto.search.request.CollapseTerm;
import com.globalsources.admin.model.dto.search.request.FilterTerm;
import com.globalsources.admin.model.dto.search.request.ISearchRequest;
import com.globalsources.admin.model.dto.search.response.ISearchResponse;
import com.globalsources.admin.model.pojo.AcHotProduct;
import com.globalsources.admin.model.vo.AcHotProductDetailVO;
import com.globalsources.admin.model.vo.recommend.AcHotProductVO;
import com.globalsources.admin.service.HotProductService;
import com.globalsources.admin.service.ProductService;
import com.globalsources.admin.service.SearchService;
import com.globalsources.admin.service.SupplierService;
import com.globalsources.admin.service.TopBrandService;
import com.globalsources.agg.supplier.api.model.dto.SupplierCommonInfoDTO;
import com.globalsources.agg.supplier.api.model.dto.request.supplier.SuppCommonInfoQueryAggDTO;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.DateUtil;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.product.agg.api.dto.product.OnlineProductEntityDTO;
import com.globalsources.product.agg.api.dto.request.OnlineProdEntityQueryAggDTO;
import com.globalsources.product.agg.api.feign.NewProductFeign;
import com.globalsources.product.agg.api.vo.AggProductVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: create by Jane Liu
 * @version: v1.0
 * @description: com.globalsources.agg.admin.service.impl
 * @date:2021/10/09
 */
@Service
@Slf4j
@Validated
public class HotProductServiceImpl extends ServiceImpl<AcHotProductMapper, AcHotProduct> implements HotProductService {

    @Autowired
    private SearchService searchService;

    @Autowired
    private NewProductFeign newProductFeign;

    @Autowired
    private ProductService productService;

    @Autowired
    private TopBrandService topBrandService;

    @Autowired
    private SupplierService supplierService;

    private static final Long DEFAULT_HOT_PRODUCT_VIDEO_SIZE = 6L;

    @Override
    public List<AcHotProductDetailVO> getHotProductLandingPageList(@NotNull HotProductVideoLandingQueryDTO query, Long userId) {
        Long size = Optional.ofNullable(query.getSize()).orElse(DEFAULT_HOT_PRODUCT_VIDEO_SIZE);
        List<Long> categoryIds = query.getCategoryIds();
        Long firstCategoryId = Optional.ofNullable(categoryIds).flatMap(list -> list.stream().filter(Objects::nonNull).findFirst()).orElse(null);
        DisplayHotProdEntityQueryDTO entityQueryDTO = new DisplayHotProdEntityQueryDTO();
        BeanUtils.copyProperties(query, entityQueryDTO);
        if (AdminConstants.HotProductLocationEnum.L1P.getValue().equals(query.getLocation())) {
            entityQueryDTO.setCategoryId(firstCategoryId);
            entityQueryDTO.setVerticalCode(null);
        } else {
            entityQueryDTO.setCategoryId(null);
        }
        entityQueryDTO.setFilterFields(Lists.newArrayList(FieldFilterConstants.HotProductFiledEnum.PRODUCT_ID));
        entityQueryDTO.setRecomType("PRODUCT");
        List<AcHotProduct> acHotProducts = queryDisplayHotProductEntity(entityQueryDTO);
        List<Long> productIds = Optional.ofNullable(acHotProducts).map(list -> list.stream().filter(Objects::nonNull).map(AcHotProduct::getProductId).collect(Collectors.toList())).orElse(Lists.newArrayList());
        List<AcHotProductDetailVO> acHotProductDetailVOS = queryHotProduct(userId, productIds, Boolean.TRUE);
        Map<Long, AcHotProductDetailVO> map = Maps.newHashMap();
        Optional.ofNullable(acHotProductDetailVOS).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull)
                .forEach(vo -> map.put(vo.getProductId(), vo));
        List<AcHotProductDetailVO> result = productIds.stream().filter(Objects::nonNull).map(map::get).filter(Objects::nonNull).collect(Collectors.toList());
        Integer currentSize = result.size();
        if (currentSize < size) {
            long num = size - currentSize;
            List<Long> recomProductIds = getRecomProductIdOfHotProduct(categoryIds, (int) num, userId, productIds);
            List<AcHotProductDetailVO> recomHotProdList = queryHotProduct(userId, recomProductIds, Boolean.TRUE);
            result.addAll(recomHotProdList);
        }
        return result;
    }

    @Override
    public List<AcHotProduct> queryDisplayHotProductEntity(DisplayHotProdEntityQueryDTO query) {
        Long size = Optional.ofNullable(query.getSize()).orElse(DEFAULT_HOT_PRODUCT_VIDEO_SIZE);
        String verticalCode = query.getVerticalCode();
        Long categoryId = query.getCategoryId();
        checkVerticalAndCategory(categoryId, verticalCode);
        Date now = new Date();
        LambdaQueryWrapper<AcHotProduct> lambdaQuery = Wrappers.lambdaQuery(AcHotProduct.class)
                .eq(Objects.nonNull(categoryId) && StringUtils.isEmpty(verticalCode), AcHotProduct::getCategoryId, categoryId)
                .eq(StringUtils.isNotEmpty(verticalCode), AcHotProduct::getVerticalCode, verticalCode)
                .eq(AcHotProduct::getAppType, query.getAppType())
                .eq(AcHotProduct::getLocation, query.getLocation())
                .eq(AcHotProduct::getRecomType, query.getRecomType())
                .eq(AcHotProduct::getOnlineStatus, AdminConstants.OnlineStatus.ONLINE)
                .le(AcHotProduct::getDisplayStartDate, now)
                .ge(AcHotProduct::getDisplayEndDate, now)
                .orderByDesc(AcHotProduct::getDisplaySeq);

        List<FieldFilterConstants.HotProductFiledEnum> filterFields = query.getFilterFields();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(filterFields)) {
            List<SFunction<AcHotProduct, ?>> filterFieldFunctionList = Lists.newArrayList();
            for (FieldFilterConstants.HotProductFiledEnum field : filterFields) {
                FieldFilterConstants.HotProductFiledEnum hotProdFiledEnum = Optional.ofNullable(field).orElse(FieldFilterConstants.HotProductFiledEnum.NULL);
                switch (hotProdFiledEnum) {
                    case ORG_ID:
                        filterFieldFunctionList.add(AcHotProduct::getOrgId);
                        break;
                    case PRODUCT_ID:
                        filterFieldFunctionList.add(AcHotProduct::getProductId);
                        break;
                    case DESCRIPTION:
                        filterFieldFunctionList.add(AcHotProduct::getDescription);
                        break;
                    default:
                        break;
                }
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(filterFieldFunctionList)) {
                lambdaQuery.select(filterFieldFunctionList.toArray(new SFunction[0]));
            }
        }
        Page<AcHotProduct> page = getLimitOnlinePage(size);
        Page<AcHotProduct> acHotProductPage = this.baseMapper.selectPage(page, lambdaQuery);
        return Optional.ofNullable(acHotProductPage).map(Page::getRecords).map(list -> list.stream().filter(Objects::nonNull).collect(Collectors.toList())).orElse(Lists.newArrayList());
    }

    @Override
    public Result<PageResult<AcHotProductDetailVO>> listPage(QueryAcHotProductDTO queryAcHotProductDTO) {
        Long userId = queryAcHotProductDTO.getUserId();
        PageResult<AcHotProductDetailVO> pageResult = new PageResult<>();
        Date currentDate = new Date();
        LambdaQueryWrapper<AcHotProduct> queryWrapper = Wrappers.lambdaQuery(AcHotProduct.class);
        queryWrapper.eq(AcHotProduct::getAppType, queryAcHotProductDTO.getAppType())
                .eq(AcHotProduct::getLocation, queryAcHotProductDTO.getLocation())
                .eq(AcHotProduct::getChannel, queryAcHotProductDTO.getChannel())
                .eq(AcHotProduct::getDeleteFlag, false)
                .eq(AcHotProduct::getOnlineStatus, AdminConstants.OnlineStatus.ONLINE)
                .le(AcHotProduct::getDisplayStartDate, currentDate)
                .ge(AcHotProduct::getDisplayEndDate, currentDate).orderByAsc(AcHotProduct::getDisplaySeq);
        List<AcHotProduct> hotProductList = this.baseMapper.selectList(queryWrapper);
        List<AcHotProductDetailVO> hotProductDetailVOS = Lists.newArrayList();

        List<Long> productIds = Lists.newArrayList();
        hotProductList.stream().forEach(hotProduct -> productIds.add(hotProduct.getProductId()));
        if (CollectionUtils.isNotEmpty(productIds)) {
            hotProductDetailVOS.addAll(queryHotProduct(userId, productIds, queryAcHotProductDTO.getVideoFlag()));
        }
        hotProductDetailVOS.addAll(queryNewProduct(userId, queryAcHotProductDTO.getCategoryIds(),
                queryAcHotProductDTO.getPageNum(), queryAcHotProductDTO.getPageSize() - hotProductDetailVOS.size(), queryAcHotProductDTO.getVideoFlag()));
        pageResult.setPageNum(queryAcHotProductDTO.getPageNum());
        pageResult.setPageSize(queryAcHotProductDTO.getPageSize());
        pageResult.setTotalPage(queryAcHotProductDTO.getPageNum());
        pageResult.setTotal((long) hotProductDetailVOS.size());
        pageResult.setList(hotProductDetailVOS);
        return Result.success(pageResult);
    }

    private List<AcHotProductDetailVO> queryHotProduct(Long userId, List<Long> productIds, Boolean videoFlag) {
        try {
            //查询new 产品
            Result<List<AggProductVO>> resp = newProductFeign.queryNewProductByProductId(userId, productIds, videoFlag);
            List<AggProductVO> data = ResultUtil.getData(resp, "failed to queryNewProductByProductId, userId: " + userId + " , productIds: " + productIds + " ,videoFlag: " + videoFlag);
            data = Optional.ofNullable(data).map(list -> list.stream().filter(Objects::nonNull).collect(Collectors.toList())).orElse(Lists.newArrayList());
            PageResult<AcHotProductDetailVO> pageResult = new PageResult<>();
            //数据格式化

            if (CollectionUtils.isEmpty(data)) {
                return Lists.newArrayList();
            }
            BeanUtils.copyProperties(data, pageResult);

            //数据格式化
            return data.stream().map(e -> {
                AcHotProductDetailVO result = OrikaMapperUtil.coverObject(e, AcHotProductDetailVO.class);
                if (e.getOnlineDate() != null) {
                    result.setFirstOnlineDate(DateUtil.date2String(e.getOnlineDate(), DateUtil.DATETIMESTRING_MM_DD_EN, Locale.ENGLISH));
                }
                return result;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("query new product occur exception userId:{} productIds:{}", userId, JSON.toJSONString(productIds), e);
            return Lists.newArrayList();
        }
    }

    private List<AcHotProductDetailVO> queryNewProduct(Long userId, List<Long> categoryIds, Long pageNum,
                                                       Long pageSize, Boolean videoFlag) {
        try {
            //查询new 产品
            Result<PageResult<AggProductVO>> resp = newProductFeign.queryNewProduct(userId, categoryIds, pageNum, pageSize, videoFlag);
            if (!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("query new product occur exception userId:{} categoryIds:{},resp:{}",
                        userId, JSON.toJSONString(categoryIds), JSON.toJSONString(resp));
                return Lists.newArrayList();
            }

            //数据格式化
            PageResult<AcHotProductDetailVO> pageResult = new PageResult<>();
            BeanUtils.copyProperties(resp.getData(), pageResult);
            if (CollectionUtils.isEmpty(resp.getData().getList())) {
                return Lists.newArrayList();
            }

            //数据格式化
            return resp.getData().getList().stream().map(e -> {
                AcHotProductDetailVO result = OrikaMapperUtil.coverObject(e, AcHotProductDetailVO.class);
                if (e.getOnlineDate() != null) {
                    result.setFirstOnlineDate(DateUtil.date2String(e.getOnlineDate(), DateUtil.DATETIMESTRING_MM_DD_EN, Locale.ENGLISH));
                }
                return result;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("query new product occur exception userId:{} categoryIds:{}", userId, JSON.toJSONString(categoryIds), e);
            return Lists.newArrayList();
        }
    }

    @Override
    public boolean addHotProduct(Long userId, AcHotProductTempDTO acHotProductDTO) {
        AcHotProduct acHotProduct = new AcHotProduct();
        BeanUtils.copyProperties(acHotProductDTO, acHotProduct);
        acHotProduct.setCreateBy(userId);
        acHotProduct.setLUpdBy(userId);
        Date currentDate = new Date();
        acHotProduct.setCreateDate(currentDate);
        acHotProduct.setLUpdDate(currentDate);
        acHotProduct.setDeleteFlag(false);
        return this.baseMapper.insert(acHotProduct) > 0;
    }


    @Override
    public PageResult<AcHotProductVO> getHotProductListPage(Long pageNum, Long pageSize, String verticalCode, Long categoryId, String appType, String location, String recomType) {
        Page<AcHotProduct> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<AcHotProduct> lambdaQuery = Wrappers.lambdaQuery(AcHotProduct.class)
                .eq(Objects.nonNull(categoryId), AcHotProduct::getCategoryId, categoryId)
                .eq(StringUtils.isNotEmpty(verticalCode), AcHotProduct::getVerticalCode, verticalCode)
                .eq(AcHotProduct::getAppType, appType)
                .eq(AcHotProduct::getLocation, location)
                .eq(AcHotProduct::getRecomType, recomType)
                .orderByDesc(AcHotProduct::getDisplaySeq);
        Page<AcHotProduct> acHotProductPage = this.baseMapper.selectPage(page, lambdaQuery);
        List<AcHotProduct> records = page.getRecords();
        List<AcHotProductVO> acHotProductVoList = convertVoBatch(records);
        fillAdminVoList(recomType, acHotProductVoList);
        return PageResult.restPage(acHotProductVoList, acHotProductPage);
    }

    private void fillAdminVoList(String recomType, List<AcHotProductVO> acHotProductVoList) {
        if ("SUPPLIER".equals(recomType)) {
            List<Long> supplierIds = acHotProductVoList.stream().filter(Objects::nonNull).map(AcHotProductVO::getOrgId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(supplierIds)) {
                List<SupplierCommonInfoDTO> suppCommonInfos = supplierService.getSuppCommonInfo(SuppCommonInfoQueryAggDTO.builder().supplierIds(supplierIds).build());
                Map<Long, SupplierCommonInfoDTO> suppMap = Maps.newHashMap();
                suppCommonInfos.stream().filter(Objects::nonNull).forEach(suppCommonInfo -> suppMap.put(suppCommonInfo.getSupplierId(), suppCommonInfo));
                for (AcHotProductVO vo : acHotProductVoList) {
                    SupplierCommonInfoDTO commonInfo = suppMap.get(vo.getOrgId());
                    //需求变更，不需要在admin console 显示公司usp，只要落地页展示
                    vo.setSupplierName(Optional.ofNullable(commonInfo).map(SupplierCommonInfoDTO::getCompanyDisplayName).orElse(StringUtils.EMPTY));
                }
            }
        } else if ("PRODUCT".equals(recomType)) {
            List<Long> productIds = acHotProductVoList.stream().filter(Objects::nonNull).map(AcHotProductVO::getProductId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            OnlineProdEntityQueryAggDTO query = OnlineProdEntityQueryAggDTO.builder().productIds(productIds)
                    .filterFields(Lists.newArrayList(
                            com.globalsources.product.agg.api.constants.FieldFilterConstants.ProductFiledEnum.PRODUCT_ID,
                            com.globalsources.product.agg.api.constants.FieldFilterConstants.ProductFiledEnum.PRODUCT_NAME
                    ))
                    .build();
            List<OnlineProductEntityDTO> onlineProductEntityDTOS = productService.queryProductEntity(query);
            Map<Long, String> productNameMap = Maps.newHashMap();
            Optional.ofNullable(onlineProductEntityDTOS).orElse(Lists.newArrayList())
                    .stream().filter(Objects::nonNull).forEach(prodEntity -> productNameMap.put(prodEntity.getProductId(), prodEntity.getProductName()));
            for (AcHotProductVO vo : acHotProductVoList) {
                vo.setProductName(Optional.ofNullable(productNameMap.get(vo.getProductId())).orElse(StringUtils.EMPTY));
            }
        }
    }

    @Override
    public Boolean deleteAcHotProduct(Long id) {
        int i = this.baseMapper.deleteById(id);
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeSeq(@NotNull Long id, String operationType, Long userId) {
        AcHotProduct bean = getById(id);
        if (Objects.isNull(bean)) {
            return false;
        }
        if (!AdminConstants.OperationType.DOWN.equals(operationType) && !AdminConstants.OperationType.UP.equals(operationType)) {
            return false;
        }
        boolean upFlag = AdminConstants.OperationType.UP.equals(operationType);
        Integer currentSeq = bean.getDisplaySeq();
        String appType = bean.getAppType();
        String location = bean.getLocation();
        Long categoryId = bean.getCategoryId();
        String verticalCode = bean.getVerticalCode();
        String recomType = bean.getRecomType();
        HotProdBasicParamDTO param = HotProdBasicParamDTO.builder().appType(appType).location(location)
                .categoryId(categoryId).verticalCode(verticalCode).recomType(recomType).build();
        boolean hasDuplicateSeq = hasDuplicateSeq(bean.getHotProductId(), currentSeq, param);
        if (hasDuplicateSeq) {
            increaseSeqBatchForDuplicateSeq(bean.getHotProductId(), currentSeq, upFlag, param);
        } else {
            //seq not null
            AcHotProduct nextSeqBean = getNextSeqBean(bean.getHotProductId(), currentSeq, upFlag, param);
            //nextSeqBean = null 查不到seq更大或更小的hot product，当前记录位于头尾
            if (Objects.nonNull(nextSeqBean)) {
                Integer nextSeq = nextSeqBean.getDisplaySeq();
                bean.setDisplaySeq(nextSeq);
                nextSeqBean.setDisplaySeq(currentSeq);
                updateProductHot(bean, userId);
                updateProductHot(nextSeqBean, userId);
            }
        }
        return true;
    }

    public int increaseSeqBatchForDuplicateSeq(@NotNull Long id, @NotNull Integer currentSeq, boolean upFlag, @Valid HotProdBasicParamDTO param) {
        return this.baseMapper.increaseSeqBatchForDuplicateSeq(id, currentSeq, upFlag, param);
    }

    public boolean hasDuplicateSeq(@NotNull Long id, @NotNull Integer currentSeq,  @Valid HotProdBasicParamDTO param) {
        checkQuerySeq(param.getCategoryId(), param.getVerticalCode(), param.getLocation());
        LambdaQueryWrapper<AcHotProduct> lambdaQuery = basicLambdaQuery(param);
        lambdaQuery.select(AcHotProduct::getHotProductId)
                .ne(AcHotProduct::getHotProductId, id)
                .eq(AcHotProduct::getDisplaySeq, currentSeq);
        List<AcHotProduct> acHotProducts = this.baseMapper.selectList(lambdaQuery);
        return CollectionUtils.isNotEmpty(acHotProducts);
    }

    @Override
    public AcHotProduct getNextSeqBean(@NotNull Long id, @NotNull Integer currentSeq, boolean upFlag, @Valid HotProdBasicParamDTO param) {
        String location = param.getLocation();
        String verticalCode = param.getVerticalCode();
        Long categoryId = param.getCategoryId();
        checkQuerySeq(categoryId, verticalCode, location);
        LambdaQueryWrapper<AcHotProduct> lambdaQuery = basicLambdaQuery(param);
        lambdaQuery = lambdaQuery.ne(AcHotProduct::getHotProductId, id)
                .gt(Boolean.TRUE.equals(upFlag), AcHotProduct::getDisplaySeq, currentSeq)
                .lt(Boolean.FALSE.equals(upFlag), AcHotProduct::getDisplaySeq, currentSeq);

        if (Boolean.TRUE.equals(upFlag)) {
            lambdaQuery.orderByAsc(AcHotProduct::getDisplaySeq);
        } else {
            lambdaQuery.orderByDesc(AcHotProduct::getDisplaySeq);
        }
        Page<AcHotProduct> page = getLimitOnlinePage();
        Page<AcHotProduct> acHotProductPage = this.baseMapper.selectPage(page, lambdaQuery);
        return Optional.ofNullable(acHotProductPage).map(Page::getRecords).flatMap(list -> list.stream().filter(Objects::nonNull).findFirst()).orElse(null);
    }

    private Page<AcHotProduct> getLimitOnlinePage() {
        return getLimitOnlinePage(BigInteger.ONE.longValue());
    }

    private Page<AcHotProduct> getLimitOnlinePage(Long limit) {
        limit = Optional.ofNullable(limit).orElse(BigInteger.ONE.longValue());
        Page<AcHotProduct> page = new Page<>();
        page.setOptimizeCountSql(false);
        page.setSearchCount(false);
        page.setMaxLimit(limit);
        return page;
    }

    private LambdaQueryWrapper<AcHotProduct> basicLambdaQuery(@Valid HotProdBasicParamDTO param) {
        return Wrappers.lambdaQuery(AcHotProduct.class)
                .eq(AcHotProduct::getAppType, param.getAppType())
                .eq(AcHotProduct::getLocation, param.getLocation())
                .eq(AcHotProduct::getRecomType, param.getRecomType())
                .eq(Objects.nonNull(param.getCategoryId()), AcHotProduct::getCategoryId, param.getCategoryId())
                .eq(StringUtils.isNotEmpty(param.getVerticalCode()), AcHotProduct::getVerticalCode, param.getVerticalCode());
    }

    private LambdaQueryWrapper<AcHotProduct> basicLambdaQuery(@NotNull String appType, @NotNull String location, Long categoryId, String verticalCode, @NotNull String recomType) {
        return Wrappers.lambdaQuery(AcHotProduct.class)
                .eq(AcHotProduct::getAppType, appType)
                .eq(AcHotProduct::getLocation, location)
                .eq(AcHotProduct::getRecomType, recomType)
                .eq(Objects.nonNull(categoryId), AcHotProduct::getCategoryId, categoryId)
                .eq(StringUtils.isNotEmpty(verticalCode), AcHotProduct::getVerticalCode, verticalCode);
    }

    public Integer getMaxSeq(@NotNull String appType, @NotNull String location, Long categoryId, String verticalCode, @NotNull String recomType) {
        checkQuerySeq(categoryId, verticalCode, location);

        LambdaQueryWrapper<AcHotProduct> lambdaQuery = basicLambdaQuery(appType, location, categoryId, verticalCode, recomType);
        lambdaQuery.orderByDesc(AcHotProduct::getDisplaySeq);
        lambdaQuery.select(AcHotProduct::getDisplaySeq);

        Page<AcHotProduct> page = getLimitOnlinePage();
        Page<AcHotProduct> acHotProductPage = this.baseMapper.selectPage(page, lambdaQuery);
        Integer maxSeq = Optional.ofNullable(acHotProductPage).map(Page::getRecords).flatMap(list -> list.stream().filter(Objects::nonNull).findFirst()).map(AcHotProduct::getDisplaySeq).orElse(null);
        if (Objects.isNull(maxSeq)) {
            log.warn("hot product/ top brand max seq is null, query: " + appType + ", " + location + ", " + categoryId + ", " + verticalCode + ", " + recomType);
        }
        return maxSeq;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AcHotProductVO switchOnlineStatus(Long id, @NotNull Long userId) {
        AcHotProduct bean = getById(id);
        if (Objects.isNull(bean)) {
            return null;
        }
        AcHotProduct updateBean = new AcHotProduct();
        updateBean.setHotProductId(bean.getHotProductId());
        if (AdminConstants.OnlineStatus.OFFLINE.equals(bean.getOnlineStatus())) {
            updateBean.setOnlineStatus(AdminConstants.OnlineStatus.ONLINE);
        } else {
            updateBean.setOnlineStatus(AdminConstants.OnlineStatus.OFFLINE);
        }
        updateBean.setLUpdBy(userId);
        updateBean = updateProductHot(updateBean, userId);
        bean.setOnlineStatus(updateBean.getOnlineStatus());
        return convertVO(updateBean);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AcHotProductVO saveHotProduct(AcHotProductSaveDTO dto, @NotNull Long userId) {
        checkProductHotSaveDto(dto);
        AcHotProduct bean = new AcHotProduct();
        BeanUtils.copyProperties(dto, bean);
        Long hotProductId = dto.getHotProductId();
        bean.setLUpdBy(userId);
        if (Objects.isNull(hotProductId)) {
            bean = insertProductHot(bean, userId);
        } else {
            AcHotProduct old = getById(hotProductId);
            checkBeforeUpdate(old, bean);
            bean = updateProductHot(bean, userId);
        }
        AcHotProductVO vo = convertVO(bean);
        fillAdminVoList(dto.getRecomType(), Lists.newArrayList(vo));
        return vo;
    }

    private List<AcHotProductVO> convertVoBatch(List<AcHotProduct> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        list = list.stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<AcHotProductVO> acHotProductVoList = OrikaMapperUtil.coverList(list, AcHotProductVO.class);
        for (AcHotProductVO vo : acHotProductVoList) {
            vo.setOnlineFlag(AdminConstants.OnlineStatus.ONLINE.equals(Optional.ofNullable(vo.getOnlineStatus()).orElse(StringUtils.EMPTY)));
        }
        return acHotProductVoList;
    }

    private AcHotProductVO convertVO(AcHotProduct bean) {
        if (Objects.isNull(bean)) {
            return null;
        }
        AcHotProductVO vo = OrikaMapperUtil.coverObject(bean, AcHotProductVO.class);
        vo.setOnlineFlag(AdminConstants.OnlineStatus.ONLINE.equals(Optional.ofNullable(bean).map(AcHotProduct::getOnlineStatus).orElse(StringUtils.EMPTY)));
        return vo;
    }


    public AcHotProduct insertProductHot(AcHotProduct bean, Long userId) {
        Integer maxSeq = getMaxSeq(bean.getAppType(), bean.getLocation(), bean.getCategoryId(), bean.getVerticalCode(), bean.getRecomType());
        //当前最大的seq
        maxSeq = Optional.ofNullable(maxSeq).orElse(-1);
        maxSeq = maxSeq + 1;
        Date now = new Date();
        bean.setDisplaySeq(maxSeq);
        if ("VMP".equals(bean.getLocation())) {
            bean.setChannel(bean.getVerticalCode());
        } else if ("L1P".equals(bean.getLocation())) {
            bean.setChannel(Optional.ofNullable(bean.getCategoryId()).map(Object::toString).orElse(StringUtils.EMPTY));
        }
        bean.setCreateDate(now);
        bean.setLUpdDate(now);
        bean.setDeleteFlag(false);
        bean.setOnlineStatus("Offline");
        bean.setLUpdBy(Optional.ofNullable(userId).orElse(bean.getLUpdBy()));
        bean.setCreateBy(bean.getLUpdBy());
        this.save(bean);
        return bean;
    }

    public AcHotProduct updateProductHot(AcHotProduct bean, Long userId) {
        Date now = new Date();
        //不使用delete flag
        bean.setDeleteFlag(false);
        bean.setLUpdDate(now);
        bean.setLUpdBy(userId);
        boolean b = this.updateById(bean);
        if (!b) {
            log.warn("failed to updateProductHot, bean: " + JSON.toJSONString(bean));
        }
        return bean;
    }

    private void checkType(AcHotProductSaveDTO dto) {
        checkType(dto.getAppType(), dto.getLocation(), dto.getRecomType());
    }

    private void checkType(String appType, String location, String recomType) {
        if (!AdminConstants.HOT_PROD_APP_TYPE_SET.contains(appType)) {
            log.error("failed to checkType,  appType: {}" + appType);
            throw new BusinessException(ResultCode.AdminConsoleResultCode.HOT_PRODUCT_TYPE_VALIDATION_FAILED);
        }
        if (!AdminConstants.HOT_PROD_LOCATION_TYPE_SET.contains(location)) {
            log.error("failed to checkType,  location: {}" + location);
            throw new BusinessException(ResultCode.AdminConsoleResultCode.HOT_PRODUCT_TYPE_VALIDATION_FAILED);
        }

        if (!AdminConstants.HOT_PROD_RECOM_TYPE_SET.contains(recomType)) {
            log.error("failed to checkType,  recomType: {}" + recomType);
            throw new BusinessException(ResultCode.AdminConsoleResultCode.HOT_PRODUCT_TYPE_VALIDATION_FAILED);
        }
    }

    private void checkVerticalAndCategory(Long categoryId, String verticalCode) {
        if (Objects.isNull(categoryId) && StringUtils.isEmpty(verticalCode)) {
            log.error("failed to checkVerticalAndCategory, categoryId: {} , verticalCode: {}", categoryId, verticalCode);
            throw new BusinessException(ResultCode.AdminConsoleResultCode.HOT_PRODUCT_PARAM_LOCATION_VALIDATION_FAILED);
        }
        if (Objects.nonNull(categoryId) && StringUtils.isNotEmpty(verticalCode)) {
            log.error("failed to checkVerticalAndCategory, categoryId: {} , verticalCode: {}", categoryId, verticalCode);
            throw new BusinessException(ResultCode.AdminConsoleResultCode.HOT_PRODUCT_PARAM_LOCATION_VALIDATION_FAILED);
        }
    }

    private void checkQuerySeq(Long categoryId, String verticalCode, String location) {
        checkVerticalAndCategory(categoryId, verticalCode);
        if (AdminConstants.HotProductLocationEnum.VMP.getValue().equals(location)) {
            if (StringUtils.isEmpty(verticalCode) || Objects.nonNull(categoryId)) {
                log.error("failed to checkQuerySeq, VMP, location: {}, categoryId: {} , verticalCode: {}", location, categoryId, verticalCode);
                throw new BusinessException(ResultCode.AdminConsoleResultCode.HOT_PRODUCT_PARAM_LOCATION_VALIDATION_FAILED);
            }
        } else if (AdminConstants.HotProductLocationEnum.L1P.getValue().equals(location)) {
            if (StringUtils.isNotEmpty(verticalCode) || Objects.isNull(categoryId)) {
                log.error("failed to checkQuerySeq, L1P, location: {}, categoryId: {} , verticalCode: {}", location, categoryId, verticalCode);
                throw new BusinessException(ResultCode.AdminConsoleResultCode.HOT_PRODUCT_PARAM_LOCATION_VALIDATION_FAILED);
            }
        } else {
            log.error("failed to checkQuerySeq, location error, location: {}", location);
            throw new BusinessException(ResultCode.AdminConsoleResultCode.HOT_PRODUCT_PARAM_LOCATION_VALIDATION_FAILED);
        }
    }

    private void checkProductHotSaveDto(AcHotProductSaveDTO dto) {
        if (Objects.isNull(dto) || (Objects.isNull(dto.getProductId()) && Objects.isNull(dto.getOrgId()))) {
            log.error("checkReplyProductHotSaveDto fail productId and orgId is null,, dto: {}", dto);
            throw new BusinessException(ResultCode.AdminConsoleResultCode.HOT_PRODUCT_PARAM_VALIDATION_FAILED);
        }
        checkType(dto);
    }

    private void checkBeforeUpdate(AcHotProduct old, AcHotProduct bean) {
        if (Objects.isNull(old) || Objects.isNull(bean) || (Objects.isNull(bean.getProductId()) && Objects.isNull(bean.getOrgId()))) {
            log.error("failed to checkBeforeUpdate, old bean is null, id: {}", bean);
            throw new BusinessException(ResultCode.AdminConsoleResultCode.HOT_PRODUCT_UPDATE_FAILED);
        }
        checkType(bean.getAppType(), bean.getLocation(), bean.getRecomType());
    }


    @Override
    public List<Long> getRecomProductIdOfHotProduct(List<Long> l1CategoryIds, Integer num, Long userId, List<Long> excludeProductIds) {
        if (CollectionUtils.isEmpty(l1CategoryIds) || Objects.isNull(num)) {
            return Lists.newArrayList();
        }
        List<Long> l2Categories = topBrandService.getL2CategoriesFromUserPreference(userId);
        List<Long> productIds = searchRecomProductIdOfHotProduct(l1CategoryIds, num, l2Categories, excludeProductIds);
        if (productIds.size() < num && CollectionUtils.isNotEmpty(l2Categories)) {
            if (CollectionUtils.isNotEmpty(productIds)) {
                if (Objects.isNull(excludeProductIds)) {
                    excludeProductIds = Lists.newArrayList();
                }
                excludeProductIds.addAll(productIds);
            }
            int i = num - productIds.size();
            List<Long> l1ProductIds = searchRecomProductIdOfHotProduct(l1CategoryIds, i, null, excludeProductIds);
            productIds.addAll(l1ProductIds);
        }
        return productIds;
    }

    private List<Long> searchRecomProductIdOfHotProduct(@NotNull List<Long> l1CategoryIds, Integer num, List<Long> l2Categories, List<Long> excludeProductIds) {
        ISearchRequest iSearchRequest = new ISearchRequest();
        iSearchRequest.setPageNum(BigInteger.ONE.intValue());
        iSearchRequest.setPageSize(num);
        List<FilterTerm> filterList = topBrandService.genFilterByL2PreferenceForProductAndTopBrand(l1CategoryIds, l2Categories);
        FilterTerm videoIfFilter = FilterTerm.builder().name(SearchConstants.EsNameSpu.VIDEO_ID).termEnum(SearchConstants.RANGE).isNested(false).values(Lists.newArrayList(1, "*")).build();
        FilterTerm levelFilter = FilterTerm.builder().name(SearchConstants.EsNameSpu.SUPPLIER_LEVEL).termEnum(SearchConstants.RANGE).isNested(false).values(Lists.newArrayList(0, "*")).build();
        filterList.add(videoIfFilter);
        filterList.add(levelFilter);

        excludeProductIds = Optional.ofNullable(excludeProductIds).map(list -> list.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList())).orElse(null);
        if (CollectionUtils.isNotEmpty(excludeProductIds)) {
            List<FilterTerm> excludeFilterTerms = Lists.newArrayList(FilterTerm.builder().name(SearchConstants.EsNameSpu.PRODUCT_ID).isNested(false).termEnum(SearchConstants.COMMON).values(excludeProductIds).build());
            iSearchRequest.setExclusionFilters(excludeFilterTerms);
        }
        if (CollectionUtils.isNotEmpty(filterList)) {
            iSearchRequest.setFilters(filterList);
        }
        iSearchRequest.setCollapseTerm(CollapseTerm.builder().name(SearchConstants.EsNameSpu.ORG_ID)
                .size(BigInteger.ONE.intValue()).build());
        iSearchRequest.setSourceFields(Lists.newArrayList(SearchConstants.EsNameSpu.PRODUCT_ID));
        Result<ISearchResponse> iSearchResponseResult = searchService.genericSearchWithObjectAndBusinessType(iSearchRequest, "hot-product-video");
        if (Objects.isNull(iSearchResponseResult)) {
            log.error("searchRecomProductIdOfHotProduct, failed to genericSearchWithObject, query: " + JSON.toJSONString(iSearchRequest));
        }
        ISearchResponse data = ResultUtil.getData(iSearchResponseResult);
        return Optional.ofNullable(data).map(ISearchResponse::getData).map(list -> list.stream().filter(Objects::nonNull).map(map -> map.get(SearchConstants.EsNameSpu.PRODUCT_ID)).filter(obj -> Objects.nonNull(obj) && NumberUtil.isLong(obj.toString())).map(obj -> Long.valueOf(obj.toString())).distinct().collect(Collectors.toList())).orElse(Lists.newArrayList());
    }
}

