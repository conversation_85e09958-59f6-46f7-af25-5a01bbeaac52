package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.dao.AcMenuMapper;
import com.globalsources.admin.model.dto.route.AuthBaseDTO;
import com.globalsources.admin.model.dto.route.RouterBaseDTO;
import com.globalsources.admin.model.pojo.AcMenu;
import com.globalsources.admin.model.pojo.AcPermission;
import com.globalsources.admin.model.pojo.AcUser;
import com.globalsources.admin.model.pojo.AcUserMenuSimplePO;
import com.globalsources.admin.model.vo.AcMenuTreeBaseVO;
import com.globalsources.admin.model.vo.AcPermissionBaseVO;
import com.globalsources.admin.model.vo.MetaBaseVO;
import com.globalsources.admin.service.IAcMenuService;
import com.globalsources.admin.service.IAcPermissionService;
import com.globalsources.admin.service.IAcUserService;
import com.globalsources.framework.vo.UserVO;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> Chen
 * @since 2021-06-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AcMenuServiceImpl extends ServiceImpl<AcMenuMapper, AcMenu> implements IAcMenuService {

    private final IAcPermissionService iAcPermissionService;
    private final IAcUserService iAcUserService;

    @Override
    public boolean importMenu(@NonNull AcMenu acMenu) {
        return this.baseMapper.importMenu(acMenu) > 0;
    }

    @Override
    public boolean updateMenu(@NonNull AcMenu acMenu) {
        return this.baseMapper.updateById(acMenu) > 0;
    }

    @Override
    public List<AcMenu> getAllMenuInfo() {
        return Option.of(this.baseMapper.selectList(new LambdaQueryWrapper<AcMenu>().eq(AcMenu::getDeleteFlag, false))).getOrElse(Collections.emptyList());
    }

    private List<AcMenu> getAllMenuInfoByUser(AcUser userVO, List<AcPermission> permissionInfoList) {
        if (Objects.nonNull(userVO.getAdministratorFlag())&&userVO.getAdministratorFlag()) {
            return getAllMenuInfo();
        }
        if (CollectionUtils.isEmpty(permissionInfoList)) {
            return new ArrayList<>();
        }
        //从permission 拿出所有 menu ids
        List<Integer> menuIds = permissionInfoList.stream().map(AcPermission::getMenuId).collect(Collectors.toList());
        List<Integer> allMenuIds = new ArrayList<>();
        allMenuIds.addAll(menuIds);
        getAllParentMenuIds(menuIds,allMenuIds);
        return batchQueryMenu(allMenuIds);
    }

    private void getAllParentMenuIds(List<Integer> menuIds, List<Integer> allMenuids) {
        if (CollectionUtils.isEmpty(menuIds)) {
            return;
        }
        List<AcMenu> acMenus = batchQueryMenu(menuIds);
        List<Integer> parentAcMenuIds = acMenus.stream().map(AcMenu::getParentMenuId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(parentAcMenuIds)) {
            List<Integer> filter = parentAcMenuIds.stream().filter(integer -> !allMenuids.contains(integer)).collect(Collectors.toList());
            allMenuids.addAll(parentAcMenuIds);
            getAllParentMenuIds(filter, allMenuids);
        }

    }

    private List<AcMenu> batchQueryMenu(List<Integer> menuIds) {
        LambdaQueryWrapper<AcMenu> wrapper = new LambdaQueryWrapper<AcMenu>()
                .eq(AcMenu::getDeleteFlag, false)
                .in(AcMenu::getMenuId, menuIds);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<AcMenuTreeBaseVO> acMenuTree(UserVO userVO) {

        List<AcPermission> permissionInfoList;
        AcUser user = iAcUserService.getUserById(userVO.getUserId());
        if (Objects.nonNull(user.getAdministratorFlag())&&user.getAdministratorFlag()) {
            permissionInfoList = iAcPermissionService.getAllPermissionInfo();
        }else{
            permissionInfoList = iAcPermissionService.selectAcPermissionListByUserId(user);
        }
        List<AcMenu> allMenuInfo = this.getAllMenuInfoByUser(user,permissionInfoList);


        Map<Integer, List<AcPermissionBaseVO>> menuPermissionMap = permissionInfoList.parallelStream()
                .filter(Objects::nonNull).map(permission -> AcPermissionBaseVO.builder()
                        .permissionId(permission.getPermissionId())
                        .permissionKey(permission.getPrivilegeType())
                        .permissionName(permission.getPermissionName())
                        .menuId(permission.getMenuId()).build()).distinct()
                .collect(Collectors.groupingBy(AcPermissionBaseVO::getMenuId));

        List<AcMenuTreeBaseVO> menuWithPermission = allMenuInfo.parallelStream()
                .map(e -> AcMenuTreeBaseVO.builder()
                        .menuId(e.getMenuId())
                        .parentMenuId(e.getParentMenuId())
                        .i18nName(e.getMenuName())
                        .isMenu(e.getMenuFlag())
                        .children(new ArrayList<>())
                        .permissionList(menuPermissionMap.getOrDefault(e.getMenuId(), Collections.emptyList())).build()).collect(Collectors.toList());
        return structureMenuTree(menuWithPermission);
    }

    @Override
    public List<RouterBaseDTO> acUserMenuTree(Long userId) {
        List<AcUserMenuSimplePO> acUserMenuSimplePOS = this.baseMapper.acUserMenuTree(userId);
        List<AcMenu> allMenuInfo = this.getAllMenuInfo();
        if (CollectionUtils.isNotEmpty(acUserMenuSimplePOS)) {
            Map<Integer, List<AcUserMenuSimplePO>> menuPermissionMap = acUserMenuSimplePOS.parallelStream().filter(Objects::nonNull).collect(Collectors.groupingBy(AcUserMenuSimplePO::getMenuId));

            List<RouterBaseDTO> allMenuList = allMenuInfo.parallelStream().filter(Objects::nonNull).map(menu -> RouterBaseDTO.builder()
                    .id(menu.getMenuId())
                    .parentMenuId(menu.getParentMenuId())
                    .path(menu.getMenuPath())
                    .i18nName(menu.getMenuName())
                    .component(menu.getComponent())
                    .level(menu.getMenuLevel())
                    .meta(MetaBaseVO.builder()
                            .auth(menuPermissionMap.getOrDefault(menu.getMenuId(), Collections.emptyList()).parallelStream().map(permission -> AuthBaseDTO.builder()
                                    .id(permission.getPermissionId())
                                    .value(permission.getPrivilegeType())
                                    .i18nName(permission.getPermissionName()).build()).collect(Collectors.toList()))
                            .icon(menu.getIcon())
                            .isMenu(menu.getMenuFlag())
                            .build())
                    .children(new ArrayList<>()).build()).collect(Collectors.toList());

            Map<Integer, Integer> menuIdMap = allMenuList.parallelStream().distinct().collect(Collectors.toMap(RouterBaseDTO::getId, RouterBaseDTO::getParentMenuId));

            List<Integer> userParentMenuIdList = acUserMenuSimplePOS.parallelStream().filter(Objects::nonNull).filter(s -> !Objects.equals(s.getParentMenuId(), 0)).map(AcUserMenuSimplePO::getParentMenuId).distinct().collect(Collectors.toList());

            Set<Integer> idSet = new LinkedHashSet<>();
            for (Integer id : userParentMenuIdList) {
                Integer parentId = menuIdMap.getOrDefault(id, null);
                if (Objects.nonNull(parentId)) {
                    idSet.add(parentId);
                }
            }

            List<Integer> userMenuIdList = acUserMenuSimplePOS.parallelStream().filter(Objects::nonNull).map(AcUserMenuSimplePO::getMenuId).distinct().collect(Collectors.toList());
            List<Integer> allMenuIdList = Stream.of(userParentMenuIdList, idSet, userMenuIdList).flatMap(Collection::parallelStream).distinct().collect(Collectors.toList());

            List<RouterBaseDTO> targetRouteMenuList = allMenuList.parallelStream().filter(s -> allMenuIdList.contains(s.getId())).collect(Collectors.toList());
            return userTreeHandlerTortoise(targetRouteMenuList);
        }
        return Collections.emptyList();
    }

    private List<RouterBaseDTO> userTreeHandlerTortoise(List<RouterBaseDTO> targetRouteMenuList) {
        List<RouterBaseDTO> parentMenuList = targetRouteMenuList.parallelStream().filter(Objects::nonNull).filter(menu -> Objects.equals(menu.getParentMenuId(), 0)).collect(Collectors.toList());
        parentMenuList.forEach(parent -> parent = handlerRouteTortoise(parent, targetRouteMenuList));
        return parentMenuList;
    }

    private RouterBaseDTO handlerRouteTortoise(RouterBaseDTO parent, List<RouterBaseDTO> original) {
        original.forEach(child -> {
            if (Objects.equals(child.getParentMenuId(), parent.getId())) {
                child = handlerRouteTortoise(child, original);
                parent.getChildren().add(child);
            }
        });
        return parent;
    }

    private List<AcMenuTreeBaseVO> structureMenuTree(List<AcMenuTreeBaseVO> original) {
        List<AcMenuTreeBaseVO> parentMenuList = original.parallelStream().filter(Objects::nonNull).filter(menu -> Objects.equals(menu.getParentMenuId(), 0)).collect(Collectors.toList());
        parentMenuList.forEach(parent -> parent = handlerTortoise(parent, original));
        return parentMenuList;
    }

    private AcMenuTreeBaseVO handlerTortoise(AcMenuTreeBaseVO parent, List<AcMenuTreeBaseVO> original) {
        original.forEach(child -> {
            if (Objects.equals(child.getParentMenuId(), parent.getMenuId())) {
                child = handlerTortoise(child, original);
                if (CollectionUtils.isEmpty(child.getChildren())) {
                    parent.setIsLastMenu(Boolean.TRUE);
                    List<AcMenuTreeBaseVO> collect = child.getPermissionList().parallelStream().filter(Objects::nonNull)
                            .map(permission -> AcMenuTreeBaseVO.builder()
                                    .i18nName(permission.getPermissionName())
                                    .value(permission.getPermissionKey())
                                    .build())
                            .distinct().collect(Collectors.toList());

                    List<AcMenuTreeBaseVO> children = parent.getChildren();
                    List<AcMenuTreeBaseVO> result = Stream.of(children, collect).flatMap(Collection::parallelStream).distinct().collect(Collectors.toList());
                    parent.setChildren(result);
                } else {
                    parent.getChildren().add(child);
                }
            }
        });
        return parent;
    }
}
