package com.globalsources.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.dao.AcHotelCtaDao;
import com.globalsources.admin.dao.AcHotelDao;
import com.globalsources.admin.dao.AcHotelRoomDao;
import com.globalsources.admin.model.dto.hotel.AcHotelDTO;
import com.globalsources.admin.model.dto.hotel.HotelCtaDTO;
import com.globalsources.admin.model.dto.hotel.HotelPhaseDateDTO;
import com.globalsources.admin.model.dto.hotel.HotelRoomDTO;
import com.globalsources.admin.model.pojo.AcHotel;
import com.globalsources.admin.model.pojo.AcHotelCta;
import com.globalsources.admin.model.pojo.AcHotelRoom;
import com.globalsources.admin.service.AcHotelCtaService;
import com.globalsources.admin.service.AcHotelRoomService;
import com.globalsources.admin.service.AcHotelService;
import com.globalsources.admin.util.FileUtil;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.utils.OrikaMapperUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * Tradeshow hotels  服务实现类
 * </p>
 *
 * <AUTHOR> Li
 * @since 2023-07-24
 */
@Service
@RequiredArgsConstructor
public class AcHotelServiceImpl extends ServiceImpl<AcHotelDao, AcHotel> implements AcHotelService {

    private final FileUtil fileUtil;
    private final AcHotelDao acHotelDao;
    private final AcHotelCtaDao acHotelCtaDao;
    private final AcHotelRoomDao acHotelRoomDao;
    private final AcHotelCtaService acHotelCtaService;
    private final AcHotelRoomService acHotelRoomService;
    private final Integer [] phase = {1,2,3};

    @Override
    public PageResult<AcHotel> pageHotelList(String searchHotelName, Integer pageNum, Integer pageSize) {
        List<AcHotel> hotelIPage = acHotelDao.pageHotelList(searchHotelName);

        PageResult<AcHotel> pageResult = new PageResult<>();
        if (CollectionUtils.isNotEmpty(hotelIPage)) {
            hotelIPage = hotelIPage.stream().map(item -> {
                item.setImageUrl(fileUtil.getKeyUrl(item.getImageUrl()));
                item.setPrefix(PinyinUtil.getFirstLetter(item.getName().substring(0, 1), "").toUpperCase(Locale.ROOT));
                return item;
            }).collect(Collectors.toList());

            hotelIPage = hotelIPage.stream().sorted(Comparator.comparing(AcHotel::getPrefix)).collect(Collectors.toList());

            List<AcHotel> list = CollectionUtil.page(pageNum - 1, pageSize, hotelIPage);
            pageResult.setList(list);
            pageResult.setTotal(Long.valueOf(hotelIPage.size()));
            pageResult.setPageNum(Long.valueOf(pageNum));
            pageResult.setPageSize(Long.valueOf(pageSize));
            pageResult.setTotalPage((long)PageUtil.totalPage(hotelIPage.size(), pageSize));
        }
        return pageResult;
    }

    @Override
    public Boolean edit(AcHotelDTO acHotelDTO) {
        AcHotel acHotel = OrikaMapperUtil.coverObject(acHotelDTO, AcHotel.class);
        if (acHotelDTO.getHotelId() != null) {
            //edit
            acHotelDao.updatePhaseDate(OrikaMapperUtil.coverObject(acHotel, HotelPhaseDateDTO.class));
            return acHotelDao.update(acHotel, new LambdaQueryWrapper<AcHotel>().eq(AcHotel::getHotelId, acHotel.getHotelId())) > 0;
        }
        //save
        acHotel.setCreateDate(new Date());
        if (acHotelDao.insert(acHotel) > 0) {
            //cta 增加三条记录, room增加9条记录
            List<AcHotelCta> acHotelCtas = new ArrayList<>();
            List<AcHotelRoom> acHotelRooms = new ArrayList<>();
            for (int cta: phase) {
                acHotelCtas.add(AcHotelCta.builder().hotelId(acHotel.getHotelId()).phase(cta).build());

                acHotelRooms.add(AcHotelRoom.builder().hotelId(acHotel.getHotelId()).phase(cta).sortSeq(1).build());
                acHotelRooms.add(AcHotelRoom.builder().hotelId(acHotel.getHotelId()).phase(cta).sortSeq(2).build());
                acHotelRooms.add(AcHotelRoom.builder().hotelId(acHotel.getHotelId()).phase(cta).sortSeq(3).build());
            }

            if (CollectionUtils.isNotEmpty(acHotelCtas) && CollectionUtils.isNotEmpty(acHotelRooms)) {
                acHotelCtaService.saveBatch(acHotelCtas);
                acHotelRoomService.saveBatch(acHotelRooms);
            }
            return true;
        }
        return false;
    }

    @Override
    public HotelCtaDTO findCtaOne(Integer hotelId, Integer phase) {
        AcHotel acHotel = acHotelDao.selectOne(new LambdaQueryWrapper<AcHotel>().eq(AcHotel::getHotelId, hotelId));
        if (ObjectUtils.isEmpty(acHotel)) {
            return null;
        }

        AcHotelCta acHotelCta = acHotelCtaService.getOne(new LambdaQueryWrapper<AcHotelCta>().eq(AcHotelCta::getHotelId, hotelId).eq(AcHotelCta::getPhase, phase));
        if (ObjectUtils.isEmpty(acHotelCta)) {
            return null;
        }

        List<AcHotelRoom> acHotelRooms = acHotelRoomService.getBaseMapper().selectList(new LambdaQueryWrapper<AcHotelRoom>()
                .eq(AcHotelRoom::getHotelId, hotelId).eq(AcHotelRoom::getPhase, phase));

        if (CollectionUtils.isEmpty(acHotelRooms)) {
            return null;
        }

        HotelCtaDTO hotelCtaDTO = OrikaMapperUtil.coverObject(acHotelCta, HotelCtaDTO.class);
        hotelCtaDTO.setHotelName(acHotel.getName());
        hotelCtaDTO.setRooms(OrikaMapperUtil.coverList(acHotelRooms, HotelRoomDTO.class));
        return hotelCtaDTO;
    }

    @Override
    public Boolean editCta(HotelCtaDTO hotelCtaDTO, Long userId) {
        AcHotelCta acHotelCta = AcHotelCta.builder().cta(hotelCtaDTO.getCta()).ctaUrl(hotelCtaDTO.getCtaUrl())
                .earlyBirdCta(hotelCtaDTO.getEarlyBirdCta()).earlyBirdCtaUrl(hotelCtaDTO.getEarlyBirdCtaUrl()).build();

        List<AcHotelRoom> list = OrikaMapperUtil.coverList(hotelCtaDTO.getRooms(), AcHotelRoom.class);
        for (AcHotelRoom acHotelRoom: list) {
            acHotelRoomDao.updateHotelRoom(acHotelRoom , hotelCtaDTO.getHotelId(), hotelCtaDTO.getPhase(), acHotelRoom.getSortSeq());
        }

        AcHotel acHotel = new AcHotel();
        acHotel.setLUpdDate(new Date());
        acHotel.setLUpdBy(userId);
        acHotelDao.update(acHotel, new LambdaQueryWrapper<AcHotel>().eq(AcHotel::getHotelId, hotelCtaDTO.getHotelId()));
        return acHotelCtaDao.updateHotelCtl(acHotelCta, hotelCtaDTO.getPhase(), hotelCtaDTO.getHotelId()) > 0;
    }
}
