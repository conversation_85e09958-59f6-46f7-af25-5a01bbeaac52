package com.globalsources.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.activity.agg.api.feign.LiveActivityQueryFeign;
import com.globalsources.activity.agg.api.feign.LiveSupplierVideoFeign;
import com.globalsources.activity.agg.api.feign.TsLiveStreamFeign;
import com.globalsources.activity.agg.api.vo.*;
import com.globalsources.admin.model.vo.AcLiveActivityVO;
import com.globalsources.admin.service.LiveActivityService;
import com.globalsources.admin.service.LivestreamService;
import com.globalsources.core.supplier.api.feign.OnlineSectionMainFeign;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.CipherUtil;
import com.globalsources.video.api.feign.LiveStreamFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class LivestreamServiceImpl implements LivestreamService {
    @Resource
    private TsLiveStreamFeign liveStreamFeign;
    @Resource
    private LiveActivityService liveActivityService;
    @Resource
    private LiveActivityQueryFeign liveActivityQueryFeign;
    @Resource
    private OnlineSectionMainFeign onlineSectionMainFeign;
    @Resource
    private LiveStreamFeign rawliveStreamFeign;
    @Resource
    private LiveSupplierVideoFeign liveSupplierVideoFeign;

    @Override
    public Result<JoinLiveVO> join(String userId, Long activityId) {
        try{
            return liveStreamFeign.getJoinLiveInfo(userId,activityId);
        }catch (Exception e){
            log.error("join live activity occur exception, userId:{} ,activityId:{}", userId , activityId,e);
            return Result.error();
        }
    }

    @Override
    public Result<StartLiveVO> createChannel(Long activityId, Long userId) {
        try{
            return liveStreamFeign.initChannel(userId,activityId);
        }catch (Exception e){
            log.error("create live channel occur exception, userId:{} ,activityId:{}", userId , activityId,e);
            return Result.error();
        }
    }

    @Override
    public Result<StartLiveVO> startLive(Long activityId, Long userId,Integer width,Integer height) {
        try{
            return liveStreamFeign.startLive(userId,activityId,width,height);
        }catch (Exception e){
            log.error("start live activity occur exception, userId:{} ,activityId:{}", userId , activityId,e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> pauseLive(Long activityId, Long userId,Long replayId) {
        try {
            return liveStreamFeign.pauseLive(activityId,userId,replayId,true);
        }catch (Exception e){
            log.error("pause live activity occur exception, userId:{} ,activityId:{}, replayId:{}", userId , activityId, replayId,e);
            return Result.error();
        }
    }

    @Override
    public Result<WsReplayCtlVO> getWsReplayCtlInfo(Long activityId) {
        try {
            return liveStreamFeign.getWsReplayCtlInfo(activityId);
        }catch (Exception e){
            log.error("get workstation replay info occur exception, activityId:{}, detail:{}", activityId ,e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> playVideo(long systemUserId, Long activityId, Long replayId, Boolean trigger) {
        try {
            return liveStreamFeign.pauseLive(activityId,systemUserId,replayId,trigger);
        }catch (Exception e){
            log.error("play replay video occur exception, activityId:{}, replayId:{}, trigger:{}, detail:{}", activityId, replayId, trigger ,e.getMessage(),e);
            return Result.error();
        }

    }

    @Override
    public Result<Void> switchCdnResolution(Long activityId, Long userId, Integer width, Integer height) {
        return rawliveStreamFeign.switchCdnResolution(activityId, userId, width, height);
    }

    @Override
    public Result<Void> sendStartSignaling(Long activityId, Long userId) {
        return rawliveStreamFeign.sendStartSignaling(activityId,userId);
    }

    @Override
    public Result<Void> closeLive(Long activityId, Long userId) {
        try{
            return liveStreamFeign.stopLive(userId,activityId);
        }catch (Exception e){
            log.error("stop live activity occur exception, userId:{} ,activityId:{}", userId , activityId,e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> switchSupplier(Long userId,Long activityId, Long supplierId) {
        try {
            //查询直播活动
            Result<AcLiveActivityVO> resp= liveActivityService.getLiveActivityInfo(activityId);
            if(resp.getData()==null){
                log.error("query live activity info return fail, activityId:{},resp:{}",activityId,resp);
                return Result.failed(ResultCode.ActivityResultCode.ACTIVITY_NOT_FOUND);
            }
            Long campaignId=resp.getData().getCampaignId();

            Map<Long, TsSupplierVO> data= liveActivityService.getLiveSupplierInfo(activityId, Arrays.asList(supplierId));
            if(data==null || !data.containsKey(supplierId)){
                return Result.failed(ResultCode.ActivityResultCode.LOAD_LIVING_SUPPLIER_FAIL);
            }

            SwitchSupplierInfo switchSupplierInfo=new SwitchSupplierInfo();
            switchSupplierInfo.setSupplierInfo(data.get(supplierId));
            Result<SupplierContactInfoVO> contactResp= liveActivityQueryFeign.getSupplierMainAccountInfo(supplierId,false);
            if(!contactResp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("get live supplier contact card info return error,supplierId:{}, resp:{}",supplierId,JSON.toJSONString(contactResp));
            }else{
               switchSupplierInfo.setMainAccountInfo(contactResp.getData());
            }

            //加载供应商描述
            Result<String> suppResp= onlineSectionMainFeign.getSupplierDesc(supplierId,false);
            if(suppResp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
                switchSupplierInfo.setSupplierDesc(suppResp.getData());
            }else{
                log.warn("load supplier desc return error, supplierId:{} ,suppResp:{}",supplierId,suppResp);
            }


            //加载供应商产品信息
            if(campaignId!=null) {
                Result<List<ProductInfoVO>> productResp=liveSupplierVideoFeign.queryLiveProduct(activityId,campaignId,supplierId);
                if(productResp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
                    log.error("query live supplier product return error,campaignId:{},supplierId:{},productResp:{}",campaignId,supplierId,productResp);
                }
                switchSupplierInfo.setProductList(productResp.getData());
            }
            return liveStreamFeign.switchLiveSupplier(userId,activityId,switchSupplierInfo);
        }catch (Exception e){
            log.error("supplier switch occur exception, userId:{}, activityId:{}, supplierId:{}, detail:{}",userId, activityId, supplierId,e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> cancelSwitchSupplier(Long userId, Long activityId) {
        try {
            return liveStreamFeign.cancelSwitchSupplier(userId,activityId);
        }catch (Exception e){
            log.error("cancel supplier switch occur exception,userId:{},activityId:{},detail:{}",userId,activityId,e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public String makeAccessSign(Long activityId, Long time) {
        final String key="9dgDFyj64LMjxc";
        Map<String,String> param=new HashMap<>();
        param.put("activityId",String.valueOf(activityId));
        param.put("time",String.valueOf(time));

        return CipherUtil.getSign(key,param);
    }

    @Override
    public String makeAccessSignV1(Long activityId, Long userId, Long time) {
        final String key="9dgDFyj64LMjxc";
        Map<String,String> param=new HashMap<>();
        param.put("activityId",String.valueOf(activityId));
        param.put("userId",String.valueOf(userId));
        param.put("time",String.valueOf(time));

        return CipherUtil.getSign(key,param);
    }
}
