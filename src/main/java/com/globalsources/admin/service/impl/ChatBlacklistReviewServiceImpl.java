package com.globalsources.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.admin.adapters.BlacklistAdapter;
import com.globalsources.admin.dao.ChatBlacklistReviewMapper;
import com.globalsources.admin.dao.RfqBlackListMapper;
import com.globalsources.admin.enums.BlacklistReviewReasonEnum;
import com.globalsources.admin.enums.BlacklistReviewStatusEnum;
import com.globalsources.admin.model.dto.ReviewSearchDTO;
import com.globalsources.admin.model.pojo.ChatBlacklistReview;
import com.globalsources.admin.model.pojo.RfqBlackListPO;
import com.globalsources.admin.model.pojo.mongo.ChatBlacklist;
import com.globalsources.admin.model.pojo.mongo.ChatBlacklistStatis;
import com.globalsources.admin.model.vo.blacklist.BlacklistReviewVO;
import com.globalsources.admin.model.vo.blacklist.BuyerChatRecordVO;
import com.globalsources.admin.model.vo.blacklist.BuyerHeadInfoVO;
import com.globalsources.admin.service.ChatBlacklistReviewService;
import com.globalsources.admin.service.RfqBlackListService;
import com.globalsources.chat.dto.QueryChatHistoryDTO;
import com.globalsources.chat.feign.ChatInfoFeign;
import com.globalsources.chat.feign.ChatQueryFeign;
import com.globalsources.chat.vo.UserChatHistoryVO;
import com.globalsources.framework.constants.CoreConstants;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.BeanUtil;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.vo.UserBaseProfileVO;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.user.api.feign.UserQueryFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ChatBlacklistReviewServiceImpl implements ChatBlacklistReviewService {
    private static final String CHAT_REVIEW_SOURCE="Chat Buyer Review";
    private static final String DESC="desc";
    private static final String ASC="asc";
    private static final String CREATE_DATE="create_date";

    @Value("${chat.blacklist.maxCount:3}")
    private Integer maxBlacklistCount;
    @Resource
    private BlacklistAdapter blacklistAdapter;
    @Resource
    private RfqBlackListMapper blackListMapper;
    @Resource
    private ChatBlacklistReviewMapper reviewMapper;
    @Resource
    private UserQueryFeign userQueryFeign;
    @Resource
    private ChatQueryFeign chatQueryFeign;
    @Resource
    private ChatInfoFeign chatInfoFeign;
    @Resource
    private RfqBlackListService rfqBlackListService;

    @Override
    public Result<PageResult<BlacklistReviewVO>> getReviewList(ReviewSearchDTO dto) {

        String orderField=CREATE_DATE;
        String sort=DESC;
        if(!StringUtils.isEmpty(dto.getOrderField()) && !StringUtils.isEmpty(dto.getSort())){
            if(dto.getSort().equalsIgnoreCase(ASC)){
                orderField=dto.getOrderField();
                sort=ASC;
            }
            if(dto.getSort().equalsIgnoreCase(DESC)){
                orderField=dto.getOrderField();
                sort=DESC;
            }

            if("createDate".equals(orderField)){
                orderField=CREATE_DATE;
            }
        }

        //分页查询
        Page<ChatBlacklistReview> page = reviewMapper.selectReviewList(new Page<>(dto.getPageNum(),dto.getPageSize()), dto.getKeyword(),orderField,sort);

        PageResult<BlacklistReviewVO> pageResult=PageResult.init(page.getCurrent(),page.getSize());
        if(CollectionUtils.isEmpty(page.getRecords())){
           return Result.success(pageResult);
        }

        //批量查询黑名单数量
        List<Long> buyerIds=page.getRecords().stream().map(ChatBlacklistReview::getBuyerId).collect(Collectors.toList());
        Map<Long,Long> blacklistMap=batchQueryBlacklistCount(buyerIds);

        //组装数据
        pageResult.setTotal(page.getTotal());
        pageResult.setTotalPage(page.getPages());
        
        List<BlacklistReviewVO> list=new ArrayList<>();
        for(ChatBlacklistReview review:page.getRecords()){
            BlacklistReviewVO item=OrikaMapperUtil.coverObject(review,BlacklistReviewVO.class);
            item.setBlacklistCount(blacklistMap.get(review.getBuyerId()));
            list.add(item);
        }

        pageResult.setList(list);

        return Result.success(pageResult);
    }

    /**
     * 批量查询被拉黑的黑名单数量
     * @param buyerList
     * @return
     */
    private Map<Long,Long> batchQueryBlacklistCount(List<Long> buyerList){
        List<String> chatIds=buyerList.stream().map(e-> e+"").collect(Collectors.toList());

        List<ChatBlacklist> chatBlacklist = blacklistAdapter.queryBlacklistList(chatIds);
        if(CollectionUtils.isEmpty(chatBlacklist)){
            chatBlacklist=new ArrayList<>();
        }

        Map<Long,Long> result=new HashMap<>();
        for(Long buyerId:buyerList){
            if(chatBlacklist.isEmpty()){
                result.put(buyerId,null);
                continue;
            }

            Map<String,Long> map = chatBlacklist.stream()
                    .filter(e-> e.getBlacklistChatId().equals(buyerId+""))
                    .collect(Collectors.groupingBy(ChatBlacklist::getBlacklistChatId,Collectors.summingLong(ChatBlacklist::getCount)));

            Long count=null;
            if(map!=null){
                count=map.get(buyerId+"");
            }
            if(count==null){
                count=0L;
            }

            result.put(buyerId,count);
        }

        return result;
    }

    @Override
    public Result<PageResult<BuyerChatRecordVO>> getChatHistory(QueryChatHistoryDTO dto) {

        try {
            if(dto.getLastDay()==null){
                dto.setLastDay(30L);
            }

            Result<PageResult<UserChatHistoryVO>> chatResp = chatQueryFeign.getBuyerChatHistory(dto);
            if (!chatResp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("query chat history return error, dto:{},resp:{}", dto, chatResp);
                return Result.error();
            }

            PageResult<BuyerChatRecordVO> pageResult = new PageResult<>();
            BeanUtil.copyProperties(chatResp.getData(), pageResult);
            if(chatResp.getData()==null || CollectionUtils.isEmpty(chatResp.getData().getList())){
                return Result.success(pageResult);
            }

            //批量查询用户信息
            List<Long> recUserIds=chatResp.getData().getList().stream().map(UserChatHistoryVO::getRecipientLdapUserId).collect(Collectors.toList());
            Result<List<UserVO>> resp= userQueryFeign.findUsersByUserId(recUserIds);
            if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
                log.error("batch query user info return error, recUserIds:{}, resp:{}",recUserIds,resp);
                return Result.error();
            }

            Map<Long,String> emailMap=new HashMap<>();
            if(!CollectionUtils.isEmpty(resp.getData())){
                emailMap=resp.getData().stream().collect(Collectors.toMap(UserVO::getUserId,UserVO::getEmail,(p1,p2)->p2));
            }

            //组装数据
            List<BuyerChatRecordVO> list=new ArrayList<>();
            for(UserChatHistoryVO item:chatResp.getData().getList()){
                BuyerChatRecordVO vo=new BuyerChatRecordVO();
                vo.setMsg(item.getChatContent());
                vo.setMsgType(item.getChatContentType());
                vo.setSendDate(item.getCreateDate());
                vo.setSource(item.getSourceAppCode());
                vo.setSupplierId(item.getSupplierId());
                vo.setSupplerEmail(emailMap.get(item.getRecipientLdapUserId()));

                list.add(vo);
            }

            pageResult.setList(list);
            return Result.success(pageResult);
        }catch (Exception e){
            log.error("query buyer chat history occur exception, dto:{} ,detail:{}",dto,e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<BuyerHeadInfoVO> getBuyerInfo(Long reviewId) {
        ChatBlacklistReview review=reviewMapper.selectById(reviewId);
        if(review==null){
            log.warn("query review info return null, reviewId:{}",reviewId);
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }


        if(review.getBuyerId()==null){
            log.warn("can`t find buyerId, at blacklist list, reviewId:{}",reviewId);
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }

        Long buyerId=review.getBuyerId();
        Result<UserBaseProfileVO> resp=userQueryFeign.getUserProfile(buyerId);
        if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
            log.warn("find user profile info return error, buyerId:{}, resp:{}",buyerId,resp);
            return Result.error();
        }

        //加载用户信息
        UserBaseProfileVO userBaseProfile=resp.getData();
        if(userBaseProfile==null){
            log.error("can`t find sso user, buyerId:{}",buyerId);
            return Result.failed(ResultCode.UserResultCode.USER_NOT_FOUND);
        }

        BuyerHeadInfoVO headInfo=new BuyerHeadInfoVO();
        headInfo.setBuyerId(buyerId);
        headInfo.setEmail(review.getBuyerEmailAddr());
        headInfo.setFirstName(review.getBuyerFirstName());
        headInfo.setLastName(review.getBuyerLastName());
        headInfo.setCompanyName(userBaseProfile.getCompanyInfo().getCompanyName());
        headInfo.setAvatarUrl(userBaseProfile.getContactInfo().getAvatar());
        headInfo.setRegDate(userBaseProfile.getContactInfo().getCreateDate());

        //加载黑名单数量
        Map<Long,Long> map= batchQueryBlacklistCount(Arrays.asList(buyerId));
        headInfo.setBlacklistCount(map.get(buyerId));

        return Result.success(headInfo);
    }

    @Override
    public Result<Void> submitReviewAct(UserVO opUser,Long reviewId, Integer act, String reason) {
        ChatBlacklistReview review=reviewMapper.selectById(reviewId);
        if(review==null || (act!=1 && act!=2)){
            log.warn("query review info return null, reviewId:{}",reviewId);
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }

        //更新状态
        review.setStatus(act);
        review.setReviewReason(reason);
        reviewMapper.updateById(review);
        String reasonDesc="";
        if(!StringUtils.isEmpty(reason)) {
            BlacklistReviewReasonEnum reasonEnum=BlacklistReviewReasonEnum.valueOf(reason);
            reasonDesc=(reasonEnum==null?"": reasonEnum.getDesc());
        }

        if(act == BlacklistReviewStatusEnum.TAG_BLACKLIST.getCode()) {
            //加入至平台黑名单
            rfqBlackListService.add(review.getBuyerId(), opUser.getUserId(), opUser.getEmail(),CHAT_REVIEW_SOURCE, reasonDesc);
        }else{
            //取消24小时禁言
            chatInfoFeign.removeNoSpeaking(review.getBuyerId(),null);
        }

        //修改计数时间的起止
        ChatBlacklistStatis chatBlacklist = blacklistAdapter.getBlacklistStatisById(review.getBuyerId()+"");
        if(chatBlacklist==null){
            log.error("can`t find chat blacklist statis, buyerId:{}",review.getBuyerId());
        }else {
            chatBlacklist.setNewStartPos(chatBlacklist.getCount());
            blacklistAdapter.saveBlacklistStatis(chatBlacklist);
        }
        return Result.success();
    }

    @Override
    public ChatBlacklistReview findWaitHandleReview(Long buyerId) {
        LambdaQueryWrapper<ChatBlacklistReview> query=new LambdaQueryWrapper<>();
        query.eq(ChatBlacklistReview::getBuyerId,buyerId).eq(ChatBlacklistReview::getStatus,0);
        List<ChatBlacklistReview> list=reviewMapper.selectList(query);

        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public void addBlacklistReview(Long buyerId) {
        log.info("add blacklist review, buyerId:{}",buyerId);

        ChatBlacklistReview review=new ChatBlacklistReview();
        review.setStatus(BlacklistReviewStatusEnum.WAIT_REVIEW.getCode());
        review.setBuyerId(buyerId);

        //查询用户信息
        Result<UserVO> resp=userQueryFeign.getUserByUserId(buyerId);
        if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode()) || resp.getData()==null){
            log.error("can`t find sso user, buyerId:{},resp:{}",buyerId,resp);
        }

        UserVO user=resp.getData();
        //校验如果存在,则过滤
        if(Boolean.TRUE.equals(rfqBlackListService.isBlacklistUser(buyerId,user.getEmail()))){
            log.info("buyer already join blacklist platform, buyerId:{},email:{}",buyerId,user.getEmail());
            return;
        }

        //保存评审数据
        review.setBuyerId(user.getUserId());
        review.setBuyerFirstName(user.getFirstName());
        review.setBuyerLastName(user.getLastName());
        review.setBuyerEmailAddr(user.getEmail());
        review.setSource("chat");
        review.setReviewReason("");
        review.setCreateBy(CoreConstants.SYSTEM_USER_ID);
        review.setCreateDate(new Date());
        review.setDeleteFlag(false);
        reviewMapper.insert(review);
    }

    @Override
    public void initBlacklistSource() {
        LambdaQueryWrapper<ChatBlacklistReview> query=new LambdaQueryWrapper<>();
        query.eq(ChatBlacklistReview::getStatus,BlacklistReviewStatusEnum.TAG_BLACKLIST);
        query.eq(ChatBlacklistReview::getDeleteFlag,false);
        query.last(" limit 100");

        List<ChatBlacklistReview> list=reviewMapper.selectList(query);
        if(CollectionUtils.isEmpty(list)){
            return;
        }


        for(ChatBlacklistReview item:list){
            //同步状态
            LambdaQueryWrapper<RfqBlackListPO> bQuery=new LambdaQueryWrapper<>();
            bQuery.eq(RfqBlackListPO::getBuyerId,item.getBuyerId()).eq(RfqBlackListPO::getDeleteFlag,false);
            RfqBlackListPO blackListPO=blackListMapper.selectOne(bQuery);
            if(blackListPO==null){
                log.warn("can`t find blacklist data, item:{}",item);
                continue;
            }

            if(StringUtils.isEmpty(blackListPO.getAddReason()) || StringUtils.isEmpty(blackListPO.getAddSource())){
                continue;
            }

            blackListPO.setAddSource(CHAT_REVIEW_SOURCE);
            BlacklistReviewReasonEnum reasonEnum=BlacklistReviewReasonEnum.valueOf(item.getReviewReason());
            if(reasonEnum==null){
                log.warn("can`t find enum,item:{}",item);
                continue;
            }
            blackListPO.setAddReason(reasonEnum.getDesc());
            blackListMapper.updateById(blackListPO);
        }
    }
}
