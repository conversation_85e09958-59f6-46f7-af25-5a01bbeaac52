package com.globalsources.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.activity.agg.api.constants.LiveConstant;
import com.globalsources.activity.agg.api.enums.SuppLevelEnum;
import com.globalsources.activity.agg.api.feign.LiveProjectAggFeign;
import com.globalsources.activity.agg.api.vo.LiveStreamReportSwitchVO;
import com.globalsources.activity.core.api.feign.LiveCampaignFeign;
import com.globalsources.activity.core.api.feign.LiveProjectCoreFeign;
import com.globalsources.activity.core.api.model.LiveCampaign;
import com.globalsources.activity.core.api.model.LiveChannel;
import com.globalsources.activity.core.api.model.LiveChannelPhase;
import com.globalsources.admin.dao.LiveCampaignSuppStatisMapper;
import com.globalsources.admin.model.dto.LivestreamReportSwitchDTO;
import com.globalsources.admin.model.dto.MakeLiveCampaignReportDTO;
import com.globalsources.admin.model.dto.report.LiveCampaignSuppReport;
import com.globalsources.admin.model.vo.QueryLiveCampaignReportStatusVO;
import com.globalsources.admin.service.LiveCampaignReportService;
import com.globalsources.agg.supplier.api.feign.SupplierRoleAggFeign;
import com.globalsources.core.supplier.api.feign.TradeShowCoreFeign;
import com.globalsources.core.supplier.api.model.dto.ts.CampaignSupplierDTO;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LiveCampaignReportServiceImpl implements LiveCampaignReportService {
    private static final String SPLIT_FLAG=",";
    private static final String REPORT_STATUS_KEY="GS:ADMIN:CONSOLE:CAMPAIGN:REPORT:STATUS";
    private enum ReportStatus{
        NONE,PENDING,DOWN
    }

    private ExecutorService threadPool;

    @Resource
    private RedisTemplate<String,String> redisTemplate;
    @Resource
    private TradeShowCoreFeign tradeShowCoreFeign;
    @Resource
    private LiveCampaignFeign liveCampaignFeign;
    @Resource
    private LiveCampaignSuppStatisMapper liveCampaignSuppStatisMapper;

    private static final String APP="App";

    private static final String MOBILE="Mobile";

    private static final String DESKTOP="Desktop";

    @Resource
    private LiveProjectAggFeign liveProjectAggFeign;

    @Resource
    private SupplierRoleAggFeign supplierRoleAggFeign;

    @Resource
    private LiveProjectCoreFeign liveProjectCoreFeign;

    public LiveCampaignReportServiceImpl(){
        threadPool= Executors.newFixedThreadPool(10);
    }

    @Override
    public QueryLiveCampaignReportStatusVO queryReportStatus() {
        QueryLiveCampaignReportStatusVO status=new QueryLiveCampaignReportStatusVO();
        status.setStatus(ReportStatus.NONE.name());

        String data=redisTemplate.opsForValue().get(REPORT_STATUS_KEY);
        if(StringUtils.isEmpty(data)){
            return status;
        }

        return JSON.parseObject(data, QueryLiveCampaignReportStatusVO.class);
    }

    @Override
    public Result<Void> makeCampaignReport(MakeLiveCampaignReportDTO dto) {
        QueryLiveCampaignReportStatusVO status=queryReportStatus();
        if(status.getStatus().equals(ReportStatus.PENDING.name())){
            return Result.failed(ResultCode.CommonResultCode.REQUEST_BUSY);
        }

        //查询campaign
        Result<List<LiveCampaign>> campaignResult = liveCampaignFeign.queryList();
        if(!campaignResult.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
            log.error("query all live campaign return error, campaignResult:{}",campaignResult);
        }

        List<LiveCampaign> campaignList=campaignResult.getData();
        if(CollectionUtils.isEmpty(campaignList)){
            return Result.success();
        }

        //过滤
        List<LiveCampaign> newCampaignList;
        if(!CollectionUtils.isEmpty(dto.getCampaignList())){
            newCampaignList=campaignList.stream().filter(e-> dto.getCampaignList().contains(e.getCampaignId())).collect(Collectors.toList());
        }else{
            newCampaignList=campaignList;
        }

        //异步提交
        threadPool.submit(() -> {
            try {
                log.info("lock campaign supplier report,dto:{}",dto);
                //写入状态为pending
                updateReportStatus(dto,ReportStatus.PENDING);

                //统计所有供应商报告
                initSuppReport(dto.getStartTime(),dto.getEndTime(),newCampaignList);

                //写入状态为down
                updateReportStatus(dto,ReportStatus.DOWN);

                log.info("release lock from campaign supplier report,dto:{}",dto);
            }catch (Exception e){
                log.error("make campaign supplier report occur exception, newCampaignList:{},detail:{}",newCampaignList,e.getMessage(),e);
                //写入状态为down
                updateReportStatus(new MakeLiveCampaignReportDTO(),ReportStatus.NONE);
            }
        });

        return Result.success();
    }

    private void updateReportStatus(MakeLiveCampaignReportDTO dto,ReportStatus status){
        QueryLiveCampaignReportStatusVO data=new QueryLiveCampaignReportStatusVO();
        data.setCampaignList(dto.getCampaignList());
        data.setStatus(status.name());
        data.setStartTime(dto.getStartTime());
        data.setEndTime(dto.getEndTime());

        redisTemplate.opsForValue().set(REPORT_STATUS_KEY,JSON.toJSONString(data), Duration.ofDays(2));
    }

    private List<Long>  getCampaignSuppIdList(List<LiveCampaign> campaignList){
        if(CollectionUtils.isEmpty(campaignList)){
            return Collections.emptyList();
        }

        Long pageSize=100000L;
        Long maxPageNum=100L;

        List<Long> supplierList=new ArrayList<>(10000);
        for(LiveCampaign campaign:campaignList){
            Long pageNum=1L;
            CampaignSupplierDTO dto=makeCampaignSupplierCondition(campaign);
            while (true){
                log.info("start query live campaign supplier, pageNum:{},pageSize:{},dto:{}",pageNum,pageSize,dto);
                Result<PageResult<Long>> suppResp=tradeShowCoreFeign.statisCampaignSupplierPageIgnoreDate(pageNum,pageSize,dto);
                if(!suppResp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
                    log.error("query live campaign supplier return error, pageNum:{},pageSize:{},dto:{},resp:{}",pageNum,pageSize,dto,suppResp);
                    throw new BusinessException(ResultCode.CommonResultCode.SYSTEM_ERROR);
                }

                //最后一页为空
                if(CollectionUtils.isEmpty(suppResp.getData().getList())){
                    break;
                }

                //递增下一页
                pageNum+=1;
                if(pageNum>maxPageNum){
                    log.error("exceed max page when query live campaign supplier,pageNum:{},pageSize:{},dto:{}",pageNum,pageSize,dto);
                    throw  new BusinessException(ResultCode.CommonResultCode.SYSTEM_ERROR);
                }

                supplierList.addAll(suppResp.getData().getList());
            }
        }
        return supplierList;
    }

    @SneakyThrows
    private void initSuppReport(Date startTime, Date endTime,List<LiveCampaign> newCampaignList) {
        List<LiveCampaignSuppReport> dataList = new ArrayList<>(9999);
        //获取供应商id
        List<Long> supplierIdList = getCampaignSuppIdList(newCampaignList);
        //拿admin数据统计开关状态
        int listSize = supplierIdList.size();
        //每次处理n个
        int n = 50;
        for (int i = 0; i < listSize; i += n) {
            dataList.addAll(fillSuppReport(supplierIdList.subList(i, Math.min(i + n, listSize)), startTime, endTime));
        }
        redisTemplate.opsForValue().set(LiveConstant.REPORT_DATA_KEY,JSON.toJSONString(dataList));
    }

    public List<LiveCampaignSuppReport> fillSuppReport(List<Long> supplierIdList, Date startTime, Date endTime){
        log.info("start fill campaign supplier report data, reportsCount:{}, startTime:{},endTime:{}",supplierIdList.size(),startTime,endTime);

        List<LiveCampaignSuppReport> reports = new ArrayList<>(supplierIdList.size());
        for(Long supplierId:supplierIdList){
            LiveCampaignSuppReport report=new LiveCampaignSuppReport();
            report.setSupplierId(supplierId);
            reports.add(report);
        }
        //批量查询供应商名字
        List<LiveCampaignSuppReport> list = liveCampaignSuppStatisMapper.batchQuerySupplierName(supplierIdList);
        Map<Long,String> supplierMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(list)){
            //如果没有查到供应商则踢出
            supplierMap=list.stream().filter(e-> StringUtils.isNotEmpty(e.getSupplierName())).collect(Collectors.toMap(LiveCampaignSuppReport::getSupplierId,LiveCampaignSuppReport::getSupplierName,(p1, p2)->p2));
        }

        //批量查询交换名片数量
        list=liveCampaignSuppStatisMapper.batchQueryExchangeCardCount(supplierIdList,startTime,endTime);
        Map<Long,Long> cardMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(list)){
            cardMap=list.stream().collect(Collectors.toMap(LiveCampaignSuppReport::getSupplierId,LiveCampaignSuppReport::getCardCount));
        }

        //批量查询关注数量
        list=liveCampaignSuppStatisMapper.batchQueryFollowCount(supplierIdList,startTime,endTime);
        Map<Long,Long> followMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(list)){
            followMap=list.stream().collect(Collectors.toMap(LiveCampaignSuppReport::getSupplierId,LiveCampaignSuppReport::getFollowCount));
        }

        //批量查询聊天数量
        list=liveCampaignSuppStatisMapper.batchQueryChatCount(supplierIdList,startTime,endTime);
        Map<Long,Long> chatMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(list)){
            chatMap=list.stream().collect(Collectors.toMap(LiveCampaignSuppReport::getSupplierId,LiveCampaignSuppReport::getChatCount));
        }

        //批量查询线下扫描数量
        list=liveCampaignSuppStatisMapper.batchQueryTsScanCount(supplierIdList,startTime,endTime);
        Map<Long,Long> tsScanMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(list)){
            tsScanMap=list.stream().collect(Collectors.toMap(LiveCampaignSuppReport::getSupplierId,LiveCampaignSuppReport::getOfflineScanCount));
        }

        //填充报告数据
        Date curTime=new Date();
        for(LiveCampaignSuppReport e:reports){
            Long supplierId=e.getSupplierId();
            e.setSupplierName(supplierMap.get(supplierId));
            if(e.getSupplierName()==null){
                e.setSupplierName("");
            }
            e.setCardCount(cardMap.get(supplierId));
            if(e.getCardCount()==null){
                e.setCardCount(0L);
            }
            e.setFollowCount(followMap.get(supplierId));
            if(e.getFollowCount()==null){
                e.setFollowCount(0L);
            }
            e.setChatCount(chatMap.get(supplierId));
            if(e.getChatCount()==null){
                e.setChatCount(0L);
            }
            e.setOfflineScanCount(tsScanMap.get(supplierId));
            if(e.getOfflineScanCount()==null){
                e.setOfflineScanCount(0L);
            }

            e.setSubmitRfiCount(-1L);
            e.setPostQcRfiCount(-1L);
            e.setOnlineScanCount(-1L);

            e.setCreateDate(curTime);
        }
        return reports;
    }

    @Override
    public List<LiveCampaignSuppReport> queryCampaignSupplierReport() {
        return JSON.parseArray(redisTemplate.opsForValue().get(LiveConstant.REPORT_DATA_KEY), LiveCampaignSuppReport.class);
    }

    @Override
    public void resetReportStatus() {
        updateReportStatus(new MakeLiveCampaignReportDTO(),ReportStatus.NONE);
    }

    @Override
    public LiveStreamReportSwitchVO reportSwitch() {
        if(Boolean.FALSE.equals(redisTemplate.hasKey(LiveConstant.REPORT_SWITCH_KEY))){
            redisTemplate.opsForValue().set(LiveConstant.REPORT_SWITCH_KEY,JSON.toJSONString(LiveStreamReportSwitchVO.builder()
                    .switchList(Arrays.asList(DESKTOP,MOBILE,APP))
                    .notifySendFlag(false)
                    .build()));
        }
        LiveStreamReportSwitchVO result = JSON.parseObject(redisTemplate.opsForValue().get(LiveConstant.REPORT_SWITCH_KEY), LiveStreamReportSwitchVO.class);
        result.setNotifySendFlag(redisTemplate.hasKey(LiveConstant.REPORT_SEND_FLAG_KEY));
        return result;
    }

    @Override
    public LiveStreamReportSwitchVO reportSwitchUpdate(LivestreamReportSwitchDTO dto) {
        redisTemplate.opsForValue().set(LiveConstant.REPORT_SWITCH_KEY,JSON.toJSONString(OrikaMapperUtil.coverObject(dto, LiveStreamReportSwitchVO.class)));
        return reportSwitch();
    }

    @Override
    public Result<Boolean> reportNotifySend(Long projectId) {
        if (redisTemplate.hasKey(LiveConstant.REPORT_SEND_FLAG_KEY)){
            return Result.failed(ResultCode.CommonResultCode.FORBIDDEN);
        }
        sendReportNotify(projectId);
        Calendar calendar = Calendar.getInstance(); // 获取当前时间
        int currentHour = calendar.get(Calendar.HOUR_OF_DAY); // 获取当前小时
        int currentMinute = calendar.get(Calendar.MINUTE); // 获取当前分钟
        int minutesUntilMidnight = (23 - currentHour) * 60 + (60 - currentMinute); // 计算到午夜的分钟数
        redisTemplate.opsForValue().set(LiveConstant.REPORT_SEND_FLAG_KEY, Strings.EMPTY, minutesUntilMidnight, TimeUnit.MINUTES);
        return Result.success();
    }

    private void sendReportNotify(Long projectId) {
        LiveChannel project=ResultUtil.getData(liveProjectCoreFeign.getLiveProjectInfo(projectId));
        log.info("reportNotifySend projectId :{} project :{}",projectId,project);

        //没有开启则不统计报告
        if(Objects.isNull(project) || Boolean.FALSE.equals(project.getOpenReportFlag())){
            return ;
        }

        List<Long> supplierIdList = ResultUtil.getData(liveProjectAggFeign.getSupplierIdByProject(projectId));
        log.info("reportNotifySend projectId :{} supplierIdList :{}",projectId,supplierIdList);

        if(CollectionUtils.isEmpty(supplierIdList)){
            return ;
        }
        //异步提交
        threadPool.submit(() -> {
            //获取供应商id
            for(Long supplierId:supplierIdList){
                List<Long> userIdList =ResultUtil.getData(supplierRoleAggFeign.getUserIdsBySupplierId(supplierId,false));
                log.info("reportNotifySend supplierId :{} userIdList :{}",supplierId,userIdList);
                userIdList.forEach(id -> sendNotice(supplierId,id));
            }
        });
    }

    private List<Long> getCampaignIdList(Long projectId) {
        PageResult<LiveChannelPhase> phaseList = ResultUtil.getData(liveProjectCoreFeign.queryPhasePage(projectId,1L,999L));
        if(Objects.isNull(phaseList) || CollectionUtils.isEmpty(phaseList.getList())){
            return Collections.emptyList();
        }
        return phaseList.getList().stream().map(LiveChannelPhase::getCampaignId).map(Long::parseLong).collect(Collectors.toList());
    }

    private void sendNotice(Long supplierId,Long supplierUserId){
        redisTemplate.opsForValue().set(LiveConstant.REPORT_NOTICE_KEY+supplierId+"_"+supplierUserId, Strings.EMPTY);
    }

    private CampaignSupplierDTO makeCampaignSupplierCondition(LiveCampaign dto){
        CampaignSupplierDTO query=new CampaignSupplierDTO();
        if(StringUtils.isNotEmpty(dto.getSupplierLevel())) {
            List<String> list= Arrays.asList(dto.getSupplierLevel().split(SPLIT_FLAG));
            query.setSupplierLevels(SuppLevelEnum.getContractList(list));
        }
        if(StringUtils.isNotEmpty(dto.getCategoryId())) {
            query.setCategoryId(Arrays.asList(dto.getCategoryId().split(SPLIT_FLAG)));
        }
        if(StringUtils.isNotEmpty(dto.getSupplierType())) {
            query.setBusinessTypeList(Arrays.asList(dto.getSupplierType().split(SPLIT_FLAG)));
        }
        if(StringUtils.isNotEmpty(dto.getExhibitionContract())){
            List<Long> tsIds= parseLongList(Arrays.asList(dto.getExhibitionContract().split(SPLIT_FLAG)));
            query.setTsIds(tsIds);
        }
        return query;
    }

    private List<Long> parseLongList(List<String> strs){
        List<Long> result=new ArrayList<>();
        if(CollectionUtils.isEmpty(strs)){
            return result;
        }

        strs.forEach(entity->{
            try{
                result.add(Long.parseLong(entity));
            }catch (Exception e){
                log.info("input str is not long, entity:{}",entity);
            }
        });

        return result;
    }
}
