package com.globalsources.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.admin.model.dto.helpcenter.ContentBatchUpdateStatusDTO;
import com.globalsources.admin.model.dto.helpcenter.ContentBatchUpdateTopicIdDTO;
import com.globalsources.admin.model.dto.helpcenter.OnlineContentSaveDTO;
import com.globalsources.admin.model.dto.helpcenter.SortDTO;
import com.globalsources.admin.model.dto.helpcenter.TopicCommonPageDTO;
import com.globalsources.admin.model.dto.helpcenter.TopicContentCommonPageDTO;
import com.globalsources.admin.model.pojo.OnlineContent;
import com.globalsources.admin.model.vo.OnlineContentVO;
import com.globalsources.admin.model.vo.helpcenter.BffContentListVO;
import com.globalsources.admin.model.vo.helpcenter.BffHomepagetListVO;
import com.globalsources.admin.model.vo.helpcenter.BffOnlineContentVO;
import com.globalsources.admin.model.vo.helpcenter.OnlineContentEditVO;
import com.globalsources.admin.model.vo.helpcenter.OnlineContentSCVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> Sun
 * @date 2021/11/3
 */
public interface OnlineContentService extends IService<OnlineContent> {
    /**
     * @param topicId
     * @param pageNum
     * @param pageSize
     * @return
     */
    Result<PageResult<OnlineContentVO>> getOnlineContent(Integer topicId, Integer pageNum, Integer pageSize);

    Result<Boolean> deleteContent(Long userId,Integer ocId);

    Result<Boolean> saveHomepage(UserVO userVO, OnlineContentSaveDTO onlineContentSaveDTO);

    Result<Boolean> saveContent(UserVO userVO, OnlineContentSaveDTO onlineContentSaveDTO);

    Result<Boolean> updateHomepage(UserVO userVO, OnlineContentSaveDTO onlineContentSaveDTO);

    Result<Boolean> updateContent(UserVO userVO, OnlineContentSaveDTO onlineContentSaveDTO);

    Result<Boolean> dragSort(Long userId,SortDTO sortDTO);

    Result<PageResult<OnlineContentSCVO>> selectContent(TopicCommonPageDTO topicCommonPageDTO);

    Result<OnlineContentEditVO> contentDetails(Integer ocId);

    Result<PageResult<BffContentListVO>> selectAllContentBff(TopicContentCommonPageDTO dto);

    Result<List<BffHomepagetListVO>> selectHomepageListBff(String lang, String sourceCode, Boolean homeFlag);

    Result<Boolean> switchContentStatus(UserVO userVO,Integer ocId);

    Result<BffOnlineContentVO> contentDetailsBff(Integer ocId, String lang);

    Result<Integer> contentBatchUpdateStatus(UserVO userVO,ContentBatchUpdateStatusDTO dto);

    Result<Integer> contentBatchUpdateTopicId(UserVO userVO,ContentBatchUpdateTopicIdDTO dto);

    Result<Integer> contentBatchImportByCsvFile(UserVO userVO, MultipartFile csvFile) throws IOException;

    Result<PageResult<OnlineContentSCVO>> selectContentForAdmin(TopicCommonPageDTO topicCommonPageDTO);

    List<BffContentListVO> getHomePageContentList(Integer homepageTopicId);
}
