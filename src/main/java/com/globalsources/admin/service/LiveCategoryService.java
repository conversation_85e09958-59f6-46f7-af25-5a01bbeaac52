package com.globalsources.admin.service;

import com.globalsources.activity.agg.api.dto.LiveCategoryDTO;
import com.globalsources.activity.agg.api.dto.LiveCategoryEditDTO;
import com.globalsources.activity.agg.api.vo.LiveCatInfoVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;

public interface LiveCategoryService {
    /**
     * 添加分类
     * @param userId
     * @param dto
     * @return
     */
    Result<Void> addCategory(Long userId, LiveCategoryDTO dto);

    /**
     * 删除分类
     * @param userId
     * @param categoryId
     * @return
     */
    Result<Void> delCategory(Long userId, Long categoryId);

    /**
     * 获取分类页
     * @param pageNum
     * @param pageSize
     * @return
     */
    Result<PageResult<LiveCatInfoVO>> getCategoryPage(Long pageNum, Long pageSize);

    /**
     * 切换状态
     * @param userId
     * @param categoryId
     * @param enabled
     * @return
     */
    Result<Void> switchEnabled(Long userId, Long categoryId, boolean enabled);

    /**
     * 编辑分类
     * @param userId
     * @param dto
     * @return
     */
    Result<Void> editCategory(Long userId, LiveCategoryEditDTO dto);
}
