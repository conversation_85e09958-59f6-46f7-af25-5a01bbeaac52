package com.globalsources.admin.service;

import com.globalsources.agg.admin.api.model.dto.app.AppExceptionCountQueryDTO;
import com.globalsources.agg.admin.api.model.dto.app.AppExceptionLogDTO;
import com.globalsources.framework.result.Result;

import java.util.Map;

public interface AppExceptionLogService {
    Result<Boolean> appExceptionSave(AppExceptionLogDTO dto);

    Map<String, Object> queryAppExceptionCount(AppExceptionCountQueryDTO dto);
}
