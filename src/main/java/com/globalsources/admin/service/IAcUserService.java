package com.globalsources.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.admin.constants.UserConstant;
import com.globalsources.admin.model.dto.AcUserUpdateDTO;
import com.globalsources.admin.model.dto.UserAdminConsoleDTO;
import com.globalsources.admin.model.dto.user.AssignUserInfoDTO;
import com.globalsources.admin.model.pojo.AcUser;
import com.globalsources.admin.model.vo.AcUserDetailVO;
import com.globalsources.admin.model.vo.AcUserVO;
import com.globalsources.framework.result.Result;

import java.util.List;
import java.util.Map;


public interface IAcUserService extends IService<AcUser>{
    List<Long> getAllParentIdByUserId(Long userId);

    List<Long> getAllChildIdByUserId(Long userId);

    boolean registerUserAdmin(Long userId, Long parentId, UserAdminConsoleDTO userDTO);

    Page<AcUserVO> selectUserAccountList(Integer pageNum, Integer pageSize, Long userId, String keyword, Integer status, Long role, Integer userType);

    boolean updateStatusAcByUserId(Long userId, Integer statusAc);

    boolean updateUserFlagByUserId(Long userId, boolean status, UserConstant.AcUserFlagTypeEnum flagType);

    List<Long> getUserAllRoleIds(Long userId);

    AcUser getUserById(Long userId);

    List<AcUser> getUserByIds(List<Long> userIds);

    /**
     * 删除ac账号
     * @param userId
     * @return
     */
    Result<Void> deleteAdminUser(Long userId);

    boolean updateEmailByUserId(Long userId, String email);

    /**
     * 编辑用户
     *
     * @param updUserId         登录用户id
     * @param dto
     * @param initPassword
     * @param registerAdminFlag
     * @return
     */
    Result<Void> editUser(Long updUserId, AcUserUpdateDTO dto, String initPassword, Boolean registerAdminFlag);

    /**
     * 获取用户详情
     *
     * @param userId
     * @param appName
     * @return
     */
    Result<AcUserDetailVO> getUserInfo(Long userId, String appName);

    List<AssignUserInfoDTO> getUserListForReviewAssignByUserId(Long userId);

    List<AssignUserInfoDTO> getUserListForReviewAssignByUserIdV2(String type);

    AcUser getUserByUserEmail(String email);

    /**
     * 获取创建者map
     * @param list
     * @return
     */
    Map<Long, AcUser> getCreatorListByMap(List<Long> list);

    AcUser getUserByUserEmailIgnoreCase(String email);
}
