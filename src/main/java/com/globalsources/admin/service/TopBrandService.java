package com.globalsources.admin.service;

import com.globalsources.admin.model.dto.search.request.FilterTerm;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/14 17:16
 */
public interface TopBrandService {
    List<Long> getRecomSupplierIdOfTopBrand(List<Long> l1CategoryIds, Integer num, Long userId, List<Long> excludeSupplierIds);

    List<FilterTerm> genFilterByL2PreferenceForProductAndTopBrand(List<Long> l1CategoryIds, List<Long> l2Categories);

    List<Long> getL2CategoriesFromUserPreference(Long userId);
}
