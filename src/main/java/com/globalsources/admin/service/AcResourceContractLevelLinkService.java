package com.globalsources.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.admin.model.dto.resource.SimpleResourceContractLevelLinkDTO;
import com.globalsources.admin.model.pojo.AcResourceContractLevelLink;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2022-10-14
 */
public interface AcResourceContractLevelLinkService extends IService<AcResourceContractLevelLink> {

    List<Long> updateResourceContractLevelLinkAndChildren(Long resourceId, List<String> contractCodeList, Boolean validParent, Long userId, Boolean updateChildren);

    List<SimpleResourceContractLevelLinkDTO> getParentSimpleResourceContractLevelLinkListByResourceId(Long resourceId);

    List<Long> updateAllChildrenContractLevelLink(Long resourceId, List<String> contractCodeList, Long userId);

    Long deleteContractCodeListByResourceKey(String appName, List<String> resourceKeys);

    void refreshResourceContractLevelLinkCache(String appName);

    void refreshResourceContractLevelLinkCache(String appName, List<Long> resourceIds, Boolean clearFlag);

    List<SimpleResourceContractLevelLinkDTO> getSimpleResourceContractLevelLinkDtoListByAppName(String appName, List<Long> resourceIds);

    SimpleResourceContractLevelLinkDTO getSimpleResourceContractLevelLinkDtoById(Long resourceId);

    void validContractCodeList(List<String> contractCodeList);
}
