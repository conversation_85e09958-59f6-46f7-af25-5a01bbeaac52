package com.globalsources.admin.service;

import com.globalsources.admin.model.dto.AcBannerDTO;
import com.globalsources.admin.model.dto.banner.AcBannerEditDetailDTO;
import com.globalsources.admin.model.dto.banner.BannerQueryDTO;
import com.globalsources.admin.model.vo.banner.AcBannerEditDetailVO;
import com.globalsources.admin.model.vo.banner.AcBannerVO;
import com.globalsources.framework.result.PageResult;

import java.util.List;
import java.util.Map;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.agg.admin.service
 * @date:2021/9/14
 */
public interface BannerService {

    List<AcBannerVO> queryBanner(BannerQueryDTO query, String headerLang);

    Map<String, List<AcBannerVO>> getAcBannerByTypes(List<String> typeList);

    PageResult<AcBannerVO> list(String type, Integer pageNum, Integer pageSize, Long categoryId, String verticalCode);

    void createNewBanner(AcBannerDTO dto,Long userId);

    void createNewI18nBanner(AcBannerEditDetailDTO dto, Long userId);

    AcBannerEditDetailVO getI18nBannerDetail(Long bannerId);

    void delete(Long bannerId, Long userId);

    void switchStatus(Long bannerId, Long userId, Boolean forceSwitch);

    void changeSeq(Long bannerId, String operationType);

    /**
     * 检查buyer hero desktop banner最大数量
     * @return
     */
    boolean checkBuyerHeroDesktopLimit(int maxCount);
}
