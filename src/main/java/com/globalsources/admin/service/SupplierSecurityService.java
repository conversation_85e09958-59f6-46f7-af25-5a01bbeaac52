package com.globalsources.admin.service;

import com.globalsources.admin.model.vo.user.AcSupplierSecurityInfoVO;
import com.globalsources.framework.result.Result;

public interface SupplierSecurityService {
    /**
     * 查询供应商安全设置
     * @param email
     * @return
     */
    Result<AcSupplierSecurityInfoVO> querySupplierSecurityInfo(String email);

    /**
     * 关闭供应商安全设置
     * @param userId
     * @param opUserId 操作人
     * @return
     */
    Result<Void> closeSupplierSecuritySettings(Long userId,Long opUserId);

    /**
     * 查询安全信息
     * @param email
     * @return
     */
    Result<AcSupplierSecurityInfoVO> querySecurityInfo(String email);
}
