package com.globalsources.admin.enums;

import com.google.common.collect.Maps;
import org.apache.commons.compress.utils.Lists;

import java.util.List;
import java.util.Map;

/**
 * EDM Display Style
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public enum EDMDisplayStyleEnum {

    ROW_2_6("ROW_2_6", "2-6");

    private static final Map<String, EDMDisplayStyleEnum> edmDisplayStyleEnumHashMap = Maps.newHashMap();

    private static final List<EDMDisplayStyleEnum> edmDisplayStyleEnumList = Lists.newArrayList();

    static {
        EDMDisplayStyleEnum[] displayStyleEnumArr = EDMDisplayStyleEnum.values();
        for (EDMDisplayStyleEnum edmDisplayStyleEnum : displayStyleEnumArr) {
            edmDisplayStyleEnumHashMap.put(edmDisplayStyleEnum.getCode(), edmDisplayStyleEnum);
            edmDisplayStyleEnumList.add(edmDisplayStyleEnum);
        }
    }

    private final String code;
    private final String description;

    EDMDisplayStyleEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(String code) {
        return edmDisplayStyleEnumHashMap.get(code).getDescription();
    }

    public static List<EDMDisplayStyleEnum> getAllDisplayStyleEnumList() {
        return edmDisplayStyleEnumList;
    }
}
