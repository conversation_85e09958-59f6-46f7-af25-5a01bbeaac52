package com.globalsources.admin.sms.model.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


@Data
@Builder(toBuilder = true)
@Accessors(chain = true)
@ApiModel(value="sms-setting 对象", description="sms-setting 对象")
@AllArgsConstructor
@NoArgsConstructor
public class SmsSettingDto implements Serializable {

    private static final long serialVersionUID = -503341344993289579L;

    private Long smsFsId;

    private String appName;

    private String ruleCode;

    private Integer maxValue;

    private Integer resumeSeconds;

    private Boolean enableFlag;


    private Date createDate;

    private Date lastUpdateDate;

    private Long lastUpdateBy;

}
