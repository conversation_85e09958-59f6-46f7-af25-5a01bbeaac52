package com.globalsources.admin.sms.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
@Accessors(chain = true)
@ApiModel(value="sms-setting update item 对象", description="sms-setting update item 对象")
@RequiredArgsConstructor
public class SmsSettingUpdateItemDto implements Serializable {

    private static final long serialVersionUID = 86393266172429342L;

    @ApiModelProperty(value = "smsFsId", notes = "smsFsId")
    @NotNull(message = "smsFsId is required")
    private Long smsFsId;


    @ApiModelProperty(value = "maxValue", notes = "sms max value")
    private Integer maxValue;

    @ApiModelProperty(value = "resumeSeconds", notes = "sms sleep value")
    private Integer resumeSeconds;


}
