package com.globalsources.admin.sms.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> <PERSON>
 * @since 2024-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SmsSetting对象", description="")
public class SmsSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "sms_setting_id", type = IdType.AUTO)
    private Long smsSettingId;

    @ApiModelProperty(value = "短信类型")
    private String otpType;

    @ApiModelProperty(value = "desktop 启用状态")
    private Boolean desktopStatus;

    @ApiModelProperty(value = "mobile 启用状态")
    private Boolean mobileStatus;

    @ApiModelProperty(value = "app 启用状态")
    private Boolean appStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private BigDecimal createBy;

    @ApiModelProperty(value = "最后更新时间")
    private Date lUpdDate;

    @ApiModelProperty(value = "最后更新人")
    private BigDecimal lUpdBy;

    @ApiModelProperty(value = "删除标记")
    private Boolean deleteFlag;
}
