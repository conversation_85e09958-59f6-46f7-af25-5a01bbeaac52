package com.globalsources.admin.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 定时任务执行器
 */
@Configuration
public class JobExecutorConfig {
    private final Logger logger = LoggerFactory.getLogger(JobExecutorConfig.class);

    @Value("${gsol.job.admin.addresses}")
    private String adminAddresses;

    @Value("${gsol.job.executor.appname}")
    private String appname;

//    @Value("${gsol.job.executor.address}")
    private String address;

//    @Value("${gsol.job.executor.ip}")
    private String ip;

    @Value("${gsol.job.executor.port}")
    private int port;

    @Value("${gsol.job.accessToken}")
    private String accessToken;

    @Value("${gsol.job.executor.logpath}")
    private String logPath;

    @Value("${gsol.job.executor.logretentiondays}")
    private int logRetentionDays;


    @Bean
    public XxlJobSpringExecutor getExecutor() {
        logger.info(">>>>>>>>>>> xxl-job executor init.");
        XxlJobSpringExecutor executor = new XxlJobSpringExecutor();
        executor.setAdminAddresses(adminAddresses);
        executor.setAppname(appname);
        executor.setAddress(address);
        executor.setIp(ip);
        executor.setPort(port);
        executor.setAccessToken(accessToken);
        executor.setLogPath(logPath);
        executor.setLogRetentionDays(logRetentionDays);

        return executor;
    }
}
