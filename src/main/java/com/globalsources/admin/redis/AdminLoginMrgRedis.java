package com.globalsources.admin.redis;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public class AdminLoginMrgRedis {
    private static final String LOGIN_KEY="GSOL_AC_LOGIN_USER_";
    @Resource
    private RedisTemplate<String,String> redisTemplate;

    private String makeLoginKey(Long userId){
        return LOGIN_KEY+userId;
    }

    public void addLoginUser(Long userId,String token){
        redisTemplate.opsForSet().add(makeLoginKey(userId),token);
    }

    public List<String> getUserLoginToken(Long userId){
        Set<String> data=redisTemplate.opsForSet().members(makeLoginKey(userId));
        if(CollectionUtils.isEmpty(data)){
            return new ArrayList<>();
        }

        return new ArrayList<>(data);
    }

    public void removeUserLoginToken(Long userId,String token){
        redisTemplate.opsForSet().remove(makeLoginKey(userId),token);
    }
}
