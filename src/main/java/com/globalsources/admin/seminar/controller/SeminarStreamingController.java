package com.globalsources.admin.seminar.controller;


import com.globalsources.admin.seminar.model.dto.SeminarStreamingDto;
import com.globalsources.admin.seminar.model.dto.SeminarStreamingLastUpdateDto;
import com.globalsources.admin.seminar.model.dto.SeminarStreamingQueryDto;
import com.globalsources.admin.seminar.model.entity.SeminarStreaming;
import com.globalsources.admin.seminar.service.SeminarStreamingService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Objects;
import java.util.Optional;

@Api(tags = "Seminar 研讨会")
@Slf4j
@RestController
@RequestMapping("/seminar")
public class SeminarStreamingController {

    @Autowired
    private SeminarStreamingService seminarStreamingService;

    @AdminLogin
    @ApiOperation(value = "研讨会-流媒体单位", notes = "研讨会-流媒体单位")
    @PostMapping("/v1/list-streaming")
    public Result<PageResult<SeminarStreamingDto>> listPage(@ApiIgnore UserVO userVO, @RequestBody SeminarStreamingQueryDto queryDto){
        return Result.success(seminarStreamingService.listPage(queryDto));

    }

    @AdminLogin
    @ApiOperation(value = "研讨会-最后更新的流媒体单位", notes = "研讨会-最后更新的流媒体单位")
    @PostMapping("/v1/last-streaming")
    public Result<SeminarStreamingLastUpdateDto> lastStreaming(){
        return Result.success(seminarStreamingService.getLastStreaming());
    }


    @AdminLogin
    @ApiOperation(value = "研讨会-创建流媒体单位", notes = "研讨会-创建流媒体单位")
    @PostMapping("/v1/add-streaming")
    public Result<Boolean> addStreaming(@ApiIgnore UserVO userVO, @RequestBody @Valid SeminarStreamingDto dto){

        Long userId = Optional.of(userVO).orElse(new UserVO()).getUserId();
        SeminarStreaming entity = OrikaMapperUtil.coverObject(dto, SeminarStreaming.class);
        entity.setLastUpdateBy(userId);

        SeminarStreaming seminarStreaming = seminarStreamingService.addStreaming(entity);
        if (Objects.nonNull(seminarStreaming) && seminarStreaming.getStreamingId() > 0L) {
            return Result.success(Boolean.TRUE);
        }
        return Result.success(Boolean.FALSE);
    }

    @AdminLogin
    @ApiOperation(value = "研讨会-修改流媒体单位", notes = "研讨会-修改流媒体单位")
    @PostMapping("/v1/update-streaming")
    public Result<Boolean> updateStreaming(@ApiIgnore UserVO userVO, @RequestBody @Valid SeminarStreamingDto dto){
        Long userId = Optional.of(userVO).orElse(new UserVO()).getUserId();
        SeminarStreaming entity = OrikaMapperUtil.coverObject(dto, SeminarStreaming.class);
        entity.setLastUpdateBy(userId);
        boolean updateStreaming = seminarStreamingService.updateStreaming(entity);
        if (updateStreaming) {
            return Result.success(Boolean.TRUE);
        }
        return Result.success(Boolean.FALSE);
    }

    @AdminLogin
    @ApiOperation(value = "研讨会-删除流媒体单位", notes = "研讨会-删除流媒体单位")
    @PostMapping("/v1/delete-streaming")
    public Result<Boolean> deleteStreaming(@ApiIgnore UserVO userVO, @RequestParam Long organizerId){
        Long userId = Optional.of(userVO).orElse(new UserVO()).getUserId();

        int deleteStreaming = seminarStreamingService.deleteStreaming(organizerId);
        if (deleteStreaming > 0) {
            log.info("co-organizer[{}] has been deleted by {}", organizerId, userId);
            return Result.success(Boolean.TRUE);
        }
        return Result.success(Boolean.FALSE);
    }

    @AdminLogin
    @ApiOperation(value = "研讨会-上下移动流媒体单位", notes = "研讨会-上下移动流媒体单位")
    @PostMapping("/v1/move-streaming")
    public Result<Boolean> moveStreaming(@ApiIgnore UserVO userVO, @RequestParam Long organizerId, @RequestParam String direction){

        int intDirection = 0;
        if ("up".equalsIgnoreCase(direction)) {
            intDirection = 1;
        }

        boolean sorted = seminarStreamingService.sorted(organizerId, intDirection);
        if (sorted) {
            return Result.success(Boolean.TRUE);
        }
        return Result.success(Boolean.FALSE);
    }

}
