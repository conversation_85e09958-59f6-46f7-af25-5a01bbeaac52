package com.globalsources.admin.seminar.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.admin.dao.SeminarStreamingMapper;
import com.globalsources.admin.model.pojo.AcUser;
import com.globalsources.admin.seminar.model.dto.SeminarStreamingDto;
import com.globalsources.admin.seminar.model.dto.SeminarStreamingLastUpdateDto;
import com.globalsources.admin.seminar.model.dto.SeminarStreamingQueryDto;
import com.globalsources.admin.seminar.model.entity.SeminarStreaming;
import com.globalsources.admin.seminar.service.SeminarStreamingService;
import com.globalsources.admin.service.IAcUserService;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.utils.OrikaMapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


@Slf4j
@Service
public class SeminarStreamingServiceImpl extends ServiceImpl<SeminarStreamingMapper, SeminarStreaming> implements SeminarStreamingService {

    @Autowired
    private SeminarStreamingService seminarStreamingService;

    @Autowired
    private IAcUserService iAcUserService;

    @Override
    public PageResult<SeminarStreamingDto> listPage(SeminarStreamingQueryDto queryDto) {

        IPage<SeminarStreaming> page = new Page<>();
        page.setCurrent(Optional.ofNullable(queryDto.getPageNum()).orElse(1L));
        page.setSize(Optional.ofNullable(queryDto.getPageSize()).orElse(20L));


        IPage<SeminarStreaming> listPage = getBaseMapper().listPage(page, queryDto);
        List<SeminarStreamingDto> seminarStreamingDtos = OrikaMapperUtil.coverList(listPage.getRecords(), SeminarStreamingDto.class);

        return new PageResult<>(listPage.getCurrent(), listPage.getSize(), listPage.getPages(), listPage.getTotal(), seminarStreamingDtos);
    }

    @Override
    public SeminarStreamingLastUpdateDto getLastStreaming() {
        SeminarStreamingLastUpdateDto lastStreaming = null;
        LambdaQueryWrapper<SeminarStreaming> queryWrapper = new LambdaQueryWrapper<>(SeminarStreaming.class);
        queryWrapper.orderByDesc(SeminarStreaming::getLastUpdateDate)
                .select(SeminarStreaming::getStreamingId,
                        SeminarStreaming::getLastUpdateBy,
                        SeminarStreaming::getLastUpdateDate)
                .last("limit 1 ");
        List<SeminarStreaming> seminarStreamings = getBaseMapper().selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(seminarStreamings)) {
            Optional<SeminarStreaming> first = seminarStreamings.stream().findFirst();
            if (first.isPresent()) {
                SeminarStreaming seminarStreaming = first.get();
                lastStreaming = OrikaMapperUtil.coverObject(seminarStreaming, SeminarStreamingLastUpdateDto.class);
            }
        }

        if (Objects.isNull(lastStreaming)) {
            return null;
        }

        //get email
        AcUser acUser = iAcUserService.getUserById(lastStreaming.getLastUpdateBy());
        lastStreaming.setEmail(acUser.getEmail());


        return lastStreaming;
    }

    @Override
    public SeminarStreaming getStreaming(Long streamingId) {
        SeminarStreaming seminarCoOrganizer = getBaseMapper().selectById(streamingId);
        log.info("getCoOrganizer seminarCoOrganizer==={}", seminarCoOrganizer);
        return seminarCoOrganizer;
    }

    @Override
    public SeminarStreaming getPreviousStreaming(int seq) {
        LambdaQueryWrapper<SeminarStreaming> queryWrapper = new LambdaQueryWrapper<>(SeminarStreaming.class);
        queryWrapper.lt(SeminarStreaming::getStreamingSeq, seq)
                .orderByDesc(SeminarStreaming::getStreamingSeq)
                .last(" limit 1  ");
        List<SeminarStreaming> previousSeminarCoOrganizers = getBaseMapper().selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(previousSeminarCoOrganizers)) {
            Optional<SeminarStreaming> first = previousSeminarCoOrganizers.stream().findFirst();
            if (first.isPresent()) {
                return first.get();
            }
        }
        return null;
    }

    @Override
    public SeminarStreaming getNextStreaming(int seq) {
        LambdaQueryWrapper<SeminarStreaming> queryWrapper = new LambdaQueryWrapper<>(SeminarStreaming.class);
        queryWrapper.gt(SeminarStreaming::getStreamingSeq, seq)
                .orderByAsc(SeminarStreaming::getStreamingSeq)
                .last(" limit 1 ");
        List<SeminarStreaming> previousSeminarCoOrganizers = getBaseMapper().selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(previousSeminarCoOrganizers)) {
            Optional<SeminarStreaming> first = previousSeminarCoOrganizers.stream().findFirst();
            if (first.isPresent()) {
                return first.get();
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SeminarStreaming addStreaming(SeminarStreaming entity) {
        Date today = new Date();
        entity.setCreateDate(today);
        entity.setLastUpdateDate(today);

        boolean save = save(entity);
        log.info("save=={}", save);

        log.info("id==={}", entity.getStreamingId());
        entity.setStreamingSeq(entity.getStreamingId().intValue());
        seminarStreamingService.updateStreaming(entity);

        return entity;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteStreaming(Long streamingId) {

        int delete = getBaseMapper().deleteById(streamingId);
        log.info("delete=={}", delete);
        return delete;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStreaming(SeminarStreaming entity) {

        Date today = new Date();
        entity.setLastUpdateDate(today);

        LambdaUpdateWrapper<SeminarStreaming> updateWrapper = new LambdaUpdateWrapper<>(SeminarStreaming.class);
        updateWrapper.set(SeminarStreaming::getStreamingLogo, entity.getStreamingLogo())
                .set(SeminarStreaming::getStreamingUrl, entity.getStreamingUrl())
                .set(entity.getStreamingSeq() != 0, SeminarStreaming::getStreamingSeq, entity.getStreamingSeq())
                .set(SeminarStreaming::getLastUpdateDate, entity.getLastUpdateDate())
                .eq(SeminarStreaming::getStreamingId, entity.getStreamingId());

        boolean update = update(updateWrapper);
        log.info("update==={}", update);
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sorted(Long streamingId, int upOrDown) {
        //1:up, 0:down

        //current seq
        SeminarStreaming currentCoOrganizer = getStreaming(streamingId);
        if (Objects.isNull(currentCoOrganizer)) {
            return false;
        }

        int currentSeq = currentCoOrganizer.getStreamingSeq();

        if (upOrDown == 0) {//down
            //next
            SeminarStreaming nextCoOrganizer = getNextStreaming(currentSeq);
            if (Objects.isNull(nextCoOrganizer)) {
                log.warn("This is the last record already.");
                return false;
            }
            int tmpSeq = nextCoOrganizer.getStreamingSeq();

            nextCoOrganizer.setStreamingSeq(currentSeq);
            seminarStreamingService.updateStreaming(nextCoOrganizer);

            currentCoOrganizer.setStreamingSeq(tmpSeq);
            seminarStreamingService.updateStreaming(currentCoOrganizer);


        } else {//up
            //previous
            SeminarStreaming previousCoOrganizer = getPreviousStreaming(currentSeq);
            if (Objects.isNull(previousCoOrganizer)) {
                log.warn("This is the first record already.");
                return false;
            }
            int tmpSeq = previousCoOrganizer.getStreamingSeq();

            previousCoOrganizer.setStreamingSeq(currentSeq);
            seminarStreamingService.updateStreaming(previousCoOrganizer);

            currentCoOrganizer.setStreamingSeq(tmpSeq);
            seminarStreamingService.updateStreaming(currentCoOrganizer);

        }

        return true;
    }

    @Override
    public int getMinimumSeq() {
        LambdaQueryWrapper<SeminarStreaming> queryWrapper = new LambdaQueryWrapper<>(SeminarStreaming.class);
        queryWrapper.select(SeminarStreaming::getStreamingSeq)
                .orderByAsc(SeminarStreaming::getStreamingSeq)
                .last(" limit 1 ")
                ;
        List<SeminarStreaming> seminarCoOrganizers = getBaseMapper().selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(seminarCoOrganizers)) {
            Optional<SeminarStreaming> first = seminarCoOrganizers.stream().findFirst();
            if (first.isPresent()) {
                return first.get().getStreamingSeq();
            }
        }
        return 0;
    }
}
