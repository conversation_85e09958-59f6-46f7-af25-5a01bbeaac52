package com.globalsources.admin.seminar.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.admin.seminar.model.dto.SeminarCoOrganizerDropdownDto;
import com.globalsources.admin.seminar.model.dto.SeminarCoOrganizerDto;
import com.globalsources.admin.seminar.model.dto.SeminarCoOrganizerLastUpdateDto;
import com.globalsources.admin.seminar.model.dto.SeminarCoOrganizerQueryDto;
import com.globalsources.admin.seminar.model.entity.SeminarCoOrganizer;
import com.globalsources.framework.result.PageResult;

import java.util.List;

public interface SeminarCoOrganizerService extends IService<SeminarCoOrganizer> {

    PageResult<SeminarCoOrganizerDto> listPage(SeminarCoOrganizerQueryDto queryDto);

    List<SeminarCoOrganizerDropdownDto> dropdownList();

    SeminarCoOrganizerLastUpdateDto getLastCoOrganizer();

    SeminarCoOrganizer getCoOrganizer(Long organizerId);

    SeminarCoOrganizer getPreviousCoOrganizer(int seq);

    SeminarCoOrganizer getNextCoOrganizer(int seq);

    SeminarCoOrganizer addCoOrganizer(SeminarCoOrganizer entity);

    int deleteCoOrganizer(Long organizerId);

    boolean updateCoOrganizer(SeminarCoOrganizer entity);

    boolean sorted(Long organizerId, int upOrDown);

    int getMinimumSeq();
}
