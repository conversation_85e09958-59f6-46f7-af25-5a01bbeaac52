package com.globalsources.admin.seminar.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Data
@Accessors(chain = true)
@ApiModel(value="Seminar Streaming query对象", description="Seminar Streaming query对象")
@RequiredArgsConstructor
public class SeminarStreamingQueryDto implements Serializable {

    private static final long serialVersionUID = 519899600882604810L;

    @ApiModelProperty(value = "pageNum", notes = "当前第几页")
    private Long pageNum;
    @ApiModelProperty(value = "pageSize", notes = "每页多少条")
    private Long pageSize;

}
