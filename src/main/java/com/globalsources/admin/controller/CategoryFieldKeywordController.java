package com.globalsources.admin.controller;

import com.globalsources.admin.constants.AdminConstants;
import com.globalsources.admin.model.dto.category.CategoryFieldKeywordDTO;
import com.globalsources.admin.model.vo.category.CategoryFieldKeywordVO;
import com.globalsources.admin.service.CategoryFieldKeywordService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> Chen
 * @since 2021-08-23
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@Api(tags = {"4th Field Settings"})
@RequestMapping("/ac")
public class CategoryFieldKeywordController {

    @Autowired
    private CategoryFieldKeywordService categoryFieldKeywordService;

    @ApiOperation(value = "添加filed keyword", notes = "添加filed keyword")
    @AdminLogin
    @PostMapping(value = "/v1/category/field-keyword/save")
    public Result<CategoryFieldKeywordVO> saveFieldKeyword(@ApiIgnore UserVO userInfo, @Valid @RequestBody CategoryFieldKeywordDTO dto) {
        boolean loginFlag = Objects.nonNull(userInfo) && Objects.nonNull(userInfo.getUserId());
        ResultCode.UserResultCode.NOT_LOGIN.isTrue(loginFlag, null);
        CategoryFieldKeywordVO categoryFieldKeywordVO = categoryFieldKeywordService.saveCategoryFieldKeyword(dto, Optional.ofNullable(userInfo).map(UserVO::getUserId).orElse(0L));
        return Result.success(categoryFieldKeywordVO);
    }

    @ApiOperation(value = "更新filed keyword", notes = "更新filed keyword")
    @AdminLogin
    @PostMapping(value = "/v1/category/field-keyword/update")
    public Result<CategoryFieldKeywordVO> updateFieldKeyword(@ApiIgnore UserVO userInfo, @Valid @RequestBody CategoryFieldKeywordDTO dto) {
        boolean loginFlag = Objects.nonNull(userInfo) && Objects.nonNull(userInfo.getUserId());
        ResultCode.UserResultCode.NOT_LOGIN.isTrue(loginFlag, null);
        CategoryFieldKeywordVO categoryFieldKeywordVO = categoryFieldKeywordService.updateCategoryFieldKeyword(dto, Optional.ofNullable(userInfo).map(UserVO::getUserId).orElse(0L));
        return Result.success(categoryFieldKeywordVO);
    }

    @ApiOperation(value = "添加filed keyword", notes = "添加filed keyword")
    @AdminLogin
    @PostMapping(value = "/v1/category/field-keyword/save/batch")
    public Result<List<CategoryFieldKeywordVO>> saveFieldKeywordBatch(@ApiIgnore UserVO userInfo, @Valid @RequestBody List<CategoryFieldKeywordDTO> dto) {
        boolean loginFlag = Objects.nonNull(userInfo) && Objects.nonNull(userInfo.getUserId());
        ResultCode.UserResultCode.NOT_LOGIN.isTrue(loginFlag, null);
        List<CategoryFieldKeywordVO> categoryFieldKeywordVoList = categoryFieldKeywordService.saveCategoryFieldKeywordBatch(dto, Optional.ofNullable(userInfo).map(UserVO::getUserId).orElse(0L));
        return Result.success(categoryFieldKeywordVoList);
    }

    @GetMapping(value = "/v1/category/field-keyword/list")
    @AdminLogin
    @ApiOperation(value = "查询category field keyword列表", notes = "查询category field keyword列表")
    public Result<PageResult<CategoryFieldKeywordVO>> getFieldKeywordListPage(@ApiIgnore UserVO userInfo,
                                                                              @RequestParam(value = "keyword", required = false) String keyword,
                                                                              @RequestParam(value = "pageNum", defaultValue = "1") Long pageNum,
                                                                              @RequestParam(value = "pageSize", defaultValue = "20") Long pageSize,
                                                                              @ApiParam(name = "orderField", value = "排序字段，CATEGORY_ID, FIELD_KEYWORD, UPDATE_DATE") @RequestParam(value = "orderField", defaultValue = "CATEGORY_ID") AdminConstants.BannerOrderField orderField,
                                                                              @ApiParam(name = "orderType", value = "顺序:ASC, 倒序:DESC") @RequestParam(value = "orderType", defaultValue = "ASC") AdminConstants.OrderType orderType) {
        PageResult<CategoryFieldKeywordVO> categoryFieldKeywords = categoryFieldKeywordService.getCategoryFieldKeywordsPage(keyword, pageNum, pageSize, orderField, orderType);
        return Result.success(categoryFieldKeywords);
    }

    @GetMapping(value = "/v1/category/name/{categoryId}")
    @AdminLogin
    @ApiOperation(value = "查询category name（通过categoryId查询）", notes = "查询category name（通过categoryId查询）")
    public Result<String> getCategoryName(@ApiIgnore UserVO userInfo, @PathVariable(value = "categoryId", required = false) Long categoryId) {
        String categoryName = categoryFieldKeywordService.getCategoryNameByCategoryId(categoryId);
        return Result.success(categoryName);
    }

    @GetMapping(value = "/v1/category/field-keyword/delete")
    @AdminLogin
    @ApiOperation(value = "通过id删除关键词", notes = "通过id删除关键词")
    public Result<List<Long>> getCpRecSupplierIdsByCountryCode(@ApiIgnore UserVO userInfo, @RequestParam("cfkId") Long cfkId) {
        categoryFieldKeywordService.deleteCategoryFieldKeywordById(cfkId, userInfo);
        return Result.success();
    }

}
