package com.globalsources.admin.controller;

import com.globalsources.activity.agg.api.dto.LiveActivityQueryDTO;
import com.globalsources.activity.core.api.dto.LiveSupplierQueryDTO;
import com.globalsources.admin.model.dto.AcEditLiveSupplierDTO;
import com.globalsources.admin.model.dto.AcLiveActivityDTO;
import com.globalsources.admin.model.dto.AcLiveSupplierDTO;
import com.globalsources.admin.model.vo.AcLiveActivityVO;
import com.globalsources.admin.model.vo.AcLiveSupplierLiteVO;
import com.globalsources.admin.model.vo.AcLiveSupplierVO;
import com.globalsources.admin.model.vo.ActLiveConfigVO;
import com.globalsources.admin.model.vo.live.AddLiveSuppVO;
import com.globalsources.admin.service.LiveActivityService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@Api(tags = "直播活动")
@Validated
@RequestMapping("/activity/live")
@RestController
public class LiveActivityController {
    @Resource
    private LiveActivityService liveActivityService;

    @ApiOperation("保存直播")
    @AdminLogin
    @PostMapping("/save")
    public Result<Long> saveLiveActivity(@ApiIgnore UserVO user, @Valid @RequestBody AcLiveActivityDTO dto) {
        return liveActivityService.saveLiveActivity(user.getUserId(), dto);
    }

    @ApiOperation("获取直播信息")
    @AdminLogin
    @GetMapping("/info")
    public Result<AcLiveActivityVO> getLiveActivity(@ApiParam("活动id") @RequestParam Long activityId) {
        return liveActivityService.getLiveActivityInfo(activityId);
    }


    @ApiOperation("查询直播列表")
    @AdminLogin
    @PostMapping("/list")
    public Result<PageResult<AcLiveActivityVO>> getLiveActivityPage(@ApiIgnore UserVO user,@RequestBody LiveActivityQueryDTO dto) {
        //只查询自己的数据
        if(!Boolean.TRUE.equals(user.getAdministratorFlag())){
            dto.setUserId(user.getUserId());
        }

        return liveActivityService.queryLiveActivityPage(user.getUserId(),dto);
    }

    @ApiOperation("删除直播")
    @AdminLogin
    @GetMapping("/del")
    public Result<Void> delLiveActivity(@ApiIgnore UserVO user, @ApiParam("活动id") @RequestParam Long activityId) {
        return liveActivityService.delLiveActivity(user.getUserId(), activityId);
    }

    @ApiOperation("启用/停用")
    @AdminLogin
    @GetMapping("/enabled/trigger")
    public Result<Void> triggerEnabledStatus(@ApiIgnore UserVO user, @ApiParam("活动id") @RequestParam Long activityId,
                                             @ApiParam("状态: 0.禁用 1.启用") @RequestParam Integer enabled) {
        boolean status = false;
        if (enabled == 1) {
            status = true;
        } else if (enabled == 0) {
            status = false;
        } else {
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }

        return liveActivityService.triggerEnabledStatus(user.getUserId(), activityId, status);
    }

    @ApiOperation("字幕启用/停用")
    @AdminLogin
    @GetMapping("/subtitle/trigger")
    public Result<Void> triggerEnabledSubtitle(@ApiIgnore UserVO user, @ApiParam("活动id") @RequestParam Long activityId,
                                               @ApiParam("状态: 0.禁用 1.启用") @RequestParam Integer enabled) {
        boolean status = false;
        if (enabled == 1) {
            status = true;
        } else if (enabled == 0) {
            status = false;
        } else {
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }

        return liveActivityService.triggerEnabledSubtitle(user.getUserId(), activityId, status);
    }

    @ApiOperation("获取直播活动配置")
    @AdminLogin
    @GetMapping("/config")
    public Result<ActLiveConfigVO> getLiveConfigInfo() {
        return liveActivityService.getLiveConfigInfo();
    }

    @ApiOperation("获取直播供应商列表页")
    @AdminLogin
    @PostMapping("/supplier/list")
    public Result<PageResult<AcLiveSupplierVO>> getLiveSupplierPage(@RequestBody LiveSupplierQueryDTO dto) {
        return liveActivityService.getLiveSupplierPage(dto);
    }

    @ApiOperation("获取直播供应商列表")
    @AdminLogin
    @GetMapping("/supplier/all/list")
    public Result<List<AcLiveSupplierLiteVO>> queryLiveSupplier(@RequestParam Long activityId){
        return liveActivityService.queryLiveSupplierList(activityId);
    }

    @ApiOperation("获取直播所有供应商")
    @AdminLogin
    @GetMapping("/supplier/id/all")
    public Result<List<Long>> getAllLiveSupplierIdList(@ApiParam("活动id") @RequestParam Long activityId) {
        return liveActivityService.getAllLiveSupplierIdList(activityId);
    }

    @ApiOperation("添加供应商")
    @AdminLogin
    @PostMapping("/supplier/add")
    public Result<Void> addSupplier(@ApiIgnore UserVO user, @RequestBody AcLiveSupplierDTO dto) {
        return liveActivityService.addSupplier(user.getUserId(), dto);
    }

    @ApiOperation("删除供应商")
    @AdminLogin
    @GetMapping("/supplier/del")
    public Result<Void> delSupplier(@ApiParam("活动id") @RequestParam Long activityId, @ApiParam("供应商id") @RequestParam Long supplierId) {
        return liveActivityService.delSupplier(activityId, supplierId);
    }

    @ApiOperation("编辑供应商")
    @AdminLogin
    @GetMapping("/supplier/edit")
    public Result<Void> editSupplier(@ApiIgnore UserVO user, @RequestBody AcEditLiveSupplierDTO dto){
        return liveActivityService.editSupplier(user.getUserId(),dto);
    }

    @ApiOperation("搜索需要添加的供应商")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "tsId", value = "展会id", required = false, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "keyword", value = "关键词", required = true, dataType = "string")
    })
    @GetMapping("/supplier/search")
    public Result<PageResult<AddLiveSuppVO>> searchSupplier(@RequestParam(value = "tsId", required = false) Long tsId,
                                                            @RequestParam(required = false) Long activityId,
                                                      @RequestParam(value = "keyword", required = false) String keyword,
                                                      @RequestParam("pageNum") Long pageNum,
                                                      @RequestParam("pageSize") Long pageSize) {
        PageResult<AddLiveSuppVO> addLiveSuppVoList = liveActivityService.searchSupplier(tsId,activityId, keyword, pageNum, pageSize);
        return Result.success(addLiveSuppVoList);
    }


    @ApiOperation(value = "评论功能 启用/停用", notes = "评论功能 启用/停用")
    @AdminLogin
    @GetMapping("/comment/trigger")
    public Result<Void> triggerCommentStatus(@ApiIgnore UserVO user, @ApiParam("活动id") @RequestParam Long activityId,
                                             @ApiParam("状态: 0.禁用 1.启用") @RequestParam Integer enabled) {
        boolean status = false;
        if (enabled == 1) {
            status = true;
        } else if (enabled == 0) {
            status = false;
        } else {
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }

        return liveActivityService.triggerCommentStatus(user.getUserId(), activityId, status);
    }

}
