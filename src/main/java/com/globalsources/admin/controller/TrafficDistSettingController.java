package com.globalsources.admin.controller;

import com.globalsources.admin.model.dto.TrafficDistQueryDTO;
import com.globalsources.admin.model.dto.TrafficDistSeetingDTO;
import com.globalsources.admin.model.dto.TrafficSupplierDTO;
import com.globalsources.admin.model.vo.TrafficDistSettingVO;
import com.globalsources.admin.service.TrafficDistSettingService;
import com.globalsources.agg.supplier.api.feign.TrafficDistAggFeign;
import com.globalsources.agg.supplier.api.model.dto.TrafficDistributionSupplierAggDTO;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.vo.UserVO;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> Zhou
 * @since 2022-10-17
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@Api(tags = {"用户分发设置"})
@RequestMapping("/traffic-distribution")
public class TrafficDistSettingController {

    @Resource
    private TrafficDistAggFeign trafficDistAggFeign;

    @Autowired
    private TrafficDistSettingService trafficDistSettingService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @GetMapping(value="/v1/setting/info")
    @ApiOperation(value = "Buyer lead traffic setting info", notes = "Buyer lead traffic setting info")
    public Result<List<TrafficDistSettingVO>> getTrafficDistributionSettingInfo() {
        return trafficDistSettingService.getTrafficDistributionSettingInfo();
    }

    @AdminLogin
    @PostMapping(value = "/v1/setting/updateall")
    @ApiOperation(value = "All Buyer lead traffic setting info update", notes = "All Buyer lead traffic setting info update")
    public Result<Boolean> updAllTrafficDistributionSettingInfo(@ApiIgnore UserVO userInfo, @RequestBody List<TrafficDistSeetingDTO> trafficDistDtoList) {
        log.info("update all buyer lead traffic seeting info in admin console, TrafficDistSeetingDTOList = {}", trafficDistDtoList);
        if(CollectionUtils.isEmpty(trafficDistDtoList)){
            return Result.failed(ResultCode.CommonResultCode.REQUIRED_REQUEST_PARAMETERS_ARE_MISSING);
        }

        if(trafficDistSettingService.updateAllTrafficDistSettingInfo(userInfo,trafficDistDtoList)){
            redisTemplate.delete("GSOL:SENSOR:BUYERLEADS:PLATFORM:SCAN-TIME");
            redisTemplate.delete("GSOL:SENSOR:BUYERLEADS:VIRTUAL_SHOW:SCAN-TIME");
            redisTemplate.delete("GSOL:SENSOR:BUYERLEADS:HK_SHOW:SCAN-TIME");
            return Result.success();
        }
        return Result.failed();
    }

    @AdminLogin
    @GetMapping(value="/v1/supplier/list")
    @ApiOperation(value = "获取已添加的供应商列表，status=t/f", notes = "获取已添加的供应商列表，status=t/f")
    public Result<List<Long>> getSupplierList(){
        return trafficDistAggFeign.getSupplierList(Boolean.FALSE);
    }

    @AdminLogin
    @ApiOperation(value = "获取分发供应商列表信息", notes = "获取分发供应商列表信息")
    @PostMapping("/v1/dist-supplier/list")
    public Result<PageResult<TrafficDistributionSupplierAggDTO>> getDistSupplierList(@Valid @RequestBody TrafficDistQueryDTO query){
        return  trafficDistAggFeign.getDistSupplierList(query.getPageNum(),query.getPageSize(),query.getSortField(),query.getSortType());
    }


    @AdminLogin
    @ApiOperation(value = "删除分发供应商", notes = "删除分发供应商")
    @PostMapping("/v1/dist-supplier/del")
    public Result<Boolean> deleteTrafficDistributionSupplier(@ApiIgnore UserVO userInfo, @RequestBody TrafficSupplierDTO dto){
        return trafficDistAggFeign.deleteTrafficDistributionSupplier(dto.getSupplierId());
    }


    @AdminLogin
    @ApiOperation(value = "批量增加删除分发供应商", notes = "批量增加删除分发供应商")
    @PostMapping("/v1/dist-supplier/batchAddAndDel")
    public Result<Boolean> batchAddAndDelTrafficDistributionSupplier(@ApiIgnore UserVO userInfo, @RequestBody TrafficSupplierDTO dto){
        List<Long> deleteList = Lists.newArrayList();
        List<Long> insertList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(dto.getOldSupplierIds())){
            deleteList = dto.getOldSupplierIds().stream().filter(Objects::nonNull)
                    .filter(s -> !dto.getNewSupplierIds().contains(s))
                    .collect(Collectors.toList());
        }

        if(CollectionUtils.isNotEmpty(dto.getNewSupplierIds())){
            insertList = dto.getNewSupplierIds().stream().filter(Objects::nonNull)
                    .filter(s -> !dto.getOldSupplierIds().contains(s))
                    .collect(Collectors.toList());
        }
        return trafficDistAggFeign.batchAddAndDelTrafficDistributionSupplier(userInfo.getUserId(),insertList,deleteList);
    }

    @AdminLogin
    @ApiOperation(value = "修改分发供应商信息", notes = "修改分发供应商信息")
    @PostMapping("/v1/dist-supplier/update")
    public Result<Boolean> updateTrafficDistributionSupplier(@ApiIgnore UserVO userInfo,@RequestBody TrafficDistributionSupplierAggDTO dto){
        dto.setLUpdBy(userInfo.getUserId());
        return trafficDistAggFeign.updateTrafficDistributionSupplier(dto);
    }

}
