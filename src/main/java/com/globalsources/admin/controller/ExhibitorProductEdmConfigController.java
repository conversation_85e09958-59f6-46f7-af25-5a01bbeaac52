package com.globalsources.admin.controller;

import com.globalsources.admin.model.dto.ExhibitorProductEdmConfigDTO;
import com.globalsources.admin.model.vo.ExhibitorProductEdmConfigVO;
import com.globalsources.admin.model.vo.ExhibitorProductEdmListVO;
import com.globalsources.admin.service.ExhibitorProductEdmConfigService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.ts.api.feign.AdminTradeShowFeign;
import com.globalsources.ts.api.model.vo.TradeShowSelectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

/**
 * 展商产品eDM配置控制器
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/exhibitor-product-edm-config")
@Api(tags = {"展商产品eDM配置管理"})
public class ExhibitorProductEdmConfigController {

    @Autowired
    private ExhibitorProductEdmConfigService exhibitorProductEdmConfigService;

    @Autowired
    private AdminTradeShowFeign adminTradeShowFeign;

    @AdminLogin
    @PostMapping("/create")
    @ApiOperation(value = "创建eDM配置", notes = "创建eDM配置")
    public Result<Long> createEdmConfig(@RequestBody @Valid ExhibitorProductEdmConfigDTO dto, @ApiIgnore UserVO userVO) {
        try {
            Long edmConfigId = exhibitorProductEdmConfigService.createEdmConfig(dto, userVO);
            return Result.success(edmConfigId);
        } catch (Exception e) {
            log.error("Create eDM config error", e);
            return Result.failed(e.getMessage());
        }
    }

    @AdminLogin
    @PutMapping("/update")
    @ApiOperation(value = "更新eDM配置", notes = "更新eDM配置")
    public Result<Boolean> updateEdmConfig(@RequestBody @Valid ExhibitorProductEdmConfigDTO dto, @ApiIgnore UserVO userVO) {
        try {
            Boolean result = exhibitorProductEdmConfigService.updateEdmConfig(dto, userVO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("Update eDM config error", e);
            return Result.failed(e.getMessage());
        }
    }

    @AdminLogin
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除eDM配置", notes = "删除eDM配置")
    public Result<Boolean> deleteEdmConfig(@PathVariable("id") Long edmConfigId, @ApiIgnore UserVO userVO) {
        try {
            Boolean result = exhibitorProductEdmConfigService.deleteEdmConfig(edmConfigId, userVO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("Delete eDM config error", e);
            return Result.failed(e.getMessage());
        }
    }

    @AdminLogin
    @GetMapping("/detail/{id}")
    @ApiOperation(value = "获取eDM配置详情", notes = "获取eDM配置详情")
    public Result<ExhibitorProductEdmConfigVO> getEdmConfigDetail(@PathVariable("id") Long edmConfigId) {
        try {
            ExhibitorProductEdmConfigVO vo = exhibitorProductEdmConfigService.getEdmConfigDetail(edmConfigId);
            return Result.success(vo);
        } catch (Exception e) {
            log.error("Get eDM config detail error", e);
            return Result.failed(e.getMessage());
        }
    }

    @AdminLogin
    @GetMapping("/list")
    @ApiOperation(value = "获取eDM配置列表", notes = "获取eDM配置列表")
    public Result<PageResult<ExhibitorProductEdmListVO>> getEdmConfigList(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            PageResult<ExhibitorProductEdmListVO> result = exhibitorProductEdmConfigService.getEdmConfigList(pageNum, pageSize);
            return Result.success(result);
        } catch (Exception e) {
            log.error("Get eDM config list error", e);
            return Result.failed(e.getMessage());
        }
    }

    @AdminLogin
    @GetMapping("/fep-trade-shows")
    @ApiOperation(value = "获取fep可选择的展会列表", notes = "获取fep可选择的展会列表")
    public Result<List<TradeShowSelectVO>> getFepTradeShowList() {
         return adminTradeShowFeign.getFepTradeShowList();
    }
}
