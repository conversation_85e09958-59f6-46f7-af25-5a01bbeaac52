package com.globalsources.admin.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.fastjson.JSON;
import com.globalsources.admin.model.vo.RfqBlackListExportVO;
import com.globalsources.admin.service.ComplainService;
import com.globalsources.admin.service.RfqBlackListService;
import com.globalsources.agg.admin.api.model.dto.BlackListAddDTO;
import com.globalsources.common.api.feign.ComplainCoreFeign;
import com.globalsources.common.api.model.po.ComplainPO;
import com.globalsources.eblock.agg.api.enums.EblockTypeEnum;
import com.globalsources.eblock.agg.api.feign.NewRfqBlackListFeign;
import com.globalsources.eblock.agg.api.model.dto.rfqblacklist.RfqBlackListAddDTO;
import com.globalsources.eblock.agg.api.model.dto.rfqblacklist.RfqBlackListQueryDTO;
import com.globalsources.eblock.agg.api.model.vo.rfqblacklist.RfqBlackListVO;
import com.globalsources.eblock.agg.api.model.vo.rfqblacklist.SearchUserInfoVO;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.supplierconsole.agg.api.constant.ApplicationCode;
import com.globalsources.supplierconsole.agg.api.constant.EntityType;
import com.globalsources.supplierconsole.agg.api.constant.OperationEnum;
import com.globalsources.supplierconsole.agg.api.log.dto.OperationLogDTO;
import com.globalsources.supplierconsole.agg.api.log.feign.OperationLogFeign;
import com.globalsources.user.api.feign.UserQueryFeign;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Api(tags = "Admin Console Blacklist")
@RequestMapping("/blacklist")
@RestController
@Slf4j
public class BlackListController {

    @Autowired
    private RfqBlackListService rfqBlackListService;

    @Autowired
    private ComplainCoreFeign complainCoreFeign;

    @Resource
    private OperationLogFeign operationLogFeign;

    @Resource
    private HttpServletRequest request;

    @Autowired
    private ComplainService complainService;

    @Autowired
    private NewRfqBlackListFeign newRfqBlackListFeign;

    private static final String BUYER_ID = "Buyer ID";

    private static final String STATUS_SUCCESS = "Success";

    private static final String STATUS_FAILED = "Failed";

    @Autowired
    private UserQueryFeign userFeign;

    private static final String BLACK_EXPORT_FILE_NAME = "attachment;  filename=Blacklist.csv";
    private static final String WHITE_EXPORT_FILE_NAME = "attachment;  filename=Whitelist.csv";
    private static final String BLOCK_EXPORT_FILE_NAME = "attachment;  filename=Blocklist.csv";

    private static final String UTF8_BOM = new String(new byte[] {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}, StandardCharsets.UTF_8);

    private final SimpleDateFormat sf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

    @AdminLogin
    @ApiOperation("用户权限列表-黑名单、白名单、灰名单列表")
    @PostMapping("/v1/list")
    public Result<PageResult<RfqBlackListVO>> list(@RequestBody RfqBlackListQueryDTO dto) {
        log.info("admin console user permissions list by dto:{}", JSON.toJSONString(dto));
        return newRfqBlackListFeign.selectUserPermissionsList(dto);
    }

    @ApiOperation("用户权限列表-黑名单、白名单、灰名单列表下载，xls")
    @GetMapping("/v1/download")
    public void download(@RequestParam String eblockType, @RequestParam(required = false) String searchKeyword,
                         @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate, HttpServletResponse response) {
        log.info("download blacklist eblockType :{}", eblockType);
        int pageNum = 1;
        int pageSize = 500;
        RfqBlackListQueryDTO queryDTO = RfqBlackListQueryDTO.builder().eblockType(eblockType).searchKeyword(searchKeyword).startDate(startDate).endDate(endDate)
                .sortField("createDate").sortType("desc").build();
        queryDTO.setPageNum(pageNum);
        queryDTO.setPageSize(pageSize);
        log.info("selectUserPermissionsList dto :{}", JSON.toJSONString(queryDTO));
        PageResult<RfqBlackListVO> pageResult = newRfqBlackListFeign.selectUserPermissionsList(queryDTO).getData();
        List<RfqBlackListVO> blackListVOS = CollUtil.newArrayList();
        if (CollectionUtils.isNotEmpty(pageResult.getList())) {
            blackListVOS.addAll(pageResult.getList());
            double pageTotal = pageResult.getTotal();
            double pages = pageTotal / pageSize;
            double pageIndex = Math.ceil(pages);
            while (pageNum <= pageIndex) {
                //查询下一页数据
                pageNum += 1;
                queryDTO.setPageNum(pageNum);
                pageResult = newRfqBlackListFeign.selectUserPermissionsList(queryDTO).getData();
                if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                    blackListVOS.addAll(pageResult.getList());
                }
            }
        }
        List<RfqBlackListExportVO> dataList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(blackListVOS)) {
            dataList = blackListVOS.stream().map(s -> {
                RfqBlackListExportVO rfqBlackListExportVO = OrikaMapperUtil.coverObject(s, RfqBlackListExportVO.class);
                rfqBlackListExportVO.setCreateDateStr(sf.format(s.getCreateDate()));
                return rfqBlackListExportVO;
            }).collect(Collectors.toList());
        }

        try (CsvWriter writer = new CsvWriter(new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8))) {
            OutputStream out = response.getOutputStream();
            // 加上UTF-8文件的标识字符
            out.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});
            response.setContentType("text/csv;charset=UTF-8");
            response.setHeader("Content-disposition", buildFileName(queryDTO.getEblockType()));

            writer.writeLine(buildTitles(queryDTO.getEblockType()));
            for (RfqBlackListExportVO v : dataList) {
                buildRowData(writer, v, queryDTO.getEblockType());
            }
        } catch (Exception e) {
            log.error("downloadEblockList error:{}", e);
        }
    }

    private String buildFileName(String eblockType) {
        String fileName = BLACK_EXPORT_FILE_NAME;
        if (EblockTypeEnum.white.name().equalsIgnoreCase(eblockType)) {
            fileName = WHITE_EXPORT_FILE_NAME;
        } else if (EblockTypeEnum.block.name().equalsIgnoreCase(eblockType)) {
            fileName = BLOCK_EXPORT_FILE_NAME;
        }
        return fileName;
    }

    private String[] buildTitles(String eblockType) {
        String[] titles;
        if (EblockTypeEnum.black.name().equalsIgnoreCase(eblockType)) {
            titles = new String[]{BUYER_ID, "Buyer Email", "Added Date", "Added By", "Source", "Reason"};
        } else {
            titles = new String[]{BUYER_ID, "Buyer Email", "Added Date", "Added By"};
        }
        return titles;
    }

    private void buildRowData(CsvWriter writer, RfqBlackListExportVO v, String eblockType) {
        if (EblockTypeEnum.black.name().equalsIgnoreCase(eblockType)) {
            writer.writeLine(v.getBuyerId(), v.getBuyerEmailAddr() , v.getCreateDateStr(), v.getReviewerEmailAddr(), v.getAddSource(), v.getAddReason() );
        } else {
            writer.writeLine(v.getBuyerId(), v.getBuyerEmailAddr() , v.getCreateDateStr(), v.getReviewerEmailAddr());
        }
    }

    @ApiOperation("移除用户权限")
    @AdminLogin
    @GetMapping("/v1/remove")
    public Result<String> remove(@ApiIgnore UserVO userInfo, @RequestParam(value = "userId", required = false) Long userId, @RequestParam(value = "eblockType", required = false) String eblockType, @RequestParam(value = "emailAddr", required = false) String emailAddr) {
        log.info("admin console remove by userId is:{}, eblockType is:{}, emailAddr is:{}, userInfo:{}", userId, eblockType, emailAddr, JSON.toJSONString(userInfo));

        boolean remove = false;
        String status = STATUS_SUCCESS;
        try {
            remove = rfqBlackListService.remove(userId, emailAddr, userInfo.getUserId(), eblockType);

            if (remove && EblockTypeEnum.black.name().equalsIgnoreCase(eblockType) && Objects.nonNull(userId)) {
                // 如果用户是被举报后拉黑，那移除黑名单后，也更新举报表
                ComplainPO complainPO = complainCoreFeign.selectComplainByUserId(userId);
                if (Objects.nonNull(complainPO)) {
                    complainService.cancelComplain(userId, userInfo.getUserId());
                }
            }
        } catch (Exception ex) {
            status = STATUS_FAILED;
        }

        try {
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("User ID/Email", ResultUtil.getData(userFeign.getUserByUserId(userId)).getEmail());

            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("Status", status);
            resultMap.put("Condition", paramMap);

            operationLogFeign.saveOperationLog(OperationLogDTO.builder().operationName(OperationEnum.REMOVE_FROM_BLACKLIST.getOperationName()).entityId(0L).entityType(EntityType.MSG_MANAGEMENT.getName())
                    .appName(ApplicationCode.ADMIN_CONSOLE.name()).orgId(0L).userId(userInfo.getUserId()).operationDate(new Date()).operationSource(request.getRequestURL().toString())
                    .operationDesc(OperationEnum.REMOVE_FROM_BLACKLIST.getOperationName() + ":" + JSON.toJSONString(resultMap)).build());
        } catch (Exception ex) {
            log.error("Error when save operation log for Remove from Blacklist due to:", ex);
        }

        return remove ? Result.success() : Result.failed(ResultCode.CommonResultCode.UPDATE_FAILED);
    }

    @GetMapping(value = "is-blacklist-user")
    @ApiOperation(value = "判断传入的id/email是否属于黑名单，true=是", notes = "判断传入的id/email是否属于黑名单")
    public Result<Boolean> isBlackListUser(@RequestParam(required = false) Long userId, @RequestParam(required = false) String email) {
        try {
            return Result.success(rfqBlackListService.isBlacklistUser(userId, email));
        } catch (Exception e) {
            return Result.failed();
        }
    }

    /**
     * 新增
     *
     * @param userId
     * @param reviewerId
     * @param reviewerEmail
     * @return
     */
    @ApiOperation(value = "新增", notes = "新增")
    @GetMapping("/add")
    public Result add(@RequestParam Long userId, @RequestParam Long reviewerId, @RequestParam String reviewerEmail){
        try {
            boolean addResult=rfqBlackListService.add(userId,reviewerId,reviewerEmail);
            return addResult?Result.success():Result.failed();
        } catch (Exception e) {
            return Result.failed();
        }
    }

    /**
     * 新增
     * @param buyerId
     * @return
     */
    @AdminLogin
    @ApiOperation(value = "新增黑名单", notes = "新增黑名单,只能提供给admin console使用")
    @GetMapping("/v1/add")
    public Result add(@ApiIgnore UserVO user,@RequestParam Long buyerId){
        try {
            boolean addResult=rfqBlackListService.add(buyerId,user.getUserId(),user.getEmail());
            return addResult?Result.success():Result.failed();
        } catch (Exception e) {
            return Result.failed();
        }
    }

    /**
     * 新增
      * @param userId
     * @param userInfo
     * @return
     */
    @ApiOperation(value = "管理页面新增", notes = "管理页面新增")
    @GetMapping("/v1/admin-add")
    @AdminLogin
    public Result add(@RequestParam Long userId, @ApiIgnore UserVO userInfo){
        try {
            boolean addResult=rfqBlackListService.add(userId,userInfo.getUserId(),userInfo.getEmail());
            return addResult?Result.success():Result.failed(ResultCode.CommonResultCode.SYSTEM_ERROR);
        } catch (Exception e) {
            log.error("admin-add error,userId:{},reviewer:{}",userId,userInfo,e);
            return Result.failed(ResultCode.CommonResultCode.SYSTEM_ERROR);
        }
    }

    @ApiOperation(value = "管理页面新增", notes = "管理页面新增")
    @GetMapping("/v2/admin-add")
    @AdminLogin
    public Result addWithReason(@RequestParam("userId") Long userId, @RequestParam(value = "reason", required = false) String reason,
                         @RequestParam(value = "eblockType", required = false) String eblockType, @ApiIgnore UserVO userInfo) {
        log.info("admin console addWithReason by userId is:{}, reason is:{}, eblockType is:{}, userInfo:{}", userId, reason, eblockType, JSON.toJSONString(userInfo));

        boolean addResult = false;
        String status = STATUS_SUCCESS;
        try {
            addResult = newRfqBlackListFeign.addWithReason(RfqBlackListAddDTO.builder().buyerId(userId).reason(reason).type(eblockType).operatorId(userInfo.getUserId()).operatorEmail(userInfo.getEmail()).build()).getData();
        } catch (Exception e) {
            log.error("admin-add error,userId:{},reviewer:{}", userId, userInfo, e);
            status = STATUS_FAILED;
        }

        try {
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("User ID/Email", ResultUtil.getData(userFeign.getUserByUserId(userId)).getEmail());
            paramMap.put("Reason", reason);

            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("Status", status);
            resultMap.put("Condition", paramMap);

            operationLogFeign.saveOperationLog(OperationLogDTO.builder().operationName(OperationEnum.ADD_TO_BLACKLIST.getOperationName()).entityId(0L).entityType(EntityType.MSG_MANAGEMENT.getName())
                    .appName(ApplicationCode.ADMIN_CONSOLE.name()).orgId(0L).userId(userInfo.getUserId()).operationDate(new Date()).operationSource(request.getRequestURL().toString())
                    .operationDesc(OperationEnum.ADD_TO_BLACKLIST.getOperationName() + ":" + JSON.toJSONString(resultMap)).build());
        } catch (Exception ex) {
            log.error("Error when save operation log for Add to Blacklist due to:", ex);
        }

        return addResult ? Result.success() : Result.failed(ResultCode.CommonResultCode.SYSTEM_ERROR);
    }

    /**
     * 用户权限根据id/email搜索
     */
    @ApiOperation(value = "用户权限根据id/email搜索", notes = "用户权限根据id/email搜索")
    @GetMapping("/v1/search-by-keyword")
    public Result<SearchUserInfoVO> searchByEmailOrId(@RequestParam String key, @ApiParam("权限类型:black,white,block") @RequestParam(required = false) String eblockType) {
        log.info("admin console search By Email Or Id by key is:{}, eblockType is:{}", key, eblockType);
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(eblockType)) {
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }
        return newRfqBlackListFeign.searchByEmailOrId(key, eblockType);
    }

    /**
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/v2/add")
    public Result<Boolean> addV2(@RequestBody BlackListAddDTO dto){
        log.info("blacklist add v2 dto:{}",dto);
        return Result.success(rfqBlackListService.add(dto.getBuyerId(), dto.getReviewerId(), dto.getReviewerEmail(),dto.getSource(),dto.getReason()));
    }

    @ApiOperation(value = "批量拉黑用户模板下载", notes = "批量拉黑用户模板下载")
    @GetMapping("/v1/add-black-list/template/download")
    public void downloadInternalOperationLog(HttpServletResponse response
    ) {
        response.setContentType("text/csv;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=Blacklist by batch" + ".csv");
        CSVFormat csvFormat = CSVFormat.EXCEL.withHeader(UTF8_BOM + BUYER_ID, "Email address", "Blacklist Reason");
        try (final PrintWriter writer = response.getWriter();
             CSVPrinter csvPrinter = new CSVPrinter(writer, csvFormat);) {
            csvPrinter.flush();
        } catch (Exception e) {
            log.error("Black list add batch - download template error, msg: {}", e.getMessage(), e);
        }
    }

    @AdminLogin
    @ApiOperation(value = "批量拉黑用户-上传文件批量拉黑", notes = "upload.20022: 超过上限，upload.20023: 文件内容空， upload.20024: 内容错误，upload.20002：格式错误  ")
    @PostMapping("/v1/add-black-list/batch/upload")
    public Result<Boolean> uploadBlackListBatch(@ApiIgnore UserVO userVO, @Valid @NotNull(message = "file is null") MultipartFile file) {
        Long userId = Optional.ofNullable(userVO).map(UserVO::getUserId).orElse(null);
        rfqBlackListService.uploadBlackListBatch(userId, file);
        return Result.success(true);
    }


}
