package com.globalsources.admin.controller;

import com.globalsources.admin.constants.UserConstant;
import com.globalsources.admin.model.dto.app.AppActivityDTO;
import com.globalsources.admin.service.AppActivityService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.JwtUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.supplierconsole.agg.api.constant.ApplicationCode;
import com.globalsources.supplierconsole.agg.api.constant.EntityType;
import com.globalsources.supplierconsole.agg.api.constant.OperationEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Chen
 * @date 2022/1/4 20:36
 */

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@Api(tags = {"app-activity"})
@RequestMapping("/app-activity")
public class AppActivityController {

    @Autowired
    private AppActivityService appActivityService;

    @PostMapping(value = "/v1/save")
    @Login
    @ApiOperation(value = "记录app activity", notes = "记录app activity")
    public Result<Boolean> saveAppActivity(@ApiIgnore UserVO userVO, @RequestBody AppActivityDTO dto) {
        Boolean save = appActivityService.save(dto);
        return Result.success(save);
    }

    @GetMapping(value = "/v1/month/list")
    @AdminLogin
    @ApiOperation(value = "月份下拉列表", notes = "月份下拉列表")
    public Result<List<Date>> queryDateList(@ApiIgnore UserVO userVO) {
        List<Date> queryDateList = appActivityService.getQueryDateList();
        return Result.success(queryDateList);
    }

    @ApiOperation(value = "下载app activity report", notes = "下载app activity report")
    @com.globalsources.supplierconsole.agg.api.log.annotation.OperationLog(operation = OperationEnum.DOWNLOAD, entityType = EntityType.APP_ACTIVE_REPORT,
            operationDesc = "download app activity report", ignoreEntityId = true, ignoreOrgId = true, applicationCode = ApplicationCode.ADMIN_CONSOLE,
            operationDescParamName = {"monthDate", "userId"}, operationDescParamValueSpEl = {"#monthDate", "#userId"})
    @GetMapping("/v1/download")
    public void download(@ApiIgnore UserVO userInfo, @RequestParam("monthDate") Long monthDate,
                         @RequestParam("auth") String auth,
                                 HttpServletResponse response){
        boolean downloadTokenValidFlag = false;
        try {
            String downloadKey = JwtUtil.decrypt(UserConstant.USER_DOWNLOAD_TOKEN_AES_KEY, auth);
            downloadKey = Optional.ofNullable(downloadKey).orElse(StringUtils.EMPTY);
            String timestampStr = StringUtils.substringAfterLast(downloadKey, "_");
            long expireMillis = Long.parseLong(timestampStr);
            downloadTokenValidFlag = System.currentTimeMillis() <= expireMillis;
        } catch (Exception e) {
            log.warn(ResultCode.AdminConsoleResultCode.INVALID_USER_DOWNLOAD_TOKEN.getMsg() + ", userId: " + userInfo.getUserId()
                    +  ", auth: " + auth + ", msg: " + e.getMessage(), e);
//            throw new BusinessException(ResultCode.AdminConsoleResultCode.INVALID_USER_DOWNLOAD_TOKEN)
        }
        if (!downloadTokenValidFlag) {
            log.warn("user download token expire" + ", userId: " + userInfo.getUserId() + ", auth: " + auth);
//            throw new BusinessException(ResultCode.AdminConsoleResultCode.INVALID_USER_DOWNLOAD_TOKEN)
        }
        Date date = new Date(monthDate);
        appActivityService.downloadReport(response, date);
    }
}
