package com.globalsources.admin.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.globalsources.admin.model.dto.AcAnnouncementDTO;
import com.globalsources.admin.model.pojo.AcAnnouncement;
import com.globalsources.admin.model.vo.AcAnnouncementMessageVO;
import com.globalsources.admin.model.vo.AcAnnouncementVO;
import com.globalsources.admin.service.AcAnnouncementService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 走马灯公告 前端控制器
 * </p>
 *
 * <AUTHOR> Li
 * @since 2022-06-20
 */
@Slf4j
@Validated
@RestController
@RequestMapping("ac-announcement")
@RequiredArgsConstructor
@Api(tags = {"走马灯公告 操作"})
public class AcAnnouncementController {

    private final AcAnnouncementService acAnnouncementService;

    @GetMapping("/list")
    @AdminLogin
    @ApiOperation(value = "走马灯公告列表:roleType(s,b),announcementType(desktop,mobile,app)", tags = "走马灯公告")
    public Result<PageResult<AcAnnouncementVO>> list(@RequestParam Integer pageNum,
                                                     @RequestParam Integer pageSize,
                                                     @RequestParam String roleType,
                                                     @RequestParam  String announcementType) {
        return Result.success(acAnnouncementService.findAll(pageNum, pageSize, roleType, announcementType));
    }

    @PostMapping("/save/{roleType}/{announcementType}")
    @AdminLogin
    @ApiOperation(value = "走马灯公告新增:roleType(s,b),announcementType(desktop,mobile,app)", tags = "走马灯公告")
    public Result<String> save(@ApiIgnore UserVO userVO, @PathVariable String roleType, @PathVariable String announcementType, @RequestBody AcAnnouncementDTO acAnnouncementDTO) {
        return acAnnouncementService.insert(acAnnouncementDTO, userVO.getUserId(), roleType, announcementType) > 0
                ? Result.success() : Result.success(ResultCode.CommonResultCode.SAVE_FAILED.getCode(), null);
    }

    @PostMapping("/edit/{roleType}/{announcementType}")
    @AdminLogin
    @ApiOperation(value = "走马灯公告修改", tags = "走马灯公告")
    public Result<String> edit(@ApiIgnore UserVO userVO,@PathVariable String roleType, @PathVariable String announcementType, @RequestBody AcAnnouncementDTO acAnnouncementDTO) {
        return acAnnouncementService.edit(acAnnouncementDTO, userVO.getUserId(), roleType, announcementType) > 0 ? Result.success() : Result.success(ResultCode.CommonResultCode.UPDATE_FAILED.getCode(), null);
    }

    @PostMapping("/remove")
    @AdminLogin
    @ApiOperation(value = "走马灯公告删除", tags = "走马灯公告")
    public Result<String> remove(@ApiIgnore UserVO userVO, @RequestParam Integer announcementId) {
        return acAnnouncementService.update(AcAnnouncement.builder()
                .deleteFlag(true)
                .lUpdBy(userVO.getUserId())
                .lUpdDate(new Date()).build(), new LambdaQueryWrapper<AcAnnouncement>()
                .eq(AcAnnouncement::getAnnouncementId, announcementId)) ? Result.success() : Result.success(ResultCode.CommonResultCode.DELETE_FAILED.getCode(), null);
    }

    @PostMapping("/on-off-switch")
    @AdminLogin
    @ApiOperation(value = "走马灯开关", tags = "走马灯公告")
    public Result<String> OnOffSwitch(@ApiIgnore UserVO userVO, @RequestParam Integer announcementId, @RequestParam Boolean flag) {
        String onlineStatus = flag ? "Online" : "Offline";
        return acAnnouncementService.update(AcAnnouncement.builder()
                .onlineStatus(onlineStatus)
                .lUpdBy(userVO.getUserId())
                .lUpdDate(new Date()).build(), new LambdaQueryWrapper<AcAnnouncement>()
                .eq(AcAnnouncement::getAnnouncementId, announcementId)) ? Result.success() : Result.success(ResultCode.CommonResultCode.DELETE_FAILED.getCode(), null);
    }

    @GetMapping("/down")
    @AdminLogin
    @ApiOperation(value = "走马灯公告down", tags = "走马灯公告")
    public Result<String> downAnnouncement(@ApiIgnore UserVO userVO, @RequestParam Integer announcementId, @RequestParam String roleType, @RequestParam  String announcementType) {
        return acAnnouncementService.downAnnouncement(announcementId, userVO.getUserId(), roleType, announcementType) > 0 ? Result.success() : Result.success(ResultCode.CommonResultCode.METHOD_NOT_ALLOWED.getCode(), null);
    }

    @GetMapping("/up")
    @AdminLogin
    @ApiOperation(value = "走马灯公告up", tags = "走马灯公告")
    public Result<String> upAnnouncement(@ApiIgnore UserVO userVO, @RequestParam Integer announcementId, @RequestParam String roleType, @RequestParam  String announcementType) {
        return acAnnouncementService.upAnnouncement(announcementId, userVO.getUserId(), roleType, announcementType) > 0 ? Result.success() : Result.success(ResultCode.CommonResultCode.METHOD_NOT_ALLOWED.getCode(), null);
    }

    @GetMapping("/notice")
    @ApiOperation(value = "走马灯api", tags = "走马灯公告")
    public Result<List<AcAnnouncementMessageVO>> notice(@RequestParam String roleType, @RequestParam String announcementType) {
        return Result.success(acAnnouncementService.notice(roleType, announcementType));
    }

}

