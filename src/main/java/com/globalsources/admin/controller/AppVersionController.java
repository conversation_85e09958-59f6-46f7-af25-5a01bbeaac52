package com.globalsources.admin.controller;

import com.globalsources.admin.model.dto.AppVersionDTO;
import com.globalsources.admin.model.pojo.AppVersion;
import com.globalsources.admin.service.AppVersionService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

@Api(tags = "app版本管理")
@Slf4j
@Validated
@RequestMapping("/app/version")
@RestController
public class AppVersionController {
    @Autowired
    private AppVersionService appVersionService;

    @ApiOperation("查询列表")
    @AdminLogin
    @GetMapping("/list")
    public Result<PageResult<AppVersion>> getAppVersionList(@ApiParam("应用名字") @RequestParam String appName,@ApiParam("页码") @RequestParam Long pageNum,
                                @ApiParam("页大小") @RequestParam Long pageSize){
        return appVersionService.getPageList(appName,pageNum,pageSize);
    }

    @ApiOperation("删除")
    @AdminLogin
    @GetMapping("/delete")
    public Result<Void> deleteAppVersion(@ApiParam("版本id") @RequestParam Long id){
        return appVersionService.delete(id);
    }

    @ApiOperation("编辑")
    @AdminLogin
    @PostMapping("/edit")
    public Result<Void> editAppVersion(@ApiIgnore UserVO userInfo, @Valid @RequestBody AppVersionDTO dto){
        return appVersionService.editAppVersion(userInfo.getUserId(),dto);
    }

    @ApiOperation("添加")
    @AdminLogin
    @PostMapping("/add")
    public Result<Void> addAppVersion(@ApiIgnore UserVO userInfo,@Valid @RequestBody AppVersionDTO dto){
        return appVersionService.addAppVersion(userInfo.getUserId(),dto);
    }
}
