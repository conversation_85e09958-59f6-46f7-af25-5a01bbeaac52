package com.globalsources.admin.controller;


import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.fastjson.JSON;
import com.globalsources.admin.model.dto.BuyerRfiSurveyListDTO;
import com.globalsources.admin.model.dto.BuyerRfiSurveyUpdateDTO;
import com.globalsources.admin.model.vo.BuyerRfiSurveyVO;
import com.globalsources.admin.model.vo.inquiry.TsBuyerSurveyProcessVO;
import com.globalsources.admin.service.BuyerRfiSurveyService;
import com.globalsources.admin.util.HttpHeadUtil;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.DateUtil;
import com.globalsources.supplierconsole.agg.api.constant.ApplicationCode;
import com.globalsources.supplierconsole.agg.api.constant.EntityType;
import com.globalsources.supplierconsole.agg.api.constant.OperationEnum;
import com.globalsources.supplierconsole.agg.api.log.aspect.OperationLogAggApiInject;
import com.globalsources.supplierconsole.agg.api.log.dto.OperationLogDTO;
import com.globalsources.supplierconsole.agg.api.log.feign.OperationLogFeign;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.OutputStreamWriter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * trade show buyer rfi survey 前端控制器
 * </p>
 *
 * <AUTHOR> Li
 * @since 2023-11-27
 */
@Slf4j
@Api(tags = "Buyer Consent Form")
@RequestMapping("/survey")
@RestController
@RequiredArgsConstructor
public class BuyerRfiSurveyController {

    private final BuyerRfiSurveyService buyerRfiSurveyService;
    @Autowired
    private OperationLogFeign operationLogFeign;
    @Autowired
    private OperationLogAggApiInject operationLogAggApiInject;
    @Autowired
    private HttpServletRequest request;

    @ApiOperation("Buyer Consent List")
    @AdminLogin
    @PostMapping("/list")
    public Result<PageResult<BuyerRfiSurveyVO>> pageResult(@RequestBody BuyerRfiSurveyListDTO dto) {
        PageResult<BuyerRfiSurveyVO> buyerRfiSurveyVOPageResult = buyerRfiSurveyService.pageResult(dto);
        if (CollectionUtils.isNotEmpty(buyerRfiSurveyVOPageResult.getList())) {
            buyerRfiSurveyVOPageResult.getList().forEach(item -> item.setCategoryIds(null));
        }
        return Result.success(buyerRfiSurveyVOPageResult);
    }

    @ApiOperation("Resend")
    @AdminLogin
    @PostMapping("/resend")
    public Result<Boolean> resend(@RequestBody List<Long> idList) {
        if(CollectionUtils.isEmpty(idList)){
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }
        return Result.success(buyerRfiSurveyService.resend(idList));
    }

    @ApiOperation("getProcessList")
    @GetMapping("/process-list")
    public Result<List<TsBuyerSurveyProcessVO>> getProcessList() {
        return Result.success(buyerRfiSurveyService.getProcessList(null));
    }

    @ApiOperation("updateStatus")
    @GetMapping("/update-status")
    public Result<Boolean> updateStatus(@RequestParam(value = "id") Integer id,@RequestParam(value = "processStatus") String processStatus,@RequestParam(value = "errorCode") String errorCode) {
        if(Objects.isNull(id) || StringUtils.isBlank(processStatus)){
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }
        return Result.success(buyerRfiSurveyService.updateStatus(id,processStatus,errorCode));
    }

    @ApiOperation("下载")
    @GetMapping("/export")
    public void export(HttpServletResponse response, @RequestParam String searchKey, @RequestParam String processStatus) {

        PageResult<BuyerRfiSurveyVO> pageResult = buyerRfiSurveyService.pageResult(BuyerRfiSurveyListDTO.builder().searchKey(searchKey).processStatus(processStatus).pageNum(1).pageSize(20).build());

        String filename= "Buyer Consent Form_"+ DateUtil.data2str(new Date(),DateUtil.DATETIMESTRING)+".csv";

        if(CollectionUtils.isEmpty(pageResult.getList())){
            return;
        }

        String exportStatus = "Success";
        String exportNumber = String.valueOf(pageResult.getList().size());

        try(CsvWriter writer=new CsvWriter(new OutputStreamWriter(response.getOutputStream()))){
            HttpHeadUtil.setDownHeader(response,filename,true);

            List<BuyerRfiSurveyVO> list = pageResult.getList();
            //写入下载表头
            writer.writeLine("No.","First Name", "Last Name","Visitor ID","Email","GSOL Account","Buyer ID","Country/Region Code","Country/Region","Continent","Other Products","No.Of Supplier","Product Quantity","Business Card(Front)","Business Card (Back)","Consent Form","Staff Name","Category","Show","Update Time","Status","Remark","Company Name");
            for (int i = 2; i<=pageResult.getTotalPage(); i++) {
                PageResult<BuyerRfiSurveyVO> buyerRfiSurveyVOPageResult = buyerRfiSurveyService.pageResult(BuyerRfiSurveyListDTO.builder().searchKey(searchKey).pageNum(i).pageSize(20).build());
                if (CollectionUtils.isNotEmpty(buyerRfiSurveyVOPageResult.getList())) {
                    list.addAll(buyerRfiSurveyVOPageResult.getList());
                }
            }
            writeCsvRowData(writer, list);
        }catch (Exception e){
            log.error("buyer rfi survey list return error : {}", JSON.toJSONString(e));
            exportStatus = "Fail";
            exportNumber = "0";
        }

        try {
            Map<String, String> descMap = Maps.newHashMap();
            descMap.put("Status", exportStatus);
            descMap.put("Number of exported rows", exportNumber);

            if (StringUtils.isNotBlank(searchKey)) {
                descMap.put("Search Key", searchKey);
            }
            if (StringUtils.isNotBlank(processStatus)) {
                descMap.put("Process Status", processStatus);
            }
            operationLogFeign.saveOperationLog(OperationLogDTO.builder().operationName(OperationEnum.EXPORT.getOperationName()).entityId(0L).entityType(EntityType.ONSITE_TRADESHOW_BUYER_CONSENT.getName())
                    .appName(ApplicationCode.ADMIN_CONSOLE.name()).orgId(0L).userId(operationLogAggApiInject.getAdminUserId()).operationDate(new Date()).operationSource(request.getRequestURL().toString())
                    .operationDesc(JSON.toJSONString(descMap)).build());
        } catch (Exception ex) {
            log.error("Error when save operation log {}", ex);
        }
    }

    private void writeCsvRowData(CsvWriter writer, List<BuyerRfiSurveyVO> list){
        String dateFormat="yyyy/MM/dd";

        for(BuyerRfiSurveyVO e:list){
            String referralTime = "";
            if(e.getLUpdDate()!=null){
                referralTime=DateUtil.data2str(e.getLUpdDate(),dateFormat);
            }
            writer.writeLine(e.getBuyerRfiSurveyId(),
                    e.getFirstName(),
                    e.getLastName(),
                    e.getVisitorId(),
                    e.getEmailAddr(),
                    Boolean.TRUE.equals(e.getSsoFlag()) ? "Yes" : "No",
                    Objects.nonNull(e.getBuyerId())?String.valueOf(e.getBuyerId()):"",
                    e.getBuyerCountryCode(),
                    e.getBuyerCountryName(),
                    e.getBuyerContinentName(),
                    e.getRemark(),
                    e.getSupplierCntPerCategory(),
                    e.getProductCntCode().equals("Pieces") ? e.getProductCnt()+"" : e.getProductCntCode(),
                    e.getBuyerBusinessCardUrl(),
                    e.getBuyerBusinessCardBackUrl(),
                    e.getSnapshotUrl(),
                    e.getStaffName(),
                    StringUtils.isNotEmpty(e.getCategoryIds()) ? e.getCategoryIds() : "",
                    e.getShowName(),
                    referralTime,
                    e.getProcessStatus(),
                    e.getAcRemark(),
                    e.getCompanyName());
        }
    }

    @ApiOperation("更新Remark")
    @AdminLogin
    @PostMapping("/update-ac-remark")
    public Result<Boolean> updateAcRemark(@Valid @RequestBody BuyerRfiSurveyUpdateDTO dto) {
        return Result.success(buyerRfiSurveyService.updateAcRemark(dto));
    }

    @ApiOperation("Mark as Completed")
    @AdminLogin
    @PostMapping("/mark-as-completed")
    public Result<Boolean> markAsCompleted(@RequestBody List<Long> idList) {
        return Result.success(buyerRfiSurveyService.markAsCompleted(idList));
    }
}

