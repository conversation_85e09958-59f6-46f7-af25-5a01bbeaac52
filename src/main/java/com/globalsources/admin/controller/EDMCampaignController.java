package com.globalsources.admin.controller;

import com.globalsources.admin.model.dto.EDMCampaignDTO;
import com.globalsources.admin.model.dto.EDMEnumListDTO;
import com.globalsources.admin.model.vo.EDMCampaignVO;
import com.globalsources.admin.service.EDMCampaignService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * EDM Campaign Controller
 *
 * <AUTHOR> @since 2021-11-12
 */
@Slf4j
@Validated
@Api(tags = {"EDM Campaign 相关接口"})
@RestController
@RequiredArgsConstructor
@RequestMapping("edm")
public class EDMCampaignController {

    @Autowired
    private EDMCampaignService edmCampaignService;

    @GetMapping(value = "/list")
    @ApiOperation(value = "EDM Campaign list 接口")
    public Result<PageResult<EDMCampaignVO>> getEDMCampaignList(Integer pageSize, Integer pageNum) {
        log.info("Input param in getEDMCampaignList, pageSize = {}, pageNum = {}", pageSize, pageNum);
        try {
            PageResult<EDMCampaignVO> edmCampaignVOPageResult = edmCampaignService.getEDMCampaignList(pageNum, pageSize);
            return Result.success(edmCampaignVOPageResult);
        } catch (Exception e) {
            log.error("getEDMCampaignList error, ", e);
            return Result.failed(e.getMessage());
        }
    }

    @AdminLogin
    @PostMapping(value = "/create")
    @ApiOperation(value = "Create EDM Campaign")
    public Result<Long> createEDMCampaign(@ApiIgnore UserVO userVO, EDMCampaignDTO edmCampaignDTO) {
        log.info("Input param in createEDMCampaign, edmCampaignDTO={}", ToStringBuilder.reflectionToString(edmCampaignDTO));
        try {
            Long campaignId = edmCampaignService.create(edmCampaignDTO, userVO);
            return Result.success(campaignId);
        } catch (Exception e) {
            log.error("createEDMCampaign error, ", e);
            return Result.failed(e.getMessage());
        }
    }

    @AdminLogin
    @PostMapping(value = "/edit/{campaignId}")
    @ApiOperation(value = "Edit EDM Campaign")
    public Result<EDMCampaignVO> editEDMCampaign(@ApiIgnore UserVO userVO, @PathVariable Long campaignId) {
        log.info("Input param in editEDMCampaign, campaignId = {}", campaignId);
        try {
            EDMCampaignVO edmCampaignVO = edmCampaignService.getEDMCampaign(campaignId);
            return Result.success(edmCampaignVO);
        } catch (Exception e) {
            log.error("editEDMCampaign error, ", e);
            return Result.failed(e.getMessage());
        }
    }

    @AdminLogin
    @PostMapping(value = "/update")
    @ApiOperation(value = "Update EDM Campaign")
    public Result<Boolean> updateEDMCampaign(@ApiIgnore UserVO userVO, EDMCampaignDTO edmCampaignDTO) {
        log.info("Input param in updateEDMCampaign, edmCampaignDTO={}", ToStringBuilder.reflectionToString(edmCampaignDTO));
        try {
            int count = edmCampaignService.update(edmCampaignDTO, userVO);
            return Result.success(count == 1);
        } catch (Exception e) {
            log.error("updateEDMCampaign error, ", e);
            if (e instanceof BusinessException) {
                return Result.failed(((BusinessException) e).getCode(), e.getMessage());
            }

            return Result.failed(e.getMessage());
        }
    }

    @GetMapping(value = "/enum/list")
    @ApiOperation(value = "Get All EDM Enum")
    public Result<EDMEnumListDTO> getAllEDMEnum() {
        try {
            return Result.success(edmCampaignService.getAllEDMEnum());
        } catch (Exception e) {
            log.error("getAllEDMEnum error, ", e);
            return Result.failed(e.getMessage());
        }
    }

    @GetMapping(value = "/copyHTML/{campaignId}")
    @ApiOperation(value = "Get HTML Template")
    public Result<String> getHTMLTemplate(@PathVariable Long campaignId) {
        try {
            return Result.success(edmCampaignService.getHTMLTemplate(campaignId));
        } catch (Exception e) {
            log.error("getHTMLTemplate error, ", e);
            return Result.failed(e.getMessage());
        }
    }

}
