package com.globalsources.admin.controller;


import com.globalsources.admin.model.pojo.VideoContentReport;
import com.globalsources.admin.service.VideoContentReportService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@Api(tags = {"Discover Report"})
@RequestMapping("/ac")
public class VideoContentReportController {

    @Autowired
    private VideoContentReportService videoContentReportService;

    @GetMapping(value = "/v1/discover/report/list")
    @AdminLogin
    @ApiOperation(value = "查询category field keyword列表", notes = "查询category field keyword列表")
    public Result<PageResult<VideoContentReport>> getDiscoverReportListPage(@ApiIgnore UserVO userInfo,
                                                                            @RequestParam(value = "pageNum", defaultValue = "1") Long pageNum,
                                                                            @RequestParam(value = "pageSize", defaultValue = "20") Long pageSize) {
        PageResult<VideoContentReport> videoContentReportPage = videoContentReportService.getVideoContentReportPage(pageNum, pageSize);
        return Result.success(videoContentReportPage);
    }

}
