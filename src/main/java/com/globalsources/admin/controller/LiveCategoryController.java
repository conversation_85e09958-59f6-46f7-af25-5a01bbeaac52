package com.globalsources.admin.controller;

import com.globalsources.activity.agg.api.dto.LiveCategoryDTO;
import com.globalsources.activity.agg.api.dto.LiveCategoryEditDTO;
import com.globalsources.activity.agg.api.vo.LiveCatInfoVO;
import com.globalsources.admin.service.LiveCategoryService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;

@Api(tags = "直播分类")
@RequestMapping("/live/category")
@RestController
public class LiveCategoryController {
    @Resource
    private LiveCategoryService liveCategoryService;

    @ApiOperation("添加分类")
    @PostMapping("/add")
    @AdminLogin
    public Result<Void> addCategory(@ApiIgnore UserVO user, @RequestBody LiveCategoryDTO dto){
        return liveCategoryService.addCategory(user.getUserId(),dto);
    }

    @ApiOperation("编辑分类")
    @PostMapping("/edit")
    @AdminLogin
    public Result<Void> editCategory(@ApiIgnore UserVO user, @RequestBody LiveCategoryEditDTO dto){
        return liveCategoryService.editCategory(user.getUserId(),dto);
    }

    @ApiOperation("启用停用")
    @GetMapping("/switch/enabled")
    @AdminLogin
    public Result<Void> switchEnabled(@ApiIgnore UserVO user, @ApiParam("分类id") @RequestParam Long categoryId, @ApiParam("0.停用, 1启用") @RequestParam Integer enabled){
        boolean enabledFlag;
        if(enabled==1){
            enabledFlag=true;
        }else if(enabled==0){
            enabledFlag=false;
        }else{
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }

        return liveCategoryService.switchEnabled(user.getUserId(),categoryId,enabledFlag);
    }

    @ApiOperation("删除")
    @GetMapping("/delete")
    @AdminLogin
    public Result<Void> delCategory(@ApiIgnore UserVO user, @ApiParam("分类id") @RequestParam Long categoryId){
        return liveCategoryService.delCategory(user.getUserId(),categoryId);
    }

    @ApiOperation("列表")
    @AdminLogin
    @GetMapping("/list")
    public Result<PageResult<LiveCatInfoVO>> list(@ApiParam("页号") @RequestParam Long pageNum, @ApiParam("页大小") @RequestParam Long pageSize){
        return liveCategoryService.getCategoryPage(pageNum,pageSize);
    }
}
