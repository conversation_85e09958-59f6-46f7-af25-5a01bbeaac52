package com.globalsources.admin.controller;

import com.globalsources.activity.agg.api.dto.ReviewDTO;
import com.globalsources.activity.core.api.dto.BestSupplierDTO;
import com.globalsources.activity.core.api.dto.SearchBestSupplierDTO;
import com.globalsources.admin.dao.AcUserMapper;
import com.globalsources.admin.model.vo.LiveBestSupplierDetailVO;
import com.globalsources.admin.model.vo.LiveBestSupplierExportVO;
import com.globalsources.admin.model.vo.LiveBestSupplierVO;
import com.globalsources.admin.model.vo.user.AssignUserInfoVO;
import com.globalsources.admin.service.AcUserReviewPermissionService;
import com.globalsources.admin.service.PopularExhibitorService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.result.ResultCodeEnum;
import com.globalsources.framework.utils.DateUtil;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "直播优选供应商")
@Validated
@Slf4j
@RequestMapping("/live/popular/exhibitor")
@RestController
public class PopularExhibitorController {
    private static final String VIRTUAL_SHOW="VIRTUAL_SHOW";
    @Resource
    private AcUserReviewPermissionService userPermissionService;
    @Resource
    private PopularExhibitorService popularExhibitorService;
    @Resource
    private AcUserMapper acUserMapper;

    private boolean isManager(Long userId){
        List<String> list = userPermissionService.getTypeOfViewAllTrueByUserId(userId);
        return list.contains(VIRTUAL_SHOW);
    }

    private boolean isAuditor(Long userId){
        List<String> list = userPermissionService.getTypeOfAssignableTrueByUserId(userId);
        return list.contains(VIRTUAL_SHOW);
    }

    @ApiOperation("可分配人列表")
    @AdminLogin
    @GetMapping("/viewer/list")
    public Result<List<AssignUserInfoVO>> getVirtualUserList(@ApiIgnore UserVO user){
        List<AssignUserInfoVO> list=acUserMapper.selectAssignUserListV3(VIRTUAL_SHOW);
        if(CollectionUtils.isEmpty(list)){
            list=new ArrayList<>();
            return Result.success(list);
        }

        Long userId= user.getUserId();

        if(isManager(userId)){//all 权限
            return Result.success(list);
        }
        else if(isAuditor(userId)){//审核人员只能看自己
            list=list.stream().filter(e-> e.getAssignedUserId().equals(userId)).collect(Collectors.toList());
            return Result.success(list);
        }else{
            return Result.success();
        }
    }

    @ApiOperation("分配视频")
    @AdminLogin
    @GetMapping("/assign")
    public Result<Void> assignTo(@ApiIgnore UserVO user,@RequestParam Long assignedUserId,@RequestParam Long bestSuppId){
        Long userId=user.getUserId();
        if(!isManager(userId)){
            return Result.failed(ResultCode.CommonResultCode.FORBIDDEN);
        }
        if(!isAuditor(assignedUserId)){
            return Result.failed(ResultCode.CommonResultCode.DATA_NON_EXISTENT.getCode());
        }

        return popularExhibitorService.assignTo(user.getUserId(),assignedUserId,bestSuppId);
    }

    @ApiOperation("搜索")
    @AdminLogin
    @PostMapping(value = "/search")
    public Result<PageResult<LiveBestSupplierVO>> search(@ApiIgnore UserVO user,@RequestBody SearchBestSupplierDTO searchDTO){
        Long userId= user.getUserId();
        //查询用户权限
        if(isManager(userId)){//all 权限
            log.info("manager search live best supplier,userId:{},searchDTO:{}",userId,searchDTO);
        }
        else if(isAuditor(userId)){//审核人员
            searchDTO.setAssignedUserId(Arrays.asList(user.getUserId()));
            log.info("auditor search live best supplier, userId:{},searchDTO:{}",userId,searchDTO);
        }else{
            return Result.success();
        }

        return popularExhibitorService.search(searchDTO);
    }

    @ApiOperation(value = "保存",notes = "如果没有id则视为创建")
    @AdminLogin
    @PostMapping("/save")
    public Result<Void> save(@ApiIgnore UserVO user, @Valid @RequestBody BestSupplierDTO dto){
        dto.setOpUserId(user.getUserId());
        return popularExhibitorService.save(dto);
    }

    @ApiOperation("详情")
    @AdminLogin
    @GetMapping("/info")
    public Result<LiveBestSupplierDetailVO> info(@ApiIgnore UserVO user,@RequestParam Long bestSuppId){
        return popularExhibitorService.getInfo(bestSuppId);
    }

    @ApiOperation("enabled切换")
    @AdminLogin
    @GetMapping("/enabled/switch")
    public Result<Void> enabledSwitch(@ApiIgnore UserVO user,@RequestParam Long bestSuppId,@RequestParam Boolean enabled){
        return popularExhibitorService.switchEnabled(user.getUserId(),bestSuppId,enabled);
    }

    @ApiOperation("删除")
    @AdminLogin
    @GetMapping("/delete")
    public Result<Void> delete(@ApiIgnore UserVO user, @RequestParam Long bestSuppId){
        return popularExhibitorService.delete(user.getUserId(),bestSuppId);
    }

    @ApiOperation("审批")
    @AdminLogin
    @PostMapping("/review")
    public Result<Void> review(@ApiIgnore UserVO user, @Valid @RequestBody ReviewDTO reviewDTO){
        Long userId= user.getUserId();
        reviewDTO.setOpUserId(userId);
        return popularExhibitorService.review(reviewDTO);
    }


    @ApiOperation("导出优选展商")
    @AdminLogin
    @PostMapping("/export")
    public void export(@ApiIgnore UserVO user, HttpServletResponse response,@RequestBody SearchBestSupplierDTO searchDTO) {
        Long userId= user.getUserId();
        //查询用户权限
        if(isManager(userId)){//all 权限
            log.info("manager search live best supplier,userId:{},searchDTO:{}",userId,searchDTO);
        }
        else if(isAuditor(userId)){//审核人员
            searchDTO.setAssignedUserId(Arrays.asList(user.getUserId()));
            log.info("auditor search live best supplier, userId:{},searchDTO:{}",userId,searchDTO);
        }else{
            return;
        }

        List<LiveBestSupplierVO> list = popularExhibitorService.export(searchDTO);

        List<LiveBestSupplierExportVO> exportList = OrikaMapperUtil.coverList(list, LiveBestSupplierExportVO.class);

        String filename="exhibitor_supplier_"+ DateUtil.date2String(new Date(),DateUtil.PATTERN_YYYYMMDD)+".xls";
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("template/exhibitorSupplierVideo.xls"); ServletOutputStream os = response.getOutputStream()) {
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("content-disposition", "attachment;filename=" + filename);
            Map<String, Object> value = new HashMap<>();
            value.put("dataList", exportList);
            Context context = new Context(value);
            JxlsHelper.getInstance().processTemplate(is, os, context);
            log.info("Download point template success");
        } catch (Exception e) {
            log.error("download point template error,{}", e);
            Result.failed(ResultCodeEnum.FAILED);
        }
    }
}
