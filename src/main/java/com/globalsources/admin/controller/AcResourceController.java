package com.globalsources.admin.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.globalsources.admin.constants.AdminConstants;
import com.globalsources.admin.model.dto.AcResourceDTO;
import com.globalsources.admin.model.dto.AcResourceMoveDTO;
import com.globalsources.admin.model.dto.resource.AcExportResourceDTO;
import com.globalsources.admin.model.dto.resource.ImportResourceCodeUriDataDTO;
import com.globalsources.admin.model.dto.resource.ImportResourceCodeUriLinkDTO;
import com.globalsources.admin.model.dto.route.RouterBaseDTO;
import com.globalsources.admin.model.vo.AcMenuTreeBaseVO;
import com.globalsources.admin.service.AcResourceContractLevelLinkService;
import com.globalsources.admin.service.AcResourceService;
import com.globalsources.admin.service.PermissionService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api(tags = "资源管理")
@Slf4j
@Validated
@RequestMapping("/ac/resource")
@RestController
public class AcResourceController {
    @Resource
    private AcResourceService resourceService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private AcResourceContractLevelLinkService acResourceContractLevelLinkService;


    @AdminLogin
    @ApiOperation("初始化")
    @PostMapping("/init")
    public Result<Void> init(@ApiParam("资源数据") @RequestBody List<RouterBaseDTO> data,
                             @ApiParam(value = "ADMIN: admin console, SC: supplier center") @RequestParam(value = "appName", defaultValue = "ADMIN") String appName) {
        log.info("init admin console resource data:{}", JSON.toJSONString(data));

        try {
            resourceService.processInit(0l, 0, data, appName);
            return Result.success();
        } catch (Exception e) {
            log.error("init admin console resource occur exception, json:{}", JSON.toJSONString(data), e);
            return Result.error();
        }
    }

    @ApiOperation("编辑")
    @AdminLogin
    @PostMapping("/edit")
    public Result<Void> edit(@ApiIgnore UserVO user, @RequestBody AcResourceDTO dto) {
        try {
            return resourceService.edit(user.getUserId(), dto);
        } catch (Exception e) {
            log.error("edit resource occur exception dto:{}", JSON.toJSONString(dto), e);
            return Result.error();
        }
    }

    @ApiOperation("删除")
    @AdminLogin
    @GetMapping("/delete")
    public Result<Void> delete(@ApiParam("资源id") @RequestParam Long resourceId) {
        return resourceService.delete(resourceId);
    }

    @ApiOperation("移动")
    @AdminLogin
    @PostMapping("/move")
    public Result<Void> move(@ApiIgnore UserVO user, @RequestBody AcResourceMoveDTO dto) {
        return resourceService.move(user.getUserId(), dto);
    }

    @ApiOperation("所有资源树")
    @AdminLogin
    @GetMapping("/tree")
    public Result<List<AcMenuTreeBaseVO>> getResourceTree(@ApiIgnore UserVO user,
                                                          @ApiParam("显示类型:0.不显示page 1.显示page") @RequestParam(required = false, defaultValue = "1") Integer type,
                                                          @ApiParam("根资源id, 0:顶级资源id") @RequestParam Long parentResourceId,
                                                          @ApiParam(value = "ADMIN: admin console, SC: supplier center") @RequestParam(value = "appName", defaultValue = "ADMIN") String appName) {

        Result<List<AcMenuTreeBaseVO>> result = resourceService.getResourceTree(type == 1, parentResourceId, appName);

        boolean adminFlag = Boolean.TRUE.equals(user.getAdministratorFlag());
        //如果是编辑角色显示的资源列表需要与用户登录的资源进行交集处理
        if (result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode()) && type != 1 && !adminFlag
                && !AdminConstants.PermissionAppNameEnum.SC.getValue().equals(appName)) {
            //卖家角色编辑页 显示全部资源
            result.setData(permissionService.getUserResourceTree(user.getUserId(), result.getData(), appName));
        }

        return result;
    }

    @ApiOperation("手动刷新 资源-角色关系缓存 和 资源-合同等级缓存")
    @GetMapping("/resource-role-link/refresh-cache")
    public Result<Void> refreshResourceRoleLinkCache(@RequestParam("appName") String appName) {
        resourceService.refreshResourceRoleLinkCache(appName);
        acResourceContractLevelLinkService.refreshResourceContractLevelLinkCache(appName);
        return Result.success();
    }


    @AdminLogin
    @ApiOperation("初始化空page下的权限按钮")
    @GetMapping("v1/init-default-button-for-empty-page")
    public Result<Boolean> initDefaultButtonForEmptyPage(@RequestParam("appName") String appName) {
        Boolean result = resourceService.initDefaultButtonForEmptyPage(appName);
        return Result.success(result);
    }


    @AdminLogin
    @ApiOperation("重置Menu和Page的Resource code")
    @GetMapping("v1/reset-menu-page-resource-code-batch")
    public Result<Boolean> resetMenuAndPageResourceCode(@ApiIgnore UserVO userVO,
                                                        @RequestParam("appName") String appName,
                                                        @RequestParam(value = "resetDefaultButton", required = false) Boolean resetDefaultButton) {
        Boolean result = resourceService.resetMenuAndPageResourceCode(appName, resetDefaultButton, userVO.getUserId());
        return Result.success(result);
    }

    @AdminLogin
    @ApiOperation("批量导入资源的请求uri(simple)")
    @PostMapping("v1/import-request-uri-batch-simple")
    public Result<Boolean> batchImportRequestUri(@ApiIgnore UserVO userVO,
                                                 @RequestBody ImportResourceCodeUriDataDTO dto, @RequestParam("appName") String appName,
                                                 @RequestParam(value = "refreshCacheFlag", required = false) Boolean refreshCacheFlag) {
        Boolean result = resourceService.batchImportRequestUri(dto, appName, refreshCacheFlag, userVO.getUserId());
        return Result.success(result);
    }

    @AdminLogin
    @ApiOperation("批量导入资源的请求uri(前端文档的格式)")
    @PostMapping("v1/import-request-uri-batch")
    public Result<Boolean> batchImportRequestUriByFrontEndDoc(@ApiIgnore UserVO userVO,
                                                              @RequestBody String json, @RequestParam("appName") String appName,
                                                              @RequestParam(value = "refreshCacheFlag", required = false) Boolean refreshCacheFlag) {
        Map<String, Map<String, ImportResourceCodeUriLinkDTO>> importMap = JSON.parseObject(json, new TypeReference<Map<String, Map<String, ImportResourceCodeUriLinkDTO>>>() {
        });
        Boolean result = resourceService.batchImportRequestUriByFrontEndDoc(importMap, appName, refreshCacheFlag, userVO.getUserId());
        return Result.success(result);
    }

    @AdminLogin
    @ApiOperation("批量导入资源表数据(通过resource code更新, 只增不减)")
    @PostMapping("v1/import-resource-infos")
    public Result<Void> importResourceByAcExportResourceDTO(@ApiIgnore UserVO userVO,
                                                            @RequestBody List<AcExportResourceDTO> importResources,
                                                            @RequestParam("appName") String appName,
                                                            @RequestParam("updateSortFlag") Boolean updateSortFlag,
                                                            @RequestParam(value = "refreshCacheFlag", required = false) Boolean refreshCacheFlag) {
        resourceService.importResourceByAcExportResourceDTO(importResources, appName, userVO.getUserId(), updateSortFlag, refreshCacheFlag);
        return Result.success();
    }

    @AdminLogin
    @ApiOperation("批量导出资源表数据")
    @GetMapping("v1/export-resource-infos")
    public Result<List<AcExportResourceDTO>> exportResourceInfos(@ApiIgnore UserVO userVO, @RequestParam("appName") String appName) {
        List<AcExportResourceDTO> acExportResourceDTOS = resourceService.exportResource(appName);
        return Result.success(acExportResourceDTOS);
    }


}
