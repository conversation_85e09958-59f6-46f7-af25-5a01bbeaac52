package com.globalsources.admin.controller;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.globalsources.coupon.agg.api.dto.CampaignEditVO;
import com.globalsources.coupon.agg.api.dto.CampaignListVO;
import com.globalsources.coupon.agg.api.dto.CampaignSaveDTO;
import com.globalsources.coupon.agg.api.dto.CouponEditVO;
import com.globalsources.coupon.agg.api.dto.CouponFilterDTO;
import com.globalsources.coupon.agg.api.dto.CouponSaveDTO;
import com.globalsources.coupon.agg.api.dto.CouponSendDTO;
import com.globalsources.coupon.agg.api.dto.CouponSendFilterDTO;
import com.globalsources.coupon.agg.api.dto.CouponSendResultDTO;
import com.globalsources.coupon.agg.api.dto.OperationPageQueryDTO;
import com.globalsources.coupon.agg.api.feign.CouponAggFeign;
import com.globalsources.coupon.agg.api.vo.CampaignPageReqDTO;
import com.globalsources.coupon.agg.api.vo.CouponPageReqDTO;
import com.globalsources.coupon.agg.api.vo.OperationVO;
import com.globalsources.coupon.core.api.dto.OperationSaveDTO;
import com.globalsources.coupon.core.api.enums.OperationEnum;
import com.globalsources.coupon.core.api.enums.OperationTypeEnum;
import com.globalsources.coupon.core.api.feign.CouponCoreFeign;
import com.globalsources.coupon.core.api.po.Campaign;
import com.globalsources.coupon.core.api.po.Coupon;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Peng
 * @since 2022-12-12
 */
@Slf4j
@RestController
@Api(tags = "优惠券")
@RequestMapping("/coupon")
public class CouponController {

    private static final String SEND_COUPON_BUYER_RESULT_KEY = "GSOL.COUPON.SEND.RESULT";

    private static final String EXPORT_FILE_NAME_COUPON_DATA = "Coupon%s.xlsx";

    private static final String EXPORT_FILE_TEMPLATE_PATH_COUPON_DATA = "template/couponDataReport.xlsx";

    private static final String EXPORT_FILE_NAME_SEND_COUPON_DATA = "CouponSend%s.xlsx";

    private static final String EXPORT_FILE_TEMPLATE_PATH_SEND_COUPON_DATA = "template/couponSend.xlsx";

    @Autowired
    private CouponAggFeign couponAggFeign;

    @Autowired
    private CouponCoreFeign couponCoreFeign;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @AdminLogin
    @ApiOperation("加载优惠券列表信息")
    @PostMapping("/v1/list")
    public Result<PageResult<CouponEditVO>> loadCouponListData(@ApiIgnore UserVO userVO, @RequestBody CouponPageReqDTO filterVo) {
        return couponAggFeign.loadCouponListData(filterVo);
    }

    @AdminLogin
    @ApiOperation("上传Excel")
    @PostMapping("/v1/upload/excel")
    public Result<List<Long>> uploadExcel(@RequestPart(value = "file") MultipartFile file) {
        List<Long> categoryIds = new ArrayList<>();
        if (!file.isEmpty()) {
            try (InputStream inputStream = file.getInputStream()) {
                // 调用用 hutool 方法读取数据 默认调用第一个sheet
                ExcelReader excelReader = ExcelUtil.getReader(inputStream);
                List<CouponFilterDTO> couponFilterDTOS = excelReader.readAll(CouponFilterDTO.class);
                if (CollectionUtils.isEmpty(couponFilterDTOS)) {
                    return Result.failed(ResultCode.OrderResultCode.UPLOAD_EXCEL_IS_NULL.getCode(), ResultCode.OrderResultCode.UPLOAD_EXCEL_IS_NULL.getMsg());
                }
                categoryIds = couponFilterDTOS.stream().map(CouponFilterDTO::getL4CategoryId).collect(Collectors.toList());
            } catch (Exception e) {
                return Result.failed(ResultCode.OrderResultCode.UPLOAD_EXCEL_ERROR.getCode(), ResultCode.OrderResultCode.UPLOAD_EXCEL_ERROR.getMsg());
            }
        }
        return Result.success(categoryIds);
    }

    @AdminLogin
    @ApiOperation("保存优惠券")
    @PostMapping("/v1/save")
    public Result<List<Integer>> saveCoupon(@ApiIgnore UserVO userVO, @RequestBody CouponSaveDTO dto) {
        Long userId = getUserId(userVO);
        dto.setCreateBy(userId);
        List<Integer> couponIds = couponAggFeign.saveCoupon(dto).getData();
        // 记录操作日志
        if (!CollectionUtils.isEmpty(couponIds)) {
            couponIds.forEach(couponId -> couponCoreFeign.saveOperation(OperationSaveDTO.builder().operationType(OperationTypeEnum.COUPON).entityId(couponId).entityName(dto.getCouponName())
                    .operation(Objects.nonNull(dto.getCouponId()) ? OperationEnum.EDIT : OperationEnum.CREATE).createBy(userId).build()));
        }
        return Result.success(couponIds);
    }

    @AdminLogin
    @ApiOperation("删除优惠券")
    @GetMapping(value = "/v1/delete")
    public Result<Boolean> deleteCoupon(@ApiIgnore UserVO userVO, @RequestParam Integer couponId) {
        Long userId = getUserId(userVO);
        Boolean deleteFlag = couponAggFeign.deleteCoupon(couponId).getData();
        // 记录操作日志
        if (deleteFlag != null && deleteFlag) {
            Coupon coupon = couponCoreFeign.queryCouponById(couponId).getData();
            if (Objects.nonNull(coupon)) {
                couponCoreFeign.saveOperation(OperationSaveDTO.builder().operationType(OperationTypeEnum.COUPON).entityId(couponId).entityName(coupon.getCouponName()).operation(OperationEnum.DELETE).createBy(userId).build());
            }
        }
        return couponAggFeign.deleteCoupon(couponId);
    }

    @AdminLogin
    @ApiOperation("进入优惠券编辑页面")
    @GetMapping("/v1/update")
    public Result<CouponEditVO> updateCoupon(@ApiIgnore UserVO userVO, @RequestParam Integer couponId) {
        return couponAggFeign.updateCoupon(couponId);
    }

    @AdminLogin
    @ApiOperation("加载优惠券活动列表信息")
    @PostMapping("/v1/campaign/list")
    public Result<PageResult<CampaignListVO>> loadCampaignListData(@ApiIgnore UserVO userVO, @RequestBody CampaignPageReqDTO filter) {
        return couponAggFeign.loadCampaignListData(filter);
    }

    @AdminLogin
    @ApiOperation("保存优惠券活动")
    @PostMapping("/v1/campaign/save")
    public Result<Integer> saveCampaign(@ApiIgnore UserVO userVO, @RequestBody CampaignSaveDTO dto) {
        log.info(" save campaign dto is ----:{}", JSON.toJSONString(dto));
        Long userId = getUserId(userVO);
        dto.setCreateBy(userId);
        Result<Integer> result = couponAggFeign.saveCampaign(dto);
        // 记录操作日志
        if (Result.success().getCode().equals(result.getCode())) {
            Integer campaignId = result.getData();
            String campaignName = dto.getCampaignName();
            if (StringUtils.isEmpty(campaignName)) {
                Campaign campaign = couponCoreFeign.queryCampaignById(campaignId).getData();
                if (Objects.nonNull(campaign)) {
                    campaignName = campaign.getCampaignName();
                }
            }
            couponCoreFeign.saveOperation(OperationSaveDTO.builder().operationType(OperationTypeEnum.CAMPAIGN).entityId(campaignId).entityName(campaignName)
                    .operation(Objects.nonNull(dto.getCampaignId()) ? OperationEnum.EDIT : OperationEnum.CREATE).createBy(userId).build());
        }
        return result;
    }

    @AdminLogin
    @ApiOperation("删除优惠券活动")
    @GetMapping(value = "/v1/campaign/delete")
    public Result<Boolean> deleteCampaign(@ApiIgnore UserVO userVO, @RequestParam Integer campaignId) {
        Long userId = getUserId(userVO);
        Boolean deleteCampaignFlag = couponAggFeign.deleteCampaign(campaignId).getData();
        // 记录操作日志
        if (deleteCampaignFlag != null && deleteCampaignFlag) {
            Campaign campaign = couponCoreFeign.queryCampaignById(campaignId).getData();
            if (Objects.nonNull(campaign)) {
                couponCoreFeign.saveOperation(OperationSaveDTO.builder().operationType(OperationTypeEnum.CAMPAIGN).entityId(campaignId).entityName(campaign.getCampaignName()).operation(OperationEnum.DELETE).createBy(userId).build());
            }
        }
        return Result.success(deleteCampaignFlag);
    }

    @AdminLogin
    @ApiOperation("进入优惠券活动编辑页面")
    @GetMapping("/v1/campaign/update")
    public Result<CampaignEditVO> updateCampaign(@ApiIgnore UserVO userVO, @RequestParam Integer campaignId) {
        return couponAggFeign.updateCampaign(campaignId);
    }

    private Long getUserId(UserVO userVO) {
        Long userId = Optional.ofNullable(userVO).map(UserVO::getUserId).orElse(null);
        if (Objects.isNull(userId)) {
            throw new BusinessException(ResultCode.UserResultCode.NOT_LOGIN);
        }
        return userId;
    }

    @AdminLogin
    @ApiOperation("查询操作日志,分页列表")
    @PostMapping("/v1/operation/list")
    public Result<PageResult<OperationVO>> selectOperationList(@RequestBody OperationPageQueryDTO queryDTO) {
        log.info(" select operation list query dto is -----:{}", queryDTO);
        return couponAggFeign.selectOperationList(queryDTO);
    }

    @Deprecated
    @AdminLogin
    @GetMapping("send-buyer-coupon")
    @ApiOperation("给买家发送优惠券")
    public Result sendBuyerCoupon(@RequestParam String buyerIds, @RequestParam Integer couponId) {
        log.info(" send buyer coupon buyerIs is---:{}, coupon id-------:{}", buyerIds, couponId);
        return couponAggFeign.sendBuyerCoupon(buyerIds, couponId);
    }

    @AdminLogin(validLogin = false)
    @PostMapping("/v2/send-buyer-coupon")
    @ApiOperation("给买家发送优惠券")
    public Result sendBuyerCoupon(@ApiIgnore UserVO userVO, @RequestPart(value = "file", required = false) MultipartFile file,
                                  @RequestParam String buyerEmail, @RequestParam Integer couponId) {
        log.info(" send buyer coupon buyerEmail is---:{}, coupon id-------:{}", buyerEmail, couponId);
        if (Objects.isNull(file) && StringUtils.isEmpty(buyerEmail)) {
            return Result.success(false);
        }
        List<String> buyerEmailList = Lists.newArrayList();
        // 优先级：表格>直接输入
        if (Objects.nonNull(file)) {
            try (InputStream inputStream = file.getInputStream()) {
                // hutool工具方法读取数据
                ExcelReader excelReader = ExcelUtil.getReader(inputStream);
                buyerEmailList = excelReader.readAll(CouponSendFilterDTO.class).stream().map(CouponSendFilterDTO::getBuyerEmail).collect(Collectors.toList());
            } catch (Exception e) {
                return Result.failed(ResultCode.OrderResultCode.UPLOAD_EXCEL_ERROR.getCode(), ResultCode.OrderResultCode.UPLOAD_EXCEL_ERROR.getMsg());
            }
        }
        if (CollectionUtils.isEmpty(buyerEmailList) && StringUtils.isNotEmpty(buyerEmail)) {
            buyerEmailList = Arrays.asList(StringUtils.split(buyerEmail, ","));
        }
        log.info("send buyer coupon excel data is---:{}", buyerEmailList);
        return couponAggFeign.sendBuyerCoupon(CouponSendDTO.builder().buyerEmailList(buyerEmailList).couponId(couponId).userId(getUserId(userVO)).build());
    }

    @GetMapping("/v1/send-buyer-coupon-result")
    @ApiOperation("给买家发送优惠券结果")
    public void downloadSendBuyerCouponReport(@RequestParam Integer couponId, @RequestParam Long userId, HttpServletResponse response) {
        log.info("-------download report coupon id:{}， user id:{}", couponId, userId);
        StopWatch stopWatch = new StopWatch("downloadSendBuyerCouponReport");
        stopWatch.start();
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(EXPORT_FILE_TEMPLATE_PATH_SEND_COUPON_DATA);
             ServletOutputStream os = response.getOutputStream()) {
            JxlsHelper.getInstance().processTemplate(is, os, getSendContext(response, couponId, userId));
            log.info("download send buyer coupon result report success");
        } catch (Exception e) {
            log.error("download send buyer coupon result report error", e);
        }
        stopWatch.stop();
        log.info("download send buyer coupon result report success:{} ", stopWatch.prettyPrint());
    }

    private Context getSendContext(HttpServletResponse response, Integer couponId, Long userId) {
        String exportFileName = String.format(EXPORT_FILE_NAME_SEND_COUPON_DATA, couponId);
        buildResponse(response, exportFileName);
        try {
            Map<String, Object> model = new HashMap<>(16);
            String cacheKey = SEND_COUPON_BUYER_RESULT_KEY + couponId + userId;
            model.put("dataList", getSendResultData(cacheKey));
            return new Context(model);
        } catch (Exception e) {
            log.error("download send buyer coupon result report log error", e);
        }
        return null;
    }

    public List<CouponSendResultDTO> getSendResultData(String cacheKey) {
        // 获取缓存的上传结果
        List<CouponSendResultDTO> sendResult = null;
        try {
            Object val = this.redisTemplate.opsForValue().get(cacheKey);
            if (Objects.nonNull(val)) {
                sendResult = JSON.parseObject(val.toString(), new TypeReference<List<CouponSendResultDTO>>() {
                });
            }
        } catch (Exception e) {
            log.error("failed to getSendResultData, errorMsg: {}", e.getMessage(), e);
        }
        return Optional.ofNullable(sendResult).orElse(Lists.newArrayList());
    }

    @GetMapping("/v1/download/report")
    @ApiOperation("优惠券数据报告下载")
    public void downloadCouponReport(@RequestParam Integer couponId, HttpServletResponse response) {
        log.info("download report coupon id-------:{}", couponId);
        StopWatch stopWatch = new StopWatch("downloadCouponReport");
        stopWatch.start();
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(EXPORT_FILE_TEMPLATE_PATH_COUPON_DATA);
             ServletOutputStream os = response.getOutputStream()) {
            JxlsHelper.getInstance().processTemplate(is, os, getContext(response, couponId));
            log.info("download coupon report success");
        } catch (Exception e) {
            log.error("download coupon report error", e);
        }
        stopWatch.stop();
        log.info("download coupon report success:{} ", stopWatch.prettyPrint());
    }

    private Context getContext(HttpServletResponse response, Integer couponId) {
        String exportFileName = String.format(EXPORT_FILE_NAME_COUPON_DATA, couponId);
        buildResponse(response, exportFileName);
        try {
            Map<String, Object> model = new HashMap<>(16);
            model.put("dataList", couponAggFeign.buildCouponReportData(couponId).getData());
            return new Context(model);
        } catch (Exception e) {
            log.error("download coupon report log error", e);
        }
        return null;
    }

    private void buildResponse(HttpServletResponse response, String exportFileName) {
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;fileName=\"" + exportFileName + "\"");
        response.setHeader("fileName", exportFileName);
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "No-cache");
        response.setDateHeader("Expires", 0);
        response.setHeader("Access-Control-Expose-Headers", "fileName");
    }
}
