package com.globalsources.admin.controller;

import com.globalsources.admin.model.dto.complain.SelectComplainBuyerListDTO;
import com.globalsources.admin.model.vo.complain.ComplainBuyerDetailVO;
import com.globalsources.admin.model.vo.complain.ComplainBuyerVO;
import com.globalsources.admin.model.vo.complain.RfiComplainDetailVO;
import com.globalsources.admin.model.vo.complain.RfqComplainDetailVO;
import com.globalsources.admin.service.ComplainService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

@Api(tags = {"举报"})
@Slf4j
@RestController
@RequestMapping(value = "/complain")
public class ComplainController {

    @Autowired
    private ComplainService complainService;

    @ApiOperation(value = "查询举报买家列表")
    @PostMapping(value = "complain-buyer-list")
    public Result<PageResult<ComplainBuyerVO>> selectComplainBuyerList(@RequestBody SelectComplainBuyerListDTO dto) {
        // 校验参数，防止sql注入
        if (StringUtils.isNotEmpty(dto.getFiled()) && StringUtils.isNotEmpty(dto.getSort())) {
            if (!dto.getFiled().equals("complain_num") && !dto.getFiled().equals("create_date")) {
                return Result.failed("Illegal sorting");
            }
            if (!dto.getSort().equalsIgnoreCase("asc") && !dto.getSort().equalsIgnoreCase("desc")) {
                return Result.failed("Illegal sorting");
            }
        }
        PageResult<ComplainBuyerVO> pageResult = complainService.selectComplainBuyerList(dto);
        return Result.success(pageResult);
    }

    @ApiOperation(value = "根据userId 查询用户被举报业务列表")
    @GetMapping(value = "complain-list-by-user-id")
    public Result<ComplainBuyerDetailVO> selectComplainListByUserId(@RequestParam Long userId) {
        ComplainBuyerDetailVO complainBuyerDetailVOS = complainService.selectComplainListByUserId(userId);
        return Result.success(complainBuyerDetailVOS);
    }

    @AdminLogin
    @ApiOperation(value = "提交举报结果")
    @GetMapping(value = "post-complain-result")
    public Result postComplainResult(@RequestParam Long userId, @RequestParam String result, @ApiIgnore UserVO userVO) {
        return complainService.postComplainResult(userId, result, userVO.getUserId(), userVO.getEmail());
    }

    @ApiOperation(value = "根据requestId查询rfq详情")
    @GetMapping(value = "request-detail-rfq")
    public Result<RfqComplainDetailVO> requestDetailRfq(@RequestParam Long userId, @RequestParam Long supplierId, @RequestParam String requestId) {
        return Result.success(complainService.requestDetailRfq(userId, supplierId, requestId));
    }

    @ApiOperation(value = "根据requestId查询rfi详情")
    @GetMapping(value = "request-detail-rfi")
    public Result<RfiComplainDetailVO> requestDetailRfi(@RequestParam Long userId, @RequestParam Long supplierId, @RequestParam String requestId) {
        return Result.success(complainService.requestDetailRfi(userId, supplierId, requestId));
    }

    @ApiOperation(value = "被举报人取消拉黑")
    @GetMapping(value = "cancel-complain")
    public Result cancelComplain(@RequestParam Long userId) {
        return complainService.cancelComplain(userId,null);
    }

    @ApiOperation(value = "查询待处理的买家数量")
    @GetMapping(value = "query-pending-buyer-num")
    public Result<Integer> queryPendingBuyerNum() {
        return Result.success(complainService.queryPendingBuyerNum());
    }

}
