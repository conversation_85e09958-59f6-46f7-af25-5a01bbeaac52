package com.globalsources.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.admin.enums.PlatformUserStatusEnum;
import com.globalsources.admin.model.dto.AcUserFlagUpdateDTO;
import com.globalsources.admin.model.dto.AcUserUpdateDTO;
import com.globalsources.admin.model.dto.UserStatusUpdateDTO;
import com.globalsources.admin.model.dto.user.AssignUserInfoDTO;
import com.globalsources.admin.model.dto.user.SaveUserReviewPermissionDTO;
import com.globalsources.admin.model.pojo.AcUser;
import com.globalsources.admin.model.vo.AcUserDetailVO;
import com.globalsources.admin.model.vo.AcUserVO;
import com.globalsources.admin.service.AcUserReviewPermissionService;
import com.globalsources.admin.service.IAcUserService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.supplierconsole.agg.api.constant.ApplicationCode;
import com.globalsources.supplierconsole.agg.api.constant.EntityType;
import com.globalsources.supplierconsole.agg.api.constant.OperationEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 用户管理
 */
@Api(tags = {"Admin用户管理"})
@Validated
@RestController
@RequestMapping("/ac")
@Slf4j
public class AdminUserController {
    @Autowired
    private IAcUserService userService;

    @Autowired
    private AcUserReviewPermissionService acUserReviewPermissionService;

    /**
     * 查询用户列表
     */
    @ApiOperation(value = "查询用户列表", notes = "查询用户列表")
    @AdminLogin
    @GetMapping("/account/list")
    public Result<Page<AcUserVO>> selectUserAccountList(@ApiIgnore UserVO userInfo, String keyword,
                                                        Boolean statusAc, Long role,
                                                        Integer pageNum, Integer pageSize,
                                                        @RequestParam(value = "type", required = false, defaultValue = "1") Integer type) {
        Integer status = null;
        if (statusAc != null) {
            status = statusAc ? PlatformUserStatusEnum.NORMAL.code() : PlatformUserStatusEnum.FORBID.code();
        }

        Page<AcUserVO> userVOList = userService.selectUserAccountList(pageNum, pageSize, userInfo.getUserId(), keyword, status, role, type);
        if (userVOList.getRecords() == null || CollectionUtils.isEmpty(userVOList.getRecords())) {
            return new Result<>(ResultCode.CommonResultCode.SUCCESS.getCode(), "admin account list is empty", null);
        }
        return Result.success(userVOList);
    }

    @ApiOperation("编辑用户")
    @AdminLogin
    @PostMapping("/account/edit")
    public Result<Void> editUser(@ApiIgnore UserVO user,@RequestBody @Valid AcUserUpdateDTO dto){
        return userService.editUser(user.getUserId(),dto, null, null);
    }


    @ApiOperation("删除Admin console平台用户")
    @AdminLogin
    @com.globalsources.supplierconsole.agg.api.log.annotation.OperationLog(operation = OperationEnum.DELETE, entityType = EntityType.AC_USER,
            operationDesc = "delete admin user", operationLogEntityIdSpEl = "#userId", ignoreOrgId = true, applicationCode = ApplicationCode.ADMIN_CONSOLE,
            operationUserIdSpEl = "#user.userId", enableLogSpEl = "#return?.code == '200'",
            operationDescParamName = {"userId"}, operationDescParamValueSpEl = {"#userId"})
    @GetMapping("/account/admin/delete")
    public Result<Void> deleteAdminUser(@ApiIgnore UserVO user, @ApiParam("用户id") @RequestParam("userId") Long userId){
        if(Objects.isNull(userId) || userId<=0) {
            return Result.error();
        }
        return userService.deleteAdminUser(userId);
    }


    /**
     * 更改用户状态
     */
    @ApiOperation(value = "更改用户状态", notes = "更改用户状态")
    @AdminLogin
    @PostMapping("/account/status")
    public Result<String> updateStatusAcByUserId(@Valid @RequestBody UserStatusUpdateDTO dto) {

        Integer status = dto.isStatusAc() ? PlatformUserStatusEnum.NORMAL.code() : PlatformUserStatusEnum.FORBID.code();

        if (!userService.updateStatusAcByUserId(dto.getUserId(), status)) {
            return Result.failed();
        } else {
            return Result.success();
        }
    }

    /**
     * 根据id查询ac用户信息,供其他服务调用
     */
    @ApiOperation(value = "根据id查询ac用户信息", notes = "根据id查询ac用户信息")
    @GetMapping("/get-user-by-id")
    public Result<AcUserDetailVO> getUserById(@RequestParam("userId") @NonNull Long userId,
                                              @RequestParam(value = "appName", required = false, defaultValue = "ADMIN") String appName) {
        return userService.getUserInfo(userId, appName);
    }

    /**
     * psc重构
     * @param dto
     * @return
     */
    @ApiOperation(value = "更改用户状态", notes = "更改用户状态")
    @AdminLogin
    @PostMapping("v1/account/update-flag")
    public Result<Boolean> updateUserFlagByUserId(@Valid @RequestBody AcUserFlagUpdateDTO dto) {
        Boolean b = userService.updateUserFlagByUserId(dto.getUserId(), Optional.ofNullable(dto.getStatus()).orElse(Boolean.FALSE), dto.getFlagType());
        return Result.success(b);
    }

    /**
     * psc 重构
     * @param userId
     * @return
     */
    @Deprecated
    @ApiOperation(value = "review assign功能 查询用户列表", notes = "review assign功能 查询用户列表")
    @GetMapping("v1/account/list-for-assign")
    public Result<List<AssignUserInfoDTO>> getUserListForReviewAssignByUserId(@RequestParam("userId") Long userId) {
        List<AssignUserInfoDTO> list = userService.getUserListForReviewAssignByUserId(userId);
        return Result.success(list);
    }

    @ApiOperation(value = "review assign功能 查询用户列表", notes = "review assign功能 查询用户列表")
    @GetMapping("v3/account/list-for-assign")
    public Result<List<AssignUserInfoDTO>> getUserListForReviewAssignByUserIdV3(@RequestParam("type") String type) {
        List<AssignUserInfoDTO> list = userService.getUserListForReviewAssignByUserIdV2(type);
        return Result.success(list);
    }

    @AdminLogin
    @ApiOperation(value = "查询Can be Assigned 或者 Al}l List 设置, 返回业务类型 PRODUCT,CERTIFICATE,VIDEO,SECTION,ANALYST,VIRTUAL_SHOW", notes = "返回业务类型 PRODUCT,CERTIFICATE,VIDEO,SECTION,ANALYST,VIRTUAL_SHOW")
    @GetMapping("v1/account/user-review-permission-types-by-function")
    public Result<List<String>> getTypeOfFunctionByUserId(@RequestParam("userId") Long userId,
                                                          @ApiParam("ASSIGNABLE / VIEW_ALL") @RequestParam("functionType") String functionType) {
        List<String> list = acUserReviewPermissionService.getTypesOfFunctionByUserId(userId, functionType);
        return Result.success(list);
    }

    @AdminLogin
    @ApiOperation(value = "设置Can be Assigned 或者 All List", notes = "设置Can be Assigned 或者 All List")
    @PostMapping("v1/account/set-user-review-permission-types")
    public Result<Boolean> saveUserAssignableAndViewAll(@ApiIgnore UserVO user, @Valid @RequestBody SaveUserReviewPermissionDTO dto) {
        Boolean b = acUserReviewPermissionService.saveUserAssignableAndViewAll(dto.getUserId(), dto.getFunctionType(), dto.getTypes(), user.getUserId());
        return Result.success(b);
    }


    @ApiOperation(value = "查询用户是否有查看审核功能列表所有数据的权限", notes = "查询用户是否有查看审核功能列表所有数据的权限")
    @GetMapping("v1/account/get-qc-view-all-flag-by-user-id")
    public Result<Boolean> getQcViewAllFlagByUserId(@RequestParam("userId") Long userId, @RequestParam("type") String type) {
        Boolean b = acUserReviewPermissionService.getQcViewAllFlagByUserId(userId, type);
        return Result.success(b);
    }

    /**
     * 根据email查询ac用户信息,供其他服务调用
     */
    @ApiOperation(value = "根据email查询ac用户信息", notes = "根据email查询ac用户信息")
    @GetMapping("/get-user-by-email")
    public Result<AcUserDetailVO> getUserByEmail(@RequestParam String email) {
        AcUser acUser = userService.getUserByUserEmail(email);
        if (Objects.nonNull(acUser)) {
            return Result.success(OrikaMapperUtil.coverObject(acUser, AcUserDetailVO.class));
        }
        return Result.success();
    }

    /**
     * 根据email查询ac用户信息,供其他服务调用
     */
    @ApiOperation(value = "根据email查询ac用户信息", notes = "根据email查询ac用户信息")
    @GetMapping("/get-user-by-email-ignore-case")
    public Result<AcUserDetailVO> getUserByEmailIgnoreCase(@RequestParam String email) {
        AcUser acUser = userService.getUserByUserEmailIgnoreCase(email);
        if (Objects.nonNull(acUser)) {
            return Result.success(OrikaMapperUtil.coverObject(acUser, AcUserDetailVO.class));
        }
        return Result.success();
    }

}
