package com.globalsources.admin.controller;

import com.globalsources.admin.model.dto.BuyerUserQueryDTO;
import com.globalsources.admin.model.dto.user.*;
import com.globalsources.admin.model.vo.UserDoiReportVO;
import com.globalsources.admin.model.vo.user.BuyerIdleTimeoutVO;
import com.globalsources.admin.model.vo.user.BuyerListVO;
import com.globalsources.admin.model.vo.user.BuyerProfileVO;
import com.globalsources.admin.model.vo.user.WebLocaleCountryListVO;
import com.globalsources.admin.service.BuyerUserInfoService;
import com.globalsources.admin.service.BuyerUserService;
import com.globalsources.admin.service.DictService;
import com.globalsources.framework.annotation.AdminLogin;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.DateUtil;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.supplierconsole.agg.api.constant.ApplicationCode;
import com.globalsources.supplierconsole.agg.api.constant.EntityType;
import com.globalsources.supplierconsole.agg.api.constant.OperationEnum;
import com.globalsources.supplierconsole.agg.api.log.annotation.OperationLog;
import com.globalsources.user.api.dto.EditNotificationAggDTO;
import com.globalsources.user.api.dto.LoginConfigDTO;
import com.globalsources.user.api.feign.BaseUserFeign;
import com.globalsources.user.api.feign.SsoUserManageFeign;
import com.globalsources.user.api.feign.UserLoginConfigFeign;
import com.globalsources.user.api.vo.LocaleCountryListVO;
import com.globalsources.user.api.vo.LoginConfigVO;
import com.globalsources.user.api.vo.UserListVO;
import com.globalsources.user.core.api.dto.DoiStatisDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Date;

@Api(tags="用户管理")
@Slf4j
@Validated
@RequestMapping("/buyer")
@RestController
public class UserController {
    @Autowired
    private BuyerUserService buyerUserService;
    @Resource
    private BuyerUserInfoService buyerUserInfoService;
    @Resource
    private UserLoginConfigFeign userLoginConfigFeign;
    @Resource
    private SsoUserManageFeign ssoUserManageFeign;
    @Resource
    private BaseUserFeign baseUserFeign;
    @Autowired
    private DictService dictService;

    @AdminLogin
    @PostMapping("/list")
    public Result<PageResult<UserListVO>> queryBuyerUser(@RequestBody BuyerUserQueryDTO query){
        //应该没有用，原先返回没有属性的VO 处理sonar问题 把vo删了
        return buyerUserService.queryBuyerUser(query);
    }

    @AdminLogin
    @com.globalsources.supplierconsole.agg.api.log.annotation.OperationLog(operation = OperationEnum.SEARCH, entityType = EntityType.USER,
            operationDesc = "Search for Users", ignoreEntityId = true, ignoreOrgId = true, applicationCode = ApplicationCode.ADMIN_CONSOLE,
            operationDescParamName = {"keyword", "query"}, operationDescParamValueSpEl = {"#query?.keyword", "#query"},
            operationDescReturnName = {"resultCode"}, operationDescReturnValueSpEl = "#return.code")
    @ApiOperation(value = "搜索买家",notes = "目前仅支持精确userId 与 邮箱")
    @PostMapping("/search")
    public Result<PageResult<BuyerListVO>> searchBuyer(@RequestBody BuyerSearchDTO query){
        return buyerUserInfoService.searchUser(query);
    }

    @AdminLogin
    @ApiOperation(value = "修改密码")
    @PostMapping("/pwd/modify")
    public Result<Void> modifyUserPwd(@ApiIgnore UserVO user, @Valid @RequestBody ModifyPwdDTO dto){
        return buyerUserService.modifyUserPwd(user.getUserId(),dto);
    }

    @AdminLogin
    @ApiOperation(value = "重置密码")
    @GetMapping("/pwd/reset")
    public Result<Boolean> resetUserPwd(@ApiIgnore UserVO user,@RequestParam Long buyerId){
        return buyerUserService.resetUserPwd(user.getUserId(),buyerId);
    }

    @AdminLogin
    @ApiOperation(value = "手动验证邮箱")
    @PostMapping("/email/verify")
    public Result<Void> verifyBuyerEmail(@ApiIgnore UserVO user,@Valid @RequestBody VerifyBuyerEmailDTO dto){
        return buyerUserService.verifyUserEmail(user.getUserId(),dto);
    }

    @AdminLogin
    @ApiOperation(value = "查询用户profile信息")
    @GetMapping("/profile/info")
    public Result<BuyerProfileVO> getBuyerProfile(@RequestParam Long buyerId){
        return buyerUserInfoService.getUserProfile(buyerId);
    }

    @AdminLogin
    @PostMapping("/login/config/save")
    public Result<Void> saveLoginConfig(@RequestBody LoginPuzzleConfigDTO dto){
        LoginConfigDTO config;

        //加载历史其它配置
        Result<LoginConfigVO> resp=userLoginConfigFeign.getLoginConfigForBackend();
        if(resp.getData()!=null){
            config= OrikaMapperUtil.coverObject(resp.getData(),LoginConfigDTO.class);
        }else{
            config=new LoginConfigDTO();
            log.info("query user login config , return null, resp:{}",resp);
        }

        //加载用户输入
        config.setSkipPuzzle(dto.getSkipPuzzle());

        return userLoginConfigFeign.saveLoginConfig(config);
    }

    @ApiOperation("用户doi开关配置")
    @AdminLogin
    @PostMapping("/login/doi/config/save")
    public Result<Void> saveDoiLoginConfig(@RequestBody LoginDoiConfigDTO dto){
        LoginConfigDTO config;

        //加载历史其它配置
        Result<LoginConfigVO> resp=userLoginConfigFeign.getLoginConfigForBackend();
        if(resp.getData()!=null){
            config= OrikaMapperUtil.coverObject(resp.getData(),LoginConfigDTO.class);
        }else{
            config=new LoginConfigDTO();
            log.info("query user login config , return null, resp:{}",resp);
        }

        //加载用户输入
        config.setOpenRfiNewDoiBoot(dto.getOpenRfiNewDoiBoot());
        config.setOpenGlobalDoiPopWind(dto.getOpenGlobalDoiPopWind());

        return userLoginConfigFeign.saveLoginConfig(config);
    }

    @ApiOperation("用户doi统计")
    @AdminLogin
    @PostMapping("/doi/report")
    public Result<UserDoiReportVO> getUserDoiReport(@RequestBody DoiStatisDTO dto){
        //默认查询最近一个月的数据
        if(dto.getStartDate()==null && dto.getEndDate()==null){
            Date curDate=new Date();
            dto.setEndDate(curDate);
            dto.setStartDate(DateUtil.addMonth(curDate,-1));
        }

        //校验参数
        if(dto.getStartDate()==null || dto.getEndDate()==null || dto.getStartDate().after(dto.getEndDate())){
            return Result.failed(ResultCode.CommonResultCode.PARAMETER_VALIDATION_FAILED);
        }

        return buyerUserService.getUserDoiReport(dto);
    }

    @AdminLogin
    @PostMapping("/login/config/")
    public Result<LoginConfigVO> getLoginConfig(){
        return userLoginConfigFeign.getLoginConfigForBackend();
    }

    @ApiOperation("获取用户登录IDLE信息")
    @AdminLogin
    @GetMapping("/login/idle/duration/info")
    public Result<BuyerIdleTimeoutVO> getBuyerIdleDuration(@RequestParam String email){
        return buyerUserService.getLoginIdleDurationInfo(email);
    }

    @ApiOperation("修改用户登录IDLE时长")
    @AdminLogin
    @GetMapping("/login/idle/duration/info/save")
    public Result<Void> saveLoginIdleDuration(@RequestParam Long userId,@ApiParam("单位秒") @RequestParam Long duration){
        return buyerUserService.saveLoginIdleDurationInfo(userId,duration);
    }

    @AdminLogin
    @ApiOperation("admin - 编辑notification订阅: PA,SA,EDM,HPA")
    @PostMapping("/v1/edit-notification")
    public Result<Boolean> editNotification(@Valid @RequestBody EditNotificationAggDTO dto) {
        return ssoUserManageFeign.editNotification(dto);
    }

    @AdminLogin
    @OperationLog(operation = OperationEnum.LOGOUT, entityType = EntityType.USER, applicationCode  = ApplicationCode.ADMIN_CONSOLE, operationDesc = "Logout by admin", operationLogEntityIdSpEl = "#userId", ignoreOrgId = true)
    @ApiOperation(value = "一键强制退出", notes = "一键强制退出")
    @GetMapping("/v1/logout-by-admin")
    public Result<Void> logoutByAdmin(@RequestParam Long userId) {
        return baseUserFeign.logoutAllPlatform(userId);
    }

    @ApiOperation("国家列表")
    @PostMapping("/v1/country/list")
    public Result<WebLocaleCountryListVO> getCountryList(HttpServletRequest request){
        Result<LocaleCountryListVO> localeCountryListVOResult = dictService.listCountry(request);
        if (ResultCode.CommonResultCode.SUCCESS.getCode().equals(localeCountryListVOResult.getCode())) {
            WebLocaleCountryListVO webLocaleCountryListVO = OrikaMapperUtil.coverObject(localeCountryListVOResult.getData(), WebLocaleCountryListVO.class);
            return Result.success(webLocaleCountryListVO);
        }
        Result<WebLocaleCountryListVO> result = new Result<>();
        result.setCode(localeCountryListVOResult.getCode());
        result.setMsg(localeCountryListVOResult.getMsg());
        return result;
    }

}
