package com.globalsources.admin.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 展商产品eDM配置DTO
 * 用于接收前端传递的eDM配置信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "展商产品eDM配置DTO")
public class ExhibitorProductEdmConfigDTO {

    @ApiModelProperty(value = "eDM配置ID")
    private Long edmConfigId;

    @ApiModelProperty(value = "eDM名称（内部使用）", required = true)
    @NotBlank(message = "eDM名称不能为空")
    @Size(max = 100, message = "eDM名称不能超过100个字符")
    private String edmName;

    @ApiModelProperty(value = "展会期数", required = true)
    @NotBlank(message = "展会期数不能为空")
    private String tsPeriod;

    @ApiModelProperty(value = "展会阶段", required = true)
    @NotBlank(message = "展会阶段不能为空")
    private String tsPhase;

    @ApiModelProperty(value = "eDM内容文案", required = true)
    @NotBlank(message = "eDM内容文案不能为空")
    @Size(max = 1000, message = "eDM内容文案不能超过1000个字符")
    private String contentCopy;

    @ApiModelProperty(value = "eDM中最大产品数量", required = true)
    @NotNull(message = "eDM中最大产品数量不能为空")
    @Min(value = 4, message = "eDM中最大产品数量不能小于4")
    private Integer maxProductQuantity;

    @ApiModelProperty(value = "是否显示Banner")
    private Boolean showBanner;

    @ApiModelProperty(value = "发送日期列表", required = true)
    @NotNull(message = "发送日期列表不能为空")
    @Size(min = 1, message = "至少需要一个发送日期")
    private List<Date> sendDates;

    @ApiModelProperty(value = "Banner信息")
    private ExhibitorProductEdmBannerDTO banner;
}
