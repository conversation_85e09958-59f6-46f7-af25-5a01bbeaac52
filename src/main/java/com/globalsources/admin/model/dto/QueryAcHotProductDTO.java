package com.globalsources.admin.model.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: create by <PERSON>
 * @version: v1.0
 * @description: com.globalsources.core.admin.api.model.dto
 * @date:2021/10/09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="QueryAcHotProductDTO")
public class QueryAcHotProductDTO {
    private Long userId;

    private String appType;

    private String location;

    private String channel;

    private List<Long> categoryIds;

    private Long pageNum;

    private  Long pageSize;

    private Boolean videoFlag;
}
