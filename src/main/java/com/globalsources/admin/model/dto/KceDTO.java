package com.globalsources.admin.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.buyer.seo.model.dto
 * @date:2021/8/11
 */
@Data
public class KceDTO {

    @ApiModelProperty(value = "上传文件名")
    private String fileName;

    @ApiModelProperty(value = "上传文件url")
    private String fileUrl;

    @ApiModelProperty(value = "Top Product Keyword Manufacturers的付费供应商产品 标记")
    private Boolean tpkFlag;

    @ApiModelProperty(value = "Top China Supplier Keyword 筛选china的付费供应商 标记")
    private Boolean tcskFlag;

    @ApiModelProperty(value = "Wholesale Keyword Wholesale的付费供应商产品 标记")
    private Boolean wskFlag;

    @ApiModelProperty(value = "Factory Keyword Manufacturers的付费供应商产品 标记")
    private Boolean factoryFlag;

    @ApiModelProperty(value = "Product Catalog Keyword 所有付费的供应商产品 标记")
    private Boolean nmkFlag;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

}
