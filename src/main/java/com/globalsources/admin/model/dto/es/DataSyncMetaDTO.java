package com.globalsources.admin.model.dto.es;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ApiModel(description = "DataSyncMetaVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataSyncMetaDTO implements Serializable {

    private static final long serialVersionUID = 6940525790221773179L;

    private String entity;

    private String action;

    private Map data;
}
