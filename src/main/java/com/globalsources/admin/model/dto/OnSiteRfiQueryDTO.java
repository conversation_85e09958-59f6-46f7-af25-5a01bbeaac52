package com.globalsources.admin.model.dto;

import com.globalsources.framework.page.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OnSiteRfiQueryDTO extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("开始时间:yyyy/mm/dd")
    private String startDate;

    @ApiModelProperty("结束时间:yyyy/mm/dd")
    private String endDate;

    @ApiModelProperty("专区，来源于Send RFIs的onSite Zone")
    private String onSiteZone;

    @ApiModelProperty(value = "邮箱")
    private String emailAddr;

    @ApiModelProperty("visitorId")
    private String visitorId;

    @ApiModelProperty("rfi id")
    private String rfiId;

    @ApiModelProperty("rfi type: ONE_TO_ONE (1:1 RFI)，ONE_TO_MANY (1:Many RFI)")
    private String rfiType;

    @ApiModelProperty("supplier id")
    private Long supplierId;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("tradeshow id")
    private Long tradeshowId;

}
