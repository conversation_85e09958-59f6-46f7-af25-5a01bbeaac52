package com.globalsources.admin.model.dto.search.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.buyer.product.feign.vo
 * @date:2021/12/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SortTerm {
    //'DESC' ,'ASC'
    private String sort;
    private String sortField;
}
