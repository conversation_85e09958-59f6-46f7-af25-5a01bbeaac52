package com.globalsources.admin.model.dto.helpcenter;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value="onlineContent 的入参对象")
public class OnlineContentSaveDTO implements Serializable {
    private static final long serialVersionUID = 406974375077787414L;

    @ApiModelProperty(value = "oc id")
    private Integer ocId;

    @NotNull(message = "topicId cannot be null")
    @ApiModelProperty(value = "分类id")
    private Integer topicId;

    @ApiModelProperty(value = "首页分类id")
    private Integer homepageTopicId;

    @NotBlank(message = "subject cannot be null")
    @Size(max = 200, message = "subject length cannot exceed 200")
    @ApiModelProperty(value = "英文标题")
    private String subject;

    @ApiModelProperty(value = "英文内容(富文本)")
    private String contentText;

    @Size(max = 100, message = "subjectZh length cannot exceed 100")
    @ApiModelProperty(value = "中文标题")
    private String subjectZh;

    @ApiModelProperty(value = "中文内容(富文本)")
    private String contentTextZh;

    @ApiModelProperty(value = "封面图地址")
    private String imageUrl;

    @ApiModelProperty(value = "英文链接")
    private String webUrl;

    @ApiModelProperty(value = "中文链接")
    private String webUrlZh;

    @ApiModelProperty(value = "true=首页内容,false=内容页文章")
    private Boolean homeFlag;

    @ApiModelProperty(value = "记录来源:BC=买家,SC=卖家")
    private String sourceCode;


}
