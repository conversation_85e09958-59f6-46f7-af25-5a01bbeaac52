package com.globalsources.admin.model.dto.tradeshow;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShowBannerSortSeqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    @ApiModelProperty("showBannerId")
    private Integer showBannerId;

    @NotBlank
    @ApiModelProperty("排序方向，down/up")
    private String direction;
}
