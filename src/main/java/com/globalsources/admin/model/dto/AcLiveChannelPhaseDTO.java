package com.globalsources.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class AcLiveChannelPhaseDTO {
    private Long lcpId;
    private String phaseCode;
    @NotNull(message = "tab name is not null")
    @Size(max = 50,message = "tab name exceed 50 char")
    private String phaseName;
    @NotNull(message = "lcId is not null")
    private Long lcId;
    @NotNull(message = "associated campaign is not null")
    private Long campaign;
    @NotNull(message = "pavilion list is not null")
    private List<String> pavilions;
    private Boolean displayFlag;
    private Integer sortNum;
    private Boolean enableFlag;
}
