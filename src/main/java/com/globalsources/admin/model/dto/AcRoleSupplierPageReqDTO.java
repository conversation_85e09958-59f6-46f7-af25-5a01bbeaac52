package com.globalsources.admin.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.admin.model.dto
 * @date:2021/7/29
 */
@Data
public class AcRoleSupplierPageReqDTO {

    @ApiModelProperty(value = "页数")
    private Integer pageNum;

    @ApiModelProperty(value = "公司名，搜索用")
    private String supplierName;

    @ApiModelProperty(value = "tradeshow id 暂时无用")
    private Long tradeShowId;


}
