package com.globalsources.admin.model.dto.helpcenter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value="拖拽排序所需要的入参对象")
public class SortDTO {

    @NotNull(message = "ids not null!")
    @ApiModelProperty(value = "拖拽后，与拖拽元素组成的新顺序的id数组（与被拖拽元素当前同级的元素）,如果被拖入分类中元素个数为0(即没有同级元素)，则传这个分类的id")
    private List<Long> ids;

    @NotNull(message = "changeId not null!")
    @ApiModelProperty(value = "被拖拽的那个元素id")
    private Long changeId;

    @NotBlank(message = "type not null!")
    @ApiModelProperty(value = "拖拽类型:拖拽的元素是分类，还是内容。分类传`menu`,内容传`content`")
    private String type;

}
