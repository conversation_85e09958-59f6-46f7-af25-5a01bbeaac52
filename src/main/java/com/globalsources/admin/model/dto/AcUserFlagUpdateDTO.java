package com.globalsources.admin.model.dto;

import com.globalsources.admin.constants.UserConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * psc重构
 */
@Data
@ApiModel(description = "更新用户flag")
public class AcUserFlagUpdateDTO implements Serializable {
    private static final long serialVersionUID = 802936882460547858L;

    @NotNull
    private Long userId;
    @NotNull
    private Boolean status;
    @ApiModelProperty("ASSIGNABLE: can be assign, VIEW_ALL: see all")
    private UserConstant.AcUserFlagTypeEnum flagType;
}
