package com.globalsources.admin.model.dto.report;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/21 18:45
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SeoReportDTO implements Serializable {
    private static final long serialVersionUID = -8251742221356287033L;

    private Long supplierId;

    private String companyName;

    private String homepageUrl;

    private String productListUrl;

    private String companyProfileUrl;

    private String usp;

    private String subUsp;

    private String companyKeyword;

    private String orgDesc;

    private String productUrl;

    private String categoryName;

    @ApiModelProperty("PP SPD (Product Name and Short Description)")
    private String productName;

    @ApiModelProperty("PP VSPD (Search Optimization Keywords )")
    private String prodShortDesc;

    @ApiModelProperty("PP Other Product Names")
    private List<String> productKeyword;

    @ApiModelProperty("PP Product Outline（Product Information的Product Outline）")
    private String prodSpecDesc;

    @ApiModelProperty("产品负责人邮箱")
    private String productOwnerEmail;

}
