package com.globalsources.admin.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

@Data
public class LivestreamCampaignDTO {
    private Long campaignId;
    @NotBlank(message = "name is not empty")
    @Size(max = 200,message = "english name max length 200")
    private String nameEn;
    @NotBlank(message = "name is not empty")
    @Size(max = 200,message = "chinese name max length 200")
    private String nameCn;

    @Size(max = 1000,message = "chinese description max length 1000")
    private String descriptionEn;
    @Size(max = 1000,message = "chinese description max length 1000")
    private String descriptionCn;

    @NotBlank(message = "banner image url is not empty")
    private String bannerImageUrl;

    @ApiModelProperty("参加campaign条件")
    @NotNull(message = "join campaign is required")
    public JoinCampaignDTO joinCampaign;
    @ApiModelProperty("注册开始时间")
    @NotNull(message = "reg start date not null")
    private Date regStartDate;
    @ApiModelProperty("注册结束时间")
    @NotNull(message = "reg end date not null")
    private Date regEndDate;

    @NotNull(message = "start date not null")
    private Date startDate;
    @NotNull(message = "end data not null")
    private Date endDate;

    @Size(max = 500,message = "remark max length 500")
    private String remark;

    @Size(max = 300, message = "welcomeWord max length 300")
    private String welcomeWord;

    @NotNull(message = "allow reg flag not null")
    private Boolean allowReg;
    @NotNull(message = "allow associate product flag not null")
    private Boolean allowAssociateProduct;
    @NotNull(message = "allow upload video flag not null")
    private Boolean allowUploadVideo;
    @NotNull(message = "max upload video num not null")
    private Integer maxUploadVideoNum;

    @ApiModelProperty("操作人")
    private Long userId;

    @ApiModelProperty("O2O展商xls")
    private String o2oExhibitorXls;

    @ApiModelProperty("O2O视频限定")
    private Integer o2oExhibitorVideoLimit;

    @Data
    public static class JoinCampaignDTO{
        private List<String> exhibitionContract;
        private List<Long> categoryId;
        private List<String> supplierLevel;
        private List<String> supplierType;
    }
}
