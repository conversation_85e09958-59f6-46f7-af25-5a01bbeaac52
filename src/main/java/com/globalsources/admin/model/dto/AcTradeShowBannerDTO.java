package com.globalsources.admin.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2022-06-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AcTradeShowBannerDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer acTradeShowBannerId;

    /**
     * show name
     */
    private String showName;

    /**
     * supplier id
     */
    private String fileUrl;

    private String fileName;

    private Long fileSize;

    /**
     * supplier data fileName
     */
    private String supplierDataFileName;

    private MultipartFile file;

    /**
     * show banner url
     */
    private String showBannerUrl;

    @ApiModelProperty("Show Banner in big size")
    private String showBigBannerUrl;

    /**
     * show url
     */
    private String showUrl;

    /**
     * start date
     */

    private Long startDate;

    /**
     * end_date
     */
    private Long endDate;

    @ApiModelProperty("摊位号背景颜色")
    private String boothBgColorVal;

    @ApiModelProperty("摊位号字体颜色")
    private String boothFontColorVal;

}
