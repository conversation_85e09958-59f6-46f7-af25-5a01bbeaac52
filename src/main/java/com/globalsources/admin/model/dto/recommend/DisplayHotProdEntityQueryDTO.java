package com.globalsources.admin.model.dto.recommend;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.globalsources.admin.constants.FieldFilterConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/1 11:13
 */
@Builder(toBuilder = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DisplayHotProdEntityQueryDTO implements Serializable {
    private static final long serialVersionUID = 7905130195096885304L;

    private Long userId;

    @NotNull
    @ApiModelProperty(value = "Hot product类型分别为“DESKTOP” “MOBILE” “APP”")
    private String appType;

    @NotNull
    @ApiModelProperty(value = "显示区域, VMP / L1P ,当前location：Vertical Mother Page.  ")
    private String location;

    @ApiModelProperty(value = "category id")
    private Long categoryId;

    @ApiModelProperty(value = "EC/LF/HW.    channel  ELEC -> EC,  LF -> LF, HARD -> HW")
    private String verticalCode;

    @NotNull
    @ApiModelProperty(value = "PRODUCT/SUPPLIER; recommend product/supplier")
    private String recomType;

    private List<FieldFilterConstants.HotProductFiledEnum> filterFields;

    @ApiModelProperty("查询结果size")
    private Long size;



}
