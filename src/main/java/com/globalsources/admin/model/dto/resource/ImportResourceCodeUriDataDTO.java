package com.globalsources.admin.model.dto.resource;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/1 8:35
 */
@Data
@ApiModel(description = "批量更新资源request uri")
public class ImportResourceCodeUriDataDTO implements Serializable {
    private static final long serialVersionUID = 40655484628968613L;

    private String appName;

    private List<ImportResourceCodeUriLinkDTO> uriLinkList;


}
