package com.globalsources.admin.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@Data
@ApiModel(value="TradeshowSearch对象", description="")
public class TradeshowCoreSearchDTO {

    @ApiModelProperty(value = "展会类型，e.g, GSHOST, EXTHOST ")
    private String tsType;

    @JsonFormat(pattern="yyyy-MM-dd")
    private Date startDate;

    @JsonFormat(pattern="yyyy-MM-dd")
    private Date endDate;

}
