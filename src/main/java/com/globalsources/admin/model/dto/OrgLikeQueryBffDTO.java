package com.globalsources.admin.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="Organization 模糊查询公司名对象", description="Organization 模糊查询公司名对象")
public class OrgLikeQueryBffDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("页数  default 1")
    private Long pageNum = 1L;

    @ApiModelProperty("每页条数 default 20")
    private Long pageSize = 20L;

    @ApiModelProperty("供应商id列表")
    private List<Long> supplierIdList;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("国家地区码")
    private String countryCode;

}
