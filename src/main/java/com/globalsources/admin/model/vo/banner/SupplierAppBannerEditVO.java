package com.globalsources.admin.model.vo.banner;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.globalsources.admin.model.pojo.AcBannerDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "SupplierAppBannerEditVO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SupplierAppBannerEditVO {

    @ApiModelProperty(value = "主键id")
    private Long bannerId;

    @ApiModelProperty(value = "Banner 标题")
    private String title;

    @ApiModelProperty(value = "Banner在线状态")
    private Boolean onlineStatus;

    @ApiModelProperty(value = "(后端使用字段)Banner在线标识 Online/Offline")
    private String onlineFlag;

    @ApiModelProperty(value = "跳转类型：H5 / None")
    private String jumpType;

    @ApiModelProperty(value = "语言版本信息")
    private List<AcBannerDetail> langDetailList;

    @ApiModelProperty(value = "开始显示时间")
    private Date displayStartDate;

    @ApiModelProperty(value = "结束显示时间")
    private Date displayEndDate;

    @ApiModelProperty(value = "最后一次修改时间")
    private Date lUpdDate;

    @ApiModelProperty(value = "合同类型")
    private String contractType;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

}
