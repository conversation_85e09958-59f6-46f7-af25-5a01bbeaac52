package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AcLiveSupplierVO {
    @ApiModelProperty("条目id")
    private Long id;
    @ApiModelProperty("供应商名字")
    private Long supplierId;
    @ApiModelProperty("供应商名字")
    private String supplierName;
    @ApiModelProperty("供应商logo")
    private String supplierLogoUrl;
    @ApiModelProperty("展会信息")
    private List<String> trashShowList;
    @ApiModelProperty("是否verified认证")
    private Boolean verifiedFlag;
    @ApiModelProperty("是否是O2O")
    private Boolean o2oFlag;
    @ApiModelProperty("供应商星级")
    private Integer supplierLevel;
    @ApiModelProperty("供应商年限")
    private Integer year;
    @ApiModelProperty("是否已经切换")
    private Boolean switchSupplier;
    @ApiModelProperty("创建时间")
    private Date createDate;
}
