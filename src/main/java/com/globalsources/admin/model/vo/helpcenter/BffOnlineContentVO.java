package com.globalsources.admin.model.vo.helpcenter;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value="BffOnlineContentVO 的出参对象")
public class BffOnlineContentVO implements Serializable {
    private static final long serialVersionUID = -4273085081332288943L;

    @ApiModelProperty(value = "文章id")
    private Integer ocId;

    @ApiModelProperty(value = "所属菜单id")
    private Integer topicId;

    @ApiModelProperty(value = "文章所属一级菜单id")
    private Integer topId;

    @ApiModelProperty(value = "标题")
    private String subject;

    @ApiModelProperty(value = "封面图地址")
    private String imageUrl;

    @ApiModelProperty(value = "链接地址")
    private String webUrl;

    @ApiModelProperty(value = "富文本内容")
    private String contentText;

    @ApiModelProperty(value = "创建人邮箱")
    private String createBy;

    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    @ApiModelProperty(value = "修改人邮箱")
    private String lUpdBy;

    @ApiModelProperty(value = "修改日期")
    private Date lUpdDate;

    @ApiModelProperty(value = "文章类目信息")
    private OnlineContentTopicVO contentTopicVO;

}
