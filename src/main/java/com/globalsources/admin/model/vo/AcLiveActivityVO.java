package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class AcLiveActivityVO {
    private Long activityId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("活动描述")
    private String description;

    @ApiModelProperty("pavilion code")
    private String pavilionCode;

    @ApiModelProperty("缩略图")
    private String thumbImgUrl;

    @ApiModelProperty(value ="缩略图文件key")
    private String thumbImgFileKey;

    @ApiModelProperty("活动海报图")
    private String posterImgUrl;

    @ApiModelProperty(value ="海报图文件key")
    private String posterImgFileKey;

    @ApiModelProperty(value = "直播活动分类id")
    private Long categoryId;

    @ApiModelProperty(value = "活动分类名字")
    private String categoryName;

    @ApiModelProperty("campaignId")
    private Long campaignId;

    @ApiModelProperty("campaignName")
    private String campaignName;

    @ApiModelProperty(value = "活动开始时间")
    private Date startDate;

    @ApiModelProperty(value = "活动持续时间,单位小时")
    private Long duration;

    @ApiModelProperty(value = "活动结束时间")
    private Date endDate;

    @ApiModelProperty(value = "活动状态: READY(0),STARTING(1),FINISH(2),UNSTART(3)")
    private Integer status;

    @ApiModelProperty(value = "活动状态文本")
    private String statusName ;

    @ApiModelProperty(value = "app下载地址")
    private String appDownloadUrl;

    @ApiModelProperty(value = "直播url")
    private String liveUrl;

    @ApiModelProperty(value = "工作台uri")
    private String workbenchUri;

    @ApiModelProperty(value = "rtmp url")
    private String rtmpUrl;

    @ApiModelProperty(value = "预告片地址")
    private String trailerUrl;

    @ApiModelProperty(value = "回放文件url")
    private String replayUrl;

    @ApiModelProperty(value = "是否开启")
    private Boolean enabled;

    @ApiModelProperty("是否开启字幕")
    private Boolean enabledSubtitle;

    @ApiModelProperty("是否开启评论功能")
    private Boolean commentFlag;

    @ApiModelProperty("评论工作台uri")
    private String commentWorkbenchUrl;

    @ApiModelProperty(value = "是否删除")
    private Boolean deleteFlag;

    @ApiModelProperty("创建人名字")
    private String createByName;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "创建日期")
    private Date createDate;


}
