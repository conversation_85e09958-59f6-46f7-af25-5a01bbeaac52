package com.globalsources.admin.model.vo.user;

import com.globalsources.supplierconsole.agg.api.user.vo.SupplierListAggVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BuyerProfileVO {
    @ApiModelProperty("1.VIP买家用户 2.已核实认证用户 3.全球前250大零售商 4.行业前500大买家 5.参加过环球资源展会的买家 6.参加过世界范围其他展会的买家 7.出席买家专场采购会的买家 8.用户是否订阅 9.用户是否订阅magazine")
    private List<Integer> buyerType;
    private SupplierInfo supplier;
    private BlacklistInfo blacklistInfo;
    private DoiInfo doiInfo;
    private ContactInfo contactInfo;
    private CompanyInfo companyInfo;
    private BehaviourInfo behaviourInfo;
    private NotificationInfo notificationInfo;
    private Date updateDate;
    private Date createdDate;
    @ApiModelProperty("Reported by Suppliers")
    private Integer complainCount;
    @ApiModelProperty("Chat Blacklisted by Suppliers")
    private Integer chatBlackedCount;

    @Data
    public static class NotificationInfo{
        private Boolean paFlag;
        private String paFrequency;
        private Boolean saFlag;
        private String saFrequency;
        private Boolean hpaFlag;
        private Boolean edmFlag;
    }

    @Data
    public static class DoiInfo{
        private Boolean status;
        private String source;
        @ApiModelProperty("doi认证时间")
        private Date authTime;
    }

    @Data
    public static class BlacklistInfo{
        private Boolean status;
        @ApiModelProperty("操作人id")
        private Long addById;
        @ApiModelProperty("操作人邮箱")
        private String addByEmail;
        private Date addTime;
    }

    @Data
    public static class SupplierInfo{
        private Boolean status;
        private Boolean innerFlag;
        private List<SupplierListAggVO> companyList;
    }

    @Data
    public static class ContactInfo{
        @ApiModelProperty("姓名")
        private String firstName;
        @ApiModelProperty("姓氏")
        private String lastName;
        @ApiModelProperty("职位名")
        private String jobTitle;
        @ApiModelProperty("头像")
        private String avatar;
        @ApiModelProperty("头像文件key")
        private String avatarKey;

        @ApiModelProperty("邮箱")
        private String email;
        @ApiModelProperty(value = "邮箱是否被激活,这个就是 是否DOI")
        private Boolean activateEmail;
        @ApiModelProperty("国家简码")
        private String countryCode;
        @ApiModelProperty("电话国家区号")
        private String telCountryCode;
        @ApiModelProperty("电话区码")
        private String telAreaCode;
        @ApiModelProperty("手机号码")
        private String phone;
        @ApiModelProperty("分机号码")
        private String telExt;
        @ApiModelProperty("聊天标志")
        private Boolean chatFlag;

        @ApiModelProperty("传真国家区码")
        private String faxCountryCode;
        @ApiModelProperty("传真区码")
        private String faxAreaCode;
        @ApiModelProperty("传真号码")
        private String faxNumber;
        @ApiModelProperty("城市")
        private String city;
        @ApiModelProperty("省份")
        private String province;
        @ApiModelProperty("邮编")
        private String zipCode;
        @ApiModelProperty("详细地址小区门牌")
        private String address;
        @ApiModelProperty("详细地址街道")
        private String address2;
        @ApiModelProperty("详细地址区域")
        private String address3;
        @ApiModelProperty("公司url")
        private String companyUrl;
        @ApiModelProperty("linkedIn url")
        private String linkedInUrl;

        @ApiModelProperty("微信账号")
        private String wechatAccount;
        @ApiModelProperty("whatsapp账号")
        private String whatsappAccount;
        @ApiModelProperty("verified标志")
        private Boolean verifiedFlag;
        @ApiModelProperty("允许显示profile")
        private Boolean showAllFlag;
        @ApiModelProperty("VIP标志")
        private Boolean vipFlag;


        @ApiModelProperty("全球前250大零售商标志")
        private Boolean retailerFlag;
        @ApiModelProperty("行业前500大买家标志")
        private Boolean vertBuyerFlag;
        @ApiModelProperty("参加过世界范围其他展会的买家标志")
        private Boolean attendeeFlag;
        @ApiModelProperty("出席买家专场采购会的买家标志")
        private Boolean pseFlag;



        //新字段
        @ApiModelProperty("性别title")
        private String salutation;
        @ApiModelProperty("联系手机国家码")
        private String contactMobileCountryCode;
        @ApiModelProperty("联系手机号码")
        private String contactMobileNumber;
        @ApiModelProperty("职位")
        private String jobFunction;
        @ApiModelProperty("自定义职位值")
        private String jobFunctionCustomValue;
        @ApiModelProperty("职级")
        private String jobLevel;
        @ApiModelProperty("部门")
        private String department;
    }

    @Data
    public static class CompanyInfo{
        @ApiModelProperty("年度采购额")
        private String annualSourcingValue;
        @ApiModelProperty("购买决策角色")
        private String influenceRole;
        @ApiModelProperty("关注的产品类型")
        private List<String> lookingForProductType;
        @ApiModelProperty("关注产品关键词")
        private String lookingForProductKeyword;

        @ApiModelProperty("公司名字")
        private String companyName;
        @ApiModelProperty("公司年份")
        private String year;
        @ApiModelProperty("年度销售额")
        private String annualSale;
        @ApiModelProperty("年度进口额")
        private String annualImport;
        @ApiModelProperty("年度出口额")
        private String annualExport;
        @ApiModelProperty("COFACE信用码")
        private String cofaceNumber;
        @ApiModelProperty("邓氏编码")
        private String dunsNumber;
        @ApiModelProperty("公司简介")
        private String introduction;

        //新字段
        @ApiModelProperty("公司类型")
        private List<String> businessType;
        @ApiModelProperty("公司类型自定义值")
        private String businessTypeCustomValue;
        @ApiModelProperty("公司主营类型")
        private String majorBusinessType;
        @ApiModelProperty("公司规模")
        private String companySize;
        @ApiModelProperty("采购市场")
        private List<String> marketImportZones;
        @ApiModelProperty("销售市场")
        private List<String> marketSellZones;
        @ApiModelProperty("销售渠道")
        private List<String> salesChannel;
        @ApiModelProperty("关注供应商类别")
        private List<String> lookingForSupplierType;
        @ApiModelProperty("单位年度采购额")
        private String departAnnualSourcingValue;
        @ApiModelProperty("采购区域")
        private String sourcingRegionValue;
        @ApiModelProperty("部门")
        private String department;
        @ApiModelProperty("采购权限")
        private String purchaseAuthority;
    }

    @Data
    public static class BehaviourInfo{
        @ApiModelProperty("注册时间")
        private String regDate;
        @ApiModelProperty("登录天数")
        private Integer daysSignIn;
        @ApiModelProperty("最近更新时间")
        private Date updateDate;
        @ApiModelProperty("RFQ 提交数量")
        private Integer rfqSubmitCount;
        @ApiModelProperty("RFI 提交数量")
        private Integer rfiSubmitCount;
        @ApiModelProperty("聊天联系人数量")
        private Long chatContactCount;
        @ApiModelProperty("交易笔数")
        private Long totalTransaction;
        @ApiModelProperty("交易金额")
        private Double transactionVolume;
        @ApiModelProperty("采购意向列表")
        private List<String> sourcingPreference;
        @ApiModelProperty("最近搜索关键词")
        private List<String> keywordList;
        @ApiModelProperty("参展次数")
        private Integer tradeShowAttendCount;
        @ApiModelProperty("展会线上注册数量")
        private Integer tradeShowRegCount;
    }
}
