package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 展商产品eDM配置列表VO
 * 用于返回eDM配置列表信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "展商产品eDM配置列表VO")
public class ExhibitorProductEdmListVO {

    @ApiModelProperty(value = "eDM配置ID")
    private Long edmConfigId;

    @ApiModelProperty(value = "eDM名称（内部使用）")
    private String edmName;

    @ApiModelProperty(value = "展会开始日期")
    private Date showStartDate;

    @ApiModelProperty(value = "eDM发送日期列表")
    private List<Date> sendDates;

    @ApiModelProperty(value = "有效状态")
    private boolean effectiveStatus;

    @ApiModelProperty(value = "最后更新人")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateDate;
}
