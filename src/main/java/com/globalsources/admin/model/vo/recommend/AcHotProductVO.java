package com.globalsources.admin.model.vo.recommend;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * admin console平台Hot product表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(description="hot product video / top brand")
public class AcHotProductVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long hotProductId;

    @ApiModelProperty(value = "Hot product类型分别为“DESKTOP” “MOBILE” “APP”")
    private String appType;

    @ApiModelProperty(value = "Banner在线状态 Online/Offline")
    private String onlineStatus;

    @ApiModelProperty(value = "Banner在线状态 Online/Offline")
    private Boolean onlineFlag;

    @ApiModelProperty(value = "显示区域, VMP / L1P ,当前location：Vertical Mother Page.  ")
    private String location;

    @ApiModelProperty(value = "产品ID")
    private Long productId;

    @ApiModelProperty(value = "公司ID")
    private Long orgId;

    @ApiModelProperty(value = "开始显示时间")
    private Date displayStartDate;

    @ApiModelProperty(value = "结束显示时间 ")
    private Date displayEndDate;

    @ApiModelProperty(value = "排序")
    private Integer displaySeq;

    @ApiModelProperty(value = "PRODUCT/SUPPLIER; recommend product/supplier")
    private String recomType;

    @Size(max = 200)
    @ApiModelProperty(value = "description")
    private String description;

    @ApiModelProperty(value = "category id")
    private Long categoryId;

    @ApiModelProperty(value = "EC/LF/HW.    channel  ELEC -> EC,  LF -> LF, HARD -> HW")
    private String verticalCode;

    @ApiModelProperty("product name")
    private String productName;

    @ApiModelProperty("supplier name, company name")
    private String supplierName;
}
