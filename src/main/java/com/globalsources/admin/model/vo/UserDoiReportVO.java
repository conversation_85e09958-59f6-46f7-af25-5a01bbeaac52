package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UserDoiReportVO {
    private static final String DEFAULT_RATE="0/0";
    @ApiModelProperty("所有的新注册用户数量")
    private Integer total;
    @ApiModelProperty("所有已经doi的用户数量")
    private Integer allDoi;
    @ApiModelProperty("welcome email doi的用户数量")
    private Integer welcomeDoi;
    @ApiModelProperty("reminder email doi的用户数量")
    private Integer reminderDoi;
    @ApiModelProperty("reminder3 email doi的用户数量")
    private Integer reminderDoi3;
    @ApiModelProperty("reminder5 email doi的用户数量")
    private Integer reminderDoi5;
    @ApiModelProperty("reminder7 email doi的用户数量")
    private Integer reminderDoi7;
    @ApiModelProperty("reminder30 email doi的用户数量")
    private Integer reminderDoi30;
    @ApiModelProperty("buyer center email doi的用户数量")
    private Integer profileDoi;
    @ApiModelProperty("register code doi的用户数量")
    private Integer regDoi;
    @ApiModelProperty("overall Site Code doi的用户数量")
    private Integer globalDoi;
    @ApiModelProperty("rfi code doi的用户数量")
    private Integer rfiDoi;
    @ApiModelProperty("2fa code doi的用户数量")
    private Integer tfaDoi;
    @ApiModelProperty("welcome email doi的用户数量转换率")
    private String welcomeDoiRate;
    @ApiModelProperty("reminder email doi的用户数量转换率")
    private String reminderDoiRate;
    @ApiModelProperty("reminder3 email doi的用户数量转换率")
    private String reminderDoiRate3;
    @ApiModelProperty("reminder5 email doi的用户数量转换率")
    private String reminderDoiRate5;
    @ApiModelProperty("reminder7 email doi的用户数量转换率")
    private String reminderDoiRate7;
    @ApiModelProperty("reminder30 email doi的用户数量转换率")
    private String reminderDoiRate30;
    @ApiModelProperty("buyer center email doi的用户数量转换率")
    private String profileDoiRate;
    @ApiModelProperty("register code doi的用户数量转换率")
    private String regDoiRate;
    @ApiModelProperty("overall Site Code doi的用户数量转换率")
    private String globalDoiRate;
    @ApiModelProperty("rfi code doi的用户数量转换率")
    private String rfiDoiRate;
    @ApiModelProperty("2fa code doi的用户数量转换率")
    private String tfaDoiRate;
    @ApiModelProperty("chat code doi的用户数量")
    private Integer chatDoi;
    @ApiModelProperty("chat code doi的用户数量转换率")
    private String chatDoiRate;
    @ApiModelProperty("名片交换 code doi的用户数量")
    private Integer exchangeDoi;
    @ApiModelProperty("名片交换 code doi的用户数量转换率")
    private String exchangeDoiRate;

    public UserDoiReportVO(){
        total=0;
        allDoi=0;
        welcomeDoi=0;
        reminderDoi=0;
        profileDoi=0;
        regDoi=0;
        globalDoi=0;
        rfiDoi=0;
        tfaDoi=0;
        chatDoi=0;
        exchangeDoi=0;

        welcomeDoiRate=DEFAULT_RATE;
        reminderDoiRate=DEFAULT_RATE;
        profileDoiRate=DEFAULT_RATE;
        regDoiRate=DEFAULT_RATE;
        globalDoiRate=DEFAULT_RATE;
        rfiDoiRate=DEFAULT_RATE;
        tfaDoiRate=DEFAULT_RATE;
        chatDoiRate=DEFAULT_RATE;
        exchangeDoiRate=DEFAULT_RATE;
    }
}
