package com.globalsources.admin.model.vo.role;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: RoleChooseVO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description:  <a>
 *
 * <AUTHOR>
 * @date 2021/7/2 11:11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "角色选择", description = "角色选择")
public class RoleChooseBaseVO implements Serializable {

    private Long roleId;

    private String roleName;
}
