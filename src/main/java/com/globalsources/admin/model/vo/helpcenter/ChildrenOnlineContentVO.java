package com.globalsources.admin.model.vo.helpcenter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value="OnlineTopic下的文章元素出参对象 标题下的文章")
public class ChildrenOnlineContentVO implements Serializable {

    private static final long serialVersionUID = 8115536963622498705L;

    @ApiModelProperty(value = "文章id")
    private Integer ocId;

    @ApiModelProperty(value = "英文标题")
    private String subject;

    @ApiModelProperty(value = "中文标题")
    private String subjectZh;
}
