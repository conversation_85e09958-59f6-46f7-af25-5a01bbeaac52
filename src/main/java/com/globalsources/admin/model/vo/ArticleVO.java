package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 首页文章
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="首页文章", description="首页文章")
public class ArticleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "文章id")
    private Integer articleId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "图片 url")
    private String imageUrl;

    @ApiModelProperty(value = "跳转类型")
    private Integer jumpType;

    @ApiModelProperty(value = "web url")
    private String webUrl;

    @ApiModelProperty(value = "开始时间")
    private Date displayStartDate;

    @ApiModelProperty(value = "结束时间")
    private Date displayEndDate;

    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    @ApiModelProperty(value = "修改日期")
    private Date lUpdDate;

    @ApiModelProperty(value = "状态，true展示，false不展示")
    private Boolean status;
}