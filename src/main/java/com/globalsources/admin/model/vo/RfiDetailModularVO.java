package com.globalsources.admin.model.vo;

import com.globalsources.rfi.vo.InquiryAttachmentVO;
import com.globalsources.rfi.vo.InquiryProdVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/3 19:04
 */
@Data
@ApiModel(description = "rfi详情页 detail模块")
public class RfiDetailModularVO implements Serializable {

    private static final long serialVersionUID = -5892375173111347850L;

    @ApiModelProperty("产品信息")
    private InquiryProdVO inquiryProd;
    @ApiModelProperty("附件list")
    private List<InquiryAttachmentVO> attachments;
    @ApiModelProperty("消息")
    private String message;
    @ApiModelProperty("产品属性 list")
    private List<ProductCategoryAttributeAggVO> productCategoryAttrs;
}
