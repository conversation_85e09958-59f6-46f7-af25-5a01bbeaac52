package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class LiveBestSupplierExportVO {
    private Long bestSuppId;

    @ApiModelProperty("pavilion名字")
    private String pavilionName;

    @ApiModelProperty("pavilion code")
    private String pavilionCode;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("优选供应商id")
    private Long supplierId;

    @ApiModelProperty("供应商名字")
    private String supplierName;

    @ApiModelProperty("缩略图")
    private String thumbImgUrl;

    @ApiModelProperty(value = "campaignId")
    private Long campaignId;

    @ApiModelProperty(value = "campaign名字")
    private String campaignName;

    @ApiModelProperty(value = "活动状态")
    private String status;

    @ApiModelProperty("是否开启")
    private Boolean enabled;

    @ApiModelProperty("审核原因")
    private String reviewReason;

    @ApiModelProperty("审核人")
    private Long reviewBy;

    @ApiModelProperty("审核人id")
    private String reviewer;

    @ApiModelProperty(value = "直播url")
    private String liveUrl;

    @ApiModelProperty("被指派人名字")
    private String assignedName;

    @ApiModelProperty("视频类型name")
    private String videoTypeName;

    @ApiModelProperty("视频地址")
    private String videoUrl;

    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    @ApiModelProperty(value = "修改日期")
    private Date lUpdDate;

    @ApiModelProperty(value = "修改日期")
    private String updateDateStr;

    @ApiModelProperty("创建人")
    private String creator;
}
