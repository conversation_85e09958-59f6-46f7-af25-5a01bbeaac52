package com.globalsources.admin.model.vo.inquiry;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/4/1
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TsBuyerSurveyProcessVO {

    private Integer buyerRfiSurveyId;
    private List<Long> l4CategoryId;
    private String message;
    private Long userId;
    private Date createDate;
    private String buyerEmail;
    private String pubCode;
    private boolean flexibleFlag;
    private Integer upsellCount;
    private Integer productNum;

    private Long tradeshowId;

    private String tradeshowName;

    private String supplierCntPerCategory;
    private String firstName;
    private String lastName;
    private String companyName;
    private String productList;
}
