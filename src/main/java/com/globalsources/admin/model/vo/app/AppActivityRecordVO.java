package com.globalsources.admin.model.vo.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/1/4 20:46
 */
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppActivityRecordVO implements Serializable {
    private static final long serialVersionUID = -5282186215177532842L;

    private String id;

    private String version;

    private String os;

    private String appType;

    private Long supplierId;

    private String supplierName;

    private String createMonth;

    private Long userId;

    private Date createDate;

    private String userEmail;

    private String loginMoth;

    private Integer loginCnt;
}
