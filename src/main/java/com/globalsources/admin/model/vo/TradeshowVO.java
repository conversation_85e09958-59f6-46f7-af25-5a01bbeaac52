package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021-08-12
 */
@Data
@ApiModel(value = "TradeshowVO对象")
public class TradeshowVO {

    @ApiModelProperty(value = "展会ID")
    private Long tradeshowId;

    @ApiModelProperty(value = "展会名称")
    private String tsName;

    @ApiModelProperty(value = "展会名称")
    private String tsShortName;

    @ApiModelProperty(value = "展会类型，e.g, GSHOST, EXTHOST ")
    private String tsType;

    @ApiModelProperty(value = "展会举办城市")
    private String tsCity;

    @ApiModelProperty(value = "展会国家/地区编码")
    private String tsCountryCode;

    @ApiModelProperty(value = "展会国家/地区名称")
    private String tsCountryName;

    @ApiModelProperty(value = "展会名称")
    private String tsLocation;

    @ApiModelProperty(value = "展会在线显示开始时间 格式化的字符串 yyyy-MM-dd HH:mm:ss")
    private Date tsOnlineStartDate;

    @ApiModelProperty(value = "展会在线显示结束时间 格式化的字符串 yyyy-MM-dd HH:mm:ss")
    private Date tsOnlineEndDate;

    @ApiModelProperty(value = "展会开始时间 格式化的字符串 yyyy-MM-dd HH:mm:ss")
    private Date tsStartDate;

    @ApiModelProperty(value = "展会结束时间 格式化的字符串 yyyy-MM-dd HH:mm:ss")
    private Date tsEndDate;

    @ApiModelProperty(value = "展会GroupCode")
    private String tsGrpCode;

    @ApiModelProperty(value = "展会图片链接")
    private String tsImageUrl;

    @ApiModelProperty(value = "展会图片链接Key")
    private String tsImageUrlKey;

    @ApiModelProperty(value = "展会链接")
    private String tsUrl;

    @ApiModelProperty(value = "展会报告链接")
    private String tsReportUrl;

    @ApiModelProperty(value = "是否在APP显示")
    private String displayAppFlag;

    @ApiModelProperty(value = "最后更新用户ID")
    private Long lastUpdateBy;

    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateDate;

    @ApiModelProperty(value = "最后更新用户Email")
    private String lastUpdateByEmail;

}
