package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RfxBuyerInfoVO {

    @ApiModelProperty("姓名")
    private String firstName;

    @ApiModelProperty("姓氏")
    private String lastName;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("最近登录时间")
    private Date lastLoginDate;

    @ApiModelProperty("公司地址")
    private String companyUrl;

    @ApiModelProperty("职位名 ,")
    private String jobTitle;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("电话国家区号")
    private String telCountryCode;

    @ApiModelProperty("电话区码")
    private String telAreaCode;

    @ApiModelProperty("联系电话")
    private String phone;

    @ApiModelProperty("联系电话,同字段phone")
    private String phoneNumber;

    @ApiModelProperty("注册时间")
    private Date registerDate;

    @ApiModelProperty("国家代号，如CN , Country")
    private String countryCode;

    @ApiModelProperty("DOI flag, 是否verify 用户")
    private Boolean doiFlag;

    @ApiModelProperty("国家名 ")
    private String countryName;

    @ApiModelProperty("公司名字")
    private String companyName;

    @ApiModelProperty("RFQ 提交数量")
    private Integer rfqSubmitCount;

    @ApiModelProperty("RFI 提交数量")
    private Integer rfiSubmitCount;

    @ApiModelProperty("聊天联系人数量")
    private Long chatContactCount;

    @ApiModelProperty("交易笔数")
    private Long totalTransaction;

    @ApiModelProperty("采购意向列表")
    private List<String> sourcingPreference;

    @ApiModelProperty("最近搜索关键词")
    private List<String> keywordList;

    @ApiModelProperty("买家类型")
    private List<Integer> buyerType;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("是否平台黑名单")
    private Boolean acBlackListFlag;

    @ApiModelProperty("该买家发送的RFI/RFQ被供应商举报的次数")
    private Integer complainCount;

    @ApiModelProperty("买家被供应商通过聊天拉黑的次数")
    private Integer chatBlackedCount;

    @ApiModelProperty("visitorId")
    private String visitorId;
    @ApiModelProperty("加入平台黑名单的时间")
    private Date acBlackListDate;

    @ApiModelProperty("买家用户名敏感词")
    private String buyerName;

    @ApiModelProperty("买家ID敏感词")
    private String buyerId;

    @ApiModelProperty("高亮邮箱")
    private String buyerEmail;

    @ApiModelProperty("电话号码字符串")
    private String contactPhoneNumberStr;

    @ApiModelProperty("联系手机号码字符串")
    private String contactMobileNumberStr;
}
