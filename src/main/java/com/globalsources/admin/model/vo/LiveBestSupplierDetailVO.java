package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class LiveBestSupplierDetailVO {
    private Long bestSuppId;

    @ApiModelProperty("pavilion名字")
    private String pavilionName;

    @ApiModelProperty("pavilion code")
    private String pavilionCode;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("优选供应商id")
    private Long supplierId;

    @ApiModelProperty("供应商名字")
    private String supplierName;

    @ApiModelProperty("缩略图")
    private String thumbImgUrl;

    @ApiModelProperty("主图")
    private String posterImgUrl;

    @ApiModelProperty(value = "campaignId")
    private Long campaignId;

    @ApiModelProperty(value = "campaign名字")
    private String campaignName;

    @ApiModelProperty(value = "活动状态")
    private String status;

    @ApiModelProperty("是否开启")
    private Boolean enabled;

    @ApiModelProperty("审核原因")
    private String reviewReason;

    @ApiModelProperty(value = "直播url")
    private String liveUrl;

    @ApiModelProperty("视频类型")
    private String videoType;

    @ApiModelProperty("视频地址")
    private String videoUrl;

    @ApiModelProperty("审核人")
    private Long reviewBy;

    @ApiModelProperty("产品列表")
    private List<ProductLiteVO> productList;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    @ApiModelProperty(value = "修改日期")
    private Date lUpdDate;
}
