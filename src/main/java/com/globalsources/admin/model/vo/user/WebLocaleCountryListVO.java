package com.globalsources.admin.model.vo.user;

import com.globalsources.admin.model.vo.CountryVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> liu
 * @date 2021/6/18
 */
@Data
public class WebLocaleCountryListVO {
    // 当前国家的vo 里面有 国家code 国家名称， 国家地区码，是否是欧美国家
    @ApiModelProperty("当前所在位置国家")
    private CountryVO locale;

    // 国家列表 list VO所有国家的名称,对应的国家code，国家地区码，是否是欧盟国家
    @ApiModelProperty("国家列表")
    private List<CountryVO> list;


}
