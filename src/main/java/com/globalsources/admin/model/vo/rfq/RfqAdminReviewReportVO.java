package com.globalsources.admin.model.vo.rfq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RfqAdminReviewReportVO {

    private String rfqId;
    private String productName;
    private Long categoryId;
    private String categoryName;
    private String originCategoryName;

    private String productDescription;
    private String createDate;
    private String reviewDate;
    private String reviewStatus;
    private String tmxPolicyScore;
    private String attachmentFlag;
    private String reviewResult;
    private String reviewBy;
}
