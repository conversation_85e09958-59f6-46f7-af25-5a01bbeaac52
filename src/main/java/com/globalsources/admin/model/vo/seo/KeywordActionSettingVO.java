package com.globalsources.admin.model.vo.seo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.jupiter.params.shadow.com.univocity.parsers.annotations.Validate;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Title KeywordActionSettingVO
 * @date 2022/7/5 17:40
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="KeywordActionSetting VO", description="keyword")
public class KeywordActionSettingVO implements Serializable {

    @ApiModelProperty(value = "特殊词ID")
    private Integer keywordActSetId;

    @ApiModelProperty(value = "关键词")
    @Validate(nullable = true)
    private String keyword;

    @ApiModelProperty(value = "关键词行为（Restricted，Protected）")
    @Validate(nullable = true)
    private String action;

    @ApiModelProperty(value = "备注,原因")
    @Validate(nullable = true)
    private String comment;

    @ApiModelProperty(value = "用户 ID")
    private Long userId;


}

