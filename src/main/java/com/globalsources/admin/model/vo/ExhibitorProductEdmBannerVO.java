package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 展商产品eDM配置Banner VO
 * 用于返回Banner信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "展商产品eDM配置Banner VO")
public class ExhibitorProductEdmBannerVO {

    @ApiModelProperty(value = "Banner ID")
    private Long bannerId;

    @ApiModelProperty(value = "Banner图片URL")
    private String bannerImageUrl;

    @ApiModelProperty(value = "Banner链接URL")
    private String bannerUrl;

    @ApiModelProperty(value = "生效开始日期")
    private Date effectiveStartDate;

    @ApiModelProperty(value = "生效结束日期")
    private Date effectiveEndDate;
}
