package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2021-08-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(description="用户分发配置表对象")
public class TrafficDistSettingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "traffic distribution seeting id")
    private Long trafficDistributionSettingId;

    @ApiModelProperty(value = "SETTING_CODE")
    private String settingCode;

    @ApiModelProperty(value = "SETTING_DESC")
    private String settingDesc;

    @ApiModelProperty(value = "FREQUENCY")
    private String frequency;

    @ApiModelProperty(value = "EVERY_NUM")
    private Integer everyNum;

    @ApiModelProperty(value = "TOTAL_NUM")
    private Integer totalNum;

    @ApiModelProperty(value = "HONGKONG_SHOW_FLAG")
    private Boolean hongkongShowFlag;

    @ApiModelProperty(value = "VIRTUAL_SHOW_FLAG")
    private Boolean virtualShowFlag;

    @ApiModelProperty(value = "GSOL_FLAG")
    private Boolean gsolFlag;

    @ApiModelProperty(value = "L_UPD_DATE")
    private Date lUpdDate;

    @ApiModelProperty(value = "L_UPD_BY")
    private Long lUpdBy;

    @ApiModelProperty(value = "DELETE_FLAG")
    private Boolean deleteFlag;



}
