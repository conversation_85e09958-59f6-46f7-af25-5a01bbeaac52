package com.globalsources.admin.model.vo.operation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/2
 */
@Data
public class ProductLiteVO {

    @ApiModelProperty("category id")
    private Long categoryId;

    @ApiModelProperty("产品ID")
    private Long productId;
    @ApiModelProperty("供应商ID")
    private Long supplierId;
    @ApiModelProperty("产品名称")
    private String productName;
    @ApiModelProperty("产品型号")
    private String modelNumber;
    @ApiModelProperty("最小订单量单位")
    private String minOrderUnit;
    @ApiModelProperty("单数单位，注意是单数的")
    private String minOrderSingleUnit;
    @ApiModelProperty("产品主图, Thumb")
    private String primaryImageThumbUrl;
    @ApiModelProperty("产品主图, Big")
    private String primaryImageUrl;
    @ApiModelProperty("最小订单量")
    private Integer minOrderQuantity;
    @ApiModelProperty("给前端显示的价格")
    private String listVoShowPriceStr;
    @ApiModelProperty("SEO优化使用的产品详情地址")
    private String desktopProductDetailUrl;

    private Boolean o2oFlag;
}
