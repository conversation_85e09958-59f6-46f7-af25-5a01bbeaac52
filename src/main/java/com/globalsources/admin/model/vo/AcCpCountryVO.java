package com.globalsources.admin.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(description="country page 设置的国家")
public class AcCpCountryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "country page country id")
    private Integer cpCountryId;

    @ApiModelProperty(value = "国家code")
    private String countryCode;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty("修改人邮箱")
    private String updateUserEmail;

    private Date createDate;

    private Date lUpdDate;

    private Long lUpdBy;

    private Long createBy;


}
