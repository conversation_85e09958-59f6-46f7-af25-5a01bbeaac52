package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ac_trade_show_banner_supplier")
public class AcTradeShowBannerSupplierPO extends Model<AcTradeShowBannerSupplierPO> {

    private static final long serialVersionUID = 1L;

    /**
     * trade show id
     */
    @TableId(value = "ac_trade_show_banner_id", type = IdType.ASSIGN_ID)
    private Integer acTradeShowBannerId;

    /**
     * supplierId
     */
    @TableField("supplier_id")
    private Long supplierId;

    /**
     * 展位id
     */
    @TableField("booth_id")
    private String boothId;


    @Override
    protected Serializable pkVal() {
        return this.acTradeShowBannerId;
    }

}
