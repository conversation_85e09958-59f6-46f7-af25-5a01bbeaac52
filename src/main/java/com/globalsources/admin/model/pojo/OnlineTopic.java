package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("online_topic")
@ApiModel(value="OnlineTopicExpand 对象", description="")
public class OnlineTopic implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "auto increment primary key")
    @TableId(value = "topic_id", type = IdType.AUTO)
    private Long topicId;

    @ApiModelProperty(value = "topic")
    private String topic;

    @ApiModelProperty(value = "菜单父id")
    private Long topicPid;

    @ApiModelProperty(value = "中文标题")
    private String topicZh;

    @ApiModelProperty(value = "true=首页菜单,false=内容页菜单")
    private Boolean homeFlag;

    @ApiModelProperty(value = "排序")
    private Integer displaySeq;

    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    @ApiModelProperty(value = "修改人id")
    private Long lUpdBy;

    @ApiModelProperty(value = "修改日期")
    private Date lUpdDate;

    @ApiModelProperty(value = "记录来源:buyer=买家,supplier=卖家")
    private String sourceCode;

    @ApiModelProperty(value = "是否是快捷导航")
    private Boolean quickNavigationFlag;


}
