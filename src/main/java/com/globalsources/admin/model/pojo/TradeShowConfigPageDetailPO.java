package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "trade_show_config_page_detail")
public class TradeShowConfigPageDetailPO {

    @TableId(value = "page_detail_id", type = IdType.AUTO)
    private Integer pageDetailId;

    private String module;

    private String coverImageUrl;

    private String videoName;

    private String videoUrl;

    private String fileName;

    private String fileUrl;

    private String description;

    private Integer pageId;

    private Boolean deleteFlag;

    private Date createDate;

    private Date lUpdDate;

    private Long lUpdBy;

    private Integer sortOrder;
}
