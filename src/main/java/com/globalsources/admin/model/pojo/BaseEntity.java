package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class BaseEntity {
    @ApiModelProperty(value = "是否删除")
    private Boolean deleteFlag;
    @ApiModelProperty(value = "创建人")
    private Long createBy;
    @ApiModelProperty(value = "创建日期")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    @ApiModelProperty(value = "修改人")
    private Long lUpdBy;
    @ApiModelProperty(value = "修改日期")
    private Date lUpdDate;
}
