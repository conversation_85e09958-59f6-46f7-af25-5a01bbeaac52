package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * seminar speaker table
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("seminar_speaker")
public class SeminarSpeakerPO extends Model<SeminarSpeakerPO> {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key
     */
    @TableId(value = "speaker_id", type = IdType.AUTO)
    private Integer speakerId;

    /**
     * Key used to get speaker image
     */
    @TableField("speaker_image")
    private String speakerImage;

    /**
     * Speaker first name
     */
    @TableField("speaker_first_name")
    private String speakerFirstName;

    /**
     * Speaker last name
     */
    @TableField("speaker_last_name")
    private String speakerLastName;

    /**
     * Speaker job title
     */
    @TableField("speaker_title")
    private String speakerTitle;

    /**
     * The description of this speaker
     */
    @TableField("speaker_desc")
    private String speakerDesc;

    /**
     * The position to show in page
     */
    @TableField("speaker_dis_seq")
    private Integer speakerDisSeq;

    /**
     * Create date
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * Last update date
     */
    @TableField("l_upd_date")
    private Date lUpdDate;

    /**
     * Last update by
     */
    @TableField("l_upd_by")
    private Long lUpdBy;


    @Override
    protected Serializable pkVal() {
        return this.speakerId;
    }

}
