package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * trade show buyer rfi survey
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("buyer_rfi_survey")
public class BuyerRfiSurvey extends Model<BuyerRfiSurvey> {

    private static final long serialVersionUID = 1L;

    /**
     * PK
     */
    @TableId(value = "buyer_rfi_survey_id", type = IdType.ASSIGN_ID)
    private Integer buyerRfiSurveyId;

    /**
     * first name
     */
    @TableField("first_name")
    private String firstName;

    /**
     * last name
     */
    @TableField("last_name")
    private String lastName;

    /**
     * visitorId
     */
    @TableField("visitor_id")
    private String visitorId;

    /**
     * pub code
     */
    @TableField("pub_code")
    private String pubCode;

    /**
     * email Addrss
     */
    @TableField("email_addr")
    private String emailAddr;

    /**
     * request for rfi number code
     */
    @TableField("supplier_cnt_per_category")
    private String supplierCntPerCategory;

    /**
     * request for pieces code
     */
    @TableField("product_cnt_code")
    private String productCntCode;

    /**
     * pieces number
     */
    @TableField("product_cnt")
    private BigDecimal productCnt;

    /**
     * buyer signature url
     */
    @TableField("buyer_signature_url")
    private String buyerSignatureUrl;

    /**
     * buyer signature date url
     */
    @TableField("buyer_signature_date_url")
    private String buyerSignatureDateUrl;

    /**
     * buyer business card url
     */
    @TableField("buyer_business_card_url")
    private String buyerBusinessCardUrl;

    /**
     * snapshot
     */
    @TableField("snapshot_url")
    private String snapshotUrl;

    /**
     * sso user
     */
    @TableField("sso_flag")
    private Boolean ssoFlag;

    /**
     * categoryIds
     */
    @TableField("category_id_list")
    private String categoryIdList;

    /**
     * other product
     */
    @TableField("remark")
    private String remark;

    @TableField("create_date")
    private Date createDate;

    @TableField("l_upd_date")
    private Date lUpdDate;


    @TableField("buyer_id")
    private Long buyerId;

    @TableField("country_code")
    private String buyerCountryCode;

    @TableField("country_name")
    private String buyerCountryName;

    @TableField("continent_name")
    private String buyerContinentName;

    @TableField("process_status")
    private String processStatus;

    @TableField("process_error_code")
    private String processErrorCode;

    @TableField("buyer_business_card_back_url")
    private String buyerBusinessCardBackUrl;

    @TableField("company_name")
    private String companyName;

    @TableField("ac_remark")
    private String acRemark;

    @TableField("product_list")
    private String productList;

    @Override
    protected Serializable pkVal() {
        return this.buyerRfiSurveyId;
    }

}
