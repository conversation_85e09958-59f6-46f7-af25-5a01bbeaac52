package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * Tradeshow hotel room price
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ac_hotel_room")
public class AcHotelRoom extends Model<AcHotelRoom> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "hotel_room_id", type = IdType.AUTO)
    private Integer hotelRoomId;

    @TableField("hotel_id")
    private Integer hotelId;

    @TableField("sort_seq")
    private Integer sortSeq;

    /**
     * phase:1,2,3
     */
    @TableField("phase")
    private Integer phase;

    /**
     * Room Type
     */
    @TableField("room_type")
    private String roomType;

    /**
     * Unit Price
     */
    @TableField("unit_price")
    private BigDecimal unitPrice;

    /**
     * Booking Deadline
     */
    @TableField("booking_deadline")
    private Date bookingDeadline;

    /**
     * Early Bird Unit Price
     */
    @TableField("early_bird_unit_price")
    private BigDecimal earlyBirdUnitPrice;

    /**
     * Early Bird Booking Deadline
     */
    @TableField("early_bird_booking_deadline")
    private Date earlyBirdBookingDeadline;

    @TableField("create_date")
    private Date createDate;

    @TableField("l_upd_date")
    private Date lUpdDate;


    @Override
    protected Serializable pkVal() {
        return this.hotelRoomId;
    }

}
