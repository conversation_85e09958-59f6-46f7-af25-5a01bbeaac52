package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("chat_blacklist_review")
public class ChatBlacklistReview {
    @TableId(type = IdType.AUTO)
    private Long reviewId;
    private Long buyerId;
    private String buyerEmailAddr;
    private String buyerFirstName;
    private String buyerLastName;
    private Integer status;
    private String reviewReason;
    private String source;

    private Boolean deleteFlag;
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    @TableField(fill = FieldFill.UPDATE)
    private Date lUpdDate;
    private Long lUpdBy;
}
