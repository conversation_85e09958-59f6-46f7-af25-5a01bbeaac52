package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 角色公司关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AcRoleSupplier对象", description="角色公司关联表")
public class AcRoleSupplier implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "角色公司表自增主键id")
    @TableId(value = "role_supplier_id", type = IdType.AUTO)
    private Long roleSupplierId;

    @ApiModelProperty(value = "角色id")
    private Long roleId;

    @ApiModelProperty(value = "公司id")
    private Long supplierId;

    @ApiModelProperty(value = "逻辑删除")
    private Boolean deleteFlag;

    @ApiModelProperty(value = "创建用户id")
    private Long createBy;

    @ApiModelProperty(value = "创建日期")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    @ApiModelProperty(value = "修改日期")
    private Date lUpdDate;

    @ApiModelProperty(value = "修改人")
    private Long lUpdBy;


}
