package com.globalsources.admin.model.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: AcUserMenuSimplePO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description:  <a>
 *
 * <AUTHOR>
 * @date 2021/6/29 14:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AcUserMenuSimplePO implements Serializable {

    private static final long serialVersionUID=1L;

    private Integer menuId;

    private String menuName;

    private String menuPath;

    private Integer parentMenuId;

    private Integer menuLevel;

    private String icon;

    private String component;

    private Boolean menuFlag;

    private String permissionType;

    private String privilegeType;

    private Integer permissionId;

    private String permissionName;
}