package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * rfq 黑名单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="rfq 黑名单", description="rfq 黑名单")
@TableName(value = "rfq_blacklist")
public class RfqBlackListPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "黑名单id")
    @TableId(value = "blacklist_id", type = IdType.AUTO)
    private Integer blackListId;

    @ApiModelProperty(value = "用户id")
    private Long buyerId;

    @ApiModelProperty(value = "用户邮箱")
    private String buyerEmailAddr;

    @ApiModelProperty(value = "审核人邮箱")
    private String reviewerEmailAddr;

    @ApiModelProperty(value = "是否已删除")
    @TableField(value = "delete_flag")
    private Boolean deleteFlag;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    @ApiModelProperty(value = "修改人")
    private Long lUpdBy;

    @ApiModelProperty(value = "修改日期")
    private Date lUpdDate;

    @ApiModelProperty(value = "添加来源")
    @TableField(value = "add_source")
    private String addSource;

    @ApiModelProperty(value = "添加原因")
    @TableField(value = "add_reason")
    private String addReason;

    @ApiModelProperty(value = "类型(black,white,block)")
    @TableField(value = "type")
    private String type;
}