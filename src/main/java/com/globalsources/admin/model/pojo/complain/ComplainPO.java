package com.globalsources.admin.model.pojo.complain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "req_complain")
public class ComplainPO {

    private Long complainId;

    private Long userId;

    private String emailAddr;

    private String firstName;

    private String lastName;

    private Integer complainNum;

    private Boolean reviewFlag;

    private Date createDate;

    private Date lUpdDate;

    private String reviewResult;

    private Long reviewBy;

}
