package com.globalsources.admin.model.pojo.supplier;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class ApiAccessLog {
    @TableId(value = "log_id", type = IdType.AUTO)
    private Long logId;
    private String apiName;
    private String apiUri;
    private String requestMethod;
    private String apiUrlParam;
    private String remoteRequestIpAddr;
    private String remoteRequestIpCountryCode;
    private Long supplierId;
    private String supplierEmailAddr;
    private Date createDate;
}
