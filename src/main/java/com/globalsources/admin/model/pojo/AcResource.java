package com.globalsources.admin.model.pojo;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class AcResource{
    @ApiModelProperty(value = "资源id")
    @TableId(type = IdType.AUTO)
    private Long resourceId;
    @ApiModelProperty(value = "资源code")
    private String resourceCode;
    @ApiModelProperty(value = "资源类型")
    private String resourceType;
    @ApiModelProperty(value = "资源的父id")
    private Long parResourceId;
    @ApiModelProperty(value = "资源层级深度")
    private Integer resourceLevel;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "资源uri")
    private String resourceUri;
    @ApiModelProperty(value = "资源图标")
    private String resourceIconClass;
    @ApiModelProperty(value = "客户端组件名字")
    private String component;
    @ApiModelProperty(value = "排序")
    private Integer displaySort;
    @ApiModelProperty(value ="默认动作")
    private String defaultAction;
    @ApiModelProperty(value = "创建人")
    private Long createBy;
    @ApiModelProperty(value = "创建日期")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    @ApiModelProperty(value = "修改人")
    private Long lUpdBy;
    @ApiModelProperty(value = "修改日期")
    private Date lUpdDate;

    @ApiModelProperty("后端请求uri")
    private String reqUri;

    @ApiModelProperty("app name, ADMIN: admin console, SC: supplier center, SC_ADMIN: supplier center admin")
    private String appName;

    @ApiModelProperty(value = "是否New Label")
    private Boolean newLabelFlag;

    public AcResource(Long createBy){
        this.createBy=createBy;
        this.lUpdBy=createBy;
        createDate=new Date();
        lUpdDate=createDate;

    }

    /**
     * 克隆一个对象
     * @return
     */
    public AcResource cloneObj(){
        String json=JSON.toJSONString(this);
        return JSON.parseObject(json,AcResource.class);
    }
}
