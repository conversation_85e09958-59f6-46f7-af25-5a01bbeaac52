package com.globalsources.admin.model.pojo.mongo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

/**
 * 仅仅初始化使用
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("chat_grp.chat_blacklist_statis")
public class ChatBlacklistStatis {
    @Id
    @TableId(type= IdType.INPUT)
    private String buyerId;
    //拉黑次数新计数起点
    private Integer newStartPos;
    @TableField("\"count\"")
    private Integer count;
}
