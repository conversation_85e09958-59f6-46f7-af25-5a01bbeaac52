package com.globalsources.admin.model.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class LiveActivity {
    @TableId(type=IdType.AUTO)
    private Long activityId;

    private String title;

    @ApiModelProperty("活动描述")
    private String desc;

    @ApiModelProperty("缩略图")
    private String thumbImgUrl;

    @ApiModelProperty("活动海报图")
    private String posterImgUrl;

    @ApiModelProperty(value = "直播活动分类id")
    private String categoryId;

    @ApiModelProperty(value = "活动开始时间")
    private Date startDate;

    @ApiModelProperty(value = "活动结束时间")
    private Date endDate;

    @ApiModelProperty(value = "活动状态")
    private Integer status;

    @ApiModelProperty(value = "预告片地址")
    private Date trailerUrl;

    @ApiModelProperty(value = "回放文件key")
    private String replayUrl;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "创建日期")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    @ApiModelProperty(value = "修改人")
    private Long lUpdBy;

    @ApiModelProperty(value = "修改日期")
    private Date lUpdDate;
}
