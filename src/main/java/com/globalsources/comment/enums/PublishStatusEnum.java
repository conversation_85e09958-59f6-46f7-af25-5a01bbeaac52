package com.globalsources.comment.enums;

public enum PublishStatusEnum {

    PENDING("Pending", "待审核"),
    PUBLISHED("Published", "审核通过"),
    REJECTED("Rejected", "拒绝"),
    REMOVED("Removed", "删除");

    private final String value;

    private final String msg;

    PublishStatusEnum(String value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public String getValue() {
        return value;
    }

    public String getMsg() {
        return msg;
    }
}
