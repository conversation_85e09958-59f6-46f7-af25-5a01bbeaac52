package com.globalsources.comment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.comment.po.CommentScoreSummaryPO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * comment message Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021/8/24
 */
public interface CommentScoreSummaryMapper extends BaseMapper<CommentScoreSummaryPO> {

    int updateCommentScoreSummary(String bizType, Long productId, String summaryType);

    List<Map<String, Object>> queryCommentScoreSummaryList(String groupBy, Long id);

}
