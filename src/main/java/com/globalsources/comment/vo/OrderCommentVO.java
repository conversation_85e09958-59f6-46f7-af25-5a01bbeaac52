package com.globalsources.comment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderCommentVO implements Serializable {

    private static final long serialVersionUID = -1242493306307174690L;

    @ApiModelProperty("订单ID")
    private String orderId;

    @ApiModelProperty("供应商ID")
    private Long supplierId;

    @ApiModelProperty("订单创建时间")
    private Long createDate;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("产品列表")
    private List<CommentProductVO> productVOList;

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty("买家信息")
    private CommentBuyerVO commentBuyerVO;
}
