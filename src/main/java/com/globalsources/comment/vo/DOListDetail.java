package com.globalsources.comment.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Data
@Slf4j
public class DOListDetail implements Serializable {

    @ApiModelProperty(value = "供应商id",required = false)
    private Long supplierId;

    @ApiModelProperty(value = "订单号",required = false)
    private Long orderId;

    @ApiModelProperty(value = "订单状态，用于显示",required = false)
    private String orderStatusName;

    @ApiModelProperty(value = "来自PSC的订单状态",required = false)
    private String orderStatus;

    @ApiModelProperty("订单code,用户前端判断")
    private Integer orderStatusCode;

    @ApiModelProperty(value = "货币code",required = false)
    private String currencyCode;

    @ApiModelProperty(value = "客户id",required = false)
    private Long customerId;

    @ApiModelProperty(value = "创建日期",required = false)
    private String createDate;

    @ApiModelProperty(value = "订单创建日期",required = false)
    private Date orderCreateDate;

    @ApiModelProperty(value = "总产品成本",required = false)
    private BigDecimal totProductCost;

    @ApiModelProperty(value = "折扣",required = false)
    private BigDecimal discountCost;

    @ApiModelProperty(value = "总订单成本",required = false)
    private BigDecimal totOrderCost;

    @ApiModelProperty(value = "原始总订单",required = false)
    private BigDecimal totProductCostOriginal;

    @ApiModelProperty(value = "运费",required = false)
    private BigDecimal shippingCost;

    @ApiModelProperty(value = "管理员总共退款成本",required = false)
    private BigDecimal totadminrefundcost;

    @ApiModelProperty(value = "总共退款",required = false)
    private BigDecimal totalRefund;

    @ApiModelProperty(value = "供应商名称",required = false)
    private String orgName;

    @ApiModelProperty(value = "状态描述",required = false)
    private String stateDesc;

    @ApiModelProperty(value = "收件人名称",required = false)
    private String recipientName;

    @ApiModelProperty(value = "快递公司名称",required = false)
    private String courierCompanyName;

    @ApiModelProperty(value = "跟踪号码",required = false)
    private String trackingNumber;

    @ApiModelProperty(value = "用户优惠卷id",required = false)
    private Long userCouponId;

    @ApiModelProperty(value = "供应商是否修改了单价 true 代表修改 false 代表没修过",required = false)
    private Boolean updateFobFlag;

    @ApiModelProperty(value = "供应商是否重复修改单价",required = false)
    private Boolean updateFobRepeat;

    @ApiModelProperty(value = "客户信息",required = false)
    private DOCustomer customer;

    @ApiModelProperty(value = "优惠卷信息",required = false)
    private DOCoupon coupon;

    @ApiModelProperty(value = "状态信息",required = false)
    private List<DOStatus> status;

    @ApiModelProperty(value = "产品信息列表",required = false)
    private List<DOProduct> products;

    @ApiModelProperty(value = "退款列表",required = false)
    private List<DOListRefund> refunds;

    @ApiModelProperty(value = "是否报价,true代表已报价，false代表未报价",required = false)
    private Boolean quotedFlag;

    @ApiModelProperty(value = "订单类型",required = false)
    private String orderType;

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
        try {
            this.orderCreateDate = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").parse(createDate);
        } catch (ParseException e){
            log.error("ParseDateException:", e);
        }
    }

    public Date getOrderCreateDate() {
        return orderCreateDate;
    }

    public void setOrderCreateDate(Date orderCreateDate) {
        this.orderCreateDate = orderCreateDate;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }


    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public BigDecimal getTotProductCost() {
        return totProductCost;
    }

    public void setTotProductCost(BigDecimal totProductCost) {
        this.totProductCost = totProductCost;
    }

    public BigDecimal getDiscountCost() {
        return discountCost;
    }

    public void setDiscountCost(BigDecimal discountCost) {
        this.discountCost = discountCost;
    }

    public BigDecimal getTotOrderCost() {
        return totOrderCost;
    }

    public void setTotOrderCost(BigDecimal totOrderCost) {
        this.totOrderCost = totOrderCost;
    }

    public BigDecimal getShippingCost() {
        return shippingCost;
    }

    public void setShippingCost(BigDecimal shippingCost) {
        this.shippingCost = shippingCost;
    }

    public BigDecimal getTotadminrefundcost() {
        return totadminrefundcost;
    }

    public void setTotadminrefundcost(BigDecimal totadminrefundcost) {
        this.totadminrefundcost = totadminrefundcost;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getStateDesc() {
        return stateDesc;
    }

    public void setStateDesc(String stateDesc) {
        this.stateDesc = stateDesc;
    }

    public String getRecipientName() {
        return recipientName;
    }

    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }

    public String getCourierCompanyName() {
        return courierCompanyName;
    }

    public void setCourierCompanyName(String courierCompanyName) {
        this.courierCompanyName = courierCompanyName;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public Long getUserCouponId() {
        return userCouponId;
    }

    public void setUserCouponId(Long userCouponId) {
        this.userCouponId = userCouponId;
    }

    public DOCustomer getCustomer() {
        return customer;
    }

    public void setCustomer(DOCustomer customer) {
        this.customer = customer;
    }

    public DOCoupon getCoupon() {
        return coupon;
    }

    public void setCoupon(DOCoupon coupon) {
        this.coupon = coupon;
    }

    public List<DOStatus> getStatus() {
        return status;
    }

    public void setStatus(List<DOStatus> status) {
        this.status = status;
    }

    public List<DOProduct> getProducts() {
        return products;
    }

    public void setProducts(List<DOProduct> products) {
        this.products = products;
    }

    public List<DOListRefund> getRefunds() {
        return refunds;
    }

    public void setRefunds(List<DOListRefund> refunds) {
        this.refunds = refunds;
    }

    public BigDecimal getTotalRefund() {
        totalRefund = new BigDecimal(0);
        if (!CollectionUtils.isEmpty(refunds)) {
            for (DOListRefund refund : refunds) {
                if(refund.getRefundSource().equalsIgnoreCase("PSCADMIN")
                    || refund.getRefundSource().equalsIgnoreCase("PAYPAL")){
                    totalRefund = totalRefund.add(refund.getRefundAmount());
                }
            }
        }
        return totalRefund;
    }

    public void setTotalRefund(BigDecimal totalRefund) {
        this.totalRefund = totalRefund;
    }
}
