package com.globalsources.comment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CommentInfoVO {

    @ApiModelProperty("业务类型ID ，ORDER,RFI等")
    private String bizId;

    @ApiModelProperty("供应商ID")
    private Long supplierId;

    @ApiModelProperty("产品ID")
    private Long productId;

    @ApiModelProperty("评价消息id")
    private Long msgId;

    @ApiModelProperty("评价id")
    private Long commentId;

    @ApiModelProperty("分数")
    private Double score;

    @ApiModelProperty("产品评价")
    private String msgContent;

    @ApiModelProperty("审核状态")
    private String publishStatus;

    @ApiModelProperty("买家或者卖家")
    private String authorType; // 买家的评价或者卖家的回复， Buyer/Supplier

    @ApiModelProperty("父级msgId")
    private Long parentMsgId;

    private Date createDate;

}
