package com.globalsources.comment.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(description = "Comment Core VO")
public class CommentCoreVO {

    private Long commentId; // 评价ID

    private String bizType; // 业务类型

    private String bizId; // 业务类型ID

    private Long productId; // 产品ID

    private Long supplierId; // 供应商ID

    private String publishStatus; // 审核状态

    private Double score; // 分数

    private Date createDate;

    private Date lastUpdateDate;

    List<CommentMsgCoreVO> commentMsgCoreVOList;

}
