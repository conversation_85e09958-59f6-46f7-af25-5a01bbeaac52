package com.globalsources.comment.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021/8/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(description = "Comment Core VO")
public class CommentMsgCoreVO {

    private Long messageId; // 主键

    private Long commentId; // 评价ID

    private String publishStatus; // 审核状态

    private String authorType; // 买家的评价或者卖家的回复， Buyer/Supplier

    private String msgContent; // 评价的内容

    private Long parentMsgId; // 买家评价的messageId。如果是买家的评价，值为null

    private Boolean deleteFlag; // 是否删除

    private String remark; // 备注

    private Date createDate;

    private Long createBy; // userId

    private Date lastUpdateDate;

    private Long lastUpdateBy;
}
