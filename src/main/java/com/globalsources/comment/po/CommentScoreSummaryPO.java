package com.globalsources.comment.po;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021/8/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(description = "Comment PO")
@TableName(value = "product_score_summary")
public class CommentScoreSummaryPO {

    private Long productId; // 产品ID

    private Long supplierId; // 供应商ID

    private String bizType; // 业务类型

    private String summaryType; // 统计类型

    private Integer commentCnt; // 评价次数

    private Double totScore;    //

    private Date createDate;

}
