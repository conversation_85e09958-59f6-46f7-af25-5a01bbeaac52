package com.globalsources.comment.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CommentScoreSummarySearchCoreDTO {

    @ApiModelProperty("Product ID")
    private Long productId; // 产品ID

    @ApiModelProperty("Supplier ID")
    private Long supplierId; // 供应商ID

    @ApiModelProperty("Business type, e.g, Order, RFI or FRQ etc.")
    private String bizType; // 业务类型

    @ApiModelProperty("Summary Type, '0.0' is Total, others is Group, e.g, 1.0, 2.0...5.0")
    private String summaryType; // 统计类型

}
