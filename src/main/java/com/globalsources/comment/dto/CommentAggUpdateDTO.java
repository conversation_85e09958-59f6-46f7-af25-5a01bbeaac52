package com.globalsources.comment.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CommentAggUpdateDTO {

    @ApiModelProperty("comment ID")
    @NotNull(message = "comment ID cannot be null")
    private Long commentId;

    @ApiModelProperty("message ID")
    @NotNull(message = "message ID cannot be null")
    private Long messageId;

    @ApiModelProperty("publish Status, e.g, Pending, Published or Rejected")
    private String publishStatus;

    private String remark;

    @ApiModelProperty("author type, e.g, Buyer or Supplier")
    private String authorType;

    @ApiModelProperty("business type, e.g, Order, RFI or FRQ etc.")
    private String bizType;

    @ApiModelProperty("delete flag")
    private Boolean deleteFlag;

    @ApiModelProperty("Last Update User Id")
    private Long lastUpdateBy;

    private Long supplierId;

    private String bizId;

    private Long productId;

    private Integer score;

    private String msgContent;
}
