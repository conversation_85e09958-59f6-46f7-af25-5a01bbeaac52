package com.globalsources.comment.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.comment.dto.*;
import com.globalsources.comment.mapper.CommentMapper;
import com.globalsources.comment.mapper.CommentMsgMapper;
import com.globalsources.comment.mapper.CommentScoreSummaryMapper;
import com.globalsources.comment.po.CommentMessagePO;
import com.globalsources.comment.po.CommentPO;
import com.globalsources.comment.po.CommentScoreSummaryPO;
import com.globalsources.comment.service.CommentCoreService;
import com.globalsources.comment.vo.CommentCoreVO;
import com.globalsources.comment.vo.CommentInfoVO;
import com.globalsources.comment.vo.CommentMsgCoreVO;
import com.globalsources.comment.vo.CommentScoreSummaryCoreVO;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;


/**
 * <AUTHOR> Chen
 * @since 2021-08-12
 */
@Slf4j
@Service
public class CommentCoreServiceImpl implements CommentCoreService {

    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private CommentMsgMapper commentMsgMapper;

    @Autowired
    private CommentScoreSummaryMapper commentScoreSummaryMapper;

    @Override
    public PageResult<CommentCoreVO> getCommentList(Integer pageNum, Integer pageSize, CommentCoreSearchDTO commentCoreSearchDTO) {
        Page<CommentPO> pageInfo = new Page<>(pageNum, pageSize);
        IPage<CommentPO> iPage = commentMapper.queryLiteCommentList(pageInfo, commentCoreSearchDTO);

        // transfer to CommentCoreVO PageResult
        Page<CommentCoreVO> commentVOPage = new Page<>();
        BeanUtils.copyProperties(iPage, commentVOPage);

        List<CommentPO> commentPOList = iPage.getRecords();
        if (!CollectionUtils.isEmpty(commentPOList)) {
            List<CommentCoreVO> commentCoreVOList = Lists.newArrayList();
            List<Long> commentIdList = Lists.newLinkedList();
            commentPOList.forEach(commentPO -> {
                commentIdList.add(commentPO.getCommentId());
                commentCoreVOList.add(OrikaMapperUtil.coverObject(commentPO, CommentCoreVO.class));
            });

            LambdaQueryWrapper<CommentMessagePO> commentMessagePOLambdaQueryWrapper = new LambdaQueryWrapper<CommentMessagePO>()
                    .in(CommentMessagePO::getCommentId, commentIdList)
                    .eq(StringUtils.isNotBlank(commentCoreSearchDTO.getPublishStatus()), CommentMessagePO::getPublishStatus, commentCoreSearchDTO.getPublishStatus());
            List<CommentMessagePO> commentMessagePOList = commentMsgMapper.selectList(commentMessagePOLambdaQueryWrapper);
            Map<Long, List<CommentMsgCoreVO>> commentCoreVOMap = Maps.newHashMap();
            commentMessagePOList.forEach(commentMessagePO -> {
                List<CommentMsgCoreVO> commentMsgCoreVOList = commentCoreVOMap.computeIfAbsent(commentMessagePO.getCommentId(), k -> Lists.newArrayList());
                commentMsgCoreVOList.add(OrikaMapperUtil.coverObject(commentMessagePO, CommentMsgCoreVO.class));
            });

            commentCoreVOList.forEach(commentCoreVO -> {
                List<CommentMsgCoreVO> commentCoreVOList1 = commentCoreVOMap.get(commentCoreVO.getCommentId());
                commentCoreVO.setCommentMsgCoreVOList(Optional.ofNullable(commentCoreVOList1).orElse(Lists.newArrayList()));
            });

            commentVOPage.setRecords(commentCoreVOList);
        }

        return PageResult.restPage(commentVOPage);
    }

    @Override
    public PageResult<CommentMsgCoreDTO> getCommentMsgList(Integer pageNum, Integer pageSize, CommentCoreSearchDTO commentCoreSearchDTO) {
        Page<CommentMsgCoreDTO> pageInfo = new Page<>(pageNum, pageSize);
        IPage<CommentMsgCoreDTO> iPage = commentMapper.queryCommentMsgList(pageInfo, commentCoreSearchDTO);

        return PageResult.restPage(iPage);
    }

    @Override
    @Transactional
    public void update(CommentCoreUpdateDTO commentCoreUpdateDTO) {
        LambdaUpdateWrapper<CommentMessagePO> commentMsgUpdateWrapper = new LambdaUpdateWrapper<CommentMessagePO>()
                .set(StringUtils.isNotBlank(commentCoreUpdateDTO.getPublishStatus()), CommentMessagePO::getPublishStatus, commentCoreUpdateDTO.getPublishStatus())
                .set(StringUtils.isNotBlank(commentCoreUpdateDTO.getRemark()), CommentMessagePO::getRemark, commentCoreUpdateDTO.getRemark())
                .set(Objects.nonNull(commentCoreUpdateDTO.getLastUpdateBy()), CommentMessagePO::getLastUpdateBy, commentCoreUpdateDTO.getLastUpdateBy())
                .set(CommentMessagePO::getLastUpdateDate, new Date())
                .eq(CommentMessagePO::getMessageId, commentCoreUpdateDTO.getMessageId());
        commentMsgMapper.update(null, commentMsgUpdateWrapper);

        CommentMessagePO commentMessagePO = commentMsgMapper.selectById(commentCoreUpdateDTO.getMessageId());
        if (commentMessagePO != null && commentMessagePO.getParentMsgId() == null) {
            LambdaUpdateWrapper<CommentPO> commentUpdateWrapper = new LambdaUpdateWrapper<CommentPO>()
                    .set(StringUtils.isNotBlank(commentCoreUpdateDTO.getPublishStatus()), CommentPO::getPublishStatus, commentCoreUpdateDTO.getPublishStatus())
                    .set(CommentPO::getLastUpdateDate, new Date())
                    .eq(CommentPO::getCommentId, commentCoreUpdateDTO.getCommentId());
            commentMapper.update(null, commentUpdateWrapper);
        }
    }

    @Override
    public List<CommentScoreSummaryCoreVO> getCommentScoreSummaryList(CommentScoreSummarySearchCoreDTO commentScoreSummarySearchCoreDTO) {
        LambdaQueryWrapper<CommentScoreSummaryPO> scoreSummaryPOLambdaQueryWrapper = new LambdaQueryWrapper<CommentScoreSummaryPO>()
                .eq(Objects.nonNull(commentScoreSummarySearchCoreDTO.getProductId()), CommentScoreSummaryPO::getProductId, commentScoreSummarySearchCoreDTO.getProductId())
                .eq(Objects.nonNull(commentScoreSummarySearchCoreDTO.getSupplierId()), CommentScoreSummaryPO::getSupplierId, commentScoreSummarySearchCoreDTO.getSupplierId())
                .eq(StringUtils.isNotBlank(commentScoreSummarySearchCoreDTO.getBizType()), CommentScoreSummaryPO::getBizType, commentScoreSummarySearchCoreDTO.getBizType())
                .eq(StringUtils.isNotBlank(commentScoreSummarySearchCoreDTO.getSummaryType()), CommentScoreSummaryPO::getSummaryType, commentScoreSummarySearchCoreDTO.getSummaryType());

        List<CommentScoreSummaryPO> commentScoreSummaryPOList = commentScoreSummaryMapper.selectList(scoreSummaryPOLambdaQueryWrapper);

        return OrikaMapperUtil.coverList(commentScoreSummaryPOList, CommentScoreSummaryCoreVO.class);
    }

    @Override
    public List<Map<String, Object>> getCommentScoreSummaryList(String groupBy, Long id) {
        return commentScoreSummaryMapper.queryCommentScoreSummaryList(groupBy, id);
    }

    @Override
    @Transactional
    public void updateCommentScoreSummary(CommentCoreUpdateDTO commentCoreUpdateDTO) {
        CommentPO commentPO = commentMapper.selectById(commentCoreUpdateDTO.getCommentId());
        if (commentPO == null) {
            throw new BusinessException(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND.getCode(), "Can't find Comment Object");
        }

        // insert or update overall score
        insertOrUpdateCommentScoreSummary(commentPO, "0.0");

        // insert or update score
        insertOrUpdateCommentScoreSummary(commentPO, String.valueOf(commentPO.getScore()));

    }

    private void insertOrUpdateCommentScoreSummary(CommentPO commentPO, String summaryType) {
        int updateCount = commentScoreSummaryMapper.updateCommentScoreSummary(commentPO.getBizType(), commentPO.getProductId(), summaryType);
        if (updateCount == 0) {
            CommentScoreSummaryPO productScoreSummaryPO = new CommentScoreSummaryPO();
            productScoreSummaryPO.setProductId(commentPO.getProductId());
            productScoreSummaryPO.setSupplierId(commentPO.getSupplierId());
            productScoreSummaryPO.setBizType(commentPO.getBizType());
            productScoreSummaryPO.setSummaryType(summaryType);
            productScoreSummaryPO.setTotScore(commentPO.getScore());
            productScoreSummaryPO.setCommentCnt(1);
            productScoreSummaryPO.setCreateDate(new Date());

            commentScoreSummaryMapper.insert(productScoreSummaryPO);
        }
    }

    @Override
    public CommentPO getCommentEntityId(Long commentId) {
        if (commentId == null) {
            return null;
        }
        return commentMapper.selectById(commentId);
    }

    @Override
    public CommentMessagePO getCommentMsgEntityId(Long msgId) {
        if (msgId == null) {
            return null;
        }
        return commentMsgMapper.selectById(msgId);
    }

    @Override
    public CommentCoreVO getCommentById(Long commentId) {
        CommentPO commentPO = commentMapper.selectById(commentId);
        if (commentPO == null) {
            throw new BusinessException(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND.getCode(), "Can't find Comment Object");
        }
        CommentCoreVO commentCoreVO = OrikaMapperUtil.coverObject(commentPO, CommentCoreVO.class);

        LambdaQueryWrapper<CommentMessagePO> lambdaQueryWrapper = new LambdaQueryWrapper<CommentMessagePO>()
                .eq(CommentMessagePO::getCommentId, commentId);
        List<CommentMessagePO> commentMessagePOList = commentMsgMapper.selectList(lambdaQueryWrapper);
        commentCoreVO.setCommentMsgCoreVOList(OrikaMapperUtil.coverList(commentMessagePOList, CommentMsgCoreVO.class));

        return commentCoreVO;
    }

    @Override
    public Long insertComment(CommentAddDTO commentAddDTO) {
        CommentPO commentPO = OrikaMapperUtil.coverObject(commentAddDTO, CommentPO.class);
        commentMapper.insert(commentPO);
        return commentPO.getCommentId();
    }

    @Override
    public int insertCommentMessage(CommentMsgAddDTO commentMsgAddDTO) {
        CommentMessagePO commentMessagePO = OrikaMapperUtil.coverObject(commentMsgAddDTO, CommentMessagePO.class);
        return commentMsgMapper.insert(commentMessagePO);
    }

    @Override
    public List<CommentCoreVO> selectCommentByBizIds(List<String> bizIds) {
        List<CommentPO> commentPOList = commentMapper.selectList(Wrappers.lambdaQuery(CommentPO.class).in(CommentPO::getBizId, bizIds));
        return OrikaMapperUtil.coverList(commentPOList, CommentCoreVO.class);
    }

    @Override
    public PageResult<CommentInfoVO> selectBizIdBySupplierId(Integer pageNum, Integer pageSize, Long supplierId, String bizType, String publishStatus, String authorType) {
        Page<CommentInfoVO> page = new Page<>(pageNum, pageSize);
        IPage<CommentInfoVO> iPage = commentMapper.selectBizIdBySupplierId(page, supplierId, bizType, publishStatus, authorType);
        return PageResult.restPage(iPage);
    }

    @Override
    public List<CommentInfoVO> selectCommentInfoByBizId(String bizId) {
        return commentMapper.selectCommentInfoByBizId(bizId);
    }

    @Override
    public PageResult<CommentInfoVO> selectBuyerCommentBySupplierId(Integer pageNum, Integer pageSize, Long supplierId, String bizType, String publishStatus, String authorType) {
        Page<CommentInfoVO> page = new Page<>(pageNum, pageSize);
        IPage<CommentInfoVO> iPage = commentMapper.selectBuyerCommentBySupplierId(page, supplierId, bizType, publishStatus, authorType);
        return PageResult.restPage(iPage);
    }

    @Override
    public CommentMsgCoreVO selectSupplierCommentByMsgId(Long msgId, String publishStatus) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("parent_msg_id", msgId);
        if (StringUtils.isNotEmpty(publishStatus)) {
            map.put("publish_status", publishStatus);
        }
        List<CommentMessagePO> commentMessagePOS = commentMsgMapper.selectByMap(map);
        if (!CollectionUtils.isEmpty(commentMessagePOS)) {
            CommentMessagePO commentMessagePO = commentMessagePOS.get(0);
            return OrikaMapperUtil.coverObject(commentMessagePO, CommentMsgCoreVO.class);
        }
        return null;
    }

    @Override
    public int updateCommentMsg(CommentMsgCoreUpdateDTO commentMsgCoreUpdateDTO) {
        UpdateWrapper<CommentMessagePO> commentMessagePOUpdateWrapper = new UpdateWrapper<>();
        commentMessagePOUpdateWrapper.eq("msg_id", commentMsgCoreUpdateDTO.getMessageId());
        CommentMessagePO commentMessagePO = new CommentMessagePO();
        commentMessagePO.setReplyFlag(commentMsgCoreUpdateDTO.getReplyFlag());
        commentMessagePO.setLastUpdateDate(commentMsgCoreUpdateDTO.getLastUpdateDate());
        return commentMsgMapper.update(commentMessagePO, commentMessagePOUpdateWrapper);
    }

    @Override
    public PageResult<CommentInfoVO> listSupplierComment(Long supplierId, String authorType, String bizType, String publishStatus, Boolean replyFlag, Integer pageNum, Integer pageSize) {
        Page<CommentInfoVO> page = new Page<>(pageNum, pageSize);
        IPage<CommentInfoVO> iPage = commentMapper.listSupplierComment(page, supplierId, authorType, bizType, publishStatus, replyFlag);
        return PageResult.restPage(iPage);
    }

    @Override
    public List<CommentInfoVO> selectCommentByBizIdAndProductId(String bizId, Long productId) {
        return commentMapper.selectCommentByBizIdAndProductId(bizId, productId);
    }

    @Override
    public PageResult<CommentInfoVO> selectBizIdByUserId(Integer pageNum, Integer pageSize, Long userId,
                                                         String bizType, String publishStatus, String authorType) {
        Page<CommentInfoVO> page = new Page<>(pageNum, pageSize);
        IPage<CommentInfoVO> iPage = commentMapper.selectBizIdByUserId(page, userId, bizType, publishStatus, authorType);
        return PageResult.restPage(iPage);
    }

    @Override
    public List<Map<String, Object>> selectCommentScoreSummaryByIds(CommentCoreRequestDTO dto) {
        return commentMapper.selectCommentScoreSummaryByIds(dto);
    }

    @Override
    public List<Long> selectCommentProductIdList(CommentCoreRequestDTO dto) {
        return commentMapper.selectCommentProductIdList(dto);
    }
}
