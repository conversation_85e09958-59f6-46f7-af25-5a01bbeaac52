package com.globalsources.comment.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.globalsources.agg.supplier.api.feign.SupplierAggFeign;
import com.globalsources.agg.supplier.api.model.dto.SupplierCommonInfoDTO;
import com.globalsources.comment.dto.CommentAddDTO;
import com.globalsources.comment.dto.CommentMsgAddDTO;
import com.globalsources.comment.dto.OrderCommentDTO;
import com.globalsources.comment.enums.AuthorTypeEnum;
import com.globalsources.comment.enums.BizTypeEnum;
import com.globalsources.comment.enums.DirectOrderStatus;
import com.globalsources.comment.enums.PublishStatusEnum;
import com.globalsources.comment.service.CommentCoreService;
import com.globalsources.comment.service.OrderCommentService;
import com.globalsources.comment.vo.CommentInfoVO;
import com.globalsources.comment.vo.CommentProductVO;
import com.globalsources.comment.vo.OrderCommentVO;
import com.globalsources.comment.vo.SupplierReplyVO;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.result.ResultCodeEnum;
import com.globalsources.order.api.dto.BuyerListQueryDTO;
import com.globalsources.order.api.feign.BuyerOrderAggFeign;
import com.globalsources.order.api.vo.BuyerListProductVO;
import com.globalsources.order.api.vo.BuyerListVO;
import com.globalsources.order.core.api.feign.OrderCoreFeign;
import com.globalsources.order.core.api.po.DOComment;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class OrderCommentServiceImpl implements OrderCommentService {

    @Autowired
    private CommentCoreService commentCoreService;

    @Autowired
    private OrderCoreFeign orderCoreFeign;

    @Autowired
    private BuyerOrderAggFeign buyerOrderAggFeign;

    @Autowired
    private SupplierAggFeign supplierAggFeign;

    private static final String PENDING_REVIEW = "PendingReview";

    public static final int DEFAULT_PAGE_SIZE = 20;

    @Override
    @GlobalTransactional
    public Boolean submitOrderComment(Long userId, List<OrderCommentDTO> orderCommentDTOList) {
        log.warn("submitOrderComment userId:{}", userId);
        String orderId = "";
        for (OrderCommentDTO orderCommentDTO : orderCommentDTOList) {
            orderId = orderCommentDTO.getOrderId();
            CommentAddDTO commentAddDTO = new CommentAddDTO();
            commentAddDTO.setBizId(orderCommentDTO.getOrderId());
            commentAddDTO.setBizType(BizTypeEnum.ORDER.name());
            commentAddDTO.setProductId(orderCommentDTO.getProductId());
            commentAddDTO.setSupplierId(orderCommentDTO.getSupplierId());
            commentAddDTO.setPublishStatus(PublishStatusEnum.PENDING.getValue());
            commentAddDTO.setScore(orderCommentDTO.getScore());
            commentAddDTO.setCreateDate(new Date());
            commentAddDTO.setLastUpdateDate(new Date());
            Long commentId;
            try {
                commentId = commentCoreService.insertComment(commentAddDTO);
            } catch (Exception e) {
                log.error("insert comment error:", e);
                throw new BusinessException(ResultCode.CommentResultCode.INSERT_COMMENT_ERROR);
            }
            CommentMsgAddDTO commentMsgAddDTO = new CommentMsgAddDTO();
            commentMsgAddDTO.setCommentId(commentId);
            commentMsgAddDTO.setPublishStatus(PublishStatusEnum.PENDING.getValue());
            commentMsgAddDTO.setMsgContent(orderCommentDTO.getContent());
            commentMsgAddDTO.setAuthorType(AuthorTypeEnum.BUYER.getName());
            commentMsgAddDTO.setReplyFlag(false);
            commentMsgAddDTO.setCreateDate(new Date());
            commentMsgAddDTO.setDeleteFlag(false);
            commentMsgAddDTO.setLastUpdateDate(new Date());
            commentMsgAddDTO.setCreateBy(userId);
            commentMsgAddDTO.setLastUpdateBy(userId);

            try {
                commentCoreService.insertCommentMessage(commentMsgAddDTO);
            } catch (Exception e) {
                log.error("insert comment msg error:", e);
                throw new BusinessException(ResultCode.CommentResultCode.INSERT_COMMENT_MSG_ERROR);
            }
        }

        DOComment doComment = new DOComment();
        if (!StringUtils.isEmpty(orderId)) {
            doComment.setOrderId(orderId);
            doComment.setBuyerReviewStatus(PublishStatusEnum.PENDING.getValue());
            boolean b = orderCoreFeign.updateOrderCommentStatus(doComment);
            if (!b) {
                // 抛出异常
                throw new BusinessException(ResultCodeEnum.PSC_ERROR);
            }
        }
        return true;
    }

    @Override
    public PageResult<OrderCommentVO> pendingReviewsByBuyer(Integer pageNum, Integer pageSize, Long userId) {
        if (pageNum != null && pageNum != 0) {
            pageNum = pageNum - 1;
        }
        if (null == pageSize) {
            pageSize = DEFAULT_PAGE_SIZE;
        }

        PageResult<BuyerListVO> pageResult = buyerOrderAggFeign.list(buildBuyerListQueryDTO(pageNum, pageSize, PENDING_REVIEW), userId, null).getData();
        List<BuyerListVO> orderList = pageResult.getList();
        if (CollUtil.isEmpty(orderList)) {
            return PageResult.restPage(pageResult, OrderCommentVO.class);
        }

        List<OrderCommentVO> orderCommentVOS = convertOrderCommentVO(orderList, false);
        return PageResult.restPage(pageResult, orderCommentVOS);
    }

    private List<OrderCommentVO> convertOrderCommentVO(List<BuyerListVO> orderList, boolean isReviewed) {
        List<OrderCommentVO> orderCommentVOS = new ArrayList<>();
        orderList.forEach(orderInfo -> {
            OrderCommentVO orderCommentVO = new OrderCommentVO();
            orderCommentVO.setOrderId(orderInfo.getOrderId());
            orderCommentVO.setCreateDate(orderInfo.getCreateDate());
            orderCommentVO.setSupplierId(orderInfo.getSupplierId());
            orderCommentVO.setSupplierName(orderInfo.getSupplierName());
            orderCommentVO.setOrderAmount(orderInfo.getTotalOrderPrice());
            List<BuyerListProductVO> productVOList = orderInfo.getProductVOList();
            List<CommentProductVO> commentProductVOS = new ArrayList<>();
            if (!CollectionUtils.isEmpty(productVOList)) {
                productVOList.forEach(product -> {
                    CommentProductVO productVO = new CommentProductVO();
                    productVO.setProductId(product.getProductId());
                    productVO.setProductName(product.getProductName());
                    productVO.setPrimaryImageUrl(product.getProductImageUrl());
                    if (Boolean.TRUE.equals(isReviewed)) {
                        assembleCommentProductVO(productVO, orderInfo.getOrderId());
                    }
                    commentProductVOS.add(productVO);
                });
            }
            orderCommentVO.setProductVOList(commentProductVOS);
            orderCommentVOS.add(orderCommentVO);
        });
        return orderCommentVOS;
    }

    @Override
    public PageResult<OrderCommentVO> reviewedByBuyer(Integer pageNum, Integer pageSize, Long userId) {
        // 查询买家已回复订单
        // 传入ul2cookie 审核状态全部传入
        if (pageNum != null && pageNum != 0) {
            pageNum = pageNum - 1;
        }
        if (null == pageSize) {
            pageSize = DEFAULT_PAGE_SIZE;
        }

        // 增加审核状态
        List<String> auditStatus = new ArrayList<>();
        auditStatus.add(PublishStatusEnum.PENDING.getValue());
        auditStatus.add(PublishStatusEnum.PUBLISHED.getValue());
        auditStatus.add(PublishStatusEnum.REJECTED.getValue());
        auditStatus.add(PublishStatusEnum.REMOVED.getValue());
        String auditString = String.join(",", auditStatus);

        PageResult<BuyerListVO> pageResult = buyerOrderAggFeign.list(buildBuyerListQueryDTO(pageNum, pageSize, auditString), userId, null).getData();
        List<BuyerListVO> orderList = pageResult.getList();
        if (CollUtil.isEmpty(orderList)) {
            return PageResult.restPage(pageResult, OrderCommentVO.class);
        }

        List<OrderCommentVO> orderCommentVOS = convertOrderCommentVO(orderList, true);
        return PageResult.restPage(pageResult, orderCommentVOS);
    }

    private BuyerListQueryDTO buildBuyerListQueryDTO(Integer pageNum, Integer pageSize, String buyerReviewStatus) {
        BuyerListQueryDTO buyerListQueryDTO = new BuyerListQueryDTO();
        buyerListQueryDTO.setPageNum(pageNum);
        buyerListQueryDTO.setPageSize(pageSize);
        buyerListQueryDTO.setOrderStatus(DirectOrderStatus.COMPLETED.name());
        buyerListQueryDTO.setBuyerReviewStatus(buyerReviewStatus);
        return buyerListQueryDTO;
    }

    private void assembleCommentProductVO(CommentProductVO productVO, String orderId) {
        List<CommentInfoVO> commentInfoVOList = commentCoreService.selectCommentByBizIdAndProductId(orderId, productVO.getProductId());
        if (CollectionUtils.isEmpty(commentInfoVOList)) {
            return;
        }

        for (CommentInfoVO commentInfoVO : commentInfoVOList) {
            // 获取到当前产品的分数和评论
            if (commentInfoVO.getAuthorType().equals(AuthorTypeEnum.BUYER.getName())) {
                productVO.setScore(commentInfoVO.getScore());
                productVO.setMsgContent(commentInfoVO.getMsgContent());
            } else if (commentInfoVO.getAuthorType().equals(AuthorTypeEnum.SUPPLIER.getName())) {
                // 说明是当前产品的回复
                SupplierReplyVO supplierReplyVO = new SupplierReplyVO();
                // 回复日期
                supplierReplyVO.setReplyDate(commentInfoVO.getCreateDate().getTime());
                supplierReplyVO.setSupplierId(commentInfoVO.getSupplierId());
                supplierReplyVO.setMsgContent(commentInfoVO.getMsgContent());
                // 获取供应商详情信息
                log.warn("commentInfoVO.getSupplierId():{}", commentInfoVO.getSupplierId());
                SupplierCommonInfoDTO supplierCommonInfoDTO = supplierAggFeign.getCommonInfo(commentInfoVO.getSupplierId()).getData();
                if (Objects.nonNull(supplierCommonInfoDTO)) {
                    supplierReplyVO.setSupplierName(supplierCommonInfoDTO.getCompanyDisplayName());
                    supplierReplyVO.setLogoUrl(supplierCommonInfoDTO.getLogoUrl());
                }
                productVO.setSupplierReplyVO(supplierReplyVO);
            }
        }
    }
}
