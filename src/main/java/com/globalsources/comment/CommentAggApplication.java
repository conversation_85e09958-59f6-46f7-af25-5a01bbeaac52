package com.globalsources.comment;

import com.globalsources.framework.configuration.FeignClientsConfig;
import com.globalsources.framework.inject.UserLoginInfoInject;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;

@Import({UserLoginInfoInject.class, FeignClientsConfig.class})
@RefreshScope
@EnableCaching
@EnableFeignClients(basePackages = {"com.globalsources.*"}, defaultConfiguration = FeignClientsConfig.class)
@EnableDiscoveryClient
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class}, scanBasePackages = {"com.globalsources.comment.*", "com.globalsources.framework.*"})
public class CommentAggApplication {

    public static void main(String[] args) {
        SpringApplication.run(CommentAggApplication.class, args);
    }
}
