package com.globalsources.operation.controller.app;

import com.globalsources.framework.result.Result;
import com.globalsources.rfx.model.vo.ProductLiteInfoVO;
import com.globalsources.rfx.model.vo.SupplierSimpleInfo;
import com.globalsources.rfx.service.IProductService;
import com.globalsources.rfx.service.ISupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/11
 */

@Slf4j
@Api(tags = "圈商圈品通用接口")
@RestController
@RequestMapping("/app/common")
@RequiredArgsConstructor
public class OperationCommonAppController {

    private final IProductService productService;

    private final ISupplierService supplierService;


    @ApiOperation(value = "产品信息")
    @PostMapping("/v1/product-info")
    public Result<List<ProductLiteInfoVO>> productInfo(@RequestBody List<Long> productIdList) {
        return Result.success(productService.productInfo(productIdList));
    }

    @ApiOperation(value = "获取供应商信息")
    @PostMapping("/v1/supplier-info")
    public Result<List<SupplierSimpleInfo>> supplierInfo(@RequestBody List<Long> supplierIdList) {
        return Result.success(supplierService.supplierInfo(supplierIdList));
    }

}
