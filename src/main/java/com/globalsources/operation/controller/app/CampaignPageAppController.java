package com.globalsources.operation.controller.app;

import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCodeEnum;
import com.globalsources.operation.agg.api.constants.PageConstants;
import com.globalsources.operation.agg.api.dto.CampaignPageContentRequestDTO;
import com.globalsources.operation.agg.api.feign.CampaignPageFeign;
import com.globalsources.operation.agg.api.vo.CampaignPageContentEditVO;
import com.globalsources.operation.agg.api.vo.campaignpage.CampaignPageBreadcrumbNavigationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR> Sun
 * @since 2022/11/28
 */
@Slf4j
@Api(tags = "运营-活动页面")
@RestController
@RequestMapping("/campaign-app-page")
public class CampaignPageAppController {
    private static final String PATH_APP = "app/";
    @Autowired
    private CampaignPageFeign campaignPageFeign;

    @ApiOperation(value = "通过page url获取app页面内容", notes = "通过page url获取app页面内容")
    @GetMapping("/v1/get-pageContentByPageUrl")
    public Result<CampaignPageContentEditVO> getPageContentByPageUrl(@ApiParam("page url") @RequestParam(name = "pageUrl", required = false) String pageUrl,
                                                                     @ApiParam("预览/展示") @RequestParam(name = "viewType", required = false) String viewType) {
        if(StringUtils.isBlank(pageUrl)){
            log.info("------ pageUrl:{}, viewType:{}", pageUrl, viewType);
            throw new BusinessException(ResultCodeEnum.PARAMETER_VALIDATION_FAILED);
        }

        Result<CampaignPageContentEditVO> rt = campaignPageFeign.getPageContent(CampaignPageContentRequestDTO.builder()
                .pageUrl(pageUrl)
                .viewType(viewType)
                .platformType(PageConstants.PlatformTypeEnum.APP.getType())
                .build());
        if(rt != null && rt.getData() != null && !StringUtils.isBlank(rt.getData().getPageUrlSuffix())){
            rt.getData().setPageUrlSuffix(PATH_APP + rt.getData().getPageUrlSuffix());
        }
        return rt;
    }

    @ApiOperation(value = "获取面包屑", notes = "获取面包屑")
    @GetMapping("/v1/get-breadcrumb-navigation")
    public Result<CampaignPageBreadcrumbNavigationVO> getBreadcrumbNavigation(@ApiParam("活动ID") @RequestParam(name = "campaignId", required = false) Long campaignId,
                                                                              @ApiParam("page url") @RequestParam(name = "pageUrl", required = false) String pageUrl) {
        if (Objects.isNull(campaignId) || StringUtils.isBlank(pageUrl)) {
            log.info("------ campaignId:{}, pageUrl:{}", campaignId, pageUrl);
            throw new BusinessException(ResultCodeEnum.PARAMETER_VALIDATION_FAILED);
        }
        return campaignPageFeign.getBreadcrumbNavigation(CampaignPageContentRequestDTO.builder()
                .campaignId(campaignId)
                .pageUrl(pageUrl)
                .platformType(PageConstants.PlatformTypeEnum.APP.getType())
                .build());
    }
}
