package com.globalsources.operation.controller.web;

import com.globalsources.framework.result.Result;
import com.globalsources.operation.agg.api.dto.CategoryFilterDTO;
import com.globalsources.operation.agg.api.feign.CategoryFeign;
import com.globalsources.operation.agg.api.vo.CategoryListVO;
import com.globalsources.operation.agg.api.vo.ProductCategoryNumVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/17
 */
@Slf4j
@Api(tags = "运营-产品类别")
@RestController
@RequestMapping("/product-category")
public class ProductCategoryController {

    @Autowired
    private CategoryFeign categoryFeign;

    @ApiOperation(value = "查询类别", notes = "查询类别")
    @PostMapping("/v1/select-category")
    public Result<List<CategoryListVO>> selectCategory(@RequestBody CategoryFilterDTO dto) {
        return categoryFeign.selectCategory(dto);
    }

    @ApiOperation(value = "查询L1-l2,带L2数量")
    @GetMapping(value = "v1/query-l1-l2-with-num")
    public Result<List<ProductCategoryNumVO>> queryL1L2CategoryListWithNum() {
        return categoryFeign.queryL1L2CategoryListWithNum();
    }
}
