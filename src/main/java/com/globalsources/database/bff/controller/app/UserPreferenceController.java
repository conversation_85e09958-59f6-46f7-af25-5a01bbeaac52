package com.globalsources.database.bff.controller.app;

import com.globalsources.database.api.feign.UserPreferenceEditFeign;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/21
 */
@Api(tags = {"App Site--偏好相关接口"})
@RequestMapping("/database-app-bff/preference")
@RestController
@Slf4j
public class UserPreferenceController {

    @Autowired
    private UserPreferenceEditFeign userSourcingFeign;

    @ApiOperation(value = "标记某个L4类别为不感兴趣",tags = {"App Site--偏好相关接口"})
    @GetMapping(value = "v1/not-interested")
    @Login(validLogin = false)
    public Result<String> notInterested(@ApiIgnore UserVO userVO, @RequestParam @NonNull Long categoryId, @ApiIgnore @RequestHeader(required = false,value="AnonymousId")String anonymousId) {
        try {
            String userId=Objects.nonNull(userVO)?userVO.getUserId().toString():anonymousId;
            log.info("not-interested userId:{},category:{}",userId,categoryId);
            boolean result=userSourcingFeign.deleteUserPreferenceScore(userId,categoryId);
            log.info("not-interested result:{}",result);
            return Result.success();
        } catch (Exception e) {
            log.error("not-interested error:{}",e);
            return Result.failed();
        }
    }
}
