package com.globalsources.job.constants;

public enum StoreType {
    RFI(1),
    RFQ(2),
    CATEGORY(3),
    COMMON(4),
    GATEWAY(5),
    ADMIN(6),
    SOURCING_CLUB(7)
    ;
    private int code;

    StoreType(int code){
        this.code=code;
    }

    public int getCode(){
        return this.code;
    }

    /**
     * 寻找类型
     * @param type
     * @return
     */
    public static StoreType from(int type){
        for(StoreType item:StoreType.values()){
            if(item.getCode()==type){
                return item;
            }
        }

        return null;
    }
}
