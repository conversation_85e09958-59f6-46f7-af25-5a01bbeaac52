/**
 * <a>Title: InquirySeqIdUtils </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/5/28-8:45
 */
package com.globalsources.job.utils;

import com.globalsources.product.agg.api.dto.product.OnlineProductEntityDTO;
import com.globalsources.rfx.model.RfiThreadBO;
import com.globalsources.rfx.service.IProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@RefreshScope
public class InquirySeqIdUtils {

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${rfi.upsell.hot.category}")
    private String hotCategory;

    @Value("${rfi.upsell.hot.count:100}")
    private Integer hotCategoryUpsellCount;

    @Value("${rfi.upsell.not.hot.count:200}")
    private Integer categoryUpsellCount;

    @Autowired
    private IProductService productService;

    public List<RfiThreadBO> filterDupSearchRfi(List<RfiThreadBO> searchResult, Long productId) {
        //判断是否是热门数据
        OnlineProductEntityDTO onlineProductEntityDTO = productService.getOnlineProductEntity(productId, null);
        if (ObjectUtils.isEmpty(onlineProductEntityDTO)) {
            return null;
        }
        //过滤第一条提交的productId
        List<RfiThreadBO> filterResult = searchResult.stream().filter(it-> !it.getSupplierId().equals(onlineProductEntityDTO.getOrgId())).collect(Collectors.toList());

        Integer hotCategoryNum = 0;
        if (hotCategory.contains(String.valueOf(onlineProductEntityDTO.getCategoryId()))){
            //热门category
            hotCategoryNum = hotCategoryUpsellCount;
        } else {
            //非热门category
            hotCategoryNum = categoryUpsellCount;
        }

        if (filterResult.size() > hotCategoryNum) {
            //过滤的数据大于200返回前200，或大于100返回前100
            List<RfiThreadBO> result = new ArrayList<>(hotCategoryNum);
            for (int i = 0; i < hotCategoryNum; i++) {
                result.add(filterResult.get(i));
            }
            return result;
        }
        return filterResult;
    }

}
