package com.globalsources.job.utils;

import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CountryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.InputStream;
import java.net.InetAddress;

/**
 * <AUTHOR>
 */
@Slf4j
public class GetIpUtil {

    private GetIpUtil() { throw new IllegalStateException("Utility class"); }

    private static GetIpUtil util = null;

    /**
     * As per method name
     *
     * @return Instance of class
     * Carry forward any exception from store
     */
    public static GetIpUtil getInstance() {
        if (util == null) {
            util = new GetIpUtil();
        }
        return util;
    }
    public static String getCountryCode(String ip)
    {
        String retCode = "";
        try {
            Resource classPathResource = new ClassPathResource("GeoLite2-Country.mmdb");
            InputStream inputStream = classPathResource.getInputStream();
            DatabaseReader reader = new DatabaseReader.Builder(inputStream).build();
            InetAddress ipAddress = InetAddress.getByName(ip);
            CountryResponse countryResponse = reader.country(ipAddress);
            retCode = countryResponse.getCountry().getIsoCode();
        } catch (Exception e) {
            e.getMessage();
        }
        return retCode;
    }

    public static String getCountryName(String ip)
    {
        String retCode = "";
        try {
            Resource classPathResource = new ClassPathResource("GeoLite2-Country.mmdb");
            InputStream inputStream = classPathResource.getInputStream();
            DatabaseReader reader = new DatabaseReader.Builder(inputStream).build();
            InetAddress ipAddress = InetAddress.getByName(ip);
            CountryResponse countryResponse = reader.country(ipAddress);
            retCode = countryResponse.getCountry().getName();
        } catch (Exception e) {
            e.getMessage();
        }
        return retCode;
    }
}
