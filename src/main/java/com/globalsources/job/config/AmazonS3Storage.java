package com.globalsources.job.config;

import cn.hutool.core.lang.UUID;
import com.globalsources.framework.utils.DateUtil;
import com.globalsources.job.constants.StoreType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.http.apache.ProxyConfiguration;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

import java.io.InputStream;
import java.net.URI;
import java.time.Duration;
import java.util.Date;


@Slf4j
@Component
public class AmazonS3Storage implements InitializingBean {
    @Value("${data.s3.region}")
    private String region;
    @Value("${data.s3.rfi.accessKey}")
    private String rfiAccessKey;
    @Value("${data.s3.rfi.secretKey}")
    private String rfiSecretKey;
    @Value("${data.s3.rfi.bucket}")
    private String rfiBucket;

    //单位天
    @Value("${data.s3.expireDay}")
    private Long expireDay;
    //代理设置
    @Value("${net.proxy.enabled:false}")
    private Boolean enableProxy;
    @Value("${net.proxy.host:}")
    private String proxyHost;
    @Value("${net.proxy.port:}")
    private Integer proxyPort;

    @Value("${data.s3.maxFileSize:10485760}")
    private Long maxFileSize;

    private S3Client s3Client;

    /**
     * 初始化
     */
    @Override
    public void afterPropertiesSet() {
        s3Client = initClient(rfiAccessKey, rfiSecretKey);
    }

    /**
     * 返回S3客户端
     *
     * @param accessKey
     * @param secretKey
     * @return
     */
    private S3Client initClient(String accessKey, String secretKey) {

        ApacheHttpClient.Builder httpClientBuilder = ApacheHttpClient.builder();
        if (Boolean.TRUE.equals(enableProxy)) {
            ProxyConfiguration proxyConfiguration = ProxyConfiguration.builder()
                    .endpoint(URI.create("http://" + proxyHost + ":" + proxyPort))
                    .build();
            httpClientBuilder.proxyConfiguration(proxyConfiguration);
        }


        StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKey, secretKey));
        return S3Client.builder()
                .region(Region.of(region))
                .httpClient(httpClientBuilder.build())
                .credentialsProvider(credentialsProvider)
                .build();
    }

    private String genFileKey(String suffix) {
        Date curDate = new Date();
        String filename = UUID.fastUUID() + "-" + DateUtil.date2String(curDate, DateUtil.DATETIMESTRING_YYYYMMDDHHMMSSS);
        if (!StringUtils.isEmpty(suffix)) {
            String[] arr = suffix.split("\\.");
            filename += "." + arr[arr.length - 1];
        }
        return filename;
    }

    /**
     * 上传文件
     *
     * @param type        文件类型
     * @param fileSize
     * @param inputStream 输入流
     * @return
     */
    public String upload(StoreType type, String filename, Long fileSize, InputStream inputStream) {
        String fileKey = genFileKey(filename);
        PutObjectRequest.Builder builder = PutObjectRequest.builder()
                .key(fileKey)
                .bucket(rfiBucket)
                .contentLength(fileSize);

        //开始执行文件上传
        try {
            s3Client.putObject(builder.build(), RequestBody.fromInputStream(inputStream, fileSize));
        } catch (Exception e) {
            log.error("upload file stream occur exception,type:{} fileSize:{}", type, fileSize, e);
            return null;
        }

        return fileKey;
    }

    public String getFileUrl(String fileKey, Duration duration) {

        if (duration == null) {
            duration = Duration.ofDays(expireDay);
        }

        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(duration)
                .getObjectRequest(d -> d.bucket(rfiBucket).key(fileKey))
                .build();

        StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(AwsBasicCredentials.create(rfiAccessKey, rfiSecretKey));
        S3Presigner presigner = S3Presigner.builder()
                .region(Region.of(region))
                .credentialsProvider(credentialsProvider)
                .build();

        //获取文件url地址
        try {
            PresignedGetObjectRequest resp = presigner.presignGetObject(presignRequest);
            return resp.url().toString();
        } catch (Exception e) {
            log.error("get s3 file url occur exception, type:rfi,fileKey:{},duration:{}", fileKey, duration.toString(), e);
            return null;
        }
    }

}
