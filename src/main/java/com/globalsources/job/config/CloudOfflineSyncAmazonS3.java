package com.globalsources.job.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.WebIdentityTokenFileCredentialsProvider;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.http.apache.ProxyConfiguration;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.regions.internal.util.EC2MetadataUtils;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.model.*;

import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.internet.MimeMessage;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;


@Slf4j
@Component
@RefreshScope
public class CloudOfflineSyncAmazonS3 implements InitializingBean {

    @Value("${mail.region:ap-east-1}")
    private String mailRegion;

    @Value("${data.s3.endpoint.url.pattern:http://bucket.vpce-0b729f23ca6a180af-au5y2yxu%s.s3.ap-east-1.vpce.amazonaws.com}")
    private String endpointUrlPattern;

    @Value("${mail.bucket:gsol-qa-message-email}")
    private String messageBucket;

    @Value("${mail.backup.folder:backup}")
    private String backupFolder;

    @Value("${mail.new.folder:new}")
    private String newFolderPrefix;

    private S3Client s3Client;
    //单位天
    @Value("${data.s3.expireDay:7}")
    private Long expireDay;
    //代理设置
    @Value("${net.proxy.enabled:false}")
    private Boolean enableProxy;
    @Value("${net.proxy.host:}")
    private String proxyHost;
    @Value("${net.proxy.port:}")
    private Integer proxyPort;

    @Value("${data.s3.maxFileSize:10485760}")
    private Long maxFileSize;

    @Value("${data.s3.dev.env.switch:false}")
    private Boolean devEnvSwitch;

    /**
     * 初始化
     */
    @Override
    public void afterPropertiesSet() {
        s3Client = initClient();

    }

    /**
     * 返回S3客户端
     *
     * @return
     */
    private S3Client initClient() {
        // 设置代理
        ApacheHttpClient.Builder httpClientBuilder = ApacheHttpClient.builder();
        if (Boolean.TRUE.equals(enableProxy)) {
            ProxyConfiguration proxyConfiguration = ProxyConfiguration.builder()
                    .endpoint(URI.create("http://" + proxyHost + ":" + proxyPort))
                    .build();
            httpClientBuilder.proxyConfiguration(proxyConfiguration);
        }
        // 创建凭证
        AwsCredentialsProvider awsCredentialsProvider = WebIdentityTokenFileCredentialsProvider.builder().build();
        log.info(">>>>>> getS3Client, awsCredentialsProvider={}", awsCredentialsProvider);

        // 构建S3Client
        S3ClientBuilder s3ClientBuilder = S3Client.builder()
                .region(Region.of(mailRegion))
                .httpClient(httpClientBuilder.build())
                .credentialsProvider(awsCredentialsProvider);

        // 仅dev使用，未提交其它分支
        if (Boolean.TRUE.equals(devEnvSwitch)) {
            s3ClientBuilder.endpointOverride(URI.create(endpointUrlPattern));
        } else {
            // 使用 PrivateLink
            if (StringUtils.isNotEmpty(endpointUrlPattern)) {
                String availabilityZone = EC2MetadataUtils.getAvailabilityZone();
                String endpointUrl = String.format(endpointUrlPattern, StringUtils.isBlank(availabilityZone) ? "" : "-" + availabilityZone);
                log.info(">>>>>> getS3Client, region={}, availabilityZone={}, endpointUrl={}", mailRegion, availabilityZone, endpointUrl);
                s3ClientBuilder.endpointOverride(URI.create(endpointUrl));
            }
        }

        return s3ClientBuilder.build();
    }

    public List<String> listBucketObjects() {
        List<String> list = null;
        try {
            ListObjectsRequest listObjects = ListObjectsRequest
                    .builder()
                    .prefix(newFolderPrefix)
                    .bucket(messageBucket)
                    .build();

            ListObjectsResponse res = s3Client.listObjects(listObjects);
            List<S3Object> objects = res.contents();

            if (CollectionUtils.isNotEmpty(objects)) {
                list = new ArrayList<>();
                for (S3Object myValue : objects) {
                    list.add(myValue.key());
                }
            }
        } catch (S3Exception e) {
            log.error("amazon offline sync amazon fail, message = {}", e.awsErrorDetails().errorMessage());
        }

        return list;
    }

    /**
     * 获取S3InputStream转MimeMessage
     *
     * @param fileKey
     * @return
     */
    public MimeMessage getS3InputStream(String fileKey) {
        try {
            GetObjectRequest.Builder getObjectRequestBuilder = GetObjectRequest.builder()
                    .bucket(messageBucket)
                    .key(fileKey);
            ResponseBytes<GetObjectResponse> resp = s3Client.getObject(getObjectRequestBuilder.build(), ResponseTransformer.toBytes());
            //获取S3中的流文件
            return getMimeMessageForRawEmailString(resp.asInputStream());
        } catch (Exception e) {
            log.warn("通过S3文件key无法找到文件, fileKey:{},详情:{}", fileKey, e.getMessage());
            return null;
        }
    }

    public MimeMessage getMimeMessageForRawEmailString(InputStream mailFileInputStream) throws MessagingException {
        Properties props = new Properties();
        Session session = Session.getDefaultInstance(props, null);
        return new MimeMessage(session, mailFileInputStream);

    }

    public Boolean delAmazonKey(String key) {
        DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder().bucket(messageBucket).key(key).build();
        DeleteObjectResponse deleteObjectResponse = s3Client.deleteObject(deleteObjectRequest);
        return deleteObjectResponse.sdkHttpResponse().statusCode() == 204;
    }


    /**
     * 文件拷贝
     *
     * @param fromBucket
     * @param objectKey
     * @param toBucket
     * @return
     */
    public Boolean copyBucketObject(String fromBucket, String objectKey, String toBucket, String folder) {
        String encodedUrl = "";
        try {
            encodedUrl = URLEncoder.encode(fromBucket + "/" + objectKey, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            log.error("Amazon S3 copy bucket object fail, URL could not be encoded, message = {}", e.getMessage());
        }
        String dateStr = new DateTime().toString("yyyyMMdd");
        CopyObjectRequest copyReq = CopyObjectRequest.builder()
                .copySource(encodedUrl)
                .destinationBucket(toBucket)
                .destinationKey(folder + "/" + dateStr + "/" + getRelFileKey(objectKey))
                .build();

        try {
            //拷贝 Amazon S3 key
            CopyObjectResponse copyRes = s3Client.copyObject(copyReq);
            Boolean flag = copyRes.sdkHttpResponse().statusCode() == 200;
            if (Boolean.TRUE.equals(flag)) {
                //拷贝完成删除Amazon S3 key
                DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder().bucket(fromBucket).key(objectKey).build();
                DeleteObjectResponse deleteObjectResponse = s3Client.deleteObject(deleteObjectRequest);
                return deleteObjectResponse.sdkHttpResponse().statusCode() == 200;
            }
        } catch (S3Exception e) {
            log.error("Amazon S3 copy bucket object fail, message = {}", e.awsErrorDetails().errorMessage());
        }
        return false;
    }

    private String getRelFileKey(String fileKey) {
        String[] fileKeyArr = fileKey.split("/");
        return fileKeyArr[fileKeyArr.length - 1];
    }

    /**
     * 文件拷贝
     *
     * @param fromBucket
     * @param objectKey
     * @param toBucket
     * @return
     */
    public Boolean copyBucketObject(String fromBucket, String objectKey, String toBucket) {
        return copyBucketObject(fromBucket, objectKey, toBucket, backupFolder);
    }

}
