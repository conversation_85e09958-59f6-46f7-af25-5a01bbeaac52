/**
 * <a>Title: AmazonAutoConfigure </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/7/2-13:20
 */
package com.globalsources.job.config;

import com.globalsources.job.vo.TmxConfigVO;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnClass(TmxProperties.class)
@EnableConfigurationProperties(TmxProperties.class)
@SuppressWarnings("all")
public class TmxAutoConfigure {

    @Bean("TmxConfigVO")
    @ConditionalOnMissingBean
    public TmxConfigVO amazonS3Config(TmxProperties properties) {
        TmxConfigVO tmxConfigVO = TmxConfigVO.builder()
                .orgId(properties.getOrgId())
                .apiKey(properties.getApiKey())
                .serviceType(properties.getServiceType())
                .policy(properties.getPolicy())
                .eventType(properties.getEventType())
                .sessionId(properties.getSessionId())
                .accountEmail(properties.getAccountEmail())
                .accountAddressCountry(properties.getAccountAddressCountry())
                .transactionId(properties.getTransactionId())
                .accountNumber(properties.getAccountNumber())
                .localAttrib1(properties.getLocalAttrib1())
                .localAttrib2(properties.getLocalAttrib2())
                .localAttrib3(properties.getLocalAttrib3())
                .localAttrib4(properties.getLocalAttrib4())
                .localAttrib5(properties.getLocalAttrib5())
                .applicationName(properties.getApplicationName())
                .lineOfBusiness(properties.getLineOfBusiness())
                .customerEventType(properties.getCustomerEventType())
                .localAttrib8(properties.getLocalAttrib8())
                .conditionAttrib3(properties.getConditionAttrib3())
                .inputIpAddress(properties.getInputIpAddress())
                .build();
        return tmxConfigVO;
    }
}
