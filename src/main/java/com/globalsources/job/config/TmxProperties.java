package com.globalsources.job.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <a>Title: TmxProperties </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/8/10-15:09
 */
@Data
@ConfigurationProperties("gsol.tmx")
public class TmxProperties {

    private String orgId;

    private String apiKey;

    private String serviceType;

    private String policy;

    private String eventType;

    private String sessionId;

    private String accountEmail;

    private String accountAddressCountry;

    private String transactionId;

    private String accountNumber;

    private String localAttrib1;

    private String localAttrib2;

    private String localAttrib3;

    private String localAttrib4;

    private String applicationName;

    private String lineOfBusiness;

    private String customerEventType;

    private String localAttrib8;

    private String conditionAttrib3;

    private String localAttrib5;

    private String inputIpAddress;
}
