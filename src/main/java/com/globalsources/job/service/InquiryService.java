package com.globalsources.job.service;

import com.globalsources.job.bean.RfiDetailDataBean;
import com.globalsources.rfi.agg.core.dto.mc.RequestRfiBuildDTO;
import com.globalsources.rfi.agg.core.vo.InquireAllVO;

public interface InquiryService {

    /**
     * 获取message邮箱
     * @param userId
     * @param supplierId
     * @return
     */
    String getMessageEmail(Long userId, Long supplierId, String firstName, String lastName, String emailAddress);

    /**
     *
     * @param inquireAllVO
     * @return
     */
    RequestRfiBuildDTO inquiryDistRuleData(InquireAllVO inquireAllVO);

    /**
     * 获取rfi详细信息
     * @param threadId
     * @return
     */
    RfiDetailDataBean getRfiDetail(String threadId);

    /**
     * 发送邮件方法
     * @param threadId
     * @param flag upsell 标识，true-upsell，false-one to one
     */
    void sendRfiNotice(String threadId, Boolean flag);

    /**
     * Rfi 分配结果
     * @param threadId
     * @return
     */
    String getRfiDistData(String threadId, Boolean upsellFlag, Boolean checkDoiFlag);

    void markPotentialOpportunity(String threadId);

}
