package com.globalsources.job.handler;

import com.alibaba.fastjson.JSON;
import com.globalsources.eblock.agg.api.model.vo.audit.rfi.InquiryResponseVO;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.rfi.agg.core.dto.record.InquiryThirdRecordDTO;
import com.globalsources.rfi.agg.core.vo.record.InquiryThirdRecordVO;
import com.globalsources.rfi.agg.dto.inquiry.InquiryUpdateEblockReasonDTO;
import com.globalsources.rfi.agg.enums.ReviewTypeEnum;
import com.globalsources.rfi.agg.feign.InquiryFeign;
import com.globalsources.rfi.agg.feign.InquiryThirdRecordFeign;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 从新Eblock获取审核结果
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RfiEBlockConsumer {

    private final InquiryThirdRecordFeign inquiryThirdRecordFeign;
    private final InquiryFeign inquiryFeign;

    private static final String INQUIRY_EBLOCK_RESPONSE_QUEUE = "GSOL.DATA.CLIENT.RFI_EBLOCK_RESPONSE_QUEUE";

    @RabbitListener(queues = INQUIRY_EBLOCK_RESPONSE_QUEUE)
    @RabbitHandler
    public void eblockResponseProcess(String data, @Header(AmqpHeaders.CHANNEL) Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) {
        try{
            InquiryResponseVO inquiryResponseVO = JSON.parseObject(data,InquiryResponseVO.class);

            log.info("eblockResponseProcess data = {}", inquiryResponseVO);

            saveEblockResult(inquiryResponseVO);
            log.info("eblockResponseProcess saveEblockResult succ ,inquiryResponseVO : {}", inquiryResponseVO);
            try {
                Integer updateCount = ResultUtil.getData(inquiryFeign.updateEblockAuditReason(InquiryUpdateEblockReasonDTO.builder()
                        .auditReason(inquiryResponseVO.getAuditReason())
                        .exportToScFlag(inquiryResponseVO.isExportToScFlag())
                        .inquiryId(inquiryResponseVO.getInquiryId())
                        .build()));
            log.info("eblockResponseProcess updateEblockAuditReason succ ,inquiryResponseVO : {} updateCount :{}", inquiryResponseVO,updateCount);
            }catch (Exception e){
                log.error("eblockResponseProcess updateEblockAuditReason error, inquiryResponseVO : {} exception = {}", inquiryResponseVO, JSON.toJSONString(e));
            }
            channel.basicAck(deliveryTag, true);

        }catch (Exception e) {
            log.error("eblockResponseProcess fail response :{} exception = {}", data, JSON.toJSONString(e));
        }
    }

    private void saveEblockResult(InquiryResponseVO inquiryResponseVO) {
        log.info("eblockResponseProcess save eblock result:{}", inquiryResponseVO);
        InquiryThirdRecordVO reqLog = ResultUtil.getData(inquiryThirdRecordFeign.getRfiRequest(inquiryResponseVO.getInquiryId(), ReviewTypeEnum.EBLOCK.getKey()));
        if (Objects.isNull(reqLog)) {
            log.error("eblockResponseProcess inquiryID :{} :cant find request log", inquiryResponseVO.getInquiryId());
            throw new BusinessException(ResultCode.CommonResultCode.DATA_NON_EXISTENT);
        }
        InquiryThirdRecordDTO param = OrikaMapperUtil.coverObject(reqLog, InquiryThirdRecordDTO.class);
        param.setBusinessCode(inquiryResponseVO.getReviewResult().toLowerCase());
        param.setRespData(JSON.toJSONString(inquiryResponseVO));
        inquiryThirdRecordFeign.save(param);
        log.info("eblockResponseProcess inquiryID :{} save eblock result succ", inquiryResponseVO.getInquiryId());
    }
}
