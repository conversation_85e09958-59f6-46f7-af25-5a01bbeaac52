package com.globalsources.job.handler;

import com.globalsources.job.enums.ResultEnum;
import com.globalsources.job.service.CommonService;
import com.globalsources.job.service.InquiryService;
import com.globalsources.job.utils.RfiThreadUtils;
import com.globalsources.rfi.agg.core.vo.InquireAllVO;
import com.globalsources.rfi.agg.core.vo.record.RfiDoiResultVO;
import com.globalsources.rfi.agg.feign.InquiryFeign;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <a>Title: RfiDOIJobHandler </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2022/3/9-18:23
 */
@Slf4j
@Component
public class RfiDoiJobHandler {

    @Autowired
    private InquiryFeign inquiryFeign;

    @Autowired
    private InquiryService inquiryService;

    @Autowired
    private RfiThreadUtils rfiThreadUtils;

    @Autowired
    private CommonService commonService;

    @XxlJob(value = "rfiDoiJobHandler")
    public void rfiDoiJobHandler() {
        List<RfiDoiResultVO> doiResultVOList = inquiryFeign.getRfiDoiList().getData();
        for (RfiDoiResultVO resultVO: doiResultVOList) {
            InquireAllVO inquireAllVO = inquiryFeign.inquiryDetail(resultVO.getInquiryId()).getData();
            if (ObjectUtils.isEmpty(inquireAllVO)) {
                log.error("invoke inquiryFeign.inquiryDetail error,inquiryId = {}", inquireAllVO.getInquiryId());
                continue;
            }

            //分配创建buyer_status, supplier_status 的关系
            String result = inquiryService.getRfiDistData(inquireAllVO.getThreadId(), false, true);
            log.info("doi rfi invoke inquiryService.getRfiDistData, inquiryId = {}, threadId = {}", inquireAllVO.getInquiryId(), inquireAllVO.getThreadId());

            //分配成功后，发送邮件通知，神策统计，积分统计，同步用户信息
            if (ResultEnum.SUCCESS.getValue().equals(result)) {
                log.info("invoke inquiryService.getRfiDistData error,threadId = {},inquiryId = {}", inquireAllVO.getThreadId(), inquireAllVO.getInquiryId());

                try {
                    //发送邮件通知
                    inquiryService.sendRfiNotice(inquireAllVO.getThreadId(), false);
                    //神策接口
                    commonService.sensorData(inquireAllVO);
                    //积分
                    commonService.sourcingClubPoint(inquireAllVO.getBuyerId(), resultVO.getInquiryId());
                    //活动积分
                    commonService.sourcingClubActivityPoint(inquireAllVO.getBuyerId(), resultVO.getInquiryId(),new Date(inquireAllVO.getCreateDate().getTime()));
                    //同步用户信息 cm
                    commonService.syncPscCm(inquireAllVO.getBuyerId(),inquireAllVO.getInquireAllItemList().getThreadId());
                }catch (Exception exception) {
                    log.error("rfi doi dist success, send mail or sensor error, inquiryId = {}, threadId = {}, exception = {}", resultVO.getInquiryId(), inquireAllVO.getThreadId(), exception.getMessage());
                }
            }
        }
    }
}
