package com.globalsources.job.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.globalsources.eblock.agg.api.model.dto.inquiry.InquiryRequestDTO;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserBaseProfileVO;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.job.bean.RfiTMXResultBean;
import com.globalsources.job.config.TMXConfig;
import com.globalsources.job.enums.ResultEnum;
import com.globalsources.job.service.CommonService;
import com.globalsources.job.service.EBlockService;
import com.globalsources.job.service.InquiryService;
import com.globalsources.rfi.agg.constants.RfiSourceEnum;
import com.globalsources.rfi.agg.core.dto.record.InquiryThirdRecordDTO;
import com.globalsources.rfi.agg.core.vo.InquireAllVO;
import com.globalsources.rfi.agg.core.vo.record.InquiryThirdRecordVO;
import com.globalsources.rfi.agg.core.vo.record.RfiTMXResultVO;
import com.globalsources.rfi.agg.dto.inquiry.InquiryStatusDTO;
import com.globalsources.rfi.agg.enums.InquirySentFromEnum;
import com.globalsources.rfi.agg.enums.ReviewResultEnum;
import com.globalsources.rfi.agg.enums.ReviewTypeEnum;
import com.globalsources.rfi.agg.feign.InquiryAggFeign;
import com.globalsources.rfi.agg.feign.InquiryFeign;
import com.globalsources.rfi.agg.feign.InquiryThirdRecordFeign;
import com.globalsources.rfx.service.IUserService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLDecoder;
import java.util.Date;
import java.util.List;

/**
 * <a>Title: RfiTMXJobHandler </a>
 * <a>Author: Levlin Li <a>
 * <a>Description：<a>
 *
 * <AUTHOR> Li
 * @date 2021/11/11-16:38
 */
@Slf4j
@Component
public class RfiTMXJobHandler {

    @Autowired
    private TMXConfig tmxConfig;

    @Autowired
    private InquiryFeign inquiryFeign;

    @Autowired
    private EBlockService eBlockService;

    @Autowired
    private InquiryService inquiryService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private InquiryThirdRecordFeign inquiryThirdRecordFeign;

    @Autowired
    private InquiryAggFeign inquiryAggFeign;

    @Autowired
    private IUserService userService;

    @XxlJob(value = "rfiTMXJobHandler")
    public void rfiTMXJobHandler() {
        log.info("rfiTMXJobHandler begin");
        List<RfiTMXResultVO> pendingTmxRfiList = ResultUtil.getData(inquiryFeign.rfiStatusList(ReviewResultEnum.PENDING.getValue()));
        if (CollectionUtils.isEmpty(pendingTmxRfiList)) {
            log.warn("rfiTMXJobHandler pending tmx list empty");
            return;
        }
        log.info("rfiTMXJobHandler pending tmx list size : {}", pendingTmxRfiList.size());
        for (RfiTMXResultVO pendingTmxVO : pendingTmxRfiList) {
            try {
                log.info("rfiTMXJobHandler inquiryId:{} threadId:{} get tmx url, request data = {}", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId(), pendingTmxVO);
                //admin发出来的不过滤
                if (RfiSourceEnum.isAdminSource(pendingTmxVO.getRfiSource())) {
                    tmxPass(pendingTmxVO);
                    continue;
                }
                //获取用户信息
                UserVO userVO = userService.getUserInfo(Long.valueOf(pendingTmxVO.getAccountNumber()));
                UserBaseProfileVO userProfile = userService.getUserBaseProfile(Long.valueOf(pendingTmxVO.getAccountNumber()));
                if (ObjectUtils.isEmpty(userVO) || ObjectUtils.isEmpty(userProfile)) {
                    log.warn("rfiTMXJobHandler inquiryId:{} threadId:{} get user info or user profile is null, request data = {}", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId(), pendingTmxVO);
                    continue;
                }

                RfiTMXResultBean rfiTMXResultBean = RfiTMXResultBean.builder()
                        .sessionId(pendingTmxVO.getSessionId())
                        .inquiryId(pendingTmxVO.getInquiryId())
                        .accountNumber(pendingTmxVO.getAccountNumber())
                        .inputIpAddress(pendingTmxVO.getInputIpAddress())
                        .clientSessionId(pendingTmxVO.getClientSessionId())
                        .accountEmail(userVO.getEmail())
                        .buyerFirstName(userVO.getFirstName())
                        .buyerLastName(userVO.getLastName())
                        .companyName(userProfile.getCompanyInfo().getCompanyName())
                        .doiStatus(pendingTmxVO.getDoiStatus())
                        .doiSource(pendingTmxVO.getDoiSource())
                        .regFlag(pendingTmxVO.getRegFlag())
                        .rfiSource(pendingTmxVO.getRfiSource())
                        .paSaFlag(InquirySentFromEnum.PA_SA_EMAIL.getKey().equals(pendingTmxVO.getSentFrom()))
                        .telCountry(userProfile.getContactInfo().getTelCountryCode())
                        .telArea(userProfile.getContactInfo().getTelAreaCode()).telExt("")
                        .build();

                //查询数据库sql tmx结果
                InquiryThirdRecordVO inquiryThirdRecordVO = ResultUtil.getData(inquiryThirdRecordFeign.getRfiRequest(pendingTmxVO.getInquiryId(), ReviewTypeEnum.TMX.getKey()));
                log.info("rfiTMXJobHandler inquiryId:{} threadId:{} tmx DB result exist = {}", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId(), ObjectUtils.isNotEmpty(inquiryThirdRecordVO));

                //数据库存在数据
                if (ObjectUtils.isEmpty(inquiryThirdRecordVO)) {
                    log.info("rfiTMXJobHandler inquiryId:{} threadId:{} send tmx request, request data = {}", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId(), rfiTMXResultBean);

                    //tmx 接口请求参数
                    InquiryThirdRecordDTO inquiryThirdRecordDTO = tmxConfig.resultTMXV2(rfiTMXResultBean);

                    log.info("rfiTMXJobHandler inquiryId:{} threadId:{} send tmx request result = {}", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId(), inquiryThirdRecordDTO);

                    if (ObjectUtils.isEmpty(inquiryThirdRecordDTO)) {
                        log.warn("rfiTMXJobHandler inquiryId:{} threadId:{} send tmx request result empty,request data = {}", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId(), rfiTMXResultBean);
                        continue;
                    }
                    inquiryThirdRecordVO = new InquiryThirdRecordVO();
                    BeanUtils.copyProperties(inquiryThirdRecordDTO, inquiryThirdRecordVO);
                    //保存请求结果到数据库
                    inquiryThirdRecordFeign.save(inquiryThirdRecordDTO);
                }

                String responseData = inquiryThirdRecordVO.getRespData();
                JSONObject jsonObject = (JSONObject) JSON.parse(responseData);
                String policyScore = String.valueOf(jsonObject.get("policy_score"));
                policyScore = URLDecoder.decode(policyScore, "UTF-8");
                String requestResult = String.valueOf(jsonObject.get("request_result"));
                String reviewStatus = String.valueOf(jsonObject.get("review_status"));
                String riskRating = String.valueOf(jsonObject.get("risk_rating"));

                if (ResultEnum.SUCCESS.getValue().equals(requestResult)
                        && ReviewResultEnum.PASS.getValue().equals(reviewStatus)) {
                    log.info("rfiTMXJobHandler inquiryId:{} threadId:{} tmx pass", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId());
                    eblockCheck(inquiryThirdRecordVO.getInquiryId(), reviewStatus, policyScore, riskRating);
                    tmxPass(pendingTmxVO);
                } else if (ReviewResultEnum.REVIEW.getValue().equals(reviewStatus)) {
                    //EBlock调用
                    log.info("rfiTMXJobHandler inquiryId:{} threadId:{} tmx review or reject", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId());
                    eblockCheckAndUpdateStatus(inquiryThirdRecordVO, reviewStatus, policyScore, requestResult, riskRating, pendingTmxVO.getThreadId(), userVO.getUserId());
                } else if (ReviewResultEnum.REJECT.getValue().equals(reviewStatus)) {
                    // tmx reject,eblock reject
                    log.warn("rfiTMXJobHandler inquiryId:{} threadId:{} tmx reject", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId());
                    eblockCheck(inquiryThirdRecordVO.getInquiryId(), reviewStatus, policyScore, riskRating);
                    updateStatus(inquiryThirdRecordVO.getInquiryId(), pendingTmxVO.getThreadId(), ReviewResultEnum.REJECT.getValue(), ReviewResultEnum.REJECT.getValue(), userVO.getUserId());
                } else if ((StringUtils.isEmpty(reviewStatus) || "null".equals(reviewStatus)) && (StringUtils.isEmpty(policyScore) || "null".equals(policyScore))) {
                    // 异常返回结果  直接review
                    log.warn("rfiTMXJobHandler inquiryId:{} threadId:{} tmx unknown result:{}", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId(), reviewStatus);
                    eblockCheckAndUpdateStatus(inquiryThirdRecordVO, ReviewResultEnum.REVIEW.getValue(), "0", "success", "neutral", pendingTmxVO.getThreadId(), userVO.getUserId());
                }
                log.info("rfiTMXJobHandler inquiryId:{} threadId:{} all done", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId());

            } catch (Exception e) {
                log.error("rfiTMXJobHandler inquiryId:{} threadId:{} error,exception:{}", pendingTmxVO.getInquiryId(), pendingTmxVO.getThreadId(), JSON.toJSONString(e));
            }
        }
    }

    private void tmxPass(RfiTMXResultVO resultVO) {
        InquireAllVO inquireAllVO = ResultUtil.getData(inquiryFeign.inquiryDetail(resultVO.getInquiryId()));

        //分配结果
        String resultMsg = inquiryService.getRfiDistData(resultVO.getThreadId(), false, true);
        log.info("rfiTMXJobHandler inquiryId:{} threadId:{} tmx pass, dist result:{}", resultVO.getInquiryId(), resultVO.getThreadId(), resultMsg);

        if (StringUtils.isEmpty(resultMsg) || ResultEnum.FAIL.getValue().equals(resultMsg)) {
            log.warn("rfiTMXJobHandler inquiryId:{} threadId:{} tmx pass, dist fail", resultVO.getInquiryId(), resultVO.getThreadId());
            return;
        }
        updateTmxStatus(inquireAllVO);

        //分配成功调用
        if (ResultEnum.SUCCESS.getValue().equals(resultMsg)) {
            //发送邮件
            tmxSuccess(inquireAllVO);
        }
    }

    public void tmxSuccess(InquireAllVO inquireAllVO) {
        String threadId = inquireAllVO.getThreadId();
        String inquiryId = inquireAllVO.getInquiryId();
        //发送邮件通知
        inquiryService.sendRfiNotice(threadId, false);
        log.info("rfiTMXJobHandler inquiryId:{} threadId:{} sendRfiNotice succ", inquiryId, threadId);

        //神策接口
        commonService.sensorData(inquireAllVO);
        log.info("rfiTMXJobHandler inquiryId:{} threadId:{} sensorData succ", inquiryId, threadId);

        //积分
        commonService.sourcingClubPoint(inquireAllVO.getBuyerId(), inquiryId);
        log.info("rfiTMXJobHandler inquiryId:{} threadId:{} sourcingClubPoint succ", inquiryId, threadId);

        //活动积分
        commonService.sourcingClubActivityPoint(inquireAllVO.getBuyerId(), inquiryId,new Date(inquireAllVO.getCreateDate().getTime()));
        log.info("rfiTMXJobHandler inquiryId:{} threadId:{} sourcingClubActivityPoint succ", inquiryId,threadId);

        //同步用户信息 cm
        commonService.syncPscCm(inquireAllVO.getBuyerId(), inquireAllVO.getInquireAllItemList().getThreadId());
        log.info("rfiTMXJobHandler inquiryId:{} threadId:{} syncPscCm succ", inquiryId, threadId);

        log.info("rfiTMXJobHandler inquiryId:{} threadId:{} tmx succ", inquiryId, threadId);

    }

    private void updateTmxStatus(InquireAllVO inquireAllVO) {
        //修改状态 2022/11/02 从core移到agg
        //部分字段为注解记录数据时使用
        InquiryStatusDTO statusDTO = InquiryStatusDTO.builder()
                .inquiryId(inquireAllVO.getInquiryId())
                .threadId(inquireAllVO.getThreadId())
                .reviewType(ReviewTypeEnum.TMX.getKey())
                .status(ReviewResultEnum.PASS.getValue())
                .userId(inquireAllVO.getBuyerId())
                .upsellFlag(inquireAllVO.getRecommendFlag())
                .build();
        inquiryAggFeign.updateReviewStatus(statusDTO);
        log.info("rfiTMXJobHandler inquiryId:{} threadId:{} updateReviewStatus succ", inquireAllVO.getInquiryId(), inquireAllVO.getThreadId());
    }

    public String eblockCheck(String inquiryId, String tmxReviewStatus, String policyScore, String riskRating) {

        InquiryRequestDTO requestDTO = eBlockService.eblockCheckNew(inquiryId, tmxReviewStatus, policyScore, riskRating);

        String result = saveEblockResult(inquiryId, requestDTO);
        log.info("rfiTMXJobHandler inquiryId:{} save eblockResult :{}", inquiryId, result);
        return result;

    }
    private String saveEblockResult(String inquiryId, InquiryRequestDTO requestDTO) {
        InquiryThirdRecordDTO eblockResult = new InquiryThirdRecordDTO();
        InquiryThirdRecordVO inquiryThirdRecordVO = inquiryThirdRecordFeign.getRfiRequest(inquiryId, ReviewTypeEnum.EBLOCK.getKey()).getData();

        eblockResult.setAssessSystem("eblock");
        eblockResult.setInquiryId(inquiryId);
        eblockResult.setReqData(JSON.toJSONString(requestDTO));
        eblockResult.setRespData("success");
        eblockResult.setResultMsg("success");
        eblockResult.setResultCode("success");
        //eblock已生成xml
        eblockResult.setBusinessCode(ReviewResultEnum.PENDING.getValue());
        if (ObjectUtils.isEmpty(inquiryThirdRecordVO)) {
            return inquiryThirdRecordFeign.save(eblockResult).getMsg();
        } else {
            return inquiryThirdRecordVO.getResultMsg();
        }
    }
    public void eblockCheckAndUpdateStatus(InquiryThirdRecordVO inquiryThirdRecordDTO, String tmxReviewStatus, String policyScore, String tmxReviewResult, String riskRating, String threadId, Long userId) {
        log.info("rfiTMXJobHandler inquiryId:{} eblock check", inquiryThirdRecordDTO.getInquiryId());
        String result = eblockCheck(inquiryThirdRecordDTO.getInquiryId(),tmxReviewStatus,policyScore,riskRating);
        //成功则修改rfi 主状态
        if (ResultEnum.SUCCESS.getValue().equalsIgnoreCase(result)) {
            //tmx review, EBlock pending
            updateStatus(inquiryThirdRecordDTO.getInquiryId(), threadId, tmxReviewStatus, ReviewResultEnum.PENDING.getValue(), userId);
        }
    }

    private void updateStatus(String inquiryId, String threadId, String tmxStatus, String eblockStatus, Long userId) {
        //tmx review, EBlock pending
        InquiryStatusDTO tmxStatusDTO = InquiryStatusDTO.builder()
                .inquiryId(inquiryId)
                .threadId(threadId)
                .reviewType(ReviewTypeEnum.TMX.getKey())
                .status(tmxStatus)
                .userId(userId)
                .build();
        inquiryAggFeign.updateReviewStatus(tmxStatusDTO);
        log.info("rfiTMXJobHandler inquiryId:{} update tmx status succ", inquiryId);

        InquiryStatusDTO eblockStatusDTO = InquiryStatusDTO.builder()
                .inquiryId(inquiryId)
                .threadId(threadId)
                .reviewType(ReviewTypeEnum.EBLOCK.getKey())
                .status(eblockStatus)
                .userId(userId)
                .build();
        inquiryAggFeign.updateReviewStatus(eblockStatusDTO);
        log.info("rfiTMXJobHandler inquiryId:{} update eblock status succ", inquiryId);
    }
}
