/**
 * <a>Title: EBlockInquiryModel </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/5/10-13:59
 */
package com.globalsources.job.bean;

import lombok.*;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class EBlockInquiryModel implements Serializable {

    /**地址*/
    private String address;

    /**附件*/
    private String attachment;

    /***/
    private String businessList;

    /**买家IP*/
    private String buyerIp;

    /**买家IP国家*/
    private String buyerIpCountry;

    /**抄送电子邮箱*/
    private String ccEmail;

    /**城市*/
    private String city;

    /**公司说明*/
    private String companyDescription;

    /**公司名称*/
    private String companyName;

    /**公司成立时间*/
    private String companyYearestablished;

    /**国家*/
    private String country;

    private String courier;

    private String courierAccount;
    /**显示图片*/
    private String displayImage;
    /**预期订单数量*/
    private String expectedOrderQty;
    /**传真号*/
    private String fxNumber;
    /**接收信息*/
    private String infoRec;
    /**接收时间*/
    private String inqDate;
    /**询盘编号*/
    private String inqId;
    private String inqPath;
    /**询盘类型*/
    private String inqType;
    /**category_id*/
    private String inqProdId;
    /**职称*/
    private String jobTitle;
    /**语言*/
    private String language;
    /**姓名*/
    private String firstName;
    private String lastName;
    /**message_to_supplier*/
    private String message;
    /**消息类型*/
    private String msgType;
    /**支付样品*/
    private String paySample;
    /**支付运费*/
    private String payShipment;
    /**电话号码*/
    private String phNumber;
    /**zip_code*/
    private String postCode;
    /**商品数量*/
    private String prodCount;
    /**注册时间*/
    private String regDate;
    /**请求回复时间*/
    private String responseDateline;
    private String sampleQty;
    /**发件人电子邮箱*/
    private String senderEmail;
    private String state;
    /**电子邮件主题*/
    private String subject;
    /**supplier_cnt*/
    private String suppCount;
    /**source_system*/
    private String systemName;
    /**标题*/
    private String title;
    /**tmx 数据*/
    private String tmErrorDetail;
    private String tmPolicyScoreNum;
    private String tmRequestResult;
    private String tmReviewStatus;
    private String tmRiskRating;
    private String tmUnknownSession;
    /**发送的电子邮箱*/
    private String toEmail;
    /**公司人数*/
    private String totalStaff;
    /**买家编号*/
    private Long userId;
    /**网址*/
    private String website;

    private List<RFxProductBean> rfxProductList;

    private List<RFxSupplierBean> rfxSupplierList;

    private String tmxSmartId;

    private String tmxExactId;

    private Boolean tmxAccountBlacklistFlag;
    private Boolean tmxDeviceBlacklistFlag;

}
