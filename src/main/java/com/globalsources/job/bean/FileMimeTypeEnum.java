/**
 * <a>Title: FileMimeTypeEnum </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/6/15-15:57
 */
package com.globalsources.job.bean;

public enum FileMimeTypeEnum {

    ATTACHMENT_IMAGE_TYPE(1, "png,gif,jpg,jpeg,psd,tiff,tif,bmp"),
    ATTACHMENT_FILE_TYPE(2, "xls,xlsx,doc,docx,ppt,pptx"),
    ATTACHMENT_OTHER_TYPE(3, "dst,dwf,dwg,dws,dxf,slb,sld,pdf"),
    ATTACHMENT_ZIP_TYPE(4, "zip,rar");

    private Integer key;

    private String value;

    FileMimeTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static Integer matchSwitch(String suffix) {
        FileMimeTypeEnum[] values = FileMimeTypeEnum.values();
        for (FileMimeTypeEnum emailEnum : values) {
            if (emailEnum.value.contains(suffix)) {
                return emailEnum.getKey();
            }
        }
        return null;
    }
}
