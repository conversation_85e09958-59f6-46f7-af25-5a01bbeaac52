package com.globalsources.eblock.agg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 规则命中记录
 * </p>
 *
 * <AUTHOR> <PERSON>
 * @since 2023-12-11
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("audit_rule_result_record")
@ApiModel(value="AuditRuleResultRecordPO", description="规则结果记录")
public class AuditRuleResultRecordPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "result_record_id", type = IdType.AUTO)
    private Integer resultRecordId;

    @ApiModelProperty(value = "流程id")
    private String ruleId;

    @ApiModelProperty(value = "执行结果")
    private String executeResult;

    @ApiModelProperty(value = "业务ID")
    private String requestId;

    @ApiModelProperty(value = "草稿标记")
    private Boolean draftFlag;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;
}
