package com.globalsources.eblock.agg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 规则历史记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("audit_rule_history")
@ApiModel(value = "AuditRuleHistoryPO对象", description = "规则历史记录")
public class AuditRuleHistoryPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "rule_history_id", type = IdType.AUTO)
    private Long ruleHistoryId;

    @ApiModelProperty(value = "流程id")
    private String ruleId;

    @ApiModelProperty(value = "流程EL数据")
    private String elData;

    @ApiModelProperty(value = "脚本id列表")
    private String scriptIdList;

    @ApiModelProperty(value = "修改人")
    private Long editBy;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;
}
