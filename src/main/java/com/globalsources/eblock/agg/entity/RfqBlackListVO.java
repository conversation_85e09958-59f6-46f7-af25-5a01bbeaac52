package com.globalsources.eblock.agg.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
@ApiModel(value="rfq 黑名单", description="rfq 黑名单")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RfqBlackListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private Long buyerId;

    @ApiModelProperty(value = "用户邮箱")
    private String buyerEmailAddr;

    @ApiModelProperty(value = "审核人邮箱")
    private String reviewerEmailAddr;

    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    @ApiModelProperty(value = "添加来源")
    private String addSource;

    @ApiModelProperty(value = "添加原因")
    private String addReason;

}