package com.globalsources.eblock.agg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-12-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "audit_script")
public class AuditScriptPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    /**
     * script_id
     */
    private String scriptId;

    /**
     * script_data
     */
    private String scriptData;

    /**
     * script_type
     */
    private String scriptType;

    /**
     * application_name
     */
    private String applicationName;

    /**
     * rule_id
     */
    private String ruleId;

    /**
     * delete_flag
     */
    private Boolean deleteFlag;

    /**
     * draft_status
     */
    private String draftStatus;

    /**
     * create_date
     */
    private Date createDate;

    /**
     * l_upd_date
     */
    private Date lUpdDate;

    /**
     * edit_by
     */
    private Long editBy;

    /**
     * create_by
     */
    private Long createBy;

}