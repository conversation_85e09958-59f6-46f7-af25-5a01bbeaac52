package com.globalsources.eblock.agg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "audit_info")
@ApiModel(value = "AuditInfo对象", description = "审核信息")
public class AuditInfoPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键id")
    @TableId(value = "audit_info_id", type = IdType.AUTO)
    private Integer auditInfoId;

    @ApiModelProperty(value = "RFI/RFQ ID")
    private String requestId;

    @ApiModelProperty(value = "业务类型 (RFI/RFQ)")
    private String requestType;

    @ApiModelProperty(value = "来源于GSOL、Mobile还是App端")
    private String requestSource;

    @ApiModelProperty(value = "RFI/RFQ创建时间")
    private Date sendDate;

    @ApiModelProperty(value = "买家提交时的IP")
    private String buyerSendIp;

    @ApiModelProperty(value = "询盘类型(PRODUCT, SUPPLIER, CATEGORY)")
    private String inquiryType;

    @ApiModelProperty(value = "RFI: inquiry path，RFQ: tracking_path")
    private String trackingPath;

    @ApiModelProperty(value = "标题")
    private String subject;

    @ApiModelProperty(value = "内容")
    private String message;

    @ApiModelProperty(value = "附件标记")
    private Boolean hasAttachmentFlag;

    @ApiModelProperty(value = "类别ID")
    private Long categoryId;

    @ApiModelProperty(value = "产品ID")
    private Long productId;

    @ApiModelProperty(value = "询盘发送供应商的ID列表")
    private String supplierIdList;

    @ApiModelProperty(value = "询盘发送供应商的数量")
    private Integer supplierCnt;

    @ApiModelProperty(value = "买家ID")
    private Long buyerId;

    @ApiModelProperty(value = "买家邮箱")
    private String buyerEmailAddr;

    @ApiModelProperty(value = "买家first name")
    private String buyerFirstName;

    @ApiModelProperty(value = "买家last name")
    private String buyerLastName;

    @ApiModelProperty(value = "买家公司名")
    private String buyerCompanyName;

    @ApiModelProperty(value = "买家公司URL")
    private String buyerCompanyWebsite;

    @ApiModelProperty(value = "买家国家码")
    private String buyerCountryCode;

    @ApiModelProperty(value = "买家国家")
    private String buyerCountryName;

    @ApiModelProperty(value = "系统审核状态(Pending, Release, Block, Reject)")
    private String systemAuditStatus;

    @ApiModelProperty(value = "系统审核时间")
    private Date systemAuditDate;

    @ApiModelProperty(value = "系统审核匹配的规则")
    private String systemAuditRules;

    @ApiModelProperty(value = "审核状态(Pending, Release, Block, Reject)")
    private String manualAuditStatus;

    @ApiModelProperty(value = "人工审核时间")
    private Date manualAuditDate;

    @ApiModelProperty(value = "审核人ID")
    private Long manualAuditBy;

    @ApiModelProperty(value = "审核人email")
    private String manualAuditEmailAddr;

    @ApiModelProperty(value = "人工审核原因ID")
    private Integer manualAuditReasonId;

    @ApiModelProperty(value = "人工审核原因")
    private String manualAuditReason;

    @ApiModelProperty(value = "resend 时间")
    private Date resendDate;

    @ApiModelProperty(value = "resend 操作人")
    private Long resendBy;

    @ApiModelProperty(value = "最终的审核状态")
    private String finalAuditStatus;

    @ApiModelProperty(value = "系统审核时间")
    private Date finalAuditDate;

    @ApiModelProperty(value = "tmx审核状态")
    private String tmxReviewStatus;

    @ApiModelProperty(value = "tmx分数")
    private Integer tmxPolicyScore;

    @ApiModelProperty(value = "tmx风险等级（包含Trusted/neutral/low)")
    private String tmxRiskRating;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "最后更新时间")
    private Date lUpdDate;

    @ApiModelProperty(value = "删除标记")
    private Boolean deleteFlag;

    @ApiModelProperty(value = "询盘期望订单量")
    private Integer expectedOrderQty;


    @ApiModelProperty(value = "buyer称呼")
    private String buyerTitle;
    @ApiModelProperty(value = "buyer 职位名")
    private String buyerJobTitle;
    @ApiModelProperty(value = "buyer省份")
    private String buyerProvince;
    @ApiModelProperty(value = "buyer城市")
    private String buyerCity;
    @ApiModelProperty(value = "buyer邮编")
    private String buyerZipCode;
    @ApiModelProperty(value = "buyer电话国家码")
    private String buyerTelCountryCode;
    @ApiModelProperty(value = "buyer电话区码")
    private String buyerTelArea;
    @ApiModelProperty(value = "'buyer电话号码")
    private String buyerTelNum;
    @ApiModelProperty(value = "buyer电话分机号")
    private String buyerTelExt;
    @ApiModelProperty(value = "buyer注册时间")
    private Date buyerRegisterDate;
    @ApiModelProperty(value = "buyer当前ip国家码")
    private String buyerIpCountryCode;
    @ApiModelProperty(value = "buyer business type")
    private String buyerBusinessType;
    @ApiModelProperty(value = "buyer linkedIn地址")
    private String buyerLinkedlnUrl;
    @ApiModelProperty(value = "分类名称")
    private String categoryName;
    @ApiModelProperty(value = "产品名")
    private String productName;
    @ApiModelProperty(value = "productModelNumber")
    private String modelNumber;
    @ApiModelProperty(value = "buyer job function")
    private String buyerJobFunction;
    @ApiModelProperty(value = "buyer wechat id")
    private String buyerWechatId;
    @ApiModelProperty(value = "公司id")
    private Long supplierId;
    @ApiModelProperty(value = "公司名")
    private String supplierName;

    @ApiModelProperty(value = "询盘命中的具体敏感词")
    private String sensitiveKeyword;

    @ApiModelProperty(value = "导入Supplier Center标识")
    private Boolean exportToScFlag;

    private String tmxSmartId;

    private String tmxExactId;

    private Boolean tmxAccountBlacklistFlag;
    private Boolean tmxDeviceBlacklistFlag;

}
