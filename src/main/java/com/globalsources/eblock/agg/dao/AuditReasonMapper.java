package com.globalsources.eblock.agg.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.eblock.agg.api.model.dto.audit.ReviewSettingSaveDTO;
import com.globalsources.eblock.agg.entity.AuditReasonPO;
import org.apache.ibatis.annotations.Param;

/**
 *
 * 审核原因 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public interface AuditReasonMapper extends BaseMapper<AuditReasonPO> {

    int checkReviewReason(@Param(value = "dto") ReviewSettingSaveDTO dto);
}
