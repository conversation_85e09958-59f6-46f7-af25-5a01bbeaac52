package com.globalsources.eblock.agg.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.eblock.agg.api.model.dto.auditrule.AuditRuleQueryDTO;
import com.globalsources.eblock.agg.api.model.dto.auditrule.UpdateTriggerCountDTO;
import com.globalsources.eblock.agg.api.model.vo.auditrule.AuditRuleListVO;
import com.globalsources.eblock.agg.entity.AuditRulePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 规则历史记录 Mapper 接口
 * </p>
 *
 * <AUTHOR> <PERSON>
 * @since 2023-12-11
 */
public interface AuditRuleMapper extends BaseMapper<AuditRulePO> {

    IPage<AuditRuleListVO> selectAuditRule(@Param("page") Page<AuditRuleListVO> page, @Param("dto") AuditRuleQueryDTO dto);

    List<String> selectAuditRuleId(@Param("lastRuleId") String lastRuleId, @Param("limit") Long limit);

    int batchUpdateTriggerCount(@Param("list") List<UpdateTriggerCountDTO> list);


    int insertSubRule();

    int insertMainRule();

}
