package com.globalsources.eblock.agg.controller;

import com.alibaba.fastjson.JSON;
import com.globalsources.eblock.agg.api.model.dto.liteflow.RuleUnitCreateDTO;
import com.globalsources.eblock.agg.service.EBlockRuleUnitService;
import com.globalsources.framework.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RefreshScope
@Api(tags = "eblock-规则单位")
@RestController
@RequestMapping("/rule-unit")
public class EBlockRuleUnitController {

    @Autowired
    private EBlockRuleUnitService eBlockRuleUnitService;

    @ApiOperation(value = "添加RuleUnit", notes = "保存rule  & unit")
    @PostMapping("/v1/add")
    public Result<List<String>> addRuleUnit(@RequestBody RuleUnitCreateDTO dto) {
        log.info("add rule unit dto is :{}", JSON.toJSONString(dto));
        return eBlockRuleUnitService.addRuleUnit(dto);
    }
}
