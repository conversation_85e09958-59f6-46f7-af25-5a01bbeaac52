package com.globalsources.eblock.agg.config;

import com.github.houbb.sensitive.word.api.IWordContext;
import com.github.houbb.sensitive.word.api.IWordDeny;
import com.github.houbb.sensitive.word.api.IWordReplace;
import com.github.houbb.sensitive.word.api.IWordResult;
import com.github.houbb.sensitive.word.bs.SensitiveWordBs;
import com.github.houbb.sensitive.word.support.deny.WordDenySystem;
import com.github.houbb.sensitive.word.support.deny.WordDenys;
import com.github.houbb.sensitive.word.utils.InnerWordCharUtils;
import com.globalsources.eblock.agg.api.enums.EblockTypeEnum;
import com.globalsources.eblock.agg.api.enums.KeywordDomainEnum;
import com.globalsources.eblock.agg.api.enums.KeywordGroupEnum;
import com.globalsources.eblock.agg.dao.SensitiveKeywordMapper;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR> Li
 * @create 2023/11/27
 */

@Configuration
@AllArgsConstructor
public class SensitiveWordConfig {
    
    private  final SensitiveKeywordMapper sensitiveKeywordMapper;

    @Bean(name = "buyerCompanyWebsiteBlack")
    SensitiveWordBs buyerCompanyWebsiteBlack() {
        IWordDeny sensitiveKeyWord = () -> {
            return sensitiveKeywordMapper.getSensitiveKeyByDomainAndType(KeywordGroupEnum.SENSITIVE_KEYWORD.getKeywordGroup(),KeywordDomainEnum.BUYER_COMPANY_WEBSITE.getKeywordDomain(), EblockTypeEnum.black.getType());
        };
        return SensitiveWordBs.newInstance()
                .wordDeny(sensitiveKeyWord)
                .ignoreRepeat(false)
                .wordReplace(new DefaultWordReplace())
                .enableUrlCheck(false)
                .enableEmailCheck(false)
                .enableNumCheck(false)
                .init();
    }

    @Bean(name = "buyerCompanyWebsiteBlock")
    SensitiveWordBs buyerCompanyWebsiteBlock() {
        IWordDeny sensitiveKeyWord = () -> {
            return sensitiveKeywordMapper.getSensitiveKeyByDomainAndType(KeywordGroupEnum.SENSITIVE_KEYWORD.getKeywordGroup(),KeywordDomainEnum.BUYER_COMPANY_WEBSITE.getKeywordDomain(), EblockTypeEnum.block.getType());
        };
        return SensitiveWordBs.newInstance()
                .wordDeny(sensitiveKeyWord)
                .ignoreRepeat(false)
                .wordReplace(new DefaultWordReplace())
                .enableUrlCheck(false)
                .enableEmailCheck(false)
                .enableNumCheck(false)
                .init();
    }

    @Bean(name = "textBlack")
    public SensitiveWordBs textBlack() {
        IWordDeny sensitiveKeyWord = () -> {
            return sensitiveKeywordMapper.getSensitiveKeyByDomainAndType(KeywordGroupEnum.SENSITIVE_KEYWORD.getKeywordGroup(),KeywordDomainEnum.TEXT.getKeywordDomain(), EblockTypeEnum.black.getType());
        };
        return SensitiveWordBs.newInstance()
                .wordDeny(sensitiveKeyWord)
                .ignoreRepeat(false)
                .wordReplace(new DefaultWordReplace())
                .enableUrlCheck(false)
                .enableEmailCheck(false)
                .enableNumCheck(false)
                .init();
    }

    @Bean(name = "textBlock")
    public SensitiveWordBs textBlock() {
        IWordDeny sensitiveKeyWord = () -> {
            return sensitiveKeywordMapper.getSensitiveKeyByDomainAndType(KeywordGroupEnum.SENSITIVE_KEYWORD.getKeywordGroup(),KeywordDomainEnum.TEXT.getKeywordDomain(), EblockTypeEnum.block.getType());
        };
        return SensitiveWordBs.newInstance()
                .wordDeny(sensitiveKeyWord)
                .ignoreRepeat(false)
                .wordReplace(new DefaultWordReplace())
                .enableUrlCheck(false)
                .enableEmailCheck(false)
                .enableNumCheck(false)
                .init();
    }

    @Bean(name = "buyerUserNameBlack")
    public SensitiveWordBs buyerUserNameBlack() {
        IWordDeny sensitiveKeyWord = () -> {
            return sensitiveKeywordMapper.getSensitiveKeyByDomainAndType(KeywordGroupEnum.SENSITIVE_KEYWORD.getKeywordGroup(),KeywordDomainEnum.BUYER_USER_NAME.getKeywordDomain(), EblockTypeEnum.black.getType());
        };
        return SensitiveWordBs.newInstance()
                .wordDeny(sensitiveKeyWord)
                .ignoreRepeat(false)
                .wordReplace(new DefaultWordReplace())
                .enableUrlCheck(false)
                .enableEmailCheck(false)
                .enableNumCheck(false)
                .init();
    }

    @Bean(name = "buyerUserNameBlock")
    public SensitiveWordBs buyerUserNameBlock() {
        IWordDeny sensitiveKeyWord = () -> {
            return sensitiveKeywordMapper.getSensitiveKeyByDomainAndType(KeywordGroupEnum.SENSITIVE_KEYWORD.getKeywordGroup(),KeywordDomainEnum.BUYER_USER_NAME.getKeywordDomain(), EblockTypeEnum.block.getType());
        };
        return SensitiveWordBs.newInstance()
                .wordDeny(sensitiveKeyWord)
                .ignoreRepeat(false)
                .wordReplace(new DefaultWordReplace())
                .enableUrlCheck(false)
                .enableEmailCheck(false)
                .enableNumCheck(false)
                .init();
    }

    @Bean(name = "domainBlack")
    public SensitiveWordBs domainBlack() {
        IWordDeny sensitiveKeyWord = () -> {
            return sensitiveKeywordMapper.getSensitiveKeyByDomainAndType(KeywordGroupEnum.DOMAIN.getKeywordGroup(),null, EblockTypeEnum.black.getType());
        };
        return SensitiveWordBs.newInstance()
                .wordDeny(sensitiveKeyWord)
                .ignoreRepeat(false)
                .wordReplace(new DefaultWordReplace())
                .enableUrlCheck(false)
                .enableEmailCheck(false)
                .enableNumCheck(false)
                .init();
    }

    @Bean(name = "domainBlock")
    public SensitiveWordBs domainBlock() {
        IWordDeny sensitiveKeyWord = () -> {
            return sensitiveKeywordMapper.getSensitiveKeyByDomainAndType(KeywordGroupEnum.DOMAIN.getKeywordGroup(),null,  EblockTypeEnum.block.getType());
        };
        return SensitiveWordBs.newInstance()
                .wordDeny(sensitiveKeyWord)
                .ignoreRepeat(false)
                .wordReplace(new DefaultWordReplace())
                .enableUrlCheck(false)
                .enableEmailCheck(false)
                .enableNumCheck(false)
                .init();
    }

    @Bean(name = "domainWhite")
    public SensitiveWordBs domainWhite() {
        IWordDeny sensitiveKeyWord = () -> {
            return sensitiveKeywordMapper.getSensitiveKeyByDomainAndType(KeywordGroupEnum.DOMAIN.getKeywordGroup(),null,  EblockTypeEnum.white.getType());
        };
        return SensitiveWordBs.newInstance()
                .wordDeny(sensitiveKeyWord)
                .ignoreRepeat(false)
                .wordReplace(new DefaultWordReplace())
                .enableUrlCheck(false)
                .enableEmailCheck(false)
                .enableNumCheck(false)
                .init();
    }

     static class DefaultWordReplace implements IWordReplace {
         private static final String BUYER_HIGH_LIGHT_PRE_TAGS = "<span class='content-red'>";
         private static final String HIGH_LIGHT_POST_TAGS = "</span>";

        @Override
        public void replace(StringBuilder stringBuilder, final char[] rawChars, IWordResult wordResult, IWordContext wordContext) {
            String sensitiveWord = InnerWordCharUtils.getString(rawChars, wordResult);
            stringBuilder.append(BUYER_HIGH_LIGHT_PRE_TAGS).append(sensitiveWord).append(HIGH_LIGHT_POST_TAGS);
        }
    }

}