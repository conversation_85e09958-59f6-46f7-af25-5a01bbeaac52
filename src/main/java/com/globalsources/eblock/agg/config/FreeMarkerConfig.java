package com.globalsources.eblock.agg.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class FreeMarkerConfig {

  @Bean(name = "freeMarkerConfigurer")
  public FreeMarkerConfigurer freeMarkerConfigurer() {
    FreeMarkerConfigurer configuration = new FreeMarkerConfigurer();
    configuration.setDefaultEncoding("UTF-8");
    configuration.setTemplateLoaderPath("classpath:/templates/");
    Map<String, Object> variables = new HashMap<>(1<<1);
    variables.put("xml_escape","fmXmlEscape");
    configuration.setFreemarkerVariables(variables);
    return configuration;
  }

}