package com.globalsources.eblock.agg.api.model.vo.audit.rfi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR> Peng
 * @since 2023/11/22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "待审核列表", description = "待审核列表")
public class AuditInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private Long buyerId;

    @ApiModelProperty(value = "买家邮箱")
    private String buyerEmailAddr;

    @ApiModelProperty(value = "买家名（公司名称）")
    private String buyerName;

    @ApiModelProperty(value = "买家公司名")
    private String buyerCompanyName;

    @ApiModelProperty(value = "该买家还未审核的询盘总数")
    private Integer notReviewedRfiCnt;

    @ApiModelProperty(value = "待审核的买家询盘的受影响供应商数量")
    private Integer supplierCnt;

    @ApiModelProperty(value = "系统审核时间")
    private Date systemAuditDate;
}