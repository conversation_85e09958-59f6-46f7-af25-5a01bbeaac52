package com.globalsources.eblock.agg.api.model.dto.auditrule;

import com.globalsources.framework.page.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AuditRuleQueryDTO")
public class AuditRuleQueryDTO extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("Rule Name")
    private String ruleName;

    @ApiModelProperty("Action")
    private String ruleAction;

    @ApiModelProperty("Rule Status")
    private String status;

    private String startDate;

    private String endDate;

    @ApiModelProperty(value = "草稿标记")
    private Boolean draftFlag = false;

    @ApiModelProperty("排序字段:ruleName,lUpdDate,ruleAction,status")
    private String sortField;

    @ApiModelProperty("排序方式:asc, desc")
    private String sortType;

    private List<String> executeResultList;
}
