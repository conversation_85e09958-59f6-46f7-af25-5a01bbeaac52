package com.globalsources.eblock.agg.api.enums;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
public enum BuyerInformationEnum {

    BUYER_ID("buyerId", "Buyer Id"),
    TITLE("title", "Title"),
    FIRST_NAME("firstName", "First Name"),
    LAST_NAME("lastName", "Last Name"),
    JO<PERSON>_TITLE("jobTitle", "Job Title"),
    JOB_FUNCTION("jobFunction", "Job Function"),
    WECHAT_ID("wechatId", "Wechat ID"),
    BUYER_EMAIL_ADDRESS("email", "Buyer Email Address"),
    BUYER_TEL("phoneNumber", "Buyer Tel"),
    CITY_TOWN("city", "City/Town"),
    PROVINCE_STATE("province", "Province/State"),
    COUNTRY_REGION("countryCode", "Country/Region"),
    ZIP_CODE("zipCode", "Zip Code"),
    COMPANY_WEBSITE("websiteUrl", "Company Website"),
    LINKEDIN_PROFILE_URL("linkedinUrl", "LinkedIn Profile URL"),
    BUYER_IP_COUNTRY("buyerIPCountry", "Buyer Ip Country"),
    BUYER_IP("buyerIP", "Buyer Ip"),
    BUYER_REGISTERED_DATE("createDate", "Buyer Registered Date"),
    COMPANY_NAME("companyName", "Company Name"),
    BUSINESS_TYPE("businessType", "Business Type");

    public String getValue() {
        return value;
    }

    public String getCopy() {
        return copy;
    }

    private String value;

    private String copy;
}
