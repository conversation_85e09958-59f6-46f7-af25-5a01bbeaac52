package com.globalsources.eblock.agg.api.model.dto.audit;

import com.globalsources.framework.page.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/11/22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AuditInfoQueryDTO")
public class AuditInfoQueryDTO extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("排序字段:buyerEmailAddr, buyerName, notReviewedRfiCnt, supplierCnt, systemAuditDate")
    private String sortField;

    @ApiModelProperty("排序方式:asc, desc")
    private String sortType;
}
