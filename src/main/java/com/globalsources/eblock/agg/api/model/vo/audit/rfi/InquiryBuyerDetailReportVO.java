package com.globalsources.eblock.agg.api.model.vo.audit.rfi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "InquiryBuyerDetailReportVO")
public class InquiryBuyerDetailReportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer auditInfoId;

    @ApiModelProperty("buyerId")
    private String buyerId;

    @ApiModelProperty("Buyer Email")
    private String buyerEmail;

    @ApiModelProperty("该买家被TMX reject的询盘数量（不包括TMX黑名单买家询盘数量）")
    private Integer tmxRejectCnt = 0;
    private Integer tmxRejectOriginalCnt = 0;
    private Integer tmxRejectFinalCnt = 0;


    @ApiModelProperty("该买家被TMX 黑名单买家询盘数量")
    private Integer tmxBlackRejectCnt = 0;
    private Integer tmxBlackRejectOriginalCnt = 0;
    private Integer tmxBlackRejectFinalCnt = 0;

    @ApiModelProperty("TMX release的询盘数量")
    private Integer tmxReleaseCnt = 0;
    private Integer tmxReleaseOriginalCnt = 0;
    private Integer tmxReleaseFinalCnt = 0;

    @ApiModelProperty("该买家在Admin console直接release的询盘数量")
    private Integer eblockReleaseCnt = 0;
    private Integer eblockReleaseOriginalCnt = 0;
    private Integer eblockReleaseFinalCnt = 0;

    @ApiModelProperty("买家在Admin console直接reject的询盘数量")
    private Integer eblockRejectCnt = 0;
    private Integer eblockRejectOriginalCnt = 0;
    private Integer eblockRejectFinalCnt = 0;

    @ApiModelProperty("该买家被人工release的询盘数量")
    private Integer manualReleaseCnt = 0;
    private Integer manualReleaseOriginalCnt = 0;
    private Integer manualReleaseFinalCnt = 0;

    @ApiModelProperty("该买家被人工block的询盘数量")
    private Integer manualBlockCnt = 0;
    private Integer manualBlockOriginalCnt = 0;
    private Integer manualBlockFinalCnt = 0;

    @ApiModelProperty("该买家被人工blacklist的询盘数量")
    private Integer manualRejectCnt = 0;
    private Integer manualRejectOriginalCnt = 0;
    private Integer manualRejectFinalCnt = 0;


    @ApiModelProperty("resend 的数量")
    private Integer resendCnt = 0;
    private Integer resendOriginalCnt = 0;
    private Integer resendFinalCnt = 0;

}
