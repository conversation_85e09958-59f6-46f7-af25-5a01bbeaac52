package com.globalsources.eblock.agg.api.model.vo.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/11/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "供应商信息", description = "供应商信息")
public class SupplierSimpleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("公司id")
    private Long supplierId;

    @ApiModelProperty("公司名")
    private String supplierName;

    @ApiModelProperty("公司主页")
    private String supplierHomepageUrl;
}
