package com.globalsources.eblock.agg.api.model.dto.audit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/11/22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ReviewSettingSaveDTO")
public class ReviewSettingSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("审核原因ID")
    private Long auditReasonId;

    @NotBlank(message = "action is blank")
    @ApiModelProperty("审核类型：Send, Block, Blacklist, Resend")
    private String action;

    @NotBlank(message = "reason is blank")
    @ApiModelProperty("添加的审核原因")
    private String reason;

    @ApiModelProperty(value = "导入Supplier Center标识")
    private Boolean exportToScFlag;

    @ApiModelProperty("后端用的字段")
    private Long userId;

    @ApiModelProperty("后端用的字段")
    private String email;
}
