package com.globalsources.eblock.agg.api.model.vo.audit.rfi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SummaryPendingReviewVO")
public class SummaryPendingReviewVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("Pending Reviewed RFI")
    private Integer totalReviewedRfiCount = 0;

    @ApiModelProperty("Total Affected supplier")
    private Integer totalAffectedRfiCount = 0;

    @ApiModelProperty("Category RFI")
    private Integer categoryRfiCount = 0;

    @ApiModelProperty("Product RFI")
    private Integer productRfiCount = 0;

    @ApiModelProperty("Supplier RFI")
    private Integer supplierRfiCount = 0;
}
