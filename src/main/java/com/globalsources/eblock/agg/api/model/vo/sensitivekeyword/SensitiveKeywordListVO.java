package com.globalsources.eblock.agg.api.model.vo.sensitivekeyword;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SensitiveKeywordListVO")
public class SensitiveKeywordListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("敏感词ID")
    private Long sensitiveKeywordId;

    @ApiModelProperty("内容")
    private String keywordContent;

    @ApiModelProperty("分组: DOMAIN(域名), SENSITIVE_KEYWORD(敏感词)")
    private String keywordGroup;

    @ApiModelProperty("作用域: COMMON(全局公共), TEXT(询盘内容敏感词), BUYER_COMPANY_WEBSITE(买家网站敏感词), BUYER_USER_NAME(买家用户名敏感词)")
    private String keywordDomain;

    @ApiModelProperty("类型：black(黑),white(白),block(灰)")
    private String auditType;

    @ApiModelProperty("创建人id")
    private Long createBy;

    @ApiModelProperty("创建人email")
    private String createEmailAddr;

    @ApiModelProperty("创建时间")
    private Date createDate;
}
