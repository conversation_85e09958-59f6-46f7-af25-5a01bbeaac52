package com.globalsources.eblock.agg.api.model.dto.auditrule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/12/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AuditRuleUpdateDTO")
public class AuditRuleUpdateDTO  implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("Rule Id:流程id")
    private String ruleId;

    @ApiModelProperty("Rule Status")
    private String status;

    @ApiModelProperty("user id")
    private Long userId;
}
