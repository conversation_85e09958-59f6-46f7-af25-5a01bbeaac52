package com.globalsources.eblock.agg.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum EblockSystemReasonEnum {
    TMX_Reject("TMX Reject", "TMX Reject","Reject","tmxResultCmp"),
    TMX_Release("TMX Release", "TMX Release","Release","tmxResultCmp"),
    TMX_Account_Blacklist("TMX BlackList","TMX BlackList", "Reject","tmxAccountBlacklistCmp"),

    Buyer_Blacklist( "Blacklist Reject","Buyer Blacklist", "Reject", "blackListBuyerCmp"),
    Domain_Blacklist( "Black Domain Reject","Domain Blacklist", "Reject", "blackListDomainCmp"),

    Buyer_Blocklist( "Buyer Blocklist", "Buyer Blocklist","Block", "blockListBuyerCmp"),
    Domain_Blocklist( "Domain Blocklist", "Domain Blocklist","Block", "blockListDomainCmp"),

    Buyer_Whitelist( "Whitelist Release", "Buyer Whitelist","Release", "whiteListBuyerCmp"),
    <PERSON><PERSON>_Whitelist( "White Domain Release", "Domian Whitelist","Release", "whiteListDomainCmp"),

    Non_English( "Non-English", "Non-English", "Block", "nonEnCharacterCmp"),
    Message_Keyword_Block( "Message Keyword", "Message Keyword","Block", "sensitiveKeyBuyerMessageCmp"),
    Message_Keyword_Reject( "Message keyword Reject", "Message keyword Reject","Reject", "sensitiveKeyBuyerMessageCmp"),

    Website_Keyword_Block( "Website Keyword", "Website Keyword","Block", "sensitiveKeyBuyerWebsiteCmp"),
    Website_Keyword_Reject( "Website keyword Reject", "Website keyword Reject","Reject", "sensitiveKeyBuyerWebsiteCmp"),

    Name_Keyword_Block( "Name Keyword", "Name Keyword", "Block", "sensitiveKeyBuyerNameCmp"),
    Name_Keyword_Reject( "Name keyword Reject", "Name keyword Reject","Reject", "sensitiveKeyBuyerNameCmp"),

    Admin_Console_Release( "System Release","System Release", "Release", "releaseCmp")
    ;


    private final String reason;

    private final String ruleName;

    private final String status;

    private final String cmp;

    public static String getReasonByCmp(String cmp,String systemStatus) {
        for (EblockSystemReasonEnum value : EblockSystemReasonEnum.values()) {
            if (value.getCmp().equals(cmp) && value.getStatus().equals(systemStatus)) {
                return value.getReason();
            }
        }
        return cmp;
    }

    public static String getRuleNameByCmp(String cmp,String systemStatus) {
        for (EblockSystemReasonEnum value : EblockSystemReasonEnum.values()) {
            if (value.getCmp().equals(cmp) && value.getStatus().equals(systemStatus)) {
                return value.getRuleName();
            }
        }
        return cmp;
    }

    public static List<String> getMessageCmp(){
        return Arrays.asList(Message_Keyword_Block.cmp,Message_Keyword_Reject.cmp);
    }

    public static List<String> getDomainCmp(){
        return Arrays.asList(Domain_Blocklist.cmp,Domain_Blacklist.cmp);
    }

    public static List<String> getNameCmp(){
        return Arrays.asList(Name_Keyword_Block.cmp,Name_Keyword_Reject.cmp);
    }

    public static List<String> getBuyerWebsiteCmp(){
        return Arrays.asList(Website_Keyword_Reject.cmp,Website_Keyword_Block.cmp);
    }
}
