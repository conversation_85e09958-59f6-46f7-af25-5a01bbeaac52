/**
 * <p>Title: liteflow</p>
 * <p>Description: 轻量级的组件式流程框架</p>
 * <AUTHOR>
 * @email <EMAIL>
 * @Date 2020/4/1
 */
package com.globalsources.eblock.agg.liteflow.component;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.globalsources.eblock.agg.api.enums.EblockAuditActionEnum;
import com.globalsources.eblock.agg.api.enums.EblockAuditStatusEnum;
import com.globalsources.eblock.agg.dao.AuditInfoTempMapper;
import com.globalsources.eblock.agg.dao.AuditRuleResultRecordMapper;
import com.globalsources.eblock.agg.entity.AuditInfoPO;
import com.globalsources.eblock.agg.entity.AuditInfoPOTemp;
import com.globalsources.eblock.agg.entity.AuditRulePO;
import com.globalsources.eblock.agg.entity.AuditRuleResultRecordPO;
import com.globalsources.eblock.agg.liteflow.bean.InquiryBean;
import com.globalsources.eblock.agg.liteflow.bean.InquiryContextBean;
import com.globalsources.eblock.agg.liteflow.exception.BlockBusinessException;
import com.globalsources.eblock.agg.liteflow.exception.RejectBusinessException;
import com.globalsources.eblock.agg.liteflow.exception.RejectWithBlackBusinessException;
import com.globalsources.eblock.agg.liteflow.exception.ReleaseBusinessException;
import com.globalsources.eblock.agg.service.AuditInfoService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.exception.LiteFlowException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 一个普通组件
 *
 */
@LiteflowComponent(id = "catchTestCmp",name = "tmx result cmp")
@Slf4j
@AllArgsConstructor
public class CatchTestCmp extends NodeComponent {

	private final AuditInfoService auditInfoService;

	private final AuditRuleResultRecordMapper auditRuleResultRecordMapper;

	private final AuditInfoTempMapper auditInfoMapper;

	@Override
	public void process() {
		Exception exception = this.getSlot().getException();
		InquiryBean inquiry = this.getContextBean(InquiryBean.class);
		InquiryContextBean contextBean = this.getContextBean(InquiryContextBean.class);
		if(exception instanceof LiteFlowException){
			String nodeId = ((LiteFlowException) exception).getCode();
			if (exception instanceof ReleaseBusinessException){
				auditInfoService.systemActionTemp(inquiry.getInquiryId(), EblockAuditActionEnum.SEND);
				contextBean.getResultMap().put(this.getNodeId(), EblockAuditStatusEnum.RELEASE.getType());
			}
			if (exception instanceof RejectBusinessException){
				auditInfoService.systemActionTemp(inquiry.getInquiryId(), EblockAuditActionEnum.BLACK_LIST);
				contextBean.getResultMap().put(this.getNodeId(), EblockAuditStatusEnum.REJECT.getType());
			}
			if (exception instanceof RejectWithBlackBusinessException){
				auditInfoService.systemActionTemp(inquiry.getInquiryId(), EblockAuditActionEnum.BLACK_LIST);
				contextBean.getResultMap().put(this.getNodeId(), EblockAuditStatusEnum.REJECT.getType());
			}
			if (exception instanceof BlockBusinessException){
				auditInfoService.systemActionTemp(inquiry.getInquiryId(), EblockAuditActionEnum.BLOCK);
				contextBean.getResultMap().put(this.getNodeId(), EblockAuditStatusEnum.BLOCK.getType());
			}
			recordUserDefineRuleResult(inquiry.getInquiryId(),contextBean);
			updateAuditInfo(inquiry.getInquiryId(),nodeId,contextBean.getSensitiveKeyword());
		}else{
			log.error("CatchTestCmp process error:{}", JSON.toJSONString(exception));
		}
	}

	private void updateAuditInfo(String inquiryId, String nodeId,String sensitiveKeyword) {
		log.info("CatchTestCmp updateAuditInfo inquiryId :{},nodeId:{},sensitiveKeyword :{}",inquiryId,nodeId,sensitiveKeyword);
		try {
			LambdaUpdateWrapper<AuditInfoPOTemp> update =  new LambdaUpdateWrapper<>();
			update.eq(AuditInfoPOTemp::getRequestId,inquiryId);
			update.set(AuditInfoPOTemp::getSystemAuditRules,nodeId);
			if(StringUtils.isNotBlank(sensitiveKeyword)){
				update.set(AuditInfoPOTemp::getSensitiveKeyword,sensitiveKeyword);
			}
			auditInfoMapper.update(null,update);
		}catch (Exception e){
			log.error("CatchTestCmp updateAuditInfo inquiryId :{} error:{}",inquiryId, JSON.toJSONString(e));

		}
	}

	private void recordUserDefineRuleResult(String inquiryId, InquiryContextBean contextBean) {
		try {
			for(AuditRulePO rule : contextBean.getUserRuleSet()){
				String result = null;
				if(contextBean.getUserDefineRuleResultMap().containsKey(rule.getRuleId()) && Objects.equals(Boolean.TRUE,contextBean.getUserDefineRuleResultMap().get(rule.getRuleId()))){
					result = rule.getRuleAction();
				}
				auditRuleResultRecordMapper.insert(AuditRuleResultRecordPO.builder().executeResult(result).requestId(inquiryId).ruleId(contextBean.isDraftFlag()?rule.getRuleId()+contextBean.getUuid() : rule.getRuleId()).draftFlag(contextBean.isDraftFlag()).build());
			}
		}catch (Exception e){
			log.error("CatchTestCmp recordUserDefineRuleResult inquiryId :{} error:{}",inquiryId, JSON.toJSONString(e));

		}
	}

}
