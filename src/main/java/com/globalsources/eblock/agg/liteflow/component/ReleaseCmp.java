/**
 * <p>Title: liteflow</p>
 * <p>Description: 轻量级的组件式流程框架</p>
 * <AUTHOR>
 * @email <EMAIL>
 * @Date 2020/4/1
 */
package com.globalsources.eblock.agg.liteflow.component;

import com.globalsources.eblock.agg.api.enums.EblockAuditStatusEnum;
import com.globalsources.eblock.agg.liteflow.bean.InquiryContextBean;
import com.globalsources.eblock.agg.liteflow.exception.ReleaseBusinessException;
import com.globalsources.eblock.agg.service.AuditInfoService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 一个普通组件
 *
 */
@LiteflowComponent(id = "releaseCmp",name = "release action")
@Slf4j
@AllArgsConstructor
public class ReleaseCmp extends NodeComponent {

	private final AuditInfoService auditInfoService;

	@Override
	public void process() {
		InquiryContextBean contextBean = this.getContextBean(InquiryContextBean.class);
		contextBean.getResultMap().put(this.getNodeId(), EblockAuditStatusEnum.RELEASE.getType());
		throw new ReleaseBusinessException(this.getNodeId(),"");
	}
}
