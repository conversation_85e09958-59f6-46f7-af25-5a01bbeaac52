/**
 * <p>Title: liteflow</p>
 * <p>Description: 轻量级的组件式流程框架</p>
 * <AUTHOR>
 * @email <EMAIL>
 * @Date 2020/4/1
 */
package com.globalsources.eblock.agg.liteflow.component;

import com.globalsources.eblock.agg.api.enums.EblockAuditStatusEnum;
import com.globalsources.eblock.agg.api.enums.EblockTypeEnum;
import com.globalsources.eblock.agg.liteflow.bean.BuyerBean;
import com.globalsources.eblock.agg.liteflow.bean.InquiryBean;
import com.globalsources.eblock.agg.liteflow.bean.InquiryContextBean;
import com.globalsources.eblock.agg.liteflow.bean.NodeResult;
import com.globalsources.eblock.agg.utils.SensitiveScanUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 一个普通组件
 *
 */
@LiteflowComponent(id = "whiteListDomainCmp",name = "是否Whitelisted Domain")
@Slf4j
@AllArgsConstructor
public class WhiteListDomainCmp extends NodeComponent {

	private final SensitiveScanUtil sensitiveScanUtil;

	@Override
	public void process() {
		InquiryBean inquiry = this.getContextBean(InquiryBean.class);

		BuyerBean buyer = this.getContextBean(BuyerBean.class);
		InquiryContextBean contextBean = this.getContextBean(InquiryContextBean.class);

		EblockTypeEnum result = sensitiveScanUtil.domainScan(buyer.getBuyerDomain());
		log.info("WhiteListDomainCmp inquiryId :{},result:{}", inquiry.getInquiryId(),result);
		if(Objects.equals(EblockTypeEnum.white,result)){
			contextBean.getResultMap().put(this.getNodeId(), EblockAuditStatusEnum.RELEASE.getType());
			EblockAuditStatusEnum nodeResult = EblockAuditStatusEnum.RELEASE;
			contextBean.getSubResultList().add(NodeResult.builder().nodeId(this.getNodeId()).result(nodeResult).build());
		}

	}

}
