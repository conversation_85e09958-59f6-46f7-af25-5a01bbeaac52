/**
 * <p>Title: liteflow</p>
 * <p>Description: 轻量级的组件式流程框架</p>
 * <AUTHOR>
 * @email <EMAIL>
 * @Date 2020/4/1
 */
package com.globalsources.eblock.agg.liteflow.component;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.globalsources.eblock.agg.api.enums.EblockAuditActionEnum;
import com.globalsources.eblock.agg.api.enums.EblockAuditStatusEnum;
import com.globalsources.eblock.agg.api.enums.EblockTypeEnum;
import com.globalsources.eblock.agg.liteflow.bean.BuyerBean;
import com.globalsources.eblock.agg.liteflow.bean.InquiryBean;
import com.globalsources.eblock.agg.liteflow.bean.InquiryContextBean;
import com.globalsources.eblock.agg.liteflow.bean.NodeResult;
import com.globalsources.eblock.agg.service.RfqBlackListService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.CmpStepTypeEnum;
import com.yomahub.liteflow.flow.entity.CmpStep;
import com.yomahub.liteflow.monitor.CompStatistics;
import com.yomahub.liteflow.slot.Slot;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 一个普通组件
 *
 */
@LiteflowComponent(id = "blackListBuyerCmp",name = "是否Blacklist Buyer（包括Buyer ID）")
@Slf4j
@AllArgsConstructor
public class BlackListBuyerCmp extends NodeComponent {

	private final RfqBlackListService rfqBlackListService;

	@Override
	public void process() {
		InquiryBean inquiry = this.getContextBean(InquiryBean.class);

		BuyerBean buyer = this.getContextBean(BuyerBean.class);

		InquiryContextBean contextBean = this.getContextBean(InquiryContextBean.class);

		Boolean result = rfqBlackListService.isBlacklistUser(buyer.getBuyerId(),null,EblockTypeEnum.black.getType());
		log.info("BlackListBuyerCmp inquiryId :{},result:{}", inquiry.getInquiryId(),result);

		if(Objects.equals(Boolean.TRUE,result)){
			contextBean.getResultMap().put(this.getNodeId(), EblockAuditActionEnum.BLACK_LIST.getType());
			contextBean.getSubResultList().add(NodeResult.builder().nodeId(this.getNodeId()).result(EblockAuditStatusEnum.REJECT).build());
		}
	}
}
