package com.globalsources.eblock.agg.liteflow.enums;

import com.yomahub.liteflow.enums.NodeTypeEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023/9/11
 */
@Getter
public enum CommonNodeTypeEnum {

        COMMON("common", "CommonNode"),
        SWITCH("switch", "SwitchNode"),
        IF("if", "IfNode"),
//        FOR("for", "循环次数"),
//        WHILE("while", "循环条件"),
//        BREAK("break", "循环跳出"),
//        ITERATOR("iterator", "循环迭代"),
        SCRIPT("script", "CommonNode"),
        SWITCH_SCRIPT("switch_script","SwitchNode"),
        IF_SCRIPT("if_script", "IfNode");
//        FOR_SCRIPT("for_script", "循环次数脚本"),
//        WHILE_SCRIPT("while_script", "循环条件脚本"),
//        BREAK_SCRIPT("break_script", "循环跳出脚本");

        private String code;
        private String name;

        private CommonNodeTypeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }

    public static String convert(NodeTypeEnum enu) {
        if(Objects.isNull(enu)){
            return null;
        }
        for(CommonNodeTypeEnum node:values()){
            if(node.code.equals(enu.getCode())){
                return node.name;
            }
        }
        return null;
    }
}
