/**
 * <p>Title: liteflow</p>
 * <p>Description: 轻量级的组件式流程框架</p>
 * <AUTHOR>
 * @email <EMAIL>
 * @Date 2020/4/1
 */
package com.globalsources.eblock.agg.liteflow.component;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.globalsources.eblock.agg.api.enums.EblockAuditActionEnum;
import com.globalsources.eblock.agg.api.enums.EblockAuditStatusEnum;
import com.globalsources.eblock.agg.dao.AuditInfoMapper;
import com.globalsources.eblock.agg.dao.AuditRuleResultRecordMapper;
import com.globalsources.eblock.agg.entity.AuditInfoPO;
import com.globalsources.eblock.agg.entity.AuditRulePO;
import com.globalsources.eblock.agg.entity.AuditRuleResultRecordPO;
import com.globalsources.eblock.agg.liteflow.bean.InquiryBean;
import com.globalsources.eblock.agg.liteflow.bean.InquiryContextBean;
import com.globalsources.eblock.agg.liteflow.exception.*;
import com.globalsources.eblock.agg.service.AuditInfoService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.exception.LiteFlowException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * 一个普通组件
 *
 */
@LiteflowComponent(id = "catchCmp",name = "tmx result cmp")
@Slf4j
@AllArgsConstructor
public class CatchCmp extends NodeComponent {

	private final AuditInfoService auditInfoService;

	private final AuditRuleResultRecordMapper auditRuleResultRecordMapper;

	private final AuditInfoMapper auditInfoMapper;

	@Override
	public void process() {
		Exception exception = this.getSlot().getException();
		InquiryBean inquiry = this.getContextBean(InquiryBean.class);
		InquiryContextBean contextBean = this.getContextBean(InquiryContextBean.class);
		boolean draftFlag = contextBean.isDraftFlag();
		if(exception instanceof LiteFlowException){
			String nodeId = ((LiteFlowException) exception).getCode();
			if (exception instanceof ReleaseBusinessException){
				if(Objects.equals(false,draftFlag)){
					auditInfoService.systemAction(inquiry.getInquiryId(), EblockAuditActionEnum.SEND,false);
				}
				contextBean.getResultMap().put(this.getNodeId(), EblockAuditStatusEnum.RELEASE.getType());
			}
			if (exception instanceof RejectBusinessException){
				if(Objects.equals(false,draftFlag)){
					auditInfoService.systemAction(inquiry.getInquiryId(), EblockAuditActionEnum.BLACK_LIST,false);
				}
				contextBean.getResultMap().put(this.getNodeId(), EblockAuditStatusEnum.REJECT.getType());
			}
			if (exception instanceof RejectWithBlackBusinessException){
				if(Objects.equals(false,draftFlag)){
					auditInfoService.systemAction(inquiry.getInquiryId(), EblockAuditActionEnum.BLACK_LIST,true);
				}
				contextBean.getResultMap().put(this.getNodeId(), EblockAuditStatusEnum.REJECT.getType());
			}
			if (exception instanceof BlockBusinessException){
				if(Objects.equals(false,draftFlag)){
					auditInfoService.systemAction(inquiry.getInquiryId(), EblockAuditActionEnum.BLOCK,false);
				}
				contextBean.getResultMap().put(this.getNodeId(), EblockAuditStatusEnum.BLOCK.getType());
			}
			recordUserDefineRuleResult(inquiry.getInquiryId(),contextBean);
			if(Objects.equals(false,draftFlag)){
				updateAuditInfo(inquiry.getInquiryId(),nodeId,contextBean.getSensitiveKeyword());
			}
		}else{
			log.error("CatchCmp process error:{}", JSON.toJSONString(exception));
		}
	}

	private void updateAuditInfo(String inquiryId, String nodeId,String sensitiveKeyword) {
		log.info("CatchCmp updateAuditInfo inquiryId :{},nodeId:{},sensitiveKeyword :{}",inquiryId,nodeId,sensitiveKeyword);
		try {
			LambdaUpdateWrapper<AuditInfoPO> update =  new LambdaUpdateWrapper<>();
			update.eq(AuditInfoPO::getRequestId,inquiryId);
			update.set(AuditInfoPO::getSystemAuditRules,nodeId);
			if(StringUtils.isNotBlank(sensitiveKeyword)){
				update.set(AuditInfoPO::getSensitiveKeyword,sensitiveKeyword);
			}
			auditInfoMapper.update(null,update);
		}catch (Exception e){
			log.error("CatchCmp updateAuditInfo inquiryId :{} error:{}",inquiryId, JSON.toJSONString(e));

		}
	}

	private void recordUserDefineRuleResult(String inquiryId, InquiryContextBean contextBean) {
		try {
			for(AuditRulePO rule : contextBean.getUserRuleSet()){
				String result = null;
				if(contextBean.getUserDefineRuleResultMap().containsKey(rule.getRuleId()) && Objects.equals(Boolean.TRUE,contextBean.getUserDefineRuleResultMap().get(rule.getRuleId()))){
					result = rule.getRuleAction();
				}
				auditRuleResultRecordMapper.insert(AuditRuleResultRecordPO.builder().executeResult(result).requestId(inquiryId).ruleId(contextBean.isDraftFlag()?rule.getRuleId()+contextBean.getUuid() : rule.getRuleId()).draftFlag(contextBean.isDraftFlag()).build());
			}
		}catch (Exception e){
			log.error("CatchCmp recordUserDefineRuleResult inquiryId :{} error:{}",inquiryId, JSON.toJSONString(e));

		}
	}

}
