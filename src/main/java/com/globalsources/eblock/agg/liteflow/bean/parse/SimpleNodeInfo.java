package com.globalsources.eblock.agg.liteflow.bean.parse;

import com.globalsources.eblock.agg.liteflow.enums.CommonNodeTypeEnum;
import com.yomahub.liteflow.enums.NodeTypeEnum;
import com.yomahub.liteflow.flow.element.Node;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 询盘上下文对象
 * <AUTHOR>
 * @create 2023/8/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimpleNodeInfo {

    private String nodeId;
    private String name;
    private String type;
    private boolean isScriptNode;

    public SimpleNodeInfo(Node node) {
        this.nodeId = node.getId();
        this.name = node.getName();
        this.type = caseType(node.getType());
        this.isScriptNode=node.getType().isScript();
    }

    public String caseType(NodeTypeEnum typeEnum) {
        return CommonNodeTypeEnum.convert(typeEnum);
    }
}
