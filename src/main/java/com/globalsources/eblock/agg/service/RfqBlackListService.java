package com.globalsources.eblock.agg.service;

import com.globalsources.eblock.agg.api.model.dto.rfqblacklist.RfqBlackListAddDTO;
import com.globalsources.eblock.agg.api.model.dto.rfqblacklist.RfqBlackListQueryDTO;
import com.globalsources.eblock.agg.api.model.vo.rfqblacklist.SearchUserInfoVO;
import com.globalsources.eblock.agg.entity.RfqBlackListVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
public interface RfqBlackListService {

    PageResult<RfqBlackListVO> selectUserPermissionsList(RfqBlackListQueryDTO dto);

    boolean saveBySystem(RfqBlackListAddDTO dto);

    boolean addEblock(RfqBlackListAddDTO dto);

    boolean saveEblock(RfqBlackListAddDTO dto);

    Result<SearchUserInfoVO> searchByEmailOrId(String key, String type);

    Boolean isBlacklistUser(Long userId, String email, String type);

    boolean remove(Long userId, Long reviewerId, String type);
}
