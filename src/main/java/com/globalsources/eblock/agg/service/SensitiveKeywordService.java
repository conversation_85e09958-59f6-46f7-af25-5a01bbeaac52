package com.globalsources.eblock.agg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.eblock.agg.api.model.dto.sensitivekeyword.SensitiveKeywordCheckDTO;
import com.globalsources.eblock.agg.api.model.dto.sensitivekeyword.SensitiveKeywordDeleteDTO;
import com.globalsources.eblock.agg.api.model.dto.sensitivekeyword.SensitiveKeywordQueryDTO;
import com.globalsources.eblock.agg.api.model.dto.sensitivekeyword.SensitiveKeywordSaveDTO;
import com.globalsources.eblock.agg.api.model.vo.sensitivekeyword.SensitiveKeywordListVO;
import com.globalsources.eblock.agg.entity.SensitiveKeywordPO;
import com.globalsources.framework.result.PageResult;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
public interface SensitiveKeywordService extends IService<SensitiveKeywordPO> {

    /**
     * 检查keywordContent是否存在
     * @param dto
     * @return
     */
    Boolean checkSensitiveKeyword(SensitiveKeywordCheckDTO dto);

    /**
     * 获取敏感词列表
     *
     * @param dto
     * @return
     */
    PageResult<SensitiveKeywordListVO> selectSensitiveKeyword(SensitiveKeywordQueryDTO dto);

    /**
     * 保存敏感词
     *
     * @param dto
     * @return
     */
    Long saveSensitiveKeyword(SensitiveKeywordSaveDTO dto);

    /**
     * 删除敏感词
     */
    Boolean deleteSensitiveKeyword(@RequestBody SensitiveKeywordDeleteDTO dto);

}
