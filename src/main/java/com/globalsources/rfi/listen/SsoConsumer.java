package com.globalsources.rfi.listen;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfi.agg.core.vo.sso.UserInfoDTO;
import com.globalsources.rfi.service.SsoUserUpdateService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <a>Title: SsoConsumer </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR> Li
 * @date 2021/12/6-15:42
 */
@Slf4j
@Component
public class SsoConsumer {

    @Autowired
    private SsoUserUpdateService ssoUserUpdateService;

    public static final String QUEUE_NAME_TRANSACTION = "GSOL.DATA.CLIENT.USER_UPDATE_QUEUE";

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final String DUP_UPDATE_USER_KEY = "RFI:UPDATE:USER:QUEUE:";

    private static final Long EXPIRE_TIME = 30L;

    /**
     * 邮件消费者
     *
     * @param body
     * @param channel
     * @param deliveryTag
     */
    @RabbitListener(queues = QUEUE_NAME_TRANSACTION)
    @RabbitHandler
    public void orderReceiver(@Payload String body, @Header(AmqpHeaders.CHANNEL) Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) {
        UserVO userVO = null;
        try {
            log.info("ssoConsumer listen body info:{}", body);

            JSONObject obj = JSON.parseObject(body);
            userVO = JSON.toJavaObject(obj, UserVO.class);
            if (null == userVO) {
                log.error("ssoConsumer listen body to userVO error");
                return;
            }
            String result = redisTemplate.opsForValue().getAndSet(DUP_UPDATE_USER_KEY + userVO.getUserId(), body);
            redisTemplate.expire(DUP_UPDATE_USER_KEY + userVO.getUserId(), EXPIRE_TIME, TimeUnit.SECONDS);
            if (body.equals(result)) {
                log.warn("ssoConsumer listen body dup send.......return message");
                return;
            }
            ssoUserUpdateService.upSsoUserInfo(UserInfoDTO.builder().userId(userVO.getUserId()).firstName(userVO.getFirstName()).lastName(userVO.getLastName()).email(userVO.getEmail()).doiStatus(userVO.getActivateEmail()).companyName(userVO.getCompanyName()).build());
            ssoUserUpdateService.ssoInquireBuyerStatus(userVO.getUserId(), userVO.getFirstName(), userVO.getLastName(), userVO.getEmail());
            ssoUserUpdateService.ssoInquirySupplierStatus(userVO.getUserId(), userVO.getFirstName(), userVO.getLastName(), userVO.getEmail());
            redisTemplate.delete(DUP_UPDATE_USER_KEY + userVO.getUserId());
            log.info("ssoConsumer update sso user success");
        } catch (Exception e) {
            log.error("update sso user fail, sso user data = {}, sender email exception = {}", body, e.getMessage());
        } finally {
            try {
                channel.basicAck(deliveryTag, true);
            } catch (IOException e) {
                log.error("update sso user fail, channel.basicAck exception = {}", e.getMessage());
            }
        }
    }
}
