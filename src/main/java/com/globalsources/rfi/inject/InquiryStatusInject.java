package com.globalsources.rfi.inject;

/**
 * <AUTHOR>
 * @since 2022/11/1
 */

import com.globalsources.rfi.agg.dto.inquiry.InquiryStatusDTO;
import com.globalsources.rfi.annotation.InquiryStatus;
import com.globalsources.rfi.data.entity.InquiryAllEntity;
import com.globalsources.rfi.data.entity.InquiryAllItemEntity;
import com.globalsources.rfi.service.InquiryStatusService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.util.StopWatch;

import java.lang.reflect.Method;
import java.util.Objects;

@Slf4j
@EnableAspectJAutoProxy
@Aspect
public class InquiryStatusInject {

    @Autowired
    private InquiryStatusService inquiryStatusService;

    @AfterReturning("@annotation(com.globalsources.rfi.annotation.InquiryStatus)")
    public void afterReturning(JoinPoint joinPoint) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();
        try {
            saveInquiryStatus(method, args);
        } catch (IllegalArgumentException e) {
            log.error("After Returning fail. e: " + e.getMessage());
        }
    }

    private void saveInquiryStatus(Method method, Object[] args) {
        StopWatch stopWatch = new StopWatch("saveInquiryStatus");
        stopWatch.start();
        try {
            InquiryStatus annotation = method.getAnnotation(InquiryStatus.class);
            String reviewType = annotation.reviewType();
            String status = annotation.status();

            String inquiryId = null;
            String threadId = null;
            Long userId = null;
            String email = null;
            Boolean upsellFlag = null;
            InquiryStatusDTO statusDTO = null;
            for (Object object : args) {
                if (object instanceof InquiryAllEntity) {
                    InquiryAllEntity inquireEntity = (InquiryAllEntity) object;
                    inquiryId = inquireEntity.getInquiryId();
                    userId = inquireEntity.getBuyerId();
                    email = inquireEntity.getEmailAddr();
                    upsellFlag = inquireEntity.getUpsellFlag();
                } else if (object instanceof InquiryAllItemEntity) {
                    InquiryAllItemEntity itemEntity = (InquiryAllItemEntity) object;
                    threadId = itemEntity.getThreadId();
                } else if (object instanceof InquiryStatusDTO) {
                    statusDTO = (InquiryStatusDTO) object;
                }
            }

            if (Objects.isNull(statusDTO) && StringUtils.isNotBlank(inquiryId) && StringUtils.isNotBlank(threadId) && Objects.nonNull(userId)) {
                //记录状态
                statusDTO = InquiryStatusDTO.builder()
                        .inquiryId(inquiryId)
                        .threadId(threadId)
                        .userId(userId)
                        .status(status)
                        .reviewType(reviewType)
                        .email(email)
                        .upsellFlag(upsellFlag)
                        .build();
            }

            if (Objects.nonNull(statusDTO)) {
                inquiryStatusService.saveInquiryStatus(statusDTO);
            }

        } catch (Exception e) {
            log.error("save Inquiry Status fail. e: " + e.getMessage());
        }
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
    }
}
