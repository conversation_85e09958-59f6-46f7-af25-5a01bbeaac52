/**
 * <a>Title: SupplierController </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/7/12-20:06
 */
package com.globalsources.rfi.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.globalsources.agg.supplier.api.feign.SupplierRoleAggFeign;
import com.globalsources.awesome.logging.annotation.AwesomeLog;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfi.agg.core.vo.InquirySupplierStatusVO;
import com.globalsources.rfi.agg.dto.inquiry.InquiryDeleteDTO;
import com.globalsources.rfi.agg.dto.inquiry.SupplierInquiryListExportDTO;
import com.globalsources.rfi.agg.request.*;
import com.globalsources.rfi.agg.request.message.InquiryMessageListDTO;
import com.globalsources.rfi.agg.request.supplier.InquiryTypeListDTO;
import com.globalsources.rfi.agg.request.supplier.SupplierReassignDTO;
import com.globalsources.rfi.agg.request.supplier.SupplierStatusCountDTO;
import com.globalsources.rfi.agg.response.*;
import com.globalsources.rfi.agg.response.detail.InquiryBuyerInfo;
import com.globalsources.rfi.agg.response.detail.InquiryMessageVO;
import com.globalsources.rfi.agg.response.detail.InquiryReplyDetailChatVO;
import com.globalsources.rfi.agg.response.detail.SupplierInquiryDetailVO;
import com.globalsources.rfi.agg.response.rfi.RfiSupplierDetailAggVO;
import com.globalsources.rfi.agg.response.rfi.SupplierInquiryListDownloadVO;
import com.globalsources.rfi.data.dao.InquirySupplierStatusDao;
import com.globalsources.rfi.data.entity.InquirySupplierStatusEntity;
import com.globalsources.rfi.service.SupplierInquiryService;
import com.globalsources.rfx.util.ContentReplaceUtil;
import com.globalsources.awesome.logging.annotation.Logging;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.hint.HintManager;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@RestController
@RequestMapping("/supplier")
@Slf4j
@Api(tags = "supplier询盘")
public class InquirySupplierController {
    @Autowired
    private SupplierInquiryService supplierInquiryService;

    private final ExecutorService executorService=Executors.newFixedThreadPool(15);
    @Autowired
    private InquirySupplierStatusDao inquirySupplierStatusDao;

    @Autowired
    private SupplierRoleAggFeign supplierRoleAggFeign;

    @Value(value = "${limit.del.day:90}")
    private Integer limitDelDayRfi;

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/inquiry-list/{supplierId}/{supplierUserId}/{roleType}")
    @ApiOperation(value = "询盘列表")
    @Logging(businessId = "#supplierUserId", businessType = "AGG-S10025", errorTag = "RFI-AGG-S10025")
    public PageResult<SupplierInquiryVO> inquiryList(@RequestBody SupplierInquireDTO supplierInquireDTO, @PathVariable Long supplierId, @PathVariable Long supplierUserId, @PathVariable Long roleType) {
        return supplierInquiryService.inquiryList(supplierInquireDTO, supplierId, supplierUserId, roleType);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/v2/inquiry-list-download")
    @ApiOperation(value = "询盘列表下载V2")
    Result<Boolean> inquiryListExportV2(@RequestBody SupplierInquiryListExportDTO dto){
        executorService.submit(()->supplierInquiryService.inquiryDownloadListV2(dto));
        return Result.success();
    }

    @AwesomeLog(enableMethodResultLog = false)
    @GetMapping(value = "/inquiry-status-count/{supplierId}/{supplierUserId}")
    @ApiOperation(value = "卖家询盘状态统计")
    @Logging(businessId = "#supplierUserId", businessType = "AGG-S10026", errorTag = "RFI-AGG-S10026")
    @Deprecated
    public Result<SupplierInquiryListTotalVO> inquiryStatusCount(@PathVariable Long supplierId, @PathVariable Long supplierUserId, @RequestParam Boolean suppAdminFlag, @RequestParam Integer tabType) {
        SupplierInquiryListTotalVO result = supplierInquiryService.supplierInquiryListTotal(supplierId, supplierUserId, suppAdminFlag, tabType);
        log.info("inquiryStatusCount supplierId = {}, result = {}", supplierId, result);
        return Result.success();

    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/v1/supplier-status-count")
    @ApiOperation(value = "供应商列表统计")
    @Logging(businessId = "#supplierStatusCountDTO.supplierUserId", businessType = "AGG-S10027", errorTag = "RFI-AGG-S10027")
    public Result<SupplierInquiryListTotalVO> supplierStatusCount(@RequestBody SupplierStatusCountDTO supplierStatusCountDTO) throws ExecutionException, InterruptedException {
        Result<SupplierInquiryListTotalVO> result = supplierInquiryService.supplierStatusCount(supplierStatusCountDTO);
        log.info("supplierStatusCount dto = {},result:{}", supplierStatusCountDTO,result);
        return result;
    }

    @PostMapping(value = "/v1/supplier-count")
    @ApiOperation(value = "卖家询盘总数统计")
    @Logging(businessId = "#supplierStatusCountDTO.supplierUserId", businessType = "AGG-S10028", errorTag = "RFI-AGG-S10028")
    public Result<SupplierInquiryTotalVO> supplierCount(@RequestBody SupplierStatusCountDTO supplierStatusCountDTO) {
        return supplierInquiryService.supplierCount(supplierStatusCountDTO);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @GetMapping(value = "/inquiry-count/{supplierId}/{supplierUserId}")
    @ApiOperation(value = "卖家询盘总数统计")
    @Logging(businessId = "#supplierUserId", businessType = "AGG-S10029", errorTag = "RFI-AGG-S10029")
    @Deprecated
    public Result<SupplierInquiryTotalVO> inquiryCount(@PathVariable Long supplierId, @PathVariable Long supplierUserId) {
        SupplierInquiryTotalVO result = supplierInquiryService.supplierInquiryTotal(supplierId, supplierUserId);
        log.info("inquiryCount supplierId = {},result:{}", supplierId,result);
        return Result.success(result);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/inquiry-del")
    @ApiOperation(value = "询盘删除")
    @Logging(businessId = "#dto.userId", businessType = "AGG-S10030", errorTag = "RFI-AGG-S10030")
    public Result<Integer> inquiryDel(@RequestBody InquiryDeleteDTO dto) {
        Integer result = supplierInquiryService.inquiryDel(dto);
        log.info("inquiryDel dto:{},result:{}",dto,result);
        return Result.success(result);
    }

    @ApiOperation(value = "卖家垃圾询盘")
    @PostMapping(value = "/inquiry-rubbish/{supplierId}/{supplierUserId}")
    @Logging(businessId = "#supplierUserId", businessType = "AGG-S10032", errorTag = "RFI-AGG-S10032")
    public Result<Integer> inquiryRubbish(@PathVariable Long supplierId, @PathVariable Long supplierUserId, @RequestBody IdsVO ids) {
        log.info("inquiryRubbish supplierId:{},inquiryIds:{},userId:{}",supplierId,ids.getInquiryIds(),supplierUserId);
        Integer result = supplierInquiryService.inquiryRubbish(supplierId, supplierUserId, ids);
        log.info("inquiryDel supplierId:{},inquiryIds:{},userId:{},result:{}",supplierId,ids.getInquiryIds(),supplierUserId,result);
        return Result.success(supplierInquiryService.inquiryRubbish(supplierId, supplierUserId, ids));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @GetMapping(value = "/inquiry-detail/{inquiryId}")
    @ApiOperation(value = "询盘详情")
    @Logging(businessId = "#inquiryId", businessType = "AGG-S10033", errorTag = "RFI-AGG-S10033")
    public Result<InquiryReplyDetailChatVO> inquiryDetail(@RequestBody UserVO userVO, @PathVariable String inquiryId) {
        return supplierInquiryService.inquiryDetail(userVO, inquiryId);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/v2/inquiry-detail")
    @ApiOperation(value = "询盘详情")
    @Logging(businessId = "#inquiryDetailDTO.inquiryId", businessType = "AGG-S10033", errorTag = "RFI-AGG-S10033")
    public Result<SupplierInquiryDetailVO> inquiryDetailV2(@RequestBody InquiryDetailDTO inquiryDetailDTO) {
           return Result.success(supplierInquiryService.inquiryDetailV2(inquiryDetailDTO));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/v2/inquiry-message-list")
    @ApiOperation(value = "询盘消息分页列表")
    public Result<PageResult<InquiryMessageVO>> getInquiryMessagePageList(@RequestBody InquiryMessageListDTO dto) {
            return Result.success(supplierInquiryService.getInquiryMessagePageList(dto));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @GetMapping(value = "/v1/inquiry-buyer-info")
    @ApiOperation(value = "询盘详情买家信息")
    public Result<InquiryBuyerInfo> inquiryBuyerInfo(@RequestParam String inquiryId) {
        try{
            return Result.success(supplierInquiryService.getInquiryBuyerInfo(inquiryId));
        }catch (BusinessException be){
            log.warn("inquiryBuyerInfo inquiryId:{},error:{}",inquiryId, JSON.toJSONString(be));
            return Result.failed(be.getCode(),be.getMessage());
        }
    }

    @AwesomeLog(enableMethodResultLog = false)
    @GetMapping(value = "/app/app-inquiry-detail/{inquiryId}")
    @ApiOperation(value = "supplier app询盘详情", notes = "app")
    @Logging(businessId = "#inquiryId", businessType = "AGG-S10034", errorTag = "RFI-AGG-S10034")
    public Result<APPInquiryReplyChatVO> appInquiryDetail(@RequestBody UserVO userVO, @PathVariable("inquiryId") String inquiryId) {
        return supplierInquiryService.appInquiryDetail(inquiryId, userVO);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/inquiry-reply")
    @ApiOperation(value = "询盘回复")
    @Logging(businessType = "AGG-S10035", errorTag = "RFI-AGG-S10035")
    public Result<String> inquiryReply(@RequestBody InquiryChatAGGDTO inquiryChatAGGDTO) {
        if(Objects.nonNull(inquiryChatAGGDTO.getInquiryChatDTO())){
            inquiryChatAGGDTO.getInquiryChatDTO().setSubject(ContentReplaceUtil.filterHtmlInjectionContent(inquiryChatAGGDTO.getInquiryChatDTO().getSubject()));
            inquiryChatAGGDTO.getInquiryChatDTO().setMessage(ContentReplaceUtil.filterHtmlInjectionContent(inquiryChatAGGDTO.getInquiryChatDTO().getMessage()));
        }
        return supplierInquiryService.inquiryReply(inquiryChatAGGDTO, false);
    }

    public static void main(String[] args) {
        String a ="<p> <a href=\"https://gssc.staging.globalsources.com/billPayOuter/corePlatform?txid=E81052\" target=\"_blank\">https://flowmore.pingpongx.com/billPayOuter/corePlatform?txid=E81052</a> </p>";
        String b ="<p> <a href=\"https://gssc.staging.globalsources.com/rfi/inquirydetails?page=1\" target=\"_blank\">https://gssc.staging.globalsources.com/rfi/inquirydetails?pageNum=1</a> </p>";
        //String c ="<p> <a href=\"https://gssc.staging.globalsources.com/billPayOuter/corePlatform?page=1\" target=\"_blank\">https://gssc.staging.globalsources.com/billPayOuter/corePlatform?pageNum=1</a> </p>";
        String c ="<p> <a href=\"https://gssc.staging.globalsources.com/billPayOuter/corePlatform?page=1\" target=\"_blank\">https://gssc.staging.globalsources.com/billPayOuter/corePlatform?pageNum=1</a> </p>";
        String d  ="<p> <a href=\"https://gssc.staging.globalsources.com/rfi/corePlatform?page=1\" target=\"_blank\">https://gssc.staging.globalsources.com/rfi/corePlatform?pageNum=1</a> </p>";

        //ContentReplaceUtil.filterHtmlInjectionContent(a);
        //ContentReplaceUtil.filterHtmlInjectionContent(b);
        ContentReplaceUtil.filterHtmlInjectionContent(c);
        ContentReplaceUtil.filterHtmlInjectionContent(d);

    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/offline-reply")
    @ApiOperation(value = "供应商离线回复")
    @Logging(businessType = "AGG-S10036", errorTag = "RFI-AGG-S10036")
    public Result<String> offlineReply(@RequestBody InquiryChatAGGDTO inquiryChatAGGDTO) {
        return supplierInquiryService.inquiryReply(inquiryChatAGGDTO, true);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "供应商最近收到的询盘数", notes = "默认7天, app")
    @GetMapping(value = "/inquiry-count/recent/{supplierId}")
    @Logging(businessId = "#supplierUserId", businessType = "AGG-S10037", errorTag = "RFI-AGG-S10037")
    @Deprecated
    public Result<Integer> supplierRecentInquiryTotal(@PathVariable("supplierId") Long supplierId,
                                                      @RequestParam(value = "supplierUserId", required = false) Long supplierUserId,
                                                      @RequestParam("recentDays") Integer recentDays) {
        Integer result = supplierInquiryService.supplierRecentInquireTotalByOrgId(supplierId, supplierUserId, recentDays);
        log.info("supplierRecentInquiryTotal supplierId:{},result:{}",supplierId,result);
        return Result.success(result);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "供应商未读询盘数", notes = "一对一询盘，一对多询盘，展会询盘, app")
    @GetMapping(value = "/inquiry/no-read/{supplierId}/{supplierUserId}")
    @Logging(businessId = "#supplierUserId", businessType = "AGG-S10038", errorTag = "RFI-AGG-S10038")
    public Result<SupplierInquiryTotalVO> supplierNoReadInquiryStatistics(@RequestBody UserVO userVO, @PathVariable("supplierId") Long supplierId,
                                                                          @PathVariable("supplierUserId") Long supplierUserId) {
        Result<SupplierInquiryTotalVO> result = supplierInquiryService.supplierNoReadInquiryStatistics(supplierId, supplierUserId, userVO);
        log.info("supplierNoReadInquiryStatistics supplierId:{},result:{}",supplierId,result);
        return result;
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "供应商未读询盘总数", notes = "未读询盘总数")
    @GetMapping(value = "/inquiry/no-read/total/{supplierId}")
    @Logging(businessId = "#supplierUserId", businessType = "AGG-S10039", errorTag = "RFI-AGG-S10039")
    public Result<Integer> supplierNoReadInquiryCnt(@PathVariable("supplierId") Long supplierId,
                                                    @RequestParam(value = "supplierUserId") Long supplierUserId,
                                                    @RequestParam("isMaster") Boolean isMaster) {
        Integer total = supplierInquiryService.supplierNoReadInquiryTotal(supplierId, supplierUserId, isMaster);
        log.info("supplierNoReadInquiryCnt supplierId:{},total:{}",supplierId,total);
        return Result.success(total);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "供应商回复询盘总数数", notes = "供应商回复总数, app")
    @GetMapping(value = "/inquiry/no-reply/total")
    @Logging(businessId = "#userVO.userId", businessType = "AGG-S10040", errorTag = "RFI-AGG-S10040")
    public Result<Integer> getSupplierNoReplyInquiryTotal(@RequestParam(required = false) Integer tabType, @RequestBody UserVO userVO) {
        Result<Integer> result = supplierInquiryService.getSupplierNoReplyInquiryTotal(tabType, userVO);
        log.info("getSupplierNoReplyInquiryTotal tabType:{},result:{}",tabType,result);
        return result;
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "卖家已回复标识")
    @PostMapping("/mark-reply/{userId}/{threadId}")
    @Logging(businessId = "#threadId", businessType = "AGG-S10042", errorTag = "RFI-AGG-S10042")
    public Result<Integer> markReply(@PathVariable Long userId, @PathVariable String threadId) {
        Integer result = supplierInquiryService.markReply(userId, threadId);
        log.info("markReply threadId:{}, userId:{},result:{}",threadId,userId,result);
        return Result.success(result);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "询盘供应商信息")
    @GetMapping(value = "/inquiry-supplier-info/{threadId}")
    @Logging(businessId = "#threadId", businessType = "AGG-S10043", errorTag = "RFI-AGG-S10043")
    public Result<RfiSupplierDetailAggVO> inquirySupplierInfo(@PathVariable String threadId) {
        return Result.success(supplierInquiryService.inquirySupplierInfo(threadId));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "提供给选择公司效验供应商用户询盘", notes = "提供给选择公司界面接口")
    @GetMapping(value = "/select-rfi-supplier-info/{supplierId}/{supplierUserId}/{threadId}")
    @Logging(businessId = "#supplierUserId", businessType = "AGG-S10044", errorTag = "RFI-AGG-S10044")
    public Result<Boolean> selectRfiSupplierInfo(@PathVariable Long supplierId, @PathVariable Long supplierUserId, @PathVariable String threadId) {

        InquirySupplierStatusVO inquirySupplierStatusVO = supplierInquiryService.selectRfiSupplierInfo(supplierId, supplierUserId, threadId);
        log.info("selectRfiSupplierInfo supplierId:{},supplierUserId:{},threadId:{},result:{}",supplierId,supplierUserId,threadId,inquirySupplierStatusVO);

        return ObjectUtils.isNotEmpty(inquirySupplierStatusVO) ? Result.success(true) : Result.success(false);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "卖家标记删除", notes = "卖家标记删除", tags = {"Rfi Web卖家侧接口"})
    @PostMapping("/v1/mark-del")
    public Result<String> markDel(@RequestBody InquiryDeleteDTO dto) {
        List<String> threadIds = dto.getThreadIdList();
        Boolean superAdmAccount = dto.getAdminFlag();
        Long supplierUserId = dto.getUserId();
        if (CollectionUtils.isEmpty(threadIds)) {
            return Result.failed();
        }
        if (!Boolean.TRUE.equals(superAdmAccount)) {
            for (String threadId : threadIds) {
                InquirySupplierStatusVO inquirySupplierStatusVO = supplierInquiryService.inquiryStateInfo(threadId);
                if (ObjectUtils.isEmpty(inquirySupplierStatusVO)) {
                    return Result.failed();
                }
                // 只有当前负责人才能标记删除
                if (!inquirySupplierStatusVO.getSupplierUserId().equals(supplierUserId)) {
                    return Result.failed(ResultCode.RfiResultCode.SUPPLIER_RFI_UNAUTHORIZED);
                }
            }
        }
        return threadIds.size() == supplierInquiryService.markDel(supplierUserId, threadIds) ? Result.success() : Result.failed();
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "卖家恢复标记删除", notes = "卖家恢复标记删除", tags = {"Rfi Web卖家侧接口"})
    @PostMapping("/v1/restore-del")
    public Result<String> restoreDel(@RequestBody InquiryDeleteDTO dto) {
        List<String> threadIds = dto.getThreadIdList();
        Boolean superAdmAccount = dto.getAdminFlag();
        Long supplierUserId = dto.getUserId();
        if (CollectionUtils.isEmpty(threadIds)) {
            return Result.failed();
        }
        if (!Boolean.TRUE.equals(superAdmAccount)) {
            for (String threadId : threadIds) {
                InquirySupplierStatusVO inquirySupplierStatusVO = supplierInquiryService.inquiryStateInfo(threadId);
                if (ObjectUtils.isEmpty(inquirySupplierStatusVO)) {
                    return Result.failed();
                }
                //只有当前负责人才能恢复标记删除
                if (!inquirySupplierStatusVO.getSupplierUserId().equals(supplierUserId)) {
                    return Result.failed(ResultCode.RfiResultCode.SUPPLIER_RFI_UNAUTHORIZED);
                }
            }
        }
        return threadIds.size() == supplierInquiryService.restoreDel(supplierUserId, threadIds) ? Result.success() : Result.failed();
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "供应商匹配反馈")
    @GetMapping(value = "/v1/matched-feedback")
    public Result<Boolean> matchedFeedback(@RequestParam String threadId,@RequestParam Boolean feedback){
        return Result.success(supplierInquiryService.matchedFeedback(threadId,feedback));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "卖家rfi--站内通知权限", notes = "卖家rfi--站内通知权限")
    @GetMapping("/v1/in-site-notification")
    public Result<Boolean> inSiteNotification(@RequestParam Long userId, @RequestParam Long supplierId, @RequestParam String inquiryId){
        Boolean result=supplierInquiryService.inSiteNotification(userId,supplierId,inquiryId);
        log.info("inSiteNotification supplierId={},inquiryId = {},userId:{},result:{}", supplierId,inquiryId,userId,result);
        if(Boolean.TRUE.equals(result)){
            return  Result.success(true);
        }
        return  Result.success("此询盘已被重新分配给其他账号!",false);
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/v2/sup-inq-reassign")
    @ApiOperation(value = "重新分配询盘负责人")
    @Logging(businessId = "#dto.supplierUserId", businessType = "AGG-S10023", errorTag = "RFI-AGG-S10023")
    public Result<Integer> supInqReassign(@RequestBody SupplierReassignDTO dto) {
        //s71 安全问题fix, 攻击者随意输入一个userId，可以把询盘分配给任一用户
        //拿到当前登录用户的supplierId，看看目标分配用户是否属于当前供应商
        List<Long> supplierUserIdList = ResultUtil.getData(supplierRoleAggFeign.getUserIdsBySupplierId(dto.getSupplierId(),false));
        if(CollectionUtils.isEmpty(supplierUserIdList) || !supplierUserIdList.contains(dto.getSupplierUserId())){
            log.warn("supInqReassign error,target user wrong, supplierUserIdList : {},dto : {}",supplierUserIdList, dto);
            //目标分配用户与当前登录用户不是同一家公司，恶意分配
            throw new BusinessException(ResultCode.RfiResultCode.RFI_THREAD_REASSIGN);
        }

        Integer count = 0;
        Result<Integer> result = supplierInquiryService.supInqReassign(dto);
        count = ResultUtil.getDataOrElseThrow(result);
        log.info("supInqReassign count:{}",count);
        if (count > 0) {
            executorService.submit(()->supplierInquiryService.sendReassignEmail(dto.getUserId(),  dto.getSupplierUserId(),dto.getSupplierId(),dto.getInquiryIds()));
            log.info("supInqReassign send email succ");
        }
        return result;
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/v1/inquiry-type-list")
    public Result<List<String>> getInquiryTypeList(@RequestBody InquiryTypeListDTO dto) {

        List<String> result = supplierInquiryService.getInquiryTypeList(dto);
        log.info("supplier getInquiryTypeList result:{}",result);
        return Result.success(result);
    }

    @ApiOperation(value = "根据时间限制永久删除标记的rfi", notes = "根据时间限制永久删除标记的rfi", tags = {"Rfi Web卖家侧接口"})
    @PostMapping(value = "/v1/limit-del-day-rfi")
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public Result<String> limitDelDayRfi() {
        Date date=new DateTime().minusDays(limitDelDayRfi).toDate();
        int resultCount=inquirySupplierStatusDao.limitDayDelRfi(date);
        log.info("limitDelDayRfi result count :{}",resultCount);
        return Result.success();
    }

    @AwesomeLog(enableMethodResultLog = false)
    @ApiOperation(value = "卖家询盘关系")
    @GetMapping(value = "/inquiry-state-info/{inquiryId}")
    @Logging(businessId = "#inquiryId",businessType = "CORE-10084", errorTag = "RFI-CORE-10084")
    public Result<InquirySupplierStatusVO> inquiryStateInfo(@PathVariable String inquiryId) {
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.setMasterRouteOnly();
            InquirySupplierStatusEntity supplierInquireStateEntity = inquirySupplierStatusDao.selectOne(new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                    .eq(InquirySupplierStatusEntity::getThreadId, inquiryId));
            log.info("inquiryStateInfo masterRoute result:{}", supplierInquireStateEntity);
            if (ObjectUtils.isNotEmpty(supplierInquireStateEntity)) {
                InquirySupplierStatusVO supplierInquireStateVO = new InquirySupplierStatusVO();
                BeanUtils.copyProperties(supplierInquireStateEntity, supplierInquireStateVO);
                return Result.success(supplierInquireStateVO);
            }
        } catch (Exception e) {
            log.error("inquiryStateInfo error,inquiryId={},error:{}", inquiryId, JSON.toJSONString(e));
            return Result.failed();
        }
        return Result.success();
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/v1/potential-opportunity-list")
    @ApiOperation(value = "潜在商机-询盘列表")
    public Result<PageResult<SupplierPotentialOpportunityVO>> potentialOpportunityList(@RequestBody SupplierPotentialOpportunityQueryDTO potentialOpportunityDTO) {
        return Result.success(supplierInquiryService.potentialOpportunityList(potentialOpportunityDTO));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/v1/potential-opportunity-status-count")
    @ApiOperation(value = "潜在商机-供应商列表统计")
    public Result<SupplierPotentialOpportunityListTotalVO> potentialOpportunityStateCount(@RequestBody SupplierPotentialOpportunityStatusCountDTO potentialOpportunityStateCountDTO) {
        return Result.success(supplierInquiryService.potentialOpportunityStateCount(potentialOpportunityStateCountDTO));
    }

    @AwesomeLog(enableMethodResultLog = false)
    @PostMapping(value = "/v1/mark-potential-opportunity-convert")
    @ApiOperation(value = "潜在商机-标记潜在商机转换完成")
    public Result<Boolean> markPotentialOpportunityConvert(@RequestBody PotentialOpportunityConvertDTO dto) {
        return Result.success(supplierInquiryService.markPotentialOpportunityConvert(dto.getThreadIdList(),dto.getSource()));
    }

}
