package com.globalsources.rfi.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCodeEnum;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.rfi.agg.request.admin.InquiryLogRequestDTO;
import com.globalsources.rfi.agg.request.admin.InquiryLogSaveDTO;
import com.globalsources.rfi.agg.response.console.InquiryLogListVO;
import com.globalsources.rfi.data.dao.InquiryLogDao;
import com.globalsources.rfi.data.entity.InquiryLogEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Objects;

@RestController
@RequestMapping("/log")
@Slf4j
@Api(tags = "rfi log相关")
public class InquiryLogController {

    @Autowired
    private InquiryLogDao logDao;

    @ApiOperation(value = "离线邮件错误log", notes = "离线邮件错误log")
    @PostMapping("/v1/offline-email-log-list")
    public Result<PageResult<InquiryLogListVO>> offlineEmailLogList(@RequestBody InquiryLogRequestDTO dto) {
        log.info("offlineEmailLogList dto:{}", dto);
        if(Objects.isNull(dto)){
            return Result.failed(ResultCodeEnum.PARAMETER_VALIDATION_FAILED);
        }
        if(StringUtils.isBlank(dto.getBusinessId())){
            return Result.success(new PageResult<>(Long.valueOf(dto.getPageNum()), Long.valueOf(dto.getPageSize()),0L,0L, Collections.emptyList()));
        }

        IPage<InquiryLogListVO> pageResult = logDao.getOfflineEmailLogList(new Page<>(dto.getPageNum(), dto.getPageSize()),dto.getBusinessId());
        return Result.success(PageResult.restPage(pageResult.getRecords(), new Page<InquiryLogListVO>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal())));
    }

    @ApiOperation(value = "save", notes = "save")
    @PostMapping("/v1/save")
    public Result<Boolean> save(@RequestBody InquiryLogSaveDTO dto) {
        return Result.success(logDao.insert(OrikaMapperUtil.coverObject(dto, InquiryLogEntity.class))>0);
    }

}
