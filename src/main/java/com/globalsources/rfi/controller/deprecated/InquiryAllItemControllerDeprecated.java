package com.globalsources.rfi.controller.deprecated;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.BeanUtil;

import com.globalsources.rfi.agg.core.dto.InquireAllItemDTO;
import com.globalsources.rfi.agg.core.vo.InquireAllItemVO;
import com.globalsources.rfi.data.dao.InquiryAllItemDao;
import com.globalsources.rfi.data.dao.InquiryBuyerStatusDao;
import com.globalsources.rfi.data.entity.InquiryAllItemEntity;
import com.globalsources.rfi.data.entity.InquiryBuyerStatusEntity;
import com.globalsources.rfi.service.InquiryAllItemService;
import com.google.common.collect.Lists;
import com.globalsources.awesome.logging.annotation.Logging;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Chen
 * @date 2021/8/2 20:26
 * 迁移到InquiryController
 */
@RestController
@RequestMapping("/all-item")
@Slf4j
@Api(tags = "inquire_all_item 表操作")
public class InquiryAllItemControllerDeprecated {

    @Autowired
    private InquiryAllItemService inquireAllItemService;

    @Autowired
    private InquiryBuyerStatusDao inquiryBuyerStatusDao;

    @Autowired
    private InquiryAllItemDao inquiryAllItemDao;

    @ApiOperation(value = "保存询盘信息")
    @PostMapping(value = "/v1/save-list-item")
    @Logging(businessType = "CORE-10036", errorTag = "RFI-CORE-10036")
    public Result<Boolean> saveInquiryAllItem(@RequestBody InquireAllItemDTO inquireAllItemDTOS) {
        log.info("InquireAllItemController saveInquiryAllItem dto:{}",inquireAllItemDTOS);
        InquiryAllItemEntity inquiryAllItemEntity = new InquiryAllItemEntity();
        BeanUtils.copyProperties(inquireAllItemDTOS, inquiryAllItemEntity);
        return Result.success(inquireAllItemService.save(inquiryAllItemEntity));
    }

    @ApiOperation(value = "判断thread是否已建立买卖家关联")
    @GetMapping(value = "/v1/has-buyer-supplier-link")
    @Logging(businessId = "#threadId", businessType = "CORE-10037", errorTag = "RFI-CORE-10037")
    public Result<Boolean> hasBuyerSupplierLink(@RequestParam String threadId) {
        return Result.success(inquiryBuyerStatusDao.selectCount(new LambdaQueryWrapper<InquiryBuyerStatusEntity>().eq(InquiryBuyerStatusEntity::getThreadId, threadId)) > 1);
    }

    @ApiOperation(value = "真的通过inquiryId查询InquireAllItem")
    @GetMapping(value = "/v2/get-items-by-inquiry-id")
    @Logging(businessId = "#inquiryId", businessType = "CORE-10038", errorTag = "RFI-CORE-10038")
    public Result<List<InquireAllItemVO>> getInquireAllItemListByInquiryId(@RequestParam String inquiryId) {
        List<InquiryAllItemEntity> list = inquiryAllItemDao.selectList(new LambdaQueryWrapper<InquiryAllItemEntity>().eq(InquiryAllItemEntity::getInquiryId, inquiryId));

        list = Optional.ofNullable(list).orElse(Lists.newArrayList());
        List<InquireAllItemVO> dtoList = BeanUtil.copyPropertiesBatch(list, InquireAllItemVO.class);
        return Result.success(dtoList);
    }

}
