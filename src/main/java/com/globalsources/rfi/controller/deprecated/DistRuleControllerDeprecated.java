package com.globalsources.rfi.controller.deprecated;

import com.globalsources.framework.result.Result;
import com.globalsources.rfi.agg.request.DistRuleReqDTO;
import com.globalsources.rfi.agg.response.DistRuleVO;
import com.globalsources.rfi.service.DistRuleService;
import com.globalsources.awesome.logging.annotation.Logging;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <a>Title: DistRuleController </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/8/9-14:56
 */
@Slf4j
@RestController
@RequestMapping("/dist-rule")
@Api(tags = "路由规则")
public class DistRuleControllerDeprecated {

    @Resource
    private DistRuleService distRuleService;


    @ApiOperation("根据产品ID， 获取路由目标供应商负责人，返回负责人user id ")
    @PostMapping("/get-target-userid")
    @Logging(businessId = "#reqDTO.buyerId", businessType = "AGG-B10015", errorTag = "RFI-AGG-B10015")
    public Result<Long> getTargetUserId(@RequestBody @ApiParam(name = "ProductId list", value = "传入JSON数据", required = true) DistRuleReqDTO reqDTO ){

        log.info("DistRuleController getTargetUserId reqDto:{}",reqDTO);

        Long buyerId = reqDTO.getBuyerId();
        Long supplierId = reqDTO.getSupplierId();
        Long productId = 0L;
        if (!CollectionUtils.isEmpty(reqDTO.getProductIdList())) {
            productId = distRuleService.getTargetProductId(reqDTO.getProductIdList());
        }
        Long userId = distRuleService.getTargetUserIdWithCache(supplierId, productId, buyerId).getUserId();
        log.info("DistRuleController getTargetUserId :{}" ,userId );
        if (Objects.isNull(userId) || userId == 0L) {
            log.error("DistRuleController getTargetUserId with null,reqDto:{},result:{}" ,reqDTO,userId);
            return Result.failed("get rule result fail");
        }

        return Result.success(userId);
    }

    @ApiOperation("根据产品ID， 获取路由目标供应商负责人，返回负责人user id ")
    @PostMapping("/get-target-userid-and-type")
    @Logging(businessId = "#reqDTO.buyerId", businessType = "AGG-B10016", errorTag = "RFI-AGG-B10016")
    public Result<DistRuleVO> getTargetUserIdWithRouteType(@RequestBody @ApiParam(name = "ProductId list", value = "传入JSON数据", required = true) DistRuleReqDTO reqDTO ){
        log.info("DistRuleController getTargetUserIdWithRouteType reqDto:{}",reqDTO);

        Long buyerId = reqDTO.getBuyerId();
        Long supplierId = reqDTO.getSupplierId();
        Long productId = 0L;
        if (!CollectionUtils.isEmpty(reqDTO.getProductIdList())) {
            productId = distRuleService.getTargetProductId(reqDTO.getProductIdList());
        }
        DistRuleVO result = distRuleService.getTargetUserIdWithCache(supplierId, productId, buyerId);
        log.info("DistRuleController getTargetUserIdWithRouteType :{}" ,result );
        if (Objects.isNull(result) || Objects.isNull(result.getUserId())||result.getUserId() == 0L) {
            log.error("DistRuleController getTargetUserIdWithRouteType with null,reqDto:{},result:{}" ,reqDTO,result);
            return Result.failed("get rule result fail");
        }
        return Result.success(result);
    }

}
