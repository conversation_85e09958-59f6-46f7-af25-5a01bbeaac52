/**
 * <a>Title: BuyerInquiryController </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/7/13-16:23
 */
package com.globalsources.rfi.controller.deprecated;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.globalsources.agg.supplier.api.model.dto.SupplierCommonInfoDTO;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.rfi.agg.response.InquiryProductDetailVO;
import com.globalsources.rfi.agg.response.InquirySessionVO;
import com.globalsources.rfx.service.IProductService;
import com.globalsources.rfx.service.ISupplierService;
import com.globalsources.search.api.vo.ProductDetailVo;
import com.globalsources.awesome.logging.annotation.Logging;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/buyer-inquiry")
@Slf4j
@Api(tags = "buyer询盘")
public class BuyerInquiryControllerDeprecated {

    @Autowired
    private ISupplierService supplierService;

    @Autowired
    private IProductService productService;

    @Value("${gsol.tmx.orgId}")
    public String orgId;

    @GetMapping(value = "/inquiry-session")
    @ApiOperation(value = "提供给TMX参数(sessionId,orgId)")
    @Logging(businessType = "AGG-B10013", errorTag = "RFI-AGG-B10013")
    public Result<InquirySessionVO> inquirySession() {
        Snowflake snowflake = IdUtil.createSnowflake(1, 1);
        InquirySessionVO inquirySessionVO = InquirySessionVO.builder()
                .sessionId(snowflake.nextId())
                .orgId(orgId).build();
        return Result.success(inquirySessionVO);
    }

    @GetMapping(value = "/inquiry-prod-detail")
    @ApiOperation(value = "一对一询盘产品信息")
    @Logging(businessId = "#productId", businessType = "AGG-B10014", errorTag = "RFI-AGG-B10014")
    public Result<InquiryProductDetailVO> inquiryProdDetail(@PathVariable Long productId) {
        InquiryProductDetailVO inquiryProductDetailVO = new InquiryProductDetailVO();
        ProductDetailVo productDetailVo = productService.getProductInfo(productId);
        if (ObjectUtils.isEmpty(productDetailVo)) {
            log.info("inquiry prod detail is empty = {}", productDetailVo);
            return Result.failed(ResultCode.RfiResultCode.RFI_PRODUCT_CODE.getCode());
        }
        SupplierCommonInfoDTO supplierMainInfoDto = supplierService.getSupplierCommonInfo(productDetailVo.getProduct().getOrgId());
        if (ObjectUtils.isNotEmpty(productDetailVo)) {
            inquiryProductDetailVO.setProductId(productId);
            inquiryProductDetailVO.setProductTitle(productDetailVo.getProductInfoMultiLan().getProductName());
            inquiryProductDetailVO.setProductDesc(productDetailVo.getProductInfoMultiLan().getProductDescription());
            inquiryProductDetailVO.setProductImage(productDetailVo.getProductPrimaryImage());
            inquiryProductDetailVO.setCategoryName(productDetailVo.getCategoryInfo().getL4CategoryVo().getCategoryName());
            inquiryProductDetailVO.setCompanyName(productDetailVo.getSupplierSnippetInfo().getCompanyName());
            inquiryProductDetailVO.setQuantity(productDetailVo.getProduct().getMinOrderQuantity());
            inquiryProductDetailVO.setUnit(productDetailVo.getProduct().getMinOrderUnit());
            inquiryProductDetailVO.setCategoryId(productDetailVo.getProductCategory().getCategoryId());
            inquiryProductDetailVO.setOrgId(productDetailVo.getProduct().getOrgId());
        }
        if (ObjectUtils.isNotEmpty(supplierMainInfoDto)) {
            inquiryProductDetailVO.setMaxContractLevel(supplierMainInfoDto.getMaxContractLevel());
            inquiryProductDetailVO.setO2oFlag(supplierMainInfoDto.getO2oFlag());
            inquiryProductDetailVO.setMemberSince(supplierMainInfoDto.getMemberSince());
            inquiryProductDetailVO.setVerifiedManufacturerFlag(supplierMainInfoDto.getVerifiedManufacturerFlag());
            inquiryProductDetailVO.setVerifiedSupplierFlag(supplierMainInfoDto.getVerifiedSupplierFlag());
        }
        return Result.success(inquiryProductDetailVO);
    }
}
