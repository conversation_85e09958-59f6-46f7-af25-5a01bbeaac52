/**
 * <a>Title: BuyerController </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/7/12-20:06
 */
package com.globalsources.rfi.controller;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.agg.admin.api.feign.RfqBlackListFeign;
import com.globalsources.agg.supplier.api.feign.SupplierSeoAggFeign;
import com.globalsources.agg.supplier.api.model.dto.supplier.SuppNameAggDTO;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.page.BasePage;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.result.ResultCodeEnum;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.product.agg.api.vo.ProductLiteVO;
import com.globalsources.rfi.agg.dto.inquiry.InquiryUpsellMatchDTO;
import com.globalsources.rfi.agg.dto.product.FilterProductDTO;
import com.globalsources.rfi.agg.core.dto.match.InquiryMatchResultDTO;
import com.globalsources.rfi.agg.core.dto.rfi.L4UpsellDTO;
import com.globalsources.rfi.agg.core.vo.InquireAllEmailChatVO;
import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import com.globalsources.rfi.agg.request.BuyerInquiryListDTO;
import com.globalsources.rfi.agg.request.InquiryPinDTO;
import com.globalsources.rfi.agg.request.InquiryChatAGGDTO;
import com.globalsources.rfi.agg.request.InquiryDetailDTO;
import com.globalsources.rfi.agg.request.message.InquiryMessageListDTO;
import com.globalsources.rfi.agg.response.*;
import com.globalsources.rfi.agg.response.detail.BuyerInquiryDetailVO;
import com.globalsources.rfi.agg.response.detail.InquiryCompanyVO;
import com.globalsources.rfi.agg.response.detail.InquiryMessageVO;
import com.globalsources.rfi.agg.response.detail.InquiryReplyDetailChatVO;
import com.globalsources.rfi.agg.response.product.MatchProductInfo;
import com.globalsources.rfi.agg.response.product.MatchRfiThreadVO;
import com.globalsources.rfi.agg.response.rfi.ProductInfoVO;
import com.globalsources.rfi.data.dao.InquiryAllDao;
import com.globalsources.rfi.data.dao.InquiryAllEmailChatDao;
import com.globalsources.rfi.data.dao.InquiryBuyerStatusDao;
import com.globalsources.rfi.data.entity.InquiryBuyerStatusEntity;
import com.globalsources.rfi.service.BuyerInquiryService;
import com.globalsources.rfi.service.SearchUpsellService;
import com.globalsources.rfi.service.InquiryMatchResultService;
import com.globalsources.rfi.service.L4UpsellService;
import com.globalsources.rfi.utils.GoogleUtil;
import com.globalsources.rfi.utils.InquirySeqIdUtils;
import com.globalsources.rfi.utils.TmxBlackUtil;
import com.globalsources.rfx.service.IProductService;
import com.globalsources.rfx.service.ISupplierService;
import com.globalsources.rfx.util.ContentReplaceUtil;
import com.globalsources.awesome.logging.annotation.Logging;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/buyer")
@Slf4j
@Api(tags = "buyer 询盘")
public class InquiryBuyerController {

    @Autowired
    private GoogleUtil googleUtil;

    @Autowired
    private TmxBlackUtil tmxBlackUtil;

    @Autowired
    private BuyerInquiryService buyerInquiryService;

    @Autowired
    private SupplierSeoAggFeign supplierSeoAggFeign;

    @Autowired
    private RfqBlackListFeign rfqBlackListFeign;

    @Value("${admin.console.reviewEmailAddr:<EMAIL>}")
    private String reviewEmailAddr;

    @Value("${admin.console.reviewUserId:1309000172450}")
    private Long reviewUserId;

    @Autowired
    private SearchUpsellService searchUpsellService;

    @Resource
    private L4UpsellService l4UpsellService;

    @Autowired
    private InquirySeqIdUtils inquirySeqIdUtils;

    @Autowired
    private InquiryMatchResultService inquiryMatchResultService;

    @Autowired
    private IProductService productService;

    @Autowired
    private ISupplierService supplierService;

    @Autowired
    private InquiryBuyerStatusDao inquiryBuyerStatusDao;

    @Autowired
    private InquiryAllDao inquiryAllDao;

    @Autowired
    private InquiryAllEmailChatDao inquiryAllEmailChatDao;
    private static final String MESSAGE = "I sent you attachments.";

    @Value("${rfi.upsell.not.hot.count:201}")
    private Integer categoryCount;

    @Value("${gsol.tmx.orgId}")
    public String orgId;

    @ApiOperation(value = "RFI列表状态统计")
    @GetMapping(value = "/app/inquiry-list-total/{buyerId}")
    @Logging(businessId = "#buyerId", businessType = "AGG-B10001", errorTag = "RFI-AGG-B10001")
    public APPInquireBuyerStatusTotalVO inquiryBuyerListStatusTotal(@PathVariable Long buyerId) {
        APPInquireBuyerStatusTotalVO result = buyerInquiryService.appInquiryBuyerListStatusTotal(buyerId);
        log.info("buyer inquiryBuyerListStatusTotal buyerId:{},result:{}",buyerId,result);
        return result;
    }

    @GetMapping(value = "/app/app-inquiry-detail/{inquiryId}")
    @ApiOperation(value = "询盘详情")
    @Logging(businessId = "#inquiryId", businessType = "AGG-B10002", errorTag = "RFI-AGG-B10002")
    public Result<APPInquiryReplyChatVO> appInquiryDetail(@RequestParam("buyerId")Long buyerId, @PathVariable String inquiryId) {
        log.info("buyer inquiryBuyerListStatusTotal buyerId:{},inquiryId:{}",buyerId,inquiryId);

        return buyerInquiryService.appInquiryDetail(buyerId,inquiryId);
    }

    @GetMapping(value = "/inquiry-session")
    @ApiOperation(value = "提供给TMX参数(sessionId,orgId)")
    @Logging(businessType = "AGG-B10013", errorTag = "RFI-AGG-B10013")
    public Result<InquirySessionVO> inquirySession() {
        Snowflake snowflake = IdUtil.createSnowflake(1, 1);
        InquirySessionVO inquirySessionVO = InquirySessionVO.builder()
                .sessionId(snowflake.nextId())
                .orgId(orgId).build();
        return Result.success(inquirySessionVO);
    }

    @PostMapping(value = "/inquiry-list/{buyerId}")
    @ApiOperation(value = "询盘列表")
    @Logging(businessId = "#buyerId", businessType = "AGG-B10003", errorTag = "RFI-AGG-B10003")
    public PageResult<BuyerInquiryVO> inquiryList(@RequestBody BuyerInquiryListDTO buyerInquiryListDTO, @PathVariable Long buyerId) {
        //异步请求message
        log.info("buyer inquiryList buyerId:{},buyerInquiryListDTO:{}",buyerId,buyerInquiryListDTO);
        return buyerInquiryService.inquiryList(buyerInquiryListDTO, buyerId);
    }

    @GetMapping(value = "/inquiry-status-count/{buyerId}")
    @ApiOperation(value = "买家询盘状态统计")
    @Logging(businessId = "#buyerId", businessType = "AGG-B10004", errorTag = "RFI-AGG-B10004")
    public Result<BuyerInquiryListTotalVO> inquiryStatusCount(@PathVariable Long buyerId, @RequestParam Integer tabType) {
        return Result.success(buyerInquiryService.inquiryStatusCount(buyerId, tabType));
    }

    @GetMapping(value = "/inquiry-count/{buyerId}")
    @ApiOperation(value = "买家询盘统计")
    @Logging(businessId = "#buyerId", businessType = "AGG-B10005", errorTag = "RFI-AGG-B10005")
    public Result<BuyerInquiryTotalVO> inquiryCount(@PathVariable Long buyerId) {
        BuyerInquiryTotalVO result = buyerInquiryService.inquiryCount(buyerId);
        log.info("buyer inquiryCount buyerId:{},result:{}",buyerId,result);
        return Result.success(result);
    }

    @GetMapping(value = "/inquiry-del/{buyerId}")
    @ApiOperation(value = "询盘删除")
    @Logging(businessId = "#buyerId", businessType = "AGG-B10006", errorTag = "RFI-AGG-B10006")
    public Result<Integer> inquiryDel(@PathVariable Long buyerId, @RequestBody IdsVO ids) {
        log.info("buyer inquiryDel buyerId:{},ids:{}",buyerId,ids);
        return Result.success(buyerInquiryService.delInquiry(buyerId, ids));
    }

    @PostMapping(value = "/inquiry-reply")
    @ApiOperation(value = "询盘回复")
    @Logging(businessType = "AGG-B10007", errorTag = "RFI-AGG-B10007")
    public Result<String> inquiryReply(@RequestBody InquiryChatAGGDTO inquiryChatAGGDTO, @RequestHeader(value = "lang", required = false, defaultValue = "enus") String lang) {
        log.info("buyer inquiryReply inquiryChatAGGDTO:{}",inquiryChatAGGDTO);

        if(Objects.isNull(inquiryChatAGGDTO)){
            return Result.failed(ResultCodeEnum.PARAMETER_VALIDATION_FAILED);
        }
        if(Objects.nonNull(inquiryChatAGGDTO.getInquiryChatDTO())){
            inquiryChatAGGDTO.getInquiryChatDTO().setLang(lang);
            inquiryChatAGGDTO.getInquiryChatDTO().setSubject(ContentReplaceUtil.filterHtmlInjectionContent(inquiryChatAGGDTO.getInquiryChatDTO().getSubject()));
            inquiryChatAGGDTO.getInquiryChatDTO().setMessage(ContentReplaceUtil.filterHtmlInjectionContent(inquiryChatAGGDTO.getInquiryChatDTO().getMessage()));
        }
        //黑名单过滤
        Boolean blackFlag = buyerReplyBlack(inquiryChatAGGDTO.getUserVO().getUserId(), inquiryChatAGGDTO.getInquiryChatDTO().getMessage(),
                inquiryChatAGGDTO.getUserVO().getEmail());
        log.info("buyer inquiryReply buyerId:{},blackFlag:{}",inquiryChatAGGDTO.getUserVO().getUserId(),blackFlag);

        if (Boolean.FALSE.equals(blackFlag)) {
            return buyerInquiryService.inquiryReply(inquiryChatAGGDTO.getInquiryChatDTO(), inquiryChatAGGDTO.getUserVO(),inquiryChatAGGDTO.getSource());
        } else {
            return buyerInquiryService.blackReply(inquiryChatAGGDTO.getInquiryChatDTO(), inquiryChatAGGDTO.getUserVO(),inquiryChatAGGDTO.getSource());
        }
    }

    private Boolean buyerReplyBlack(Long userId, String message, String mail) {
        //admin console 黑名单检测
        log.info("buyer inquiryReply blacklist detection, userId = {}, message = {}, mail = {}", userId, message, mail);
        Boolean flag = rfqBlackListFeign.isBlackListUser(userId, "").getData();
        if (Boolean.TRUE.equals(flag)) {
            return true;
        }

        //tmx 接口检测
        Boolean tmxFlag = tmxBlackUtil.getTmxBlackResult(mail);
        log.info("buyer inquiryReply blacklist detection, tmxFlag {}, mail = {}", tmxFlag, mail);

        if (Boolean.TRUE.equals(tmxFlag)) {
            log.info("buyer inquiryReply add blacklist userId = {}", userId);
            rfqBlackListFeign.add(userId, reviewUserId, reviewEmailAddr);
            return true;
        }
        return false;
    }

    @GetMapping(value = "/inquiry-detail/{inquiryId}")
    @ApiOperation(value = "询盘详情")
    @Logging(businessId = "#inquiryId", businessType = "AGG-B10009", errorTag = "RFI-AGG-B10009")
    public Result<InquiryReplyDetailChatVO> inquiryDetail(@PathVariable String inquiryId, @RequestBody UserVO userVO, @RequestHeader(value = "lang", required = false, defaultValue = "enus") String lang) {
        log.info("buyer inquiryDetail inquiryId:{},lang:{},userVo:{}",inquiryId,lang,userVO);
        return buyerInquiryService.inquiryDetail(inquiryId, userVO, lang);
    }

    @PostMapping(value = "/v2/inquiry-detail")
    @ApiOperation(value = "询盘详情V2")
    @Logging(businessId = "#inquiryDetailDTO.inquiryId", businessType = "AGG-B10009", errorTag = "RFI-AGG-B10009")
    public Result<BuyerInquiryDetailVO> inquiryDetailV2(@RequestBody InquiryDetailDTO inquiryDetailDTO) {
        try{
            log.info("buyer inquiryDetailV2 dto:{}",inquiryDetailDTO);
            return Result.success(buyerInquiryService.inquiryDetailV2(inquiryDetailDTO));
        }catch (BusinessException be){
            return Result.failed(be.getCode(),be.getMessage());
        }
    }

    @GetMapping(value = "/v1/inquiry-supplier-info")
    @ApiOperation(value = "询盘详情supplier信息")
    public Result<InquiryCompanyVO> inquirySupplierInfo(@RequestParam String inquiryId) {
        log.info("buyer inquirySupplierInfo inquiryId:{}",inquiryId);

        try{
            InquiryDetailDTO dto = new InquiryDetailDTO();
            dto.setInquiryId(inquiryId);
            return Result.success(buyerInquiryService.getSupplierInfoByInquiryId(dto));
        }catch (BusinessException be){
            return Result.failed(be.getCode(),be.getMessage());
        }
    }

    @GetMapping(value = "/v2/inquiry-supplier-info")
    @ApiOperation(value = "询盘详情supplier信息")
    public Result<InquiryCompanyVO> inquirySupplierInfoV2(@RequestBody InquiryDetailDTO dto) {
        log.info("buyer inquirySupplierInfoV2 dto:{}",dto);
        try{
            return Result.success(buyerInquiryService.getSupplierInfoByInquiryId(dto));
        }catch (BusinessException be){
            return Result.failed(be.getCode(),be.getMessage());
        }
    }

    @PostMapping(value = "/v2/inquiry-message-list")
    @ApiOperation(value = "询盘消息分页列表")
    public Result<PageResult<InquiryMessageVO>> getInquiryMessagePageList(@RequestBody InquiryMessageListDTO dto) {
        log.info("buyer getInquiryMessagePageList dto:{}",dto);
        return Result.success(buyerInquiryService.getInquiryMessagePageList(dto));
    }

    @ApiOperation(value = "买家已回复标识")
    @PostMapping("/mark-reply/{userId}/{threadId}")
    @Logging(businessId = "#threadId", businessType = "AGG-B10010", errorTag = "RFI-AGG-B10010")
    public Result<Integer> markReply(@PathVariable Long userId, @PathVariable String threadId) {
        log.info("buyer markReply threadId:{},userId:{}",threadId,userId);
        InquiryBuyerStatusEntity inquiryBuyerStatusEntity = InquiryBuyerStatusEntity.builder().chatReplyFlag(true).replyFlag(true).build();
        int count = inquiryBuyerStatusDao.update(inquiryBuyerStatusEntity, new LambdaQueryWrapper<InquiryBuyerStatusEntity>()
                .eq(InquiryBuyerStatusEntity::getThreadId, threadId).eq(InquiryBuyerStatusEntity::getBuyerId, userId));
        return Result.success(count);
    }

    @PostMapping(value = "/h5-inquiry-list/{buyerId}")
    @ApiOperation("H5分页询盘列表")
    @Logging(businessId = "#buyerId", businessType = "AGG-B10011", errorTag = "RFI-AGG-B10011")
    public Result<PageResult<H5InquiryVO>> h5InquiryList(@RequestBody BasePage basePage, @PathVariable Long buyerId) {
        log.info("buyer h5InquiryList buyerId:{},basePage:{}",buyerId,basePage);

        Page<H5InquiryVO> page = new Page<>(basePage.getPageNum(), basePage.getPageSize());
        IPage<H5InquiryVO> data = inquiryAllDao.getH5InquiryListPage(page, buyerId);
        PageResult<H5InquiryVO> pageResult = new PageResult<>();
        data.getRecords().stream().forEach(v -> {
            InquireAllEmailChatVO inquireAllEmailChatVO = inquiryAllEmailChatDao.findNewReplyByOne(v.getInquiryId());
            if (ObjectUtils.isEmpty(inquireAllEmailChatVO)) {
                v.setMessage(v.getMessage());
            } else {
                if (StringUtils.isNotBlank(inquireAllEmailChatVO.getMessage())) {
                    v.setMessage(inquireAllEmailChatVO.getMessage());
                    v.setCreateDate(inquireAllEmailChatVO.getCreateDate());
                } else {
                    v.setMessage(MESSAGE);
                }
            }
        });
        pageResult.setList(data.getRecords());
        pageResult.setTotal(data.getTotal());
        pageResult.setPageNum(data.getCurrent());
        pageResult.setPageSize(data.getSize());
        pageResult.setTotalPage(data.getPages());
        return Result.success(pageResult);
    }

    @ApiOperation(value = "买家询盘未读数")
    @GetMapping(value = "/inquiry/unread/count/{buyerId}")
    @Logging(businessId = "#buyerId", businessType = "AGG-B10012", errorTag = "RFI-AGG-B10012")
    public Result<Integer> inquiryUnreadCount(@PathVariable Long buyerId) {
        Integer count = buyerInquiryService.inquiryUnreadCount(buyerId);
        log.info("buyer inquiryUnreadCount buyerId:{},count:{}",buyerId,count);

        return Result.success(count);
    }

    @ApiOperation(value = "upsell 询盘")
    @PostMapping(value = "/match-upsell-rfi")
    @Logging(businessType = "AGG-B10013", errorTag = "RFI-AGG-B10013")
    public Result<MatchRfiThreadVO> matchUpsellRfi(@RequestBody InquiryUpsellMatchDTO dto) {
        log.info("buyer matchUpsellRfi dto:{}",dto);

        List<MatchProductInfo> result = searchUpsellService.getSearchResult(dto);
        List<Long> productIdList = result.stream().filter(Objects::nonNull).map(MatchProductInfo::getProductId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productIdList)) {
            return Result.success();
        }
        Snowflake riskAssess = IdUtil.createSnowflake(RandomUtils.nextLong(0, 32), RandomUtils.nextLong(0, 32));

        String matchId = riskAssess.nextIdStr();
        MatchRfiThreadVO matchRfiThreadVO = MatchRfiThreadVO.builder().matchId(matchId).productInfoList(result).build();

        InquiryMatchResultDTO inquiryMatchResultDTO = InquiryMatchResultDTO.builder()
                .matchId(matchId)
                .categoryId(0L)
                .matchType(InquiryTypeEnum.PRODUCT_UPSELL.getKey())
                .productId(dto.getProductId())
                .matchCompleteFlag(false)
                .matchResultJson(JSON.toJSONString(productIdList)).build();
        inquiryMatchResultService.saveMatchResult(inquiryMatchResultDTO);

        return Result.success(matchRfiThreadVO);
    }

    @PostMapping(value = "/l4-upsell/{productId}/{buyerId}")
    @ApiOperation(value = "l4匹配规则")
    @Logging(businessType = "AGG-B10014", errorTag = "RFI-AGG-B10014")
    public Result<MatchRfiThreadVO> l4Upsell(@PathVariable Long productId, @PathVariable Long buyerId) {
        log.info("buyer l4Upsell productId:{},buyerId:{}",productId,buyerId);
        ProductLiteVO productLiteVO =  productService.getLiteProductById(productId, false, true, false);
        if (Objects.isNull(productLiteVO)) {
            return Result.success(ResultCode.CommonResultCode.SUCCESS.getCode(), ResultCode.RfiResultCode.RFI_MATCH_EMPTY_DATA.getMsg(), null);
        }
        log.info("buyer l4Upsell productId = {}, buyerId = {},data = {}", productId, buyerId,productLiteVO);
        L4UpsellDTO l4UpsellDTO = new L4UpsellDTO();
        l4UpsellDTO.setCategoryId(productLiteVO.getCategoryId());
        l4UpsellDTO.setSupplierId(productLiteVO.getSupplierId());
        l4UpsellDTO.setLimitCnt(categoryCount-1);
        List<Long> productIdList = l4UpsellService.categoryUpsellSupplierProductId(l4UpsellDTO);

        if (CollectionUtils.isEmpty(productIdList)) {
            return Result.success();
        }
        //匹配的数据
        Snowflake riskAssess = IdUtil.createSnowflake(RandomUtils.nextLong(0, 32), RandomUtils.nextLong(0, 32));
        String matchId = riskAssess.nextIdStr();

        InquiryMatchResultDTO inquiryMatchResultDTO = InquiryMatchResultDTO.builder()
                .matchId(matchId)
                .categoryId(productLiteVO.getCategoryId())
                .matchType(InquiryTypeEnum.CATEGORY.getKey())
                .productId(productId)
                .matchCompleteFlag(false)
                .matchResultJson(JSON.toJSONString(productIdList))
                .build();
        inquiryMatchResultService.saveMatchResult(inquiryMatchResultDTO);

        MatchRfiThreadVO matchRfiThreadVO = MatchRfiThreadVO
                .builder()
                .matchId(matchId)
                .productInfoList(inquirySeqIdUtils.filterDupSearchRfi(getMatchResult(productIdList),productId))
                .build();
        return Result.success(matchRfiThreadVO);
    }

    @GetMapping(value = "/v2/l4-upsell")
    @ApiOperation(value = "l4匹配规则")
    @Logging(businessType = "AGG-B10014", errorTag = "RFI-AGG-B10014")
    public Result<MatchRfiThreadVO> l4UpsellV2(@RequestParam Long productId, @RequestParam Long buyerId,@RequestParam(required = false) String inquiryId) {
        log.info("buyer l4Upsell productId:{},buyerId:{},inquiryId:{}",productId,buyerId,inquiryId);
        ProductLiteVO productLiteVO =  productService.getLiteProductById(productId, false, true, false);
        if (Objects.isNull(productLiteVO)) {
            return Result.success(ResultCode.CommonResultCode.SUCCESS.getCode(), ResultCode.RfiResultCode.RFI_MATCH_EMPTY_DATA.getMsg(), null);
        }
        log.info("buyer l4Upsell productId = {}, buyerId = {},data = {}", productId, buyerId,productLiteVO);
        L4UpsellDTO l4UpsellDTO = new L4UpsellDTO();
        l4UpsellDTO.setCategoryId(productLiteVO.getCategoryId());
        l4UpsellDTO.setSupplierId(productLiteVO.getSupplierId());
        l4UpsellDTO.setLimitCnt(categoryCount-1);
        List<Long> productIdList = l4UpsellService.categoryUpsellSupplierProductId(l4UpsellDTO);

        if (CollectionUtils.isEmpty(productIdList)) {
            return Result.success();
        }
        //匹配的数据
        Snowflake riskAssess = IdUtil.createSnowflake(RandomUtils.nextLong(0, 32), RandomUtils.nextLong(0, 32));
        String matchId = riskAssess.nextIdStr();

        InquiryMatchResultDTO inquiryMatchResultDTO = InquiryMatchResultDTO.builder()
                .matchId(matchId)
                .categoryId(productLiteVO.getCategoryId())
                .matchType(InquiryTypeEnum.CATEGORY.getKey())
                .productId(productId)
                .matchCompleteFlag(false)
                .matchResultJson(JSON.toJSONString(productIdList))
                .inquiryId(inquiryId)
                .build();
        inquiryMatchResultService.saveMatchResult(inquiryMatchResultDTO);

        MatchRfiThreadVO matchRfiThreadVO = MatchRfiThreadVO
                .builder()
                .matchId(matchId)
                .productInfoList(inquirySeqIdUtils.filterDupSearchRfi(getMatchResult(productIdList),productId))
                .build();
        return Result.success(matchRfiThreadVO);
    }

    /**
     * get L4 match result
     * @param productIdList 产品idList
     * @return List<MatchProductInfo>
     */
    private List<MatchProductInfo> getMatchResult(List<Long> productIdList) {
        List<ProductLiteVO> productDetailList = productService.getLiteProductListByIds(productIdList, true, true, false);
        List<MatchProductInfo> result = new ArrayList<>(productIdList.size());
        // 获取公司名
        Map<Long, String> companyNameMap;
        if(CollectionUtils.isNotEmpty(productDetailList)){
            companyNameMap = getCompanyNameMap(productDetailList.stream().filter(Objects::nonNull).map(ProductLiteVO::getSupplierId).collect(Collectors.toList()));
        } else {
            companyNameMap = new HashMap<>();
        }

        for (Long id : productIdList) {
            MatchProductInfo rfiThreadBO = new MatchProductInfo();
            rfiThreadBO.setProductId(id);
            for (ProductLiteVO productDetailVO : productDetailList) {
                if (productDetailVO.getProductId().equals(id)) {
                    rfiThreadBO.setSupplierId(productDetailVO.getSupplierId());
                    rfiThreadBO.setCompanyName(companyNameMap.get(productDetailVO.getSupplierId()));
                    rfiThreadBO.setProductName(productDetailVO.getProductName());
                    rfiThreadBO.setProductModel(productDetailVO.getModelNumber());
                    rfiThreadBO.setProductImageUrl(productDetailVO.getProductPrimaryImage());
                    rfiThreadBO.setExpectedOrderQty(productDetailVO.getMinOrderQuantity());
                    rfiThreadBO.setExpectedOrderQtyUom(productDetailVO.getMinOrderUnit());
                    rfiThreadBO.setCategoryName(productDetailVO.getCategoryName());
                    result.add(rfiThreadBO);
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 获取公司名
     * @param supplierIdList 公司id
     * @return Map<Long, String>
     */
    private Map<Long, String> getCompanyNameMap(List<Long> supplierIdList) {
        Map<Long, String> result = new HashMap<>();
        List<SuppNameAggDTO> suppNameList = supplierService.getSupplierNameList(supplierIdList);
        if(CollectionUtils.isNotEmpty(suppNameList)){
            suppNameList.stream().filter(Objects::nonNull).forEach(supp -> result.put(supp.getOrgId(), supp.getOrgName()));
        }
        return result;
    }

    @ApiOperation(value = "买家一对多供应商询盘（搜索结果页）限制")
    @GetMapping(value = "/search-result-supplier-inquiry-limit/{buyerId}")
    public Result<Boolean> searchResultSupplierInquiryLimit(@PathVariable Long buyerId) {
        Boolean result = buyerInquiryService.searchResultSupplierInquiryLimit(buyerId);
        log.info("buyer searchResultSupplierInquiryLimit buyerId= {},result = {}", buyerId, result);

        return Result.success(result);
    }

    @ApiOperation(value = "获取rfi的buyer message")
    @GetMapping(value = "/inquiry-message")
    public Result<String> getLastInquiryMessage(@RequestParam Long userId,@RequestParam String inquiryId){
        return Result.success(buyerInquiryService.getInquiryMessage(userId, inquiryId));
    }

    @ApiOperation(value = "买家发送关联产品一对多过滤")
    @PostMapping(value = "/related-product-inquiry-filter")
    public Result<List<ProductInfoVO>> relatedProductSendInquiryFilter(@RequestBody FilterProductDTO dto){
        return Result.success(buyerInquiryService.relatedProductSendInquiryFilter(dto));
    }

    @ApiOperation(value = "买家发送关联产品一对多限制")
    @GetMapping(value = "/related-product-inquiry-limit/{buyerId}")
    public Result<Boolean> relatedProductInquiryLimit(@PathVariable Long buyerId) {
        return Result.success(buyerInquiryService.relatedProductInquiryCount(buyerId));
    }

}
