package com.globalsources.rfi.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/9/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rfi_grp.rfi_tradeshow_zone_product")
public class RfiTradeshowZoneProduct extends Model<RfiTradeshowZoneProduct> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "rfi_tszp_id", type = IdType.AUTO)
    private Long rfiTszpId;

    /**
     * 专区，来源于Send RFIs的RFI Type
     */
    @TableField("on_site_zone")
    private String onSiteZone;

    /**
     * 分组 code
     */
    @TableField("group_code")
    private String groupCode;

    /**
     * 产品二维码code
     */
    @TableField("product_inquiry_code")
    private String productInquiryCode;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 展会id
     */
    @TableField("tradeshow_id")
    private Long tradeshowId;

    /**
     * 创建人
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 最后更新人
     */
    @TableField("l_upd_by")
    private Long lUpdBy;

    /**
     * 最后更新时间
     */
    @TableField("l_upd_date")
    private Date lUpdDate;

    @TableField("delete_flag")
    private Boolean deleteFlag;


    @Override
    protected Serializable pkVal() {
        return this.rfiTszpId;
    }
}
