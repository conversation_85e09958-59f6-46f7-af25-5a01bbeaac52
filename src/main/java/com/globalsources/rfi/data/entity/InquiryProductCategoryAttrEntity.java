package com.globalsources.rfi.data.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InquiryProductCategoryAttr对象", description="")
@TableName("rfi_grp.inquiry_product_category_attr")
public class InquiryProductCategoryAttrEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "inq_pca_id", type = IdType.AUTO)
    private Integer inqPcaId;

    @ApiModelProperty(value = "inquiry_id")
    private String inquiryId;

    @TableField(value = "pc_attr_id")
    @ApiModelProperty(value = "冗余产品属性id")
    private Long attrId;

    @ApiModelProperty(value = "产品属性名称")
    private String attrName;

    @ApiModelProperty(value = "Category L4 id")
    private Long categoryId;

    @ApiModelProperty(value = "supplier_id")
    private Long supplierId;

    @ApiModelProperty(value = "Buyer user id")
    private Long buyerId;

    @Version
    @ApiModelProperty(value = "乐观锁")
    private Integer version;

    @TableLogic
    @ApiModelProperty(value = "是否已删除")
    private Boolean deleteFlag;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @TableField(value = "l_upd_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    @ApiModelProperty(value = "更新人id")
    private Long lUpdBy;


}
