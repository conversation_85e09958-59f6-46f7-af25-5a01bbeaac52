package com.globalsources.rfi.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @since 2023-5-14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rfi_grp.inquire_country")
public class InquiryCountryEntity extends Model<InquiryCountryEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 文件编号
     */
    @TableId(value = "inquiry_id", type = IdType.INPUT)
    private String inquiryId;

    /**
     * 用户IP
     */
    @TableField("buyer_ip")
    private String buyerIp;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * country
     */
    @TableField("country")
    private String country;

    /**
     * country
     */
    @TableField("province")
    private String province;

    /**
     * country
     */
    @TableField("region")
    private String region;

}
