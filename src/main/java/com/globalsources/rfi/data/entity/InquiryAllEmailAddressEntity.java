package com.globalsources.rfi.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 电子邮箱发送记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rfi_grp.inquire_all_email_address")
public class InquiryAllEmailAddressEntity extends Model<InquiryAllEmailAddressEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "email_id", type = IdType.ASSIGN_ID)
    private Long emailId;

    /**
     * 发送角色；FROM,TO
     */
    @TableField("header_type")
    private String headerType;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 电子邮箱
     */
    @TableField("email_addr")
    private String emailAddr;

    @TableField("create_date")
    private Date createDate;

    @TableField("l_upd_date")
    private Date lUpdDate;

    @TableField("id")
    private Integer id;


    @Override
    protected Serializable pkVal() {
        return this.emailId;
    }

}
