package com.globalsources.rfi.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 附件上传
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rfi_grp.inquire_all_file_attachment")
public class InquiryAllFileAttachmentEntity extends Model<InquiryAllFileAttachmentEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 询盘编号
     */
    @TableId(value = "inquiry_id", type = IdType.ASSIGN_ID)
    private String inquiryId;

    /**
     * 文件编号
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 用户能否查询
     */
    @TableField("user_select_flag")
    private String userSelectFlag;

    /**
     * 文件类型
     */
    @TableField("attachment_type")
    private Integer attachmentType;

    @TableField("create_date")
    private Date createDate;

    @TableField("l_upd_date")
    private Date lUpdDate;

    @TableField("inquiry_file_id")
    private Integer inquiryFileId;

    /**
     * 数据来源
     */
    @TableField("source_name")
    private String sourceName;

    /**
     * 文件路径
     */
    @TableField("url")
    private String url;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件大小
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 扫描状态, 0: 初始化(扫描中), -1: 文件不存在, 1: 没有病毒, 3: 有病毒, 99: 历史数据
     */
    @TableField("scan_status")
    private Long scanStatus;

    @Override
    protected Serializable pkVal() {
        return this.inquiryId;
    }

}
