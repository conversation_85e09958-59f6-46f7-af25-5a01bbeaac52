package com.globalsources.rfi.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InquiryProductCategoryAttrValue对象", description="")
@TableName("rfi_grp.inquiry_product_category_attr_value")
public class InquiryProductCategoryAttrValueEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "inquiry_id")
    @TableId(value = "inquiry_id", type = IdType.INPUT)
    private String inquiryId;

    @TableField(value = "pc_attr_id")
    @ApiModelProperty(value = "冗余产品属性id")
    private Long attrId;

    @ApiModelProperty(value = "产品属性名称")
    private String attrValue;

    @ApiModelProperty(value = "the position of value")
    private Long valuePos;


}
