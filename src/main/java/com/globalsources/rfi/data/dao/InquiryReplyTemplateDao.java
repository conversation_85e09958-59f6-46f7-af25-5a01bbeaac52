package com.globalsources.rfi.data.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.rfi.data.entity.InquiryReplyTemplate;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-13
 */
public interface InquiryReplyTemplateDao extends BaseMapper<InquiryReplyTemplate> {

    int resetSuppUserDefaultFlag(@Param("userId") Long userId, @Param("supplierId") Long supplierId);

    int setDefaultTemplateByIdAndUserIdAndSuppId(@Param("irtId") Long irtId, @Param("userId") Long userId, Long supplierId);

}
