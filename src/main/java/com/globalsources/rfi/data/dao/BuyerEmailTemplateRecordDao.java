package com.globalsources.rfi.data.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.rfi.agg.dto.inquiry.InquiryTemplateListDTO;
import com.globalsources.rfi.agg.response.rfi.InquiryTemplateVO;
import com.globalsources.rfi.data.entity.BuyerEmailTemplateRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface BuyerEmailTemplateRecordDao extends BaseMapper<BuyerEmailTemplateRecordEntity> {

    int increment(@Param("templateId")Integer templateId, @Param("userId")Long userId);

    List<InquiryTemplateVO> quickMessageListV2(@Param("dto")InquiryTemplateListDTO dto);
}
