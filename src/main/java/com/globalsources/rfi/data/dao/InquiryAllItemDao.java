package com.globalsources.rfi.data.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.globalsources.rfi.agg.request.ReceiveInquirySupplierDTO;
import com.globalsources.rfi.agg.request.SupplierInquiryDTO;
import com.globalsources.rfi.data.entity.InquiryAllItemEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description inquire_all_item entity
 * @Version 1.0
 * @since 2022/5/11
 */
public interface InquiryAllItemDao extends BaseMapper<InquiryAllItemEntity> {

    String selectSubjectByThreadId(String threadId);

    Integer receiveInquirySupplierCount(@Param(value = "dto") ReceiveInquirySupplierDTO receiveInquirySupplierDTO);

    List<Long> receiveInquirySupplierList(@Param(value = "dto") ReceiveInquirySupplierDTO receiveInquirySupplierDTO);

    List<SupplierInquiryDTO> inquiryDataBySupplier(@Param(value = "dto")ReceiveInquirySupplierDTO receiveInquirySupplierDTO);

    int updateInquiryAllItemUpdDate(@Param("threadIds") List<String> threadIds, @Param("updateDate") Date updateDate);

    List<Long> repliedSupplierList(@Param("supplierIdList")List<Long> supplierIdList);

}
