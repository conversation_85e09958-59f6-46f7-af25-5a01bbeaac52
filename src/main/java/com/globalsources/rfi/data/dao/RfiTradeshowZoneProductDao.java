package com.globalsources.rfi.data.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.rfi.agg.response.TsOnSiteZoneProductVO;
import com.globalsources.rfi.agg.response.admin.AdminTradeshowZoneProductVO;
import com.globalsources.rfi.data.entity.RfiTradeshowZoneProduct;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/15
 */
public interface RfiTradeshowZoneProductDao extends BaseMapper<RfiTradeshowZoneProduct> {

    int batchInsertRfiTradeshowZoneProduct(@Param("list") List<RfiTradeshowZoneProduct> list);

    IPage<AdminTradeshowZoneProductVO> searchRfiTradeshowZoneProductList(Page<AdminTradeshowZoneProductVO> page, @Param("keyword") String keyword, @Param("numFlag") boolean numFlag);

    List<AdminTradeshowZoneProductVO> searchRfiTradeshowZoneProductList();

    List<AdminTradeshowZoneProductVO> getRfiTradeshowZoneProduct(@Param("rfiTszpId") Long rfiTszpId, @Param("onSiteZone") String onSiteZone, @Param("productInquiryCode") String productInquiryCode, @Param("productId") Long productId);

    List<String> getTradeshowZoneProductGroupCodeList(@Param("rfiType")String rfiType);

    List<TsOnSiteZoneProductVO> searchZoneProductList(@Param("onSiteZone") String onSiteZone);
}
