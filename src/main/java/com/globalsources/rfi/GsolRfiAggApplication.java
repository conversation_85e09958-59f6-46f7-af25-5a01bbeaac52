/**
 * <a>Title: G<PERSON>LRFIApplication </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description gsol-rfi-core application<a>
 *
 * <AUTHOR>
 * @date 2021/6/28-18:58
 */
package com.globalsources.rfi;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import com.globalsources.framework.configuration.FeignClientsConfig;
import com.globalsources.framework.configuration.GsRedisConfig;
import com.globalsources.framework.inject.UserLoginInfoInject;
import com.globalsources.rfi.configuration.UpSellProperties;
import com.globalsources.rfi.inject.InquiryStatusInject;
import com.globalsources.rfi.rabbit.RfiMsgRabbitConfig;
import com.globalsources.rfi.rabbit.SsoUserUpdateRabbitConfig;
import com.globalsources.rfi.socket.SocketProcessor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.context.annotation.Import;

@EnableCaching
@RefreshScope
@EnableKnife4j
@EnableDiscoveryClient
@EnableBinding(value = {SocketProcessor.class})
@EnableConfigurationProperties(value = {UpSellProperties.class})
@Import({GsRedisConfig.class, UserLoginInfoInject.class, FeignClientsConfig.class, SsoUserUpdateRabbitConfig.class, RfiMsgRabbitConfig.class, InquiryStatusInject.class})
@MapperScan(basePackages = {"com.globalsources.rfi.data.dao"})
@SpringBootApplication(scanBasePackages = {"com.globalsources.rfi","com.globalsources.framework.*","com.globalsources.rfx.*","com.globalsources.awesome.logging.*"})
@EnableFeignClients(basePackages = {
        "com.globalsources.rfi.feign",
        "com.globalsources.rfi.agg.feign",
        "com.globalsources.search.api.*",
        "com.globalsources.analysis.api.feign",
        "com.globalsources.product.*",
        "com.globalsources.chat",
        "com.globalsources.agg.*",
        "com.globalsources.common.api.*"        ,
        "com.globalsources.user.*",
        "com.globalsources.file.api.feign",
        "com.globalsources.core.supplier.api.feign",
        "com.globalsources.rfx.feign",
        "com.globalsources.database.api.feign",
        "com.globalsources.supplierconsole.agg.api.supplier.feign",
        "com.globalsources.sensordata.core.api.feign",
        "com.globalsources.message.feign",
        "com.globalsources.common.util.virusscanner.feign"
}, defaultConfiguration = {FeignClientsConfig.class})
public class GsolRfiAggApplication {

    public static void main(String[] args) {
        SpringApplication.run(GsolRfiAggApplication.class, args);
    }

}
