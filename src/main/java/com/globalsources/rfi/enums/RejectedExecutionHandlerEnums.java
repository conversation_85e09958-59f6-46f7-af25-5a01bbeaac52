package com.globalsources.rfi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <a>Title: RejectedExecutionHandlerEnums </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: RejectedExecutionHandlerEnums <a>
 *
 * <AUTHOR>
 * @date 2021/12/31 10:14
 */
@Getter
@AllArgsConstructor
public enum RejectedExecutionHandlerEnums {

    ABORT_POLICY(new ThreadPoolExecutor.AbortPolicy()),

    CALLER_RUNS_POLICY(new ThreadPoolExecutor.CallerRunsPolicy()),

    DISCARD_OLDEST_POLICY(new ThreadPoolExecutor.DiscardOldestPolicy()),

    DISCARD_POLICY(new ThreadPoolExecutor.DiscardPolicy());

    private RejectedExecutionHandler rejectedExecutionHandler;

}
