/**
 * <a>Title: EmailEnum </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：email template enum<a>
 *
 * <AUTHOR>
 * @date 2021/4/9-14:19
 */
package com.globalsources.rfi.enums;

public enum TradeShowEnum {

    CE("ce", "Global Sources Consumer Electronics Show"),
    ME("me", "Global Sources Mobile Electronics Show"),
    FA("fa", "Global Sources Lifestyle x Fashion Show"),
    HK("hk", "Global Sources Home & Kitchen Show"),
    SH("sh", "Global Sources Home & Kitchen Show"),
    IDCE("idce", "Global Indonesia Electronics Show"),
    SA("sa", "Global Sourcing Fair South Africa Show"),
    SKC("skc", "Global Sourcing Sourcing Knowledge Center");

    private String key;

    private String value;

    TradeShowEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String match(String name) {
        TradeShowEnum[] values = TradeShowEnum.values();
        for (TradeShowEnum emailEnum : values) {
            if (emailEnum.key.equals(name)) {
                return emailEnum.value;
            }
        }
        return null;
    }

}
