package com.globalsources.rfi.log;

import com.alibaba.fastjson.JSON;
import com.globalsources.awesome.logging.collector.ILogCollector;
import com.globalsources.awesome.logging.enums.StringPool;
import com.globalsources.awesome.logging.model.dto.LoggingDTO;
import com.globalsources.awesome.logging.processor.ILogModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <a>Title: AwesomeLoggingCollector </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: AwesomeLoggingCollector <a>
 *
 * <AUTHOR>
 * @date 2021/12/31 10:20
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AwesomeLoggingCollector implements ILogCollector {

    private final RfiLogSender rfiLogSender;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public void collect(ILogModel logInfo) {
        if (Boolean.FALSE.equals(logInfo.getSuccess())) {
            LoggingDTO logDTO = new LoggingDTO();
            logDTO.setResultCode(StringPool.ERROR_CODE_FIV_HUNDRED_ONE.value());
            logDTO.setBusinessId(logInfo.getBusinessId());
            logDTO.setServerName(logInfo.getServerName());
            logDTO.setBusinessType(logInfo.getBusinessType());
            logDTO.setOperationLog(StringUtils.isNotBlank(logInfo.getContent()) ? JSON.toJSONString(logInfo.getContent()) : StringPool.EMPTY_BRACKETS.value());
            logDTO.setRequestData(Objects.nonNull(logInfo.getArgs()) ? JSON.toJSONString(logInfo.getArgs()) : StringPool.EMPTY_BRACKETS.value());
            rfiLogSender.send(logDTO);
        }
    }
}
