package com.globalsources.rfi.log;

import com.alibaba.fastjson.JSON;
import com.globalsources.awesome.logging.model.dto.LoggingDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <a>Title: RfiLogSender </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: RfiLogSender <a>
 *
 * <AUTHOR>
 * @date 2021/12/31 10:20
 */
@Slf4j
@RequiredArgsConstructor
@Component("RfiLogSender")
public class RfiLogSender {

    @Value("${awesome.logging.exchange:awesome-logging}")
    private String awesomeLoggingExchange;

    @Value("${awesome.logging.router.key:log}")
    private String awesomeLoggingRouterKey;

    private final RabbitTemplate rabbitTemplate;

    /**
     * async send log
     *
     * @param logDTO
     */
    @Async("asyncExecutorService")
    public void send(LoggingDTO logDTO) {
        try {
            rabbitTemplate.convertAndSend(awesomeLoggingExchange, awesomeLoggingRouterKey, JSON.toJSONString(logDTO));
        } catch (Exception e) {
            log.error("rfi send log error, error message is : {}", e.getMessage());
        }
    }

}
