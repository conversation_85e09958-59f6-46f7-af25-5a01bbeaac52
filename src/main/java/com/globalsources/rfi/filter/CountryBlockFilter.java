package com.globalsources.rfi.filter;

import com.alibaba.fastjson.JSON;
import com.globalsources.rfi.agg.dto.inquiry.InquiryAttachmentDTO;
import com.globalsources.rfi.agg.dto.product.InquiryProductCategoryAttributeDTO;
import com.globalsources.rfi.data.entity.InquiryAllEntity;
import com.globalsources.rfi.data.entity.InquiryAllItemEntity;
import io.vavr.Tuple4;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR> Li
 * @create 2022/11/1
 */
@Component
@RefreshScope
@Slf4j
public class CountryBlockFilter implements InquiryFilter {

    private static final Integer WEIGHTS= 30;

    @Value("${rfi.block.country:RU}")
    private String blockCountryCodeList;

    @Override
    public Object filter(Object... param) {
        try {
            log.warn("CountryBlockFilter check");

            List<Tuple4<InquiryAllEntity, InquiryAllItemEntity, InquiryProductCategoryAttributeDTO, InquiryAttachmentDTO>> inquiryTupleList = (List<Tuple4<InquiryAllEntity, InquiryAllItemEntity,InquiryProductCategoryAttributeDTO, InquiryAttachmentDTO>>) param[1];
            if(CollectionUtils.isNotEmpty(inquiryTupleList)){
                Iterator<Tuple4<InquiryAllEntity, InquiryAllItemEntity, InquiryProductCategoryAttributeDTO, InquiryAttachmentDTO>> it = inquiryTupleList.iterator();
                Tuple4<InquiryAllEntity, InquiryAllItemEntity, InquiryProductCategoryAttributeDTO, InquiryAttachmentDTO> dto = null;
                InquiryAllEntity entity=null;
                while (it.hasNext()) {
                    dto = it.next();
                    if (Objects.nonNull(dto) && Objects.nonNull(dto._1)) {
                        entity=dto._1;
                        if(StringUtils.isNotBlank(blockCountryCodeList) && blockCountryCodeList.contains(entity.getCountryCode())){
                            log.warn("CountryBlockFilter rfi block entity:{}", entity);
                            entity.setTmxStatus(entity.getCountryCode().toLowerCase(Locale.ROOT)+"_block");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("CountryBlockFilter error,param:{},error:{}", JSON.toJSONString(param),JSON.toJSONString(e));
        }
        return null;
    }

    @Override
    public Integer getWeights() {
        return WEIGHTS;
    }
}
