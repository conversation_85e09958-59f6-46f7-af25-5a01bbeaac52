package com.globalsources.rfi.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.globalsources.framework.vo.MessageVO;
import com.globalsources.rfi.agg.core.vo.InquireAllVO;
import com.globalsources.rfi.agg.core.vo.InquirySupplierStatusVO;
import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import com.globalsources.rfi.bean.PointMessageDTO;
import com.globalsources.rfi.data.dao.InquiryAllDao;
import com.globalsources.rfi.data.entity.InquiryAllEntity;
import com.globalsources.rfi.data.entity.InquiryAllItemEntity;
import com.globalsources.rfi.service.CommonService;
import com.globalsources.rfi.service.McUserService;
import com.globalsources.rfi.service.SupplierInquiryService;
import com.globalsources.rfx.service.IContractService;
import com.globalsources.rfx.service.IProductService;
import com.globalsources.rfx.service.ISupplierService;
import com.globalsources.search.api.vo.ProductDetailVo;
import com.globalsources.sensordata.core.api.dto.RfiSensorDTO;
import com.globalsources.sensordata.core.api.dto.SensorDataTrackDTO;
import com.globalsources.sensordata.core.api.enums.EventTypeEnum;
import com.globalsources.sensordata.core.api.feign.SensorDataTrackFeign;
import com.globalsources.user.api.dto.UserContactDTO;
import com.globalsources.user.api.enums.TransCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <a>Title: MPEBlockServiceImpl </a>
 * <a>Author: Levlin Li <a>
 * <a>Description：<a>
 *
 * <AUTHOR> Li
 * @date 2021/8/5-16:43
 */
@Slf4j
@Service
@RefreshScope
public class CommonServiceImpl implements CommonService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private ISupplierService supplierService;

    @Autowired
    private IProductService productService;

    @Autowired
    private SensorDataTrackFeign sensorDataTrackFeign;

    @Autowired
    private InquiryAllDao inquiryAllDao;

    @Autowired
    private SupplierInquiryService supplierInquiryService;

    @Autowired
    private McUserService mcUserService;

    @Autowired
    private IContractService contractService;

    @Value("${cm.exchange:GSOL.DATA.CLIENT.EXCHANGE.CM}")
    private String cmExchange;

    @Value("${cm.queue:GSOL.DATA.CLIENT.new-cm}")
    private String cmRoutingKey;

    @Value("${data.point.exchange:GSOL.DATA.CLIENT.EXCHANGE}")
    private String pointExchange;

    @Value("${data.point.routing:GSOL.DATA.CLIENT.new-gsol-point}")
    private String pointRoutingKey;

    @Override
    public void sensorData(List<String> threadIdList) {
        log.info("sensorData threadIdList :{}", threadIdList);
        for(String threadId:threadIdList){
            try {
                InquireAllVO inquireAllVO = inquiryAllDao.findOneByThreadId(threadId);
                SensorDataTrackDTO sensorDataTrackDTO = new SensorDataTrackDTO();
                sensorDataTrackDTO.setEventTypeEnum(EventTypeEnum.RFI_SUB);
                RfiSensorDTO sensorDTO = new RfiSensorDTO();
                sensorDTO.setUserId(inquireAllVO.getBuyerId());
                sensorDTO.setSupplierId(inquireAllVO.getInquireAllItemList().getSupplierId());
                sensorDTO.setSupplierName(supplierService.getSupplierName(inquireAllVO.getInquireAllItemList().getSupplierId()));
                sensorDTO.setRfiId(Long.valueOf(inquireAllVO.getInquiryId()));
                if (InquiryTypeEnum.PRODUCT.getKey().equals(inquireAllVO.getInquiryType().getKey())) {
                    sensorDTO.setProductQuantity(inquireAllVO.getInquireAllItemList().getExpectedOrderQty().toString());
                    sensorDTO.setQuantityUnit(inquireAllVO.getInquireAllItemList().getExpectedOrderQtyUom());
                    sensorDTO.setProductId(inquireAllVO.getInquireAllItemList().getProductId());
                    sensorDTO.setProductName(inquireAllVO.getInquireAllItemList().getProductName());
                    ProductDetailVo productDetailDTO = productService.getProductInfo(inquireAllVO.getInquireAllItemList().getProductId());
                    //L4 category
                    sensorDTO.setL1categoryId(productDetailDTO.getCategoryInfo().getL1CategoryVo().getCategoryId());
                    sensorDTO.setL2categoryId(productDetailDTO.getCategoryInfo().getL2CategoryVo().getCategoryId());
                    sensorDTO.setL3categoryId(productDetailDTO.getCategoryInfo().getL3CategoryVo().getCategoryId());
                    sensorDTO.setL4categoryId(productDetailDTO.getCategoryInfo().getL4CategoryVo().getCategoryId());
                    sensorDTO.setPpAttributeValue(0);
                    if (CollectionUtils.isNotEmpty(productDetailDTO.getProduct().getProductAttribute())) {
                        sensorDTO.setPpAttributeValue(1);
                    }
                }
                if (InquiryTypeEnum.PRODUCT_UPSELL.equals(inquireAllVO.getInquiryType()) || InquiryTypeEnum.CATEGORY.equals(inquireAllVO.getInquiryType())) {
                    sensorDTO.setInquiryType("match upsell");
                } else if (InquiryTypeEnum.PRODUCT.equals(inquireAllVO.getInquiryType())) {
                    sensorDTO.setInquiryType("product");
                } else if (InquiryTypeEnum.SUPPLIER.equals(inquireAllVO.getInquiryType())) {
                    sensorDTO.setInquiryType("supplier");
                }

                //product name
                String supplierType = "AGG";
                Integer supplierPackage = null;
                Integer type = contractService.getMaxContractLevel(inquireAllVO.getInquireAllItemList().getSupplierId());
                if (type != null && type > -1) {
                    //付费供应商
                    supplierType = "ADV";
                    supplierPackage = type;
                } else if (type == null || type < -1) {
                    supplierType = "FL";
                }
                sensorDTO.setSupplierType(supplierType);
                sensorDTO.setSupplierPackage(supplierPackage != null ? String.valueOf(supplierPackage) : "");
                sensorDTO.setPlatform(inquireAllVO.getRfiSource());
                sensorDTO.setCreateDate(inquireAllVO.getCreateDate());
                sensorDTO.setLang(inquireAllVO.getLangCode());
                sensorDTO.setDoiDate(inquireAllVO.getDoiDate());
                sensorDTO.setPotentialOpportunityConvertFlag(inquireAllVO.isPotentialOpportunityConvertFlag());
                sensorDataTrackDTO.setObject(sensorDTO);
                sensorDataTrackFeign.dataTrack(sensorDataTrackDTO);
            } catch (Exception exception) {
                log.warn("sensorData error,{}", exception.getMessage());
            }
        }

    }

    @Override
    public void syncPscCmAndScPoint(List<InquiryAllItemEntity> itemList) {
        log.info("rfi sync cm info,info:{}",itemList);

        try{
            List<InquiryAllEntity> inquiryList = inquiryAllDao.selectList(new LambdaQueryWrapper<InquiryAllEntity>()
                    .in(InquiryAllEntity::getInquiryId, itemList.stream().map(InquiryAllItemEntity::getInquiryId).collect(Collectors.toList())));
            Map<String,String> mapping = new HashMap<>(itemList.size());
            itemList.forEach(data -> mapping.put(data.getInquiryId(),data.getThreadId()));

            for(InquiryAllEntity inquiry:inquiryList){
                UserContactDTO userInfo=new UserContactDTO();
                String mcUserEmail= mcUserService.queryMCUser(0L,inquiry.getBuyerId());
                userInfo.setMcMail(mcUserEmail);
                InquirySupplierStatusVO threadSupplierStatus= supplierInquiryService.inquiryStateInfo(mapping.get(inquiry.getInquiryId()));
                userInfo.setUserId(threadSupplierStatus.getSupplierUserId());
                userInfo.setContactUserId(inquiry.getBuyerId());
                userInfo.setOrgId(threadSupplierStatus.getSupplierId());
                userInfo.setContactSourceType("RFI");
                log.info("rfi sync cm info,info:{}",userInfo);

                rabbitTemplate.convertAndSend(cmExchange,
                        cmRoutingKey, JSON.toJSONString(userInfo), new CorrelationData(UUID.randomUUID().toString()));
                log.info("rfi sync cm succ");

                sourcingClubPoint(inquiry.getBuyerId(), inquiry.getInquiryId());
            }
        }catch (Exception e){
            log.error("rfi sync cm info error,threadId:{}",itemList,e);
        }
    }

    @Override
    public void sourcingClubPoint(Long buyerId, String inquiryId) {
        try {
            String uuId = UUID.randomUUID().toString();
            PointMessageDTO pointMessage = PointMessageDTO
                    .builder()
                    .action(TransCodeEnum.RFI)
                    .userId(buyerId)
                    .objectId(inquiryId)
                    .build();


            MessageVO messageVO = new MessageVO();
            messageVO.setType(1);
            messageVO.setBizType("TRANDS_POINT");
            messageVO.setData(JSON.toJSONString(pointMessage));

            CorrelationData correlationData = new CorrelationData(uuId);
            rabbitTemplate.convertAndSend(pointExchange,
                    pointRoutingKey, JSON.toJSONString(messageVO), correlationData);
            log.info("rfi sourcing club point succ buyerId:{}, inquiryId:{}", buyerId, inquiryId);
        } catch (Exception exception) {
            log.error("rfi send potin fail, userId = {}, inquiryId = {}", buyerId, inquiryId);
        }
    }
}
