package com.globalsources.rfi.service.impl;

import com.globalsources.product.agg.api.vo.ProductLiteVO;
import com.globalsources.rfi.agg.core.dto.rfi.L4UpsellDTO;
import com.globalsources.rfi.agg.core.vo.rfi.RfiProductDwdVO;
import com.globalsources.rfi.data.dao.RfiProductDwdDao;
import com.globalsources.rfi.service.L4UpsellService;
import com.globalsources.rfx.service.IProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RefreshScope
public class L4UpsellServiceImpl implements L4UpsellService {

    @Autowired
    private L4UpsellService l4UpsellService;

    @Value("${rfi.upsell.hot.category}")
    private String hotCategory;

    @Value("${rfi.upsell.hot.count:101}")
    private Integer upsellCount;

    @Value("${rfi.upsell.not.hot.count:201}")
    private Integer categoryCount;

    @Autowired
    private RfiProductDwdDao rfiProductDwdDao;

    @Autowired
    private IProductService productService;


    @Override
    public List<Long> categoryUpsellSupplierProductId(L4UpsellDTO l4UpsellDTO) {

        // 热门数据匹配100
        if (hotCategory.contains(String.valueOf(l4UpsellDTO.getCategoryId()))){
            l4UpsellDTO.setLimitCnt(upsellCount);
        } else {
            l4UpsellDTO.setLimitCnt(categoryCount);
        }

        List<RfiProductDwdVO> dwdList = rfiProductDwdDao.categoryUpsellSupplier(l4UpsellDTO);

        if (CollectionUtils.isEmpty(dwdList)){
            return Collections.emptyList();
        }
        //这样是无序的，为了方便测试，调整一下
        List<Long> productIdList = new ArrayList<>(dwdList.size());
        dwdList.forEach(p->productIdList.add(p.getProductId()));
        return productIdList;
    }

    @Override
    public List<Long> getValidUpsellProductId(L4UpsellDTO l4UpsellDTO){
        log.info("getValidUpsellProductId dto :{}",l4UpsellDTO);
        List<RfiProductDwdVO> dwdList = rfiProductDwdDao.categoryUpsellSupplier(l4UpsellDTO);
        log.info("getValidUpsellProductId dwdList :{}",dwdList);

        List<Long> productIdList = new ArrayList<>(dwdList.size());
        dwdList.forEach(p->productIdList.add(p.getProductId()));
        if(CollectionUtils.isEmpty(productIdList)){
            return Collections.emptyList();
        }
        List<Long> validProductIdList= productService.getLiteProductListByIds(productIdList).stream().filter(Objects::nonNull).map(ProductLiteVO::getProductId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(validProductIdList)){
            return Collections.emptyList();
        }
        log.info("getValidUpsellProductId validProductIdList:{}",validProductIdList);
        List<Long> result = new ArrayList<>(validProductIdList.size());
        for(Long id: productIdList){
            if(validProductIdList.contains(id)){
                result.add(id);
            }
        }
        log.info("getValidUpsellProductId result:{}",result);
        return result;
    }

    @Override
    public Integer categoryUpsellSupplierCount(L4UpsellDTO l4UpsellDTO) {
        log.info("categoryUpsellSupplierCount dto:{}",l4UpsellDTO);
        return Optional.ofNullable(rfiProductDwdDao.categoryUpsellSupplierCount(l4UpsellDTO)).orElse(0);
    }


    @Override
    public List<Long> getValidUpsellProductIdResult(L4UpsellDTO l4UpsellDTO) {
        log.info("getValidUpsellProductIdResult dto:{}",l4UpsellDTO);
        List<RfiProductDwdVO> dwdList = rfiProductDwdDao.categoryUpsellSupplierResult(l4UpsellDTO);
        log.info("getValidUpsellProductIdResult dwdList:{}",dwdList);

        List<Long> productIdList = new ArrayList<>(dwdList.size());
        dwdList.forEach(p->productIdList.add(p.getProductId()));
        if(CollectionUtils.isEmpty(productIdList)){
            return Collections.emptyList();
        }
        List<Long> validProductIdList= productService.getLiteProductListByIds(productIdList).stream().filter(Objects::nonNull).map(ProductLiteVO::getProductId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(validProductIdList)){
            return Collections.emptyList();
        }
        log.info("getValidUpsellProductIdResult validProductIdList:{}",validProductIdList);
        List<Long> result = new ArrayList<>(validProductIdList.size());
        for(Long id: productIdList){
            if(validProductIdList.contains(id)){
                result.add(id);
            }
        }
        log.info("getValidUpsellProductIdResult result:{}",result);
        return result;
    }

}
