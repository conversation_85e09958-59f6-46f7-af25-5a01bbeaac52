package com.globalsources.rfi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.globalsources.rfi.agg.enums.SourceNameEnum;
import com.globalsources.rfi.agg.response.ResponseAttachmentVO;
import com.globalsources.rfi.data.dao.AttachmentDao;
import com.globalsources.rfi.service.AttachmentService;
import com.globalsources.rfi.utils.FileUtil;
import com.globalsources.rfx.service.IAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AttachmentServiceImpl implements AttachmentService {

    @Value("${third.sso.dataKey}")
    private String secretKey;

    private static final String OLD = "old";
    private static final String NEW = "new";

    @Autowired
    private AttachmentDao attachmentDao;

    @Autowired
    private IAttachmentService attachmentService;

    @Override
    public List<ResponseAttachmentVO> getMessageAttachmentListByReplyId(String replyId) {

        try{
            // refactor add
            log.info("------ getMessageAttachmentListByReplyId, replyId:{}", replyId);
            List<ResponseAttachmentVO> entityList = attachmentDao.getInquiryAttachmentListByReplyId(replyId);

            // refactor remove 旧查询
            if (org.apache.commons.collections.CollectionUtils.isEmpty(entityList)) {
                log.info("------ getMessageAttachmentListByReplyId isEmpty, replyId:{}", replyId);
                entityList = attachmentDao.getAttachmentListByReplyId(replyId);
            }

            if(CollectionUtils.isEmpty(entityList)){
                return Collections.emptyList();
            }

            JSONObject jsonObject = getFileJsonObj(entityList);

            if(Objects.nonNull(jsonObject) && !jsonObject.isEmpty()){
                entityList.stream().forEach(att -> att.setUrl(jsonObject.get(att.getUrl()).toString()));
            }
            for(ResponseAttachmentVO vo:entityList){
                vo.setType(FileUtil.fileMimeType(vo.getName()));
                vo.setSourceType(SourceNameEnum.MESSAGE.getKey());
            }
            return entityList;
        }catch (Exception e){
            log.error("getAttachmentListByReplyId error,replyId:{},{}", replyId,JSON.toJSONString(e));
            return Collections.emptyList();
        }
    }

    private JSONObject getFileJsonObj(List<ResponseAttachmentVO> entityList){
        List<String> fileKeys = getKeyList(entityList);
        return attachmentService.batchGetFileUrl(fileKeys, "rfi", 1);
    }

    private List<String> getKeyList(List<ResponseAttachmentVO> entityList){
        List<String> fileKeys = new ArrayList<>();
        entityList.stream().forEach(att -> {
            if (!OLD.equals(att.getAttachmentType())) {
                fileKeys.add(att.getUrl());
            }
            if (NEW.equals(att.getAttachmentType())) {
                fileKeys.add(att.getUrl());
            }
        });
        return fileKeys;
    }
}
