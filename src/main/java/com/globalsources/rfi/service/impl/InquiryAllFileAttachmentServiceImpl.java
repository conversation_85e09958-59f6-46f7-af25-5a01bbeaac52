/**
 * <a>Title: IInquireAllFileAttachmentServiceImpl </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/7/13-19:01
 */
package com.globalsources.rfi.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.rfi.agg.dto.inquiry.InquiryAttachmentDTO;
import com.globalsources.rfi.agg.request.AttachmentAggDTO;
import com.globalsources.rfi.data.dao.InquiryAllFileAttachmentDao;
import com.globalsources.rfi.data.entity.InquiryAllFileAttachmentEntity;
import com.globalsources.rfi.service.InquiryAllFileAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class InquiryAllFileAttachmentServiceImpl extends ServiceImpl<InquiryAllFileAttachmentDao, InquiryAllFileAttachmentEntity> implements InquiryAllFileAttachmentService {

    @Override
    public void saveInquiryFile(List<AttachmentAggDTO> attachmentEmailDTOList, String threadId, Long userId) {
        List<InquiryAllFileAttachmentEntity> list = new ArrayList<>();
        Snowflake snowflake = IdUtil.createSnowflake(RandomUtils.nextLong(0, 32), RandomUtils.nextLong(0, 32));
        attachmentEmailDTOList.forEach(item -> {
            String fileId = String.valueOf(snowflake.nextId());
            String fileKey = item.getFileKey() != null ? item.getFileKey() : item.getUrl();

            InquiryAllFileAttachmentEntity entity = InquiryAllFileAttachmentEntity.builder()
                    .inquiryId(threadId)
                    .fileId(fileId)
                    .attachmentType(item.getType())
                    .url(fileKey)
                    .fileName(item.getName())
                    .fileSize(item.getSize())
                    .build();
            list.add(entity);
        });
        //保存上传附件
        this.saveBatch(list);
    }

    @Override
    public void saveInquiryAttachmentBatch(List<InquiryAttachmentDTO> dtoList) {
        List<InquiryAllFileAttachmentEntity> list = new ArrayList<>();
        Snowflake snowflake = IdUtil.createSnowflake(RandomUtils.nextLong(0, 32), RandomUtils.nextLong(0, 32));
        for (InquiryAttachmentDTO dto : dtoList) {
            dto.getAttachmentList().forEach(item -> {

                String fileId = String.valueOf(snowflake.nextId());
                String fileKey = item.getFileKey() != null ? item.getFileKey() : item.getUrl();

                InquiryAllFileAttachmentEntity entity = InquiryAllFileAttachmentEntity.builder()
                        .inquiryId(dto.getThreadId())
                        .fileId(fileId)
                        .attachmentType(item.getType())
                        .url(fileKey)
                        .fileName(item.getName())
                        .fileSize(item.getSize())
                        .build();
                list.add(entity);
            });
        }
        this.saveBatch(list);
    }
}
