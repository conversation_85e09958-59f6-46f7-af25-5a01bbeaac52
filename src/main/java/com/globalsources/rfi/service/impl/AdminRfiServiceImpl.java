package com.globalsources.rfi.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.agg.supplier.api.model.dto.SupplierCommonInfoDTO;
import com.globalsources.agg.supplier.api.model.dto.supplier.SuppNameAggDTO;
import com.globalsources.awesome.logging.annotation.AwesomeLog;
import com.globalsources.common.api.vo.DictionaryItemVO;
import com.globalsources.file.api.dto.FileReqDTO;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.result.ResultCodeEnum;
import com.globalsources.framework.utils.CipherUtil;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.product.agg.api.dto.product.OnlineProductEntityDTO;
import com.globalsources.product.agg.api.dto.request.OnlineProdEntityQueryAggDTO;
import com.globalsources.product.agg.api.feign.BuyerProductFeign;
import com.globalsources.product.agg.api.vo.ProductCategory;
import com.globalsources.product.agg.api.vo.ProductLiteVO;
import com.globalsources.rfi.agg.constants.RfiCommonConstants;
import com.globalsources.rfi.agg.constants.RfiSourceEnum;
import com.globalsources.rfi.agg.core.vo.InquireAllEmailChatVO;
import com.globalsources.rfi.agg.core.vo.InquireAllVO;
import com.globalsources.rfi.agg.core.vo.InquiryAttachmentVO;
import com.globalsources.rfi.agg.core.vo.InquirySupplierStatusVO;
import com.globalsources.rfi.agg.core.vo.rfi.RfiInquiryEmailAddressCoreVO;
import com.globalsources.rfi.agg.dto.admin.AdminInquireDetailAggDTO;
import com.globalsources.rfi.agg.dto.admin.AdminInquiryStatusAggDTO;
import com.globalsources.rfi.agg.dto.admin.AdminSearchInquiryDTO;
import com.globalsources.rfi.agg.dto.admin.AdminSearchInquiryFaqDTO;
import com.globalsources.rfi.agg.dto.chat.InquiryChatMessageAggDTO;
import com.globalsources.rfi.agg.dto.product.ProductCategoryAttributeAggDTO;
import com.globalsources.rfi.agg.dto.user.BuyerUserInfoDTO;
import com.globalsources.rfi.agg.dto.user.CommonUserDTO;
import com.globalsources.rfi.agg.dto.user.SellerUserInfoDTO;
import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import com.globalsources.rfi.agg.enums.RfiAdminTypeEnum;
import com.globalsources.rfi.agg.enums.SourceNameEnum;
import com.globalsources.rfi.agg.request.TsQueryDTO;
import com.globalsources.rfi.agg.request.admin.AdminInquiryEDMDTO;
import com.globalsources.rfi.agg.request.admin.AdminInquiryQueryDTO;
import com.globalsources.rfi.agg.request.admin.AdminOnSiteRfiQueryDTO;
import com.globalsources.rfi.agg.request.admin.AdminProductQueryDTO;
import com.globalsources.rfi.agg.request.admin.AdminSearchInquiryQueryDTO;
import com.globalsources.rfi.agg.request.admin.AdminTradeshowZoneProductSaveDTO;
import com.globalsources.rfi.agg.request.product.InquiryProductCategoryAttrCoreDTO;
import com.globalsources.rfi.agg.response.InquiryProdVO;
import com.globalsources.rfi.agg.response.ResponseAttachmentVO;
import com.globalsources.rfi.agg.response.RfiConvertRfqCountVO;
import com.globalsources.rfi.agg.response.TsOnSiteZoneGroupVO;
import com.globalsources.rfi.agg.response.TsOnSiteZoneProductVO;
import com.globalsources.rfi.agg.response.admin.AdminOnSiteRfiListVO;
import com.globalsources.rfi.agg.response.admin.AdminTradeshowZoneProductVO;
import com.globalsources.rfi.agg.response.console.RfiDetailMessageVO;
import com.globalsources.rfi.agg.response.console.RfiProdDetailVO;
import com.globalsources.rfi.agg.response.console.RfiThreadDetailVO;
import com.globalsources.rfi.agg.response.product.ProductCategoryAttributeVO;
import com.globalsources.rfi.agg.response.rfi.InquiryChatMessageCountDTO;
import com.globalsources.rfi.agg.response.rfi.InquiryEDMAnalysisVO;
import com.globalsources.rfi.agg.response.rfi.InquiryTradeshowZoneProductVO;
import com.globalsources.rfi.agg.response.rfi.RfiInquiryEmailAddressAggVO;
import com.globalsources.rfi.constants.InquiryCoreConstants;
import com.globalsources.rfi.data.dao.InquiryAllDao;
import com.globalsources.rfi.data.dao.InquiryAllEmailChatDao;
import com.globalsources.rfi.data.dao.InquiryStatusDao;
import com.globalsources.rfi.data.dao.RfiTradeshowZoneProductDao;
import com.globalsources.rfi.data.entity.InquiryAllEntity;
import com.globalsources.rfi.data.entity.InquiryAllItemEntity;
import com.globalsources.rfi.data.entity.InquiryStatusEntity;
import com.globalsources.rfi.data.entity.RfiTradeshowZoneProduct;
import com.globalsources.rfi.excel.RfiTradeshowZoneProductExcelDTO;
import com.globalsources.rfi.excel.RfiTradeshowZoneProductListener;
import com.globalsources.rfi.service.AdminRfiService;
import com.globalsources.rfi.service.InquiryAllEmailAddressService;
import com.globalsources.rfi.service.InquiryAllEmailChatService;
import com.globalsources.rfi.service.InquiryAllEmailService;
import com.globalsources.rfi.service.InquiryAllItemService;
import com.globalsources.rfi.service.InquiryAllService;
import com.globalsources.rfi.service.InquiryProductCategoryAttrService;
import com.globalsources.rfi.service.InquiryStatusService;
import com.globalsources.rfi.service.SupplierInquiryService;
import com.globalsources.rfi.utils.DictCountryUtils;
import com.globalsources.rfi.utils.FileUtil;
import com.globalsources.rfi.utils.InquiryReplyUtil;
import com.globalsources.rfx.service.IAttachmentService;
import com.globalsources.rfx.service.ICategoryService;
import com.globalsources.rfx.service.IDictService;
import com.globalsources.rfx.service.IProductService;
import com.globalsources.rfx.service.ISupplierService;
import com.globalsources.rfx.service.IUserService;
import com.globalsources.ts.api.enums.SurveyGrpCodeEnums;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Chen
 * @date 2021/8/2 18:47
 */
@Slf4j
@Service
public class AdminRfiServiceImpl implements AdminRfiService {

    @Autowired
    private DictCountryUtils dictCountryUtils;

    @Autowired
    private SupplierInquiryService supplierInquiryService;

    @Autowired
    private InquiryAllEmailChatService inquireAllEmailChatService;

    @Autowired
    private InquiryAllEmailAddressService inquiryAllEmailAddressService;

    @Autowired
    private InquiryAllEmailService inquiryAllEmailService;

    @Autowired
    private InquiryProductCategoryAttrService inquiryProductCategoryAttrService;

    @Autowired
    private InquiryStatusService inquiryStatusService;

    @Autowired
    private InquiryAllService inquiryAllService;

    @Autowired
    private InquiryStatusDao inquiryStatusDao;

    @Value("${third.sso.dataKey}")
    private String secretKey;

    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private IProductService productService;

    @Autowired
    private ISupplierService supplierService;

    @Autowired
    private ICategoryService categoryService;

    @Autowired
    private IUserService userService;

    @Autowired
    private IDictService dictService;

    private String regexStr = "^[0-9]+$";

    @Autowired
    private InquiryAllDao inquiryDao;

    @Autowired
    private InquiryAllEmailChatDao inquiryAllEmailChatDao;

    @Autowired
    private InquiryAllItemService inquiryAllItemService;

    @Autowired
    private RfiTradeshowZoneProductDao rfiTradeshowZoneProductDao;

    @Autowired
    private RfiTradeshowZoneProductListener rfiTradeshowZoneProductListener;

    @Autowired
    private BuyerProductFeign productAggFeign;

    private static final String COUNTRY = "Country";

    @Override
    public AdminInquireDetailAggDTO detail(String inquiryId) {
        AdminInquireDetailAggDTO detailDto = new AdminInquireDetailAggDTO();
        InquireAllVO inquireAllVO = inquiryDao.findOneByThreadId(inquiryId);
        if (Objects.isNull(inquireAllVO)) {
            return detailDto;
        } else {
            log.error("invoke inquiryFeign.inquiryAllInfo inquire is empty = {}", inquiryId);
        }

        detailDto.setSubject(inquireAllVO.getSubject());
        detailDto.setInquiryId(inquireAllVO.getInquiryId());
        detailDto.setCreateDate(inquireAllVO.getCreateDate());
        detailDto.setRfiSources(Optional.ofNullable(inquireAllVO.getRfiSource()).orElse(1).toString());
        detailDto.setInquiryType(inquireAllVO.getInquiryType().getKey());
        if (InquiryTypeEnum.PRODUCT.getKey().equals(inquireAllVO.getInquiryType().getKey())) {
            detailDto.setParInquiryId(inquireAllVO.getInquiryId());
            if (Objects.isNull(inquireAllVO.getRecommendFlag())) {
                //一对多产品询盘
                detailDto.setInquiryType(InquiryTypeEnum.PRODUCT_UPSELL.getKey());
            }
        }

        //message
        detailDto.setMessage(InquiryReplyUtil.replyRepMessage(inquireAllVO.getBuyerMessage()));

        //inquiry status
        List<InquiryStatusEntity> list = inquiryStatusService.getInquiryStatusListByThreadId(inquireAllVO.getInquiryId(), inquiryId);

        //AdminInquiryStatusAggDTO
        List<AdminInquiryStatusAggDTO> statusRecords = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            statusRecords = list.stream().filter(Objects::nonNull)
                    .map(item -> {
                        AdminInquiryStatusAggDTO adminInquiryStatusAggDTO = new AdminInquiryStatusAggDTO();
                        adminInquiryStatusAggDTO.setStatus(item.getStatus());
                        adminInquiryStatusAggDTO.setStatusDesc(item.getStatusDesc());
                        adminInquiryStatusAggDTO.setUpdateDate(item.getCreateDate());
                        return adminInquiryStatusAggDTO;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(statusRecords)) {
                //当前状态
                AdminInquiryStatusAggDTO statusEntity = statusRecords.get(statusRecords.size() - 1);
                detailDto.setStatus(statusEntity.getStatus());
                detailDto.setStatusDesc(statusEntity.getStatusDesc());
            }
        }
        detailDto.setStatusRecords(Lists.newArrayList());
        detailDto.setStatusList(statusRecords);
        //附件
        detailDto.setAttachments(getAttachment(inquiryId));

        //产品属性

        List<InquiryProductCategoryAttrCoreDTO> pcAttrDtoList = inquiryProductCategoryAttrService.getAttrDtoByInquiryId(Objects.nonNull(inquiryId) ? Lists.newArrayList(inquiryId) : null);

        List<ProductCategoryAttributeAggDTO> pcAttrAggDtoList = Optional.ofNullable(pcAttrDtoList).orElse(Lists.newArrayList())
                .stream().map(pcAttrDto -> {
                    ProductCategoryAttributeAggDTO productCategoryAttributeVO = OrikaMapperUtil.coverObject(pcAttrDto, ProductCategoryAttributeAggDTO.class);
                    productCategoryAttributeVO.setAttrValue(pcAttrDto.getAttrValues());
                    return productCategoryAttributeVO;
                }).collect(Collectors.toList());
        detailDto.setProductCategoryAttrs(pcAttrAggDtoList);

        setInquiryProd(inquireAllVO,detailDto);

        //history chat message
        List<InquiryChatMessageAggDTO> historyMessageOfInquiry = supplierInquiryService.getHistoryMessageOfInquiry(inquiryId, inquireAllVO.getBuyerId(), InquiryCoreConstants.OrderType.ASC, true);
        detailDto.setInquiryHistoryMessages(historyMessageOfInquiry);

        Long buyerId = inquireAllVO.getBuyerId();
        BuyerUserInfoDTO buyerUserInfoDTO = new BuyerUserInfoDTO();
        UserVO buyerUser =  userService.getUserInfo(buyerId);
        if (Objects.nonNull(buyerUser)) {
            covertCommonUserInfoByUser(buyerUserInfoDTO, buyerUser);

            buyerUserInfoDTO.setCompanyName(buyerUser.getCompanyName());
            buyerUserInfoDTO.setJobTitle(buyerUser.getJobTitle());
            buyerUserInfoDTO.setRegDate(buyerUser.getCreateDate());
            buyerUserInfoDTO.setRegion(dictCountryUtils.convertMapForOrderStatus(buyerUser.getCountryCode()));
        } else {
            log.error("invoke userService.getUserInfo user is empty = {}", buyerId);
        }
        detailDto.setBuyerUserInfo(buyerUserInfoDTO);

        //供应商信息
        SellerUserInfoDTO sellerUserInfoDTO = new SellerUserInfoDTO();
        InquirySupplierStatusVO supplierInquireStateVO = supplierInquiryService.inquiryStateInfo(inquiryId);
        if (Objects.isNull(supplierInquireStateVO)) {
            log.error("invoke supplierInquiryFeign.inquiryStateInfo supplier status is empty = {}", inquiryId);
            return detailDto;
        }
            Long supplierId = supplierInquireStateVO.getSupplierId();

            //supplierUserId info
            UserVO supplierUser =  userService.getUserInfo(supplierInquireStateVO.getSupplierUserId());
            if (Objects.nonNull(supplierUser)) {
                covertCommonUserInfoByUser(sellerUserInfoDTO, supplierUser);
            } else {
                log.error("invoke userService.getUserInfo supplierUserId is empty = {}", supplierInquireStateVO.getSupplierUserId());
            }

            //supplier info
            SupplierCommonInfoDTO supplierInfo = supplierService.getSupplierCommonInfo(supplierId);
            sellerUserInfoDTO.setSupplierId(supplierId);
            if (Objects.nonNull(supplierInfo)) {
                sellerUserInfoDTO.setCompanyName(supplierInfo.getCompanyDisplayName());
                sellerUserInfoDTO.setVerifiedManufacturerFlag(supplierInfo.getVerifiedManufacturerFlag());
                sellerUserInfoDTO.setVerifiedSupplierFlag(supplierInfo.getVerifiedSupplierFlag());
                sellerUserInfoDTO.setMaxContractLevel(supplierInfo.getMaxContractLevel());
                sellerUserInfoDTO.setO2oFlag(supplierInfo.getO2oFlag());
                sellerUserInfoDTO.setMemberSince(supplierInfo.getMemberSince());
            } else {
                log.error("invoke supplierService.getSupplierCommonInfo supplierId is empty = {}", supplierId);
            }
            detailDto.setSellerUserInfo(sellerUserInfoDTO);

        return detailDto;
    }

    private void setInquiryProd(InquireAllVO inquireAllVO, AdminInquireDetailAggDTO detailDto) {
                InquiryAllItemEntity result = inquiryAllItemService.getOne(new LambdaQueryWrapper<InquiryAllItemEntity>()
                        .eq(InquiryAllItemEntity::getThreadId, inquireAllVO.getThreadId()));
                if (Objects.isNull(result)) {
                    log.info("invoke inquiryFeign.getInquireAllItemByInquiryId is empty = {}", inquireAllVO.getThreadId());
                    return;
                }
                ProductLiteVO product = productService.getLiteProductById(result.getProductId());
                InquiryProdVO inquiryProdVo = InquiryProdVO.builder()
                                .attachmentType("MC".equals(result.getSourceName()) ? "old" : "new")
                                .categoryName(Objects.nonNull(product)?product.getCategoryName():StringUtils.EMPTY)
                                .productId(Objects.nonNull(result.getProductId()) ? result.getProductId().toString() : StringUtils.EMPTY)
                                .productName(Optional.ofNullable(result.getProductName()).orElse(StringUtils.EMPTY))
                                .productImage(result.getProductImageUrl())
                                .productNum(result.getExpectedOrderQty())
                                .productUnit(result.getExpectedOrderQtyUom())
                                .modelNumber(result.getModelNumber())
                                .build();
                detailDto.setInquiryProd(inquiryProdVo);
    }

    private List<ResponseAttachmentVO> getAttachment(String inquiryId){
        try{
            // refactor add 发送邮件通知 - 询盘附件（job）
            log.info("------ getAttachment, inquiryId:{}", inquiryId);
            List<InquiryAttachmentVO> attachment = inquiryDao.getInquiryAttachmentList(inquiryId);

            // refactor remove 旧查询（AC RFI Details通过threadId查询全部的附件）
            if(CollectionUtils.isNotEmpty(attachment)) {
                List<String> findIds = attachment.stream().filter(Objects::nonNull).map(InquiryAttachmentVO::getFileId).collect(Collectors.toList());

                List<InquiryAttachmentVO> attachmentList = inquiryDao.getInquiryFileList(inquiryId, findIds);
                if(CollectionUtils.isNotEmpty(attachmentList)){
                    attachment.addAll(attachmentList);
                }
                log.info("------ getAttachment, inquiryId:{}, findIds size:{}, attachment size:{}", inquiryId, findIds.size(), attachment.size());
            } else{
                attachment = inquiryDao.getInquiryFileList(inquiryId, null);
                log.info("------ getAttachment, inquiryId:{}, attachment size:{}", inquiryId, attachment.size());
            }

            if (CollectionUtils.isNotEmpty(attachment)) {
                String sign = CipherUtil.getSign(secretKey, "rfi");
                attachment.stream().forEach(att -> {
                    FileReqDTO fileReqDTO = new FileReqDTO();
                    fileReqDTO.setFileKey(att.getUrl());
                    fileReqDTO.setSign(sign);
                    fileReqDTO.setType(1);

                    //获取最大下标

                    if (!"old".equals(att.getAttachmentType())) {
                        String url = attachmentService.getFileUrl(fileReqDTO);
                        att.setUrl(url);
                    }
                    if ("new".equals(att.getAttachmentType())) {
                        String url = attachmentService.getFileUrl(fileReqDTO);
                        att.setUrl(url);
                    }
                    att.setType(FileUtil.fileMimeType(att.getName()));
                    att.setSourceType(SourceNameEnum.MESSAGE.getKey());
                });
                return OrikaMapperUtil.coverList(attachment, ResponseAttachmentVO.class);
            }
        }catch (Exception e){
            log.error("admin rfi detail attachment error :{},{}",inquiryId, JSON.toJSONString(e));
        }
        return new ArrayList<>();
    }

    @Override
    public RfiThreadDetailVO rfiDetail(String threadId) {
        InquireAllVO inquireAllVO = inquiryDao.findOneByThreadId(threadId);
        if (ObjectUtils.isEmpty(inquireAllVO)) {
            log.error("invoke rfi detail is empty, threadId = {}", threadId);
            return null;
        }
        //询盘信息
        RfiThreadDetailVO rfiThreadDetailVO = new RfiThreadDetailVO();
        BeanUtils.copyProperties(inquireAllVO, rfiThreadDetailVO);
        rfiThreadDetailVO.setInquiryType(inquireAllVO.getInquiryType().getKey());
        rfiThreadDetailVO.setSupplierId(inquireAllVO.getInquireAllItemList().getSupplierId());

        List<RfiDetailMessageVO> rfiDetailMessageVOList = new ArrayList<>();

        List<InquireAllEmailChatVO> chatEntityList = inquiryAllEmailChatDao.inquiryAllEmailChatList(threadId, false);
        if (CollectionUtils.isEmpty(chatEntityList)) {
            return null;
        }

        if (!inquireAllVO.getInquiryType().equals(InquiryTypeEnum.SUPPLIER)) {
            RfiProdDetailVO rfiProdDetailVO = new RfiProdDetailVO();
            String desktopProductDetailUrl = productService.getProductUrl(inquireAllVO.getInquireAllItemList().getProductId());
            rfiProdDetailVO.setDesktopProductDetailUrl(desktopProductDetailUrl);

            rfiProdDetailVO.setProductId(inquireAllVO.getInquireAllItemList().getProductId());
            rfiProdDetailVO.setModelNumber(inquireAllVO.getInquireAllItemList().getModelNumber());
            rfiProdDetailVO.setProductName(inquireAllVO.getInquireAllItemList().getProductName());
            rfiProdDetailVO.setProductUrl(inquireAllVO.getInquireAllItemList().getProductImageUrl());
            if (ObjectUtils.isNotEmpty(inquireAllVO.getInquireAllItemList().getExpectedOrderQty())) {
                rfiProdDetailVO.setExpectedOrderQty(Long.valueOf(inquireAllVO.getInquireAllItemList().getExpectedOrderQty()));
            }
            rfiProdDetailVO.setExpectedOrderQtyUom(inquireAllVO.getInquireAllItemList().getExpectedOrderQtyUom());
            List<InquiryProductCategoryAttrCoreDTO> pcAttrDtoList = inquiryProductCategoryAttrService.getAttrDtoByInquiryId(Objects.nonNull(inquireAllVO.getInquiryId()) ? Lists.newArrayList(inquireAllVO.getInquiryId()) : null);

            List<ProductCategoryAttributeVO> pcAttrVOS = Optional.ofNullable(pcAttrDtoList).orElse(Lists.newArrayList()).stream().map(pcAttrDto -> {
                ProductCategoryAttributeVO productCategoryAttributeVO = OrikaMapperUtil.coverObject(pcAttrDto, ProductCategoryAttributeVO.class);
                productCategoryAttributeVO.setAttrValue(pcAttrDto.getAttrValues());
                return productCategoryAttributeVO;
            }).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(pcAttrVOS)) {
                rfiProdDetailVO.setProductCategoryAttributeVOS(pcAttrVOS);
            }
            rfiThreadDetailVO.setProdDetailVO(rfiProdDetailVO);

        }

        RfiDetailMessageVO rfiDetailMessageVO = null;
        for (int i = 0; i < chatEntityList.size(); i++) {

            InquireAllEmailChatVO inquireAllEmailChatVO = chatEntityList.get(i);
            rfiDetailMessageVO = new RfiDetailMessageVO();
            rfiDetailMessageVO.setMessage(inquireAllEmailChatVO.getMessage());
            rfiDetailMessageVO.setCreateDate(inquireAllEmailChatVO.getCreateDate());
            rfiDetailMessageVO.setFrom(inquireAllEmailChatVO.getSenderEmailAddr());
            rfiDetailMessageVO.setTo(Collections.singletonList(inquireAllEmailChatVO.getRecipientEmailAddr()));
            rfiDetailMessageVO.setFirstName(inquireAllEmailChatVO.getFirstName());
            rfiDetailMessageVO.setLastName(inquireAllEmailChatVO.getLastName());

            //CC 抄送
            // refactor 举报-根据requestId查询rfi详情
            List<RfiInquiryEmailAddressCoreVO> rfiInquiryEmailAddressCoreVOS = inquiryAllEmailService.selectInquireEmailList(RfiCommonConstants.CC, inquireAllEmailChatVO.getReplyId());

            // refactor remove 旧查询
            if (CollectionUtils.isEmpty(rfiInquiryEmailAddressCoreVOS)) {
                rfiInquiryEmailAddressCoreVOS = inquiryAllEmailAddressService.emailAddress(inquireAllEmailChatVO.getReplyId());
            }

            if (CollectionUtils.isNotEmpty(rfiInquiryEmailAddressCoreVOS)) {
                rfiDetailMessageVO.setCc(OrikaMapperUtil.coverList(rfiInquiryEmailAddressCoreVOS, RfiInquiryEmailAddressAggVO.class));
            }

            //附件
            // refactor add
            List<InquiryAttachmentVO> attachment = inquiryDao.getInquiryAttachmentArrayByReplyId(chatEntityList.get(i).getReplyId());

            // refactor remove 旧查询
            if (CollectionUtils.isEmpty(attachment)) {
                attachment = inquiryDao.getInquiryFileArrayByReplyId(chatEntityList.get(i).getReplyId());
            }

            if (CollectionUtils.isNotEmpty(attachment)) {
                //s3附件
                rfiDetailMessageVO.setAttachmentVOList(supplierInquiryService.fileUpload(attachment, i));

            }
            rfiDetailMessageVOList.add(rfiDetailMessageVO);
        }

        rfiDetailMessageVOList = rfiDetailMessageVOList.stream().sorted(Comparator.comparing(RfiDetailMessageVO::getCreateDate)).collect(Collectors.toList());
        rfiThreadDetailVO.setRfiDetailMessageVOList(rfiDetailMessageVOList);
        return rfiThreadDetailVO;
    }


    private void covertCommonUserInfoByUser(CommonUserDTO sellerUserInfoDTO, UserVO supplierUser) {
        sellerUserInfoDTO.setUserId(supplierUser.getUserId());
        sellerUserInfoDTO.setEmail(supplierUser.getEmail());
        sellerUserInfoDTO.setFirstName(supplierUser.getFirstName());
        sellerUserInfoDTO.setLastName(supplierUser.getLastName());
        sellerUserInfoDTO.setAvatar(supplierUser.getPhoto());
    }

    @Override
    public PageResult<AdminSearchInquiryDTO> searchRfiList(Integer pageNum, Integer pageSize, String keyword) {
        Page<InquiryAllEntity> page = new Page<>(pageNum, pageSize);
        Boolean numFlag = false;
        if (StringUtils.isNotEmpty(keyword)) {
            Pattern regex = Pattern.compile(regexStr);
            Matcher numRegex = regex.matcher(keyword);
            if (numRegex.find()) {
                numFlag = true;
            }
        }
        return inquiryAllService.searchRfiList(page, keyword, numFlag);
    }

    @Override
    public PageResult<AdminSearchInquiryFaqDTO> searchFaqRfiList(AdminSearchInquiryQueryDTO query) {
        String keyword = query.getKeyword();
        PageResult<AdminSearchInquiryFaqDTO> respData = inquiryAllService.searchFaqRfiCoreList(query, convertKeyword(keyword));
        return buildFaqRfiList(respData, keyword);
    }

    @Override
    public List<InquiryEDMAnalysisVO> edmAnalysis(AdminInquiryEDMDTO dto) {
        List<InquiryEDMAnalysisVO> result = inquiryDao.getEdmTotalCount(dto.getStartDate(),dto.getEndDate());
        List<String> dateSeries=inquiryDao.getDateSeries(dto.getStartDate(),dto.getEndDate());
        if(!Objects.equals(result.size(),dateSeries.size())){
            Set<String> dataSet = result.stream().map(InquiryEDMAnalysisVO::getCreateDate).collect(Collectors.toSet());
            dateSeries.forEach(d->{
                if(!dataSet.contains(d)){
                    result.add(new InquiryEDMAnalysisVO(d));
                }
            });
            result.sort(Comparator.comparing(InquiryEDMAnalysisVO::getCreateDate).reversed());
        }
        List<InquiryEDMAnalysisVO> supplierData = inquiryDao.getEdmTotalSupplierCount(dto.getStartDate(),dto.getEndDate());
        setSupplierData(result,supplierData);

        List<InquiryEDMAnalysisVO> repliedSupplierData = inquiryDao.getEdmRepliedSupplierCount(dto.getStartDate(),dto.getEndDate());
        setRepliedSupplierData(result,repliedSupplierData);

        List<InquiryEDMAnalysisVO> repliedByChatData = inquiryDao.getEdmRepliedByChatSupplierCount(dto.getStartDate(),dto.getEndDate());
        setRepliedByChatData(result,repliedByChatData);

        List<InquiryEDMAnalysisVO> repliedData = inquiryDao.getEdmRepliedInquiryCount(dto.getStartDate(),dto.getEndDate());
        setRepliedData(result,repliedData);

        return result;
    }

    private void setRepliedData(List<InquiryEDMAnalysisVO> result, List<InquiryEDMAnalysisVO> repliedData) {
        Map<String,Integer> dataMap=new HashMap<>(repliedData.size());
        for (InquiryEDMAnalysisVO data:repliedData) {
            dataMap.put(data.getCreateDate(),data.getRepliedCount());
        }
        result.forEach(r->r.setRepliedCount(dataMap.getOrDefault(r.getCreateDate(),0)));
    }

    private void setSupplierData(List<InquiryEDMAnalysisVO> result, List<InquiryEDMAnalysisVO> supplierData) {
        Map<String,Integer> dataMap=new HashMap<>(supplierData.size());
        for (InquiryEDMAnalysisVO data:supplierData) {
            dataMap.put(data.getCreateDate(),data.getSupplierCount());
        }
        result.forEach(r->r.setSupplierCount(dataMap.getOrDefault(r.getCreateDate(),0)));
    }
    private void setRepliedSupplierData(List<InquiryEDMAnalysisVO> result, List<InquiryEDMAnalysisVO> repliedData) {
        Map<String,Integer> dataMap=new HashMap<>(repliedData.size());
        for (InquiryEDMAnalysisVO data:repliedData) {
            dataMap.put(data.getCreateDate(),data.getRepliedCount());
        }
        result.forEach(r->r.setRepliedSupplierCount(dataMap.getOrDefault(r.getCreateDate(),0)));
    }

    private void setRepliedByChatData(List<InquiryEDMAnalysisVO> result, List<InquiryEDMAnalysisVO> repliedByChatData) {
        Map<String,Integer> dataMap=new HashMap<>(repliedByChatData.size());
        for (InquiryEDMAnalysisVO data:repliedByChatData) {
            dataMap.put(data.getCreateDate(),data.getRepliedByChatSupplierCount());
        }
        result.forEach(r->r.setRepliedByChatSupplierCount(dataMap.getOrDefault(r.getCreateDate(),0)));
    }

    private String convertKeyword(String keyword) {
        String processedKeywords = keyword;
        if (StringUtils.isBlank(keyword)) {
            processedKeywords = "-1";
        }
        if (!isInquiry(processedKeywords)) {
            InquiryAllItemEntity result = inquiryAllItemService.getOne(new LambdaQueryWrapper<InquiryAllItemEntity>().eq(InquiryAllItemEntity::getThreadId, keyword));
            if (Objects.nonNull(result)) {
                processedKeywords = result.getInquiryId();
            }
        }
        return processedKeywords;
    }

    private PageResult<AdminSearchInquiryFaqDTO> buildFaqRfiList(PageResult<AdminSearchInquiryFaqDTO> respData, String keyword) {
        List<AdminSearchInquiryFaqDTO> respDataList = respData.getList();
        if (CollectionUtils.isEmpty(respDataList)) {
            return respData;
        }
        String inquiryType = respDataList.get(0).getInquiryType();
        boolean isOneToMany = InquiryTypeEnum.PRODUCT_UPSELL.getKey().equals(inquiryType) || InquiryTypeEnum.CATEGORY.getKey().equals(inquiryType);
        fillCategoryName(respDataList);

        if (isInquiry(keyword) && isOneToMany) {
            List<AdminSearchInquiryFaqDTO> modifiedList = new ArrayList<>(respDataList);
            modifiedList.removeIf(x -> !"Y".equals(x.getSupplierType()));
            modifiedList.add(0, isOriginalInquiry(respDataList));
            return PageResult.restPage(respData, modifiedList);
        }

        if (!isInquiry(keyword)) {
            List<AdminSearchInquiryFaqDTO> rfiIdList = respDataList.stream().filter(x -> keyword.equals(x.getRfiId())).collect(Collectors.toList());
            if (isOneToMany) {
                rfiIdList.add(0, isOriginalInquiry(respDataList));
            }
            // 去除重复元素
            Set<AdminSearchInquiryFaqDTO> rfiIdSet = new LinkedHashSet<>(rfiIdList);
            return PageResult.restPage(respData, new ArrayList<>(rfiIdSet));
        }
        return respData;
    }

    private void fillCategoryName(List<AdminSearchInquiryFaqDTO> respDataList) {
        List<Long> productIds = respDataList.stream().map(AdminSearchInquiryFaqDTO::getProductId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ProductLiteVO> productList = productService.getLiteProductListByIds(productIds);
        Map<Long, String> categoryNameMap = productList.stream().filter(vo -> Objects.nonNull(vo) && Objects.nonNull(vo.getCategoryName())).collect(Collectors.toMap(ProductLiteVO::getProductId, ProductLiteVO::getCategoryName, (key1, key2) -> key2));
        respDataList.stream().filter(x -> Objects.nonNull(x.getProductId())).forEach(x -> {
            if (categoryNameMap.containsKey(x.getProductId())) {
                x.setCategoryName(categoryNameMap.get(x.getProductId()));
            }
            x.setPpId(x.getProductId().toString());
            if (!"Y".equals(x.getSupplierType())) {
                x.setPpId(x.getProductId() + " (Original Inquiry)");
            }
        });
    }

    private AdminSearchInquiryFaqDTO isOriginalInquiry(List<AdminSearchInquiryFaqDTO> respDataList) {
        return respDataList.stream().filter(x -> !"Y".equals(x.getSupplierType())).findFirst().orElse(new AdminSearchInquiryFaqDTO());
    }

    private boolean isInquiry(String keyword) {
        return keyword.startsWith("8005");
    }

    @Override
    public List<RfiConvertRfqCountVO> getConvertRfqCountList(AdminInquiryQueryDTO dto) {
        List<RfiConvertRfqCountVO> result = inquiryDao.getConvertRfqCountList(dto);
        List<String> dateSeries=inquiryDao.getDateSeries(dto.getSDate(),dto.getEDate());
        if(!Objects.equals(result.size(),dateSeries.size())){
            Set<String> dataSet = result.stream().map(RfiConvertRfqCountVO::getCreateDate).collect(Collectors.toSet());
            dateSeries.forEach(d->{
                if(!dataSet.contains(d)){
                    result.add(new RfiConvertRfqCountVO(d));
                }
            });
            result.sort(Comparator.comparing(RfiConvertRfqCountVO::getCreateDate).reversed());
        }
        return result;
    }

    @Override
    public PageResult<AdminTradeshowZoneProductVO> searchTradeshowZoneProductList(AdminSearchInquiryQueryDTO dto) {
        PageResult<AdminTradeshowZoneProductVO> pageResult = new PageResult<>();
        boolean numFlag = NumberUtil.isNumber(dto.getKeyword());
        IPage<AdminTradeshowZoneProductVO> data = rfiTradeshowZoneProductDao.searchRfiTradeshowZoneProductList(new Page<>(dto.getPageNum(), dto.getPageSize()), dto.getKeyword(), numFlag);
        if (Objects.nonNull(data)) {
            if (CollectionUtils.isEmpty(data.getRecords())) {
                data.setRecords(new ArrayList<>());
            } else {

                List<Long> productIdList = data.getRecords().stream().filter(Objects::nonNull).map(AdminTradeshowZoneProductVO::getProductId).distinct().collect(Collectors.toList());
                List<ProductLiteVO> productList = productService.getLiteProductListByIds(productIdList);
                Map<Long, String> productSEOUrlMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(productList)) {
                    productSEOUrlMap = productList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ProductLiteVO::getProductId, ProductLiteVO::getProductSEOUrl, (key1, key2) -> key1));
                }

                String productSEOUrl;
                for (AdminTradeshowZoneProductVO vo : data.getRecords()) {
                    productSEOUrl = MapUtils.getString(productSEOUrlMap, vo.getProductId(), null);
                    if (StringUtils.isNotBlank(productSEOUrl)) {
                        vo.setProductSEOUrl(productSEOUrl);
                    }
                }
                pageResult.setList(data.getRecords());
                pageResult.setTotal(data.getTotal());
                pageResult.setPageNum(data.getCurrent());
                pageResult.setPageSize(data.getSize());
                pageResult.setTotalPage(data.getPages());
            }
        }
        return pageResult;
    }

    @Transactional
    @Override
    public Result<Boolean> saveTradeshowZoneProduct(AdminTradeshowZoneProductSaveDTO dto) {
        if (Objects.isNull(dto)) {
            throw new BusinessException(ResultCode.CommonResultCode.REQUIRED_REQUEST_PARAMETERS_ARE_MISSING);
        }

        //（2）Product Inquiry Code：同一个On-site Zone下不可重复（提示文案：Please enter a unique product inquiry code.）
        if (StringUtils.isNotBlank(dto.getProductInquiryCode()) && checkDuplicate(dto.getRfiTszpId(), dto.getOnSiteZone(), dto.getProductInquiryCode(), null)) {
            throw new BusinessException(ResultCode.RfiResultCode.ADMIN_RFI_TS_ZONE_INQUIRY_CODE_EXISTS);
        }

        //（3）Online Product ID：同一个On-site Zone下不可重复（提示文案：This online product ID already exists.），且此Online Product ID可以正常查询到（提示文案：No matches were found. Please input correct PP ID.）
        if (Objects.nonNull(dto.getProductId()) && checkDuplicate(dto.getRfiTszpId(), dto.getOnSiteZone(), null, dto.getProductId())) {
            throw new BusinessException(ResultCode.RfiResultCode.ADMIN_RFI_TS_ZONE_PRODUCT_ID_EXISTS);
        }

        Long rfiTszpId = dto.getRfiTszpId();
        Date now = new Date();
        if (Objects.isNull(rfiTszpId) || rfiTszpId <= 0) {
            RfiTradeshowZoneProduct rfiTradeshowZoneProduct = OrikaMapperUtil.coverObject(dto, RfiTradeshowZoneProduct.class);
            rfiTradeshowZoneProduct.setCreateBy(dto.getUserId());
            rfiTradeshowZoneProduct.setCreateDate(now);
            rfiTradeshowZoneProduct.setLUpdBy(dto.getUserId());
            rfiTradeshowZoneProduct.setLUpdDate(now);
            rfiTradeshowZoneProduct.setDeleteFlag(false);
            return Result.success(rfiTradeshowZoneProductDao.batchInsertRfiTradeshowZoneProduct(Lists.newArrayList(rfiTradeshowZoneProduct)) > 0);
        } else {
            RfiTradeshowZoneProduct rfiTradeshowZoneProduct = rfiTradeshowZoneProductDao.selectOne(new LambdaQueryWrapper<RfiTradeshowZoneProduct>()
                    .eq(RfiTradeshowZoneProduct::getRfiTszpId, rfiTszpId)
                    .eq(RfiTradeshowZoneProduct::getDeleteFlag, false));

            if (Objects.isNull(rfiTradeshowZoneProduct)) {
                log.info("saveTradeshowZoneProduct zoneProduct is empty, rfiTszpId:{}", rfiTszpId);
                throw new BusinessException(ResultCodeEnum.DATA_NON_EXISTENT);
            }
            LambdaUpdateWrapper<RfiTradeshowZoneProduct> updateWrapper = new LambdaUpdateWrapper<RfiTradeshowZoneProduct>()
                    .set(RfiTradeshowZoneProduct::getOnSiteZone, dto.getOnSiteZone())
                    .set(RfiTradeshowZoneProduct::getGroupCode, dto.getGroupCode())
                    .set(RfiTradeshowZoneProduct::getProductInquiryCode, dto.getProductInquiryCode())
                    .set(RfiTradeshowZoneProduct::getProductId, dto.getProductId())
                    .set(RfiTradeshowZoneProduct::getTradeshowId, dto.getTradeshowId())
                    .set(RfiTradeshowZoneProduct::getLUpdBy, dto.getUserId())
                    .set(RfiTradeshowZoneProduct::getLUpdDate, now)
                    .eq(RfiTradeshowZoneProduct::getRfiTszpId, rfiTszpId);
            return Result.success(rfiTradeshowZoneProductDao.update(null, updateWrapper) > 0);
        }
    }

    public boolean checkDuplicate(Long rfiTszpId, String onSiteZone, String productInquiryCode, Long productId) {
        if (StringUtils.isBlank(onSiteZone) && (StringUtils.isBlank(productInquiryCode) || Objects.isNull(productId))) {
            return false;
        }

        LambdaQueryWrapper<RfiTradeshowZoneProduct> queryWrapper = new LambdaQueryWrapper<RfiTradeshowZoneProduct>()
                .eq(RfiTradeshowZoneProduct::getDeleteFlag, false)
                .eq(RfiTradeshowZoneProduct::getOnSiteZone, onSiteZone)
                .eq(StringUtils.isNotBlank(productInquiryCode), RfiTradeshowZoneProduct::getProductInquiryCode, productInquiryCode)
                .eq(Objects.nonNull(productId), RfiTradeshowZoneProduct::getProductId, productId);

        if (Objects.nonNull(rfiTszpId) && rfiTszpId > 0) {
            queryWrapper.ne(RfiTradeshowZoneProduct::getRfiTszpId, rfiTszpId);
        }

        return Optional.ofNullable(rfiTradeshowZoneProductDao.selectCount(queryWrapper)).orElse(BigInteger.ZERO.intValue()) > 0;
    }

    @AwesomeLog(enableMethodResultLog = false)
    @Override
    public Boolean deleteTradeshowZoneProduct(Long rfiTszpId, Long userId) {
        RfiTradeshowZoneProduct rfiTradeshowZoneProduct = rfiTradeshowZoneProductDao.selectOne(new LambdaQueryWrapper<RfiTradeshowZoneProduct>()
                .eq(RfiTradeshowZoneProduct::getRfiTszpId, rfiTszpId)
                .eq(RfiTradeshowZoneProduct::getDeleteFlag, false));

        if (Objects.isNull(rfiTradeshowZoneProduct)) {
            log.info("deleteTradeshowZoneProduct zoneProduct is empty, rfiTszpId:{}", rfiTszpId);
            throw new BusinessException(ResultCodeEnum.DATA_NON_EXISTENT);
        }

        LambdaUpdateWrapper<RfiTradeshowZoneProduct> updateWrapper = new LambdaUpdateWrapper<RfiTradeshowZoneProduct>()
                .eq(RfiTradeshowZoneProduct::getRfiTszpId, rfiTszpId)
                .eq(RfiTradeshowZoneProduct::getDeleteFlag, false)
                .set(RfiTradeshowZoneProduct::getDeleteFlag, true)
                .set(RfiTradeshowZoneProduct::getOnSiteZone, rfiTradeshowZoneProduct.getOnSiteZone() + "_" + UUID.randomUUID().toString().toLowerCase())
                .set(RfiTradeshowZoneProduct::getLUpdBy, userId)
                .set(RfiTradeshowZoneProduct::getLUpdDate, new Date());
        return rfiTradeshowZoneProductDao.update(null, updateWrapper) > 0;
    }

    @AwesomeLog(enableMethodResultLog = false)
    @Override
    public Boolean deleteAllTradeshowZoneProduct(Long userId) {
        LambdaUpdateWrapper<RfiTradeshowZoneProduct> updateWrapper = new LambdaUpdateWrapper<RfiTradeshowZoneProduct>()
                .eq(RfiTradeshowZoneProduct::getDeleteFlag, false)
                .set(RfiTradeshowZoneProduct::getDeleteFlag, true)
                .set(RfiTradeshowZoneProduct::getLUpdBy, userId)
                .set(RfiTradeshowZoneProduct::getLUpdDate, new Date());
        return rfiTradeshowZoneProductDao.update(null, updateWrapper) > 0;
    }

    @Override
    public Boolean batchUploadTradeshowZoneProduct(Long userId, MultipartFile uploadFile) {
        if (Objects.isNull(uploadFile) || StringUtils.isBlank(uploadFile.getOriginalFilename())) {
            log.error("batchUploadTradeshowZoneProduct save null, fileName:{}, userId:{}", Objects.isNull(uploadFile) ? "null" : uploadFile.getOriginalFilename(), userId);
            return false;
        }

        try {
            rfiTradeshowZoneProductListener.setUserIdVar(userId);
            //插入数据
            EasyExcelFactory.read(uploadFile.getInputStream(), RfiTradeshowZoneProductExcelDTO.class, rfiTradeshowZoneProductListener).sheet().doRead();
            return true;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("--------------batchUploadTradeshowZoneProduct error, fileName:{}, userId:{}", uploadFile.getOriginalFilename(), userId, e);
            throw new BusinessException(ResultCode.CommonResultCode.ARGUMENT_VALID_FAILED);
        }
    }

    @Override
    public List<AdminTradeshowZoneProductVO> downloadTradeshowZoneProductList() {
        List<AdminTradeshowZoneProductVO> list = rfiTradeshowZoneProductDao.searchRfiTradeshowZoneProductList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.stream().filter(Objects::nonNull).forEach(pp ->
                    pp.setOnSiteZone(RfiAdminTypeEnum.getTypeByKey(pp.getOnSiteZone()))
            );
        }
        return list;
    }

    @Override
    public List<String> getTradeshowZoneProductGroupCodeList(String rfiType) {
        return rfiTradeshowZoneProductDao.getTradeshowZoneProductGroupCodeList(rfiType);
    }

    @Override
    public List<InquiryTradeshowZoneProductVO> getTradeshowZoneProductInfo(AdminProductQueryDTO dto) {
        List<ProductLiteVO> productList = null;

        LambdaQueryWrapper<RfiTradeshowZoneProduct> query = new LambdaQueryWrapper<RfiTradeshowZoneProduct>()
                .eq(RfiTradeshowZoneProduct::getDeleteFlag, false);

        if(Objects.equals("ProductId",dto.getType())){
            productList = productService.getLiteProductListByIds(dto.getCodeList().stream().map(Long::parseLong).collect(Collectors.toList()));
            if(CollectionUtils.isEmpty(productList)){
                return Collections.emptyList();
            }
            query.in(RfiTradeshowZoneProduct::getProductId,dto.getCodeList());
        }
        if(Objects.equals("GroupCode",dto.getType())){
            query.in(RfiTradeshowZoneProduct::getGroupCode,dto.getCodeList());
            if(StringUtils.isNotBlank(dto.getRfiType())){
                query.eq(RfiTradeshowZoneProduct::getOnSiteZone, dto.getRfiType());
            }
        }
        if(Objects.equals("ProductInquiryCode",dto.getType())){
            query.in(RfiTradeshowZoneProduct::getProductInquiryCode,dto.getCodeList());
        }
        List<RfiTradeshowZoneProduct> rfiTradeshowZoneProductList = rfiTradeshowZoneProductDao.selectList(query);

        if(CollectionUtils.isEmpty(productList) && CollectionUtils.isNotEmpty(rfiTradeshowZoneProductList)){
            productList = productService.getLiteProductListByIds(rfiTradeshowZoneProductList.stream().map(RfiTradeshowZoneProduct::getProductId).collect(Collectors.toList()));
            if(CollectionUtils.isEmpty(productList)){
                return Collections.emptyList();
            }
        }
        List<InquiryTradeshowZoneProductVO> result = OrikaMapperUtil.coverList(productList, InquiryTradeshowZoneProductVO.class);
        Map<Long,RfiTradeshowZoneProduct> dataMap = new HashMap<>(rfiTradeshowZoneProductList.size());
        rfiTradeshowZoneProductList.forEach(vo->dataMap.put(vo.getProductId(),vo));
        for(InquiryTradeshowZoneProductVO vo:result){
            if(dataMap.containsKey(vo.getProductId())){
                vo.setGroupCode(dataMap.get(vo.getProductId()).getGroupCode());
                vo.setProductInquiryCode(dataMap.get(vo.getProductId()).getProductInquiryCode());
            }
        }
        return result;
    }

    @Override
    public PageResult<AdminOnSiteRfiListVO> searchOnSiteRfiList(AdminOnSiteRfiQueryDTO dto) {
        PageResult<AdminOnSiteRfiListVO> pageResult = new PageResult<>();
        try {
            IPage<AdminOnSiteRfiListVO> result = inquiryDao.searchOnSiteRfiList(new Page<>(dto.getPageNum(), dto.getPageSize()), dto);

            if(Objects.isNull(result)){
                return pageResult;
            }

            List<AdminOnSiteRfiListVO> list = result.getRecords();
            if (CollectionUtils.isNotEmpty(list)) {
                buildAdminOnSiteRfiListVO(list);
            }

            pageResult.setList(result.getRecords());
            pageResult.setTotal(result.getTotal());
            pageResult.setPageNum(result.getCurrent());
            pageResult.setPageSize(result.getSize());
            pageResult.setTotalPage(result.getPages());
        } catch (Exception e) {
            log.error("buyer inquiryList error, dto:{},error:{}", dto, JSON.toJSONString(e));
        }
        return pageResult;
    }

    @Override
    public List<AdminOnSiteRfiListVO> downloadOnSiteRfiList(AdminOnSiteRfiQueryDTO dto) {
        try {
            List<AdminOnSiteRfiListVO> list = inquiryDao.searchOnSiteRfiList(dto);
            if (CollectionUtils.isNotEmpty(list)) {
                buildAdminOnSiteRfiListVO(list);
            }

            return list;
        } catch (Exception e) {
            log.error("buyer inquiryList error, dto:{},error:{}", dto, JSON.toJSONString(e));
        }
        return new ArrayList<>();
    }

    private void buildAdminOnSiteRfiListVO(List<AdminOnSiteRfiListVO> list) {
        // 产品ID列表
        List<Long> productIdList = list.stream().filter(Objects::nonNull).map(AdminOnSiteRfiListVO::getProductId).collect(Collectors.toList());

        // 产品列表
        //先拿在线产品
        List<OnlineProductEntityDTO> productDetailList = ResultUtil.getData(productAggFeign.getOnlineProductEntity(OnlineProdEntityQueryAggDTO.builder().onlineFlag(true).productIds(productIdList).build()));
        if(CollectionUtils.isEmpty(productDetailList) || !Objects.equals(productIdList.size(),productDetailList.size())){
            //拿离线产品
            productDetailList.addAll(ResultUtil.getData(productAggFeign.getOnlineProductEntity(OnlineProdEntityQueryAggDTO.builder().onlineFlag(false).productIds(productIdList).build())));
        }
        Map<Long, OnlineProductEntityDTO> productMap = getProductLiteMap(productDetailList);

        // L4列表
        List<Long> l4CategoryIdList = productDetailList.stream().filter(Objects::nonNull).map(OnlineProductEntityDTO::getCategoryId).collect(Collectors.toList());
        List<ProductCategory> l4CategoryList = getCategoryList(l4CategoryIdList);
        Map<Long, ProductCategory> l4CategoryMap = getProductCategoryMap(l4CategoryList);

        // L1列表
        List<Long> l1CategoryIdList = l4CategoryList.stream().filter(Objects::nonNull).map(ProductCategory::getL1CategoryId).collect(Collectors.toList());
        List<ProductCategory> l1CategoryList = getCategoryList(l1CategoryIdList);
        Map<Long, ProductCategory> l1CategoryMap = getProductCategoryMap(l1CategoryList);

        Map<Long, String> companyNameMap = getCompanyName(list);

        Map<String, String> countryMap = dictService.getDicKeyValueMap(COUNTRY, "enus");

        List<String> countryCodeList = list.stream().filter(Objects::nonNull).map(AdminOnSiteRfiListVO::getCountryCode).collect(Collectors.toList());
        Map<String, DictionaryItemVO> regionItemMap = dictService.getRegionItemMap(countryCodeList);

        Map<String, InquiryChatMessageCountDTO> messageCountMap = getInquiryChatMessageCountMap(list);

        list.forEach(v -> {
            v.setOnSiteZone(RfiSourceEnum.getRfiAdminType(v.getRfiSource()));

            OnlineProductEntityDTO product = (OnlineProductEntityDTO) MapUtils.getObject(productMap, v.getProductId(), new OnlineProductEntityDTO());
            ProductCategory l4Cat = (ProductCategory) MapUtils.getObject(l4CategoryMap, product.getCategoryId(), new ProductCategory());
            if (Objects.nonNull(l4Cat.getCategoryId()) && StringUtils.isNotBlank(l4Cat.getDescEn())) {
                v.setL4Category(l4Cat.getCategoryId() + "-" + l4Cat.getDescEn());
            }

            if (Objects.nonNull(l4Cat.getL1CategoryId())) {
                ProductCategory l1Cat = (ProductCategory) MapUtils.getObject(l1CategoryMap, l4Cat.getL1CategoryId(), new ProductCategory());
                if (Objects.nonNull(l1Cat.getCategoryId()) && StringUtils.isNotBlank(l1Cat.getDescEn())) {
                    v.setL1Category(l1Cat.getCategoryId() + "-" + l1Cat.getDescEn());
                }
            }

            v.setCountry(countryMap.get(v.getCountryCode()));
            v.setSupplierIdStr(v.getSupplierId().toString());
            v.setSupplierName(companyNameMap.get(v.getSupplierId()));

            // 大洲
            DictionaryItemVO itemVO =(DictionaryItemVO) MapUtils.getObject(regionItemMap, v.getCountryCode(), new DictionaryItemVO());
            v.setBuyerContinent(itemVO.getI18nValue());
            InquiryChatMessageCountDTO countDTO = (InquiryChatMessageCountDTO) MapUtils.getObject(messageCountMap, v.getRfiId());
            v.setSupplierReplied(getSupplierReplied(countDTO));
            v.setBuyerReplied(getBuyerReplied(countDTO));
        });
    }

    private Map<Long, OnlineProductEntityDTO> getProductLiteMap(List<OnlineProductEntityDTO> productDetailList) {
        Map<Long, OnlineProductEntityDTO> productMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productDetailList)) {
            productDetailList.stream().filter(Objects::nonNull).forEach(pp -> productMap.put(pp.getProductId(), pp));
        }
        return productMap;
    }

    private Map<Long, ProductCategory> getProductCategoryMap(List<ProductCategory> l4CategoryList) {
        Map<Long, ProductCategory> l4CategoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(l4CategoryList)) {
            l4CategoryList.stream().filter(Objects::nonNull).forEach(cat -> l4CategoryMap.put(cat.getCategoryId(), cat));
        }
        return l4CategoryMap;
    }

    private Map<Long, String> getCompanyName(List<AdminOnSiteRfiListVO> list) {
        // 供应商ID列表
        List<Long> supplierIdList = list.stream().filter(Objects::nonNull).map(AdminOnSiteRfiListVO::getSupplierId).collect(Collectors.toList());

        Map<Long, String> companyNameMap = new HashMap<>();
        // 供应商名称列表
        if(CollectionUtils.isNotEmpty(supplierIdList)) {
            List<SuppNameAggDTO> suppNameList = supplierService.getSupplierNameList(supplierIdList);
            if (CollectionUtils.isNotEmpty(suppNameList)) {
                suppNameList.stream().filter(Objects::nonNull).forEach(supp -> companyNameMap.put(supp.getOrgId(), supp.getOrgName()));
            }
        }
        return companyNameMap;
    }

    private Map<String, InquiryChatMessageCountDTO> getInquiryChatMessageCountMap(List<AdminOnSiteRfiListVO> list) {
        // 卖家和买家回复标记
        List<String> rfiIdList = list.stream().filter(Objects::nonNull).map(AdminOnSiteRfiListVO::getRfiId).collect(Collectors.toList());
        Map<String, InquiryChatMessageCountDTO> messageCountMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(rfiIdList)) {
            List<InquiryChatMessageCountDTO> messageCountList = inquiryAllEmailChatDao.getInquiryChatMessageCount(rfiIdList);
            if (CollectionUtils.isNotEmpty(messageCountList)) {
                messageCountList.stream().filter(Objects::nonNull).forEach(msg -> messageCountMap.put(msg.getThreadId(), msg));
            }
        }
        return messageCountMap;
    }

    private String getSupplierReplied(InquiryChatMessageCountDTO countDTO) {
        return (Objects.nonNull(countDTO) && countDTO.getSupplierMessageCount() > 0) ? "Yes" : "No";
    }

    private String getBuyerReplied(InquiryChatMessageCountDTO countDTO) {
        return (Objects.nonNull(countDTO) && countDTO.getBuyerMessageCount() > 1) ? "Yes" : "No";
    }

    private List<ProductCategory> getCategoryList(List<Long> categoryIdList) {
        List<ProductCategory> categoryList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(categoryIdList)){
            categoryList = categoryService.queryCategoryListByIds(categoryIdList, "enus");
        }
        return categoryList;
    }

    @Override
    public List<TsOnSiteZoneGroupVO> searchZoneProductList(TsQueryDTO dto) {
        log.info("------ searchZoneProductList dto:{}", JSON.toJSONString(dto));
        if(StringUtils.isNotBlank(dto.getOnSiteZone()) && Objects.equals(dto.getOnSiteZone(), SurveyGrpCodeEnums.ROW.getCode())){
            dto.setOnSiteZone(RfiAdminTypeEnum.TF.getKey());
        }
        List<TsOnSiteZoneProductVO> zoneProductList = rfiTradeshowZoneProductDao.searchZoneProductList(dto.getOnSiteZone());

        if (CollectionUtils.isEmpty(zoneProductList)) {
            return Collections.emptyList();
        }

        List<Long> productIdList = zoneProductList.stream().filter(Objects::nonNull).map(TsOnSiteZoneProductVO::getProductId).distinct().collect(Collectors.toList());

        List<ProductLiteVO> productList = productService.getLiteProductListByIds(productIdList, true, true, false);

        log.info("------ searchZoneProductList productIdList:{}, productList:{}", JSON.toJSONString(productIdList), JSON.toJSONString(productList));
        Map<Long, ProductLiteVO> productMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productList)) {
            productMap = productList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ProductLiteVO::getProductId, Function.identity(), (k1, k2) -> k2));
        }

        ProductLiteVO liteVO;
        List<TsOnSiteZoneProductVO> voList = new ArrayList<>();
        for (TsOnSiteZoneProductVO pp : zoneProductList) {
            liteVO = productMap.get(pp.getProductId());
            if (ObjectUtils.isNotEmpty(liteVO)) {
                pp.setProductName(liteVO.getProductName());
                pp.setMinOrderQuantity(liteVO.getMinOrderQuantity());
                pp.setMinOrderUnit(liteVO.getMinOrderUnit());
                if(Objects.nonNull(liteVO.getMinOrderQuantity()) && Objects.equals(liteVO.getMinOrderQuantity(), 1)){
                    pp.setMinOrderUnit(liteVO.getMinOrderSingleUnit());
                }
                pp.setListVoShowPriceStr(liteVO.getListVoShowPriceStr());
                pp.setProductPrimaryImage(liteVO.getProductPrimaryImage());
                pp.setCategoryId(liteVO.getCategoryId());
                pp.setCategoryName(liteVO.getCategoryName());
                voList.add(pp);
            } else {
                log.info("------ searchZoneProductList product:[{}] is offline.", JSON.toJSONString(pp));
            }
        }

        Map<String, List<TsOnSiteZoneProductVO>> zoneProductMap = voList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(TsOnSiteZoneProductVO::getGroupCode, LinkedHashMap::new, Collectors.toList()));

        List<TsOnSiteZoneGroupVO> zoneList = new ArrayList<>();
        for (Map.Entry<String, List<TsOnSiteZoneProductVO>> entry : zoneProductMap.entrySet()) {
            TsOnSiteZoneGroupVO zoneVO = TsOnSiteZoneGroupVO.builder()
                    .groupCode(entry.getKey())
                    .productList(zoneProductMap.get(entry.getKey()))
                    .build();
            zoneList.add(zoneVO);
        }
        return zoneList;
    }

}