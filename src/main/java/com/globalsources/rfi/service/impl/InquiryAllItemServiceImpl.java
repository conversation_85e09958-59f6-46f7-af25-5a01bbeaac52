package com.globalsources.rfi.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.globalsources.rfi.data.dao.InquiryAllItemDao;
import com.globalsources.rfi.data.entity.InquiryAllItemEntity;
import com.globalsources.rfi.service.InquiryAllItemService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-11
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@Validated
public class InquiryAllItemServiceImpl extends ServiceImpl<InquiryAllItemDao, InquiryAllItemEntity> implements InquiryAllItemService {

    @Override
    public List<InquiryAllItemEntity> getInquireAllItemByThreadIds(List threadIds) {
        if (CollectionUtils.isEmpty(threadIds)) {
            return Lists.newArrayList();
        }
        List<InquiryAllItemEntity> itemList = this.baseMapper.selectList(Wrappers.lambdaQuery(InquiryAllItemEntity.class)
                .in(InquiryAllItemEntity::getThreadId, threadIds));
        return Optional.ofNullable(itemList).orElse(Lists.newArrayList());
    }

    @Override
    public boolean updateSubjectByThreadId(String threadId, String subject) {
        log.info("InquiryAllItemServiceImpl updateSubjectByThreadId threadId:{},subject:{}",threadId,subject);

        int resultCount = this.baseMapper.update(null, new LambdaUpdateWrapper<InquiryAllItemEntity>().eq(InquiryAllItemEntity::getThreadId, threadId)
                .set(InquiryAllItemEntity::getSubject, subject)
                .set(InquiryAllItemEntity::getLUpdDate, new Date()));
        return resultCount>0;
    }

    @Override
    public boolean updatePotentialOpportunityConvertFlag(String threadId) {
        log.info("InquiryAllItemServiceImpl updatePotentialOpportunityConvertFlag threadId:{}",threadId);

        int resultCount = this.baseMapper.update(null, new LambdaUpdateWrapper<InquiryAllItemEntity>().eq(InquiryAllItemEntity::getThreadId, threadId)
                .set(InquiryAllItemEntity::getPotentialOpportunityConvertFlag, Boolean.TRUE)
                .set(InquiryAllItemEntity::getLUpdDate, new Date()));
        return resultCount>0;
    }

    @Override
    public boolean markPotentialOpportunityConvert(List<String> threadIdList, String source) {
        //s69 潜在商机转换标记
        int resultCount = this.baseMapper.update(null,
                new LambdaUpdateWrapper<InquiryAllItemEntity>()
                        .eq(InquiryAllItemEntity::getPotentialOpportunityFlag,Boolean.TRUE)
                        .eq(InquiryAllItemEntity::getPotentialOpportunityConvertFlag,Boolean.FALSE)
                        .in(InquiryAllItemEntity::getThreadId, threadIdList)
                        .set(InquiryAllItemEntity::getPotentialOpportunityConvertFlag, Boolean.TRUE)
                        .set(InquiryAllItemEntity::getLUpdDate, new Date())
                        .set(InquiryAllItemEntity::getPotentialOpportunityConvertSource,source));
        log.info("markPotentialOpportunityConvert threadIdList :{} source :{} resultCount :{}", threadIdList,source,resultCount);
        return resultCount>0;
    }
}
