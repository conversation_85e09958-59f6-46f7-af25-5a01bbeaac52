/**
 * <a>Title: IBuyerInquiryServiceImpl </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/7/13-20:34
 */
package com.globalsources.rfi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.globalsources.agg.admin.api.feign.RfqSensitiveKeywordFeign;
import com.globalsources.agg.supplier.api.model.dto.SupplierCommonInfoDTO;
import com.globalsources.agg.supplier.api.model.dto.certificate.OnlineCertificateDTO;
import com.globalsources.agg.supplier.api.model.dto.organization.OrganizationDTO;
import com.globalsources.agg.supplier.api.model.dto.section.SectionVerifiableFieldDTO;
import com.globalsources.email.enums.RfxEmailTemplateEnum;
import com.globalsources.email.po.EmailModelPO;
import com.globalsources.email.vo.InquiryProductVO;
import com.globalsources.email.vo.InquiryReplyDetailVO;
import com.globalsources.email.vo.InquiryReplyVO;
import com.globalsources.file.api.dto.FileReqDTO;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.IResultCode;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.CipherUtil;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserBaseProfileVO;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.product.agg.api.dto.category.SuppProductCategoryLinkAggDTO;
import com.globalsources.product.agg.api.dto.product.OnlineProductEntityDTO;
import com.globalsources.product.agg.api.vo.ProductCategoryVO;
import com.globalsources.product.agg.api.vo.ProductLiteVO;
import com.globalsources.rfi.agg.constants.RfiCommonConstants;
import com.globalsources.rfi.agg.core.dto.InquiryBuyerStatusDTO;
import com.globalsources.rfi.agg.core.dto.attachment.AttachmentCoreDTO;
import com.globalsources.rfi.agg.core.dto.match.InquiryMatchResultDTO;
import com.globalsources.rfi.agg.core.dto.mc.RequestRfiReadDTO;
import com.globalsources.rfi.agg.core.vo.InquireAllEmailChatVO;
import com.globalsources.rfi.agg.core.vo.InquireAllVO;
import com.globalsources.rfi.agg.core.vo.InquiryAttachmentVO;
import com.globalsources.rfi.agg.core.vo.InquiryBuyerStatusVO;
import com.globalsources.rfi.agg.core.vo.InquirySupplierStatusVO;
import com.globalsources.rfi.agg.core.vo.StateInquiryVO;
import com.globalsources.rfi.agg.core.vo.rfi.RfiInquiryEmailAddressCoreVO;
import com.globalsources.rfi.agg.dto.inquiry.InquiryStatusDTO;
import com.globalsources.rfi.agg.dto.product.FilterProductDTO;
import com.globalsources.rfi.agg.email.RfiMsgVO;
import com.globalsources.rfi.agg.enums.InquiryStatusEnum;
import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import com.globalsources.rfi.agg.enums.ReviewResultEnum;
import com.globalsources.rfi.agg.enums.SourceNameEnum;
import com.globalsources.rfi.agg.request.*;
import com.globalsources.rfi.agg.request.message.InquiryMessageListDTO;
import com.globalsources.rfi.agg.request.product.InquiryProductCategoryAttrCoreDTO;
import com.globalsources.rfi.agg.response.APPInquireBuyerStatusTotalVO;
import com.globalsources.rfi.agg.response.BuyerInquiryListTotalVO;
import com.globalsources.rfi.agg.response.BuyerInquiryTotalVO;
import com.globalsources.rfi.agg.response.BuyerInquiryVO;
import com.globalsources.rfi.agg.response.IdsVO;
import com.globalsources.rfi.agg.response.ResponseAttachmentVO;
import com.globalsources.rfi.agg.response.app.RfiFileVO;
import com.globalsources.rfi.agg.response.detail.BuyerInquiryDetailVO;
import com.globalsources.rfi.agg.response.detail.CertificateVO;
import com.globalsources.rfi.agg.response.detail.InquiryBuyerDetailVO;
import com.globalsources.rfi.agg.response.detail.InquiryCompanyVO;
import com.globalsources.rfi.agg.response.detail.InquiryMessageVO;
import com.globalsources.rfi.agg.response.detail.InquiryProductInfo;
import com.globalsources.rfi.agg.response.detail.InquiryReplyDetailChatVO;
import com.globalsources.rfi.agg.response.detail.SupplierDynamicVO;
import com.globalsources.rfi.agg.response.product.ProductCategoryAttributeVO;
import com.globalsources.rfi.agg.response.rfi.ProductInfoVO;
import com.globalsources.rfi.agg.response.rfi.RfiInquiryEmailAddressAggVO;
import com.globalsources.rfi.data.dao.InquiryAllDao;
import com.globalsources.rfi.data.dao.InquiryAllEmailChatDao;
import com.globalsources.rfi.data.dao.InquiryAllItemDao;
import com.globalsources.rfi.data.dao.InquiryBuyerStatusDao;
import com.globalsources.rfi.data.dao.InquiryMatchResultDao;
import com.globalsources.rfi.data.dao.InquirySupplierStatusDao;
import com.globalsources.rfi.data.entity.InquiryAllEmailChatEntity;
import com.globalsources.rfi.data.entity.InquiryAllEntity;
import com.globalsources.rfi.data.entity.InquiryAllItemEntity;
import com.globalsources.rfi.data.entity.InquiryBuyerStatusEntity;
import com.globalsources.rfi.data.entity.InquirySupplierStatusEntity;
import com.globalsources.rfi.service.BuyerInquiryService;
import com.globalsources.rfi.service.InquiryAllEmailAddressService;
import com.globalsources.rfi.service.InquiryAllEmailChatService;
import com.globalsources.rfi.service.InquiryAllEmailService;
import com.globalsources.rfi.service.InquiryAllFileAttachmentService;
import com.globalsources.rfi.service.InquiryAllService;
import com.globalsources.rfi.service.InquiryProductCategoryAttrService;
import com.globalsources.rfi.service.InquiryStatusService;
import com.globalsources.rfi.service.RabbitService;
import com.globalsources.rfi.service.SupplierInquiryService;
import com.globalsources.rfi.utils.AmazonUtil;
import com.globalsources.rfi.utils.DictCountryUtils;
import com.globalsources.rfi.utils.FileUtil;
import com.globalsources.rfi.utils.GoogleUtil;
import com.globalsources.rfi.utils.InquiryEmailAddressUtil;
import com.globalsources.rfi.utils.InquiryReplyUtil;
import com.globalsources.rfi.utils.LocalDateUtils;
import com.globalsources.rfi.utils.MyBeanUtils;
import com.globalsources.rfx.enums.ContractCodeEnum;
import com.globalsources.rfx.service.IAttachmentService;
import com.globalsources.rfx.service.ICategoryService;
import com.globalsources.rfx.service.IChatService;
import com.globalsources.rfx.service.IProductService;
import com.globalsources.rfx.service.ISupplierService;
import com.globalsources.rfx.service.IUserService;
import com.globalsources.rfx.util.IdGenerator;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.shardingsphere.api.hint.HintManager;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class BuyerInquiryServiceImpl implements BuyerInquiryService {

    
    private final AmazonUtil amazonUtil;

    @Value("${third.sso.dataKey}")
    private String secretKey;

    private static final String HOST_NAME = "www.globalsources.com";

    
    private final IChatService chatService;

    
    private final RabbitService rabbitService;

    
    private final DictCountryUtils dictCountryUtils;

    
    private final InquiryAllService inquireAllService;

    
    private final InquiryEmailAddressUtil inquiryEmailAddressUtil;

    
    private final InquiryAllFileAttachmentService inquireAllFileAttachmentService;

    
    private final RabbitService rfiMsgService;

    
    private final InquiryAllEmailChatDao inquiryAllEmailChatDao;

    
    private final InquiryAllEmailChatService inquireAllEmailChatService;
    
    private final RfqSensitiveKeywordFeign sensitiveKeywordFeign;

    
    private final GoogleUtil googleUtil;

    
    private final InquiryAllItemDao inquiryAllItemDao;

    
    private final InquiryAllDao inquiryAllDao;

    
    private final InquirySupplierStatusDao inquirySupplierStatusDao;

    
    private final InquiryBuyerStatusDao inquiryBuyerStatusDao;

    
    private final InquiryStatusService inquiryStatusService;

    
    private final IProductService productService;

    
    private final ISupplierService supplierService;

    
    private final ICategoryService categoryService;

    
    private final IUserService userService;

    
    private final IAttachmentService attachmentService;

    @Value("${supplier.inquiry.search.result.count.limit:5}")
    private Integer supplierInquirySearchResultCountLimit;

    @Value("${supplier.inquiry.search.result.hour.limit:24}")
    private Integer supplierInquirySearchResultHourLimit;

    @Value("${related.product.count.limit:2}")
    private Integer relatedProductCountLimit;

    @Value("${related.product.hour.limit:24}")
    private Integer relatedProductHourLimit;
    
    private final InquiryMatchResultDao inquiryMatchResultDao;

    
    private final SupplierInquiryService supplierInquiryService;

    
    private final InquiryAllEmailAddressService inquiryAllEmailAddressService;

    
    private final InquiryAllEmailService inquiryAllEmailService;

    
    private final InquiryProductCategoryAttrService inquiryProductCategoryAttrService;

    @Override
    public PageResult<BuyerInquiryVO> inquiryList(BuyerInquiryListDTO buyerInquiryListDTO, Long buyerId) {
        log.info("buyer inquiryList buyerInquiryListDTO : {} buyerId :{}",buyerInquiryListDTO,buyerId);
        PageResult<BuyerInquiryVO> pageResult = new PageResult<>();
        try{
            Page<BuyerInquiryVO> page = new Page<>(buyerInquiryListDTO.getPageNum(), buyerInquiryListDTO.getPageSize());
            IPage<BuyerInquiryVO> data = inquiryAllDao.getBuyerTablePage(page, buyerInquiryListDTO, buyerId);
            if(Objects.isNull(data) || CollectionUtils.isEmpty(data.getRecords())){
                pageResult.setList(new ArrayList<>());
                pageResult.setTotal(0L);
                pageResult.setPageNum(Long.valueOf(buyerInquiryListDTO.getPageNum()));
                pageResult.setPageSize(Long.valueOf(buyerInquiryListDTO.getPageSize()));
                pageResult.setTotalPage(0L);
                return pageResult;
            }
            processInquiryIdJump(buyerInquiryListDTO,data.getRecords(),buyerId);

            //set childrenUnreadCount
            Map<Long, SupplierCommonInfoDTO> supplierInfoMap = getSupplierCompanyNameBatch(data.getRecords());
            Map<Long, String> supplierUserPhotoMap = getSupplierUserPhotoBatch(data.getRecords());
            Map<Long, Long> productCategoryMap = getProductCategoryMap(data.getRecords());

            for (BuyerInquiryVO vo : data.getRecords()) {
                setNewMsgAndRepliedFlag(vo);
                if(Objects.nonNull(supplierInfoMap.get(vo.getSupplierId()))){
                    vo.setCompanyName(supplierInfoMap.get(vo.getSupplierId()).getCompanyDisplayName());
                    vo.setMaxContractLevel(supplierInfoMap.get(vo.getSupplierId()).getMaxContractLevel());
                    vo.setSupplierType(supplierInfoMap.get(vo.getSupplierId()).getSupplierType());
                }
                vo.setPrincipalPhoto(supplierUserPhotoMap.get(vo.getSupplierUserId()));
                vo.setProductCategoryId(productCategoryMap.get(vo.getProductId()));
                if (Boolean.TRUE.equals(vo.getRecommendFlag()) && Objects.equals(buyerInquiryListDTO.getType(), 1) && Objects.equals(buyerInquiryListDTO.getTabType(), 1)) {
                    processChildrenData(vo,supplierInfoMap,supplierUserPhotoMap,buyerInquiryListDTO,productCategoryMap);
                }
            }
            pageResult.setList(data.getRecords());
            pageResult.setTotal(data.getTotal());
            pageResult.setPageNum(data.getCurrent());
            pageResult.setPageSize(data.getSize());
            pageResult.setTotalPage(data.getPages());

        }catch (Exception e){
            log.error("buyer inquiryList error, dto :{},buyerId :{},error :{}",buyerInquiryListDTO,buyerId, JSON.toJSONString(e));
        }
        return pageResult;
    }

    private void processChildrenData(BuyerInquiryVO vo,Map<Long, SupplierCommonInfoDTO> supplierInfoMap,Map<Long, String> supplierUserPhotoMap,BuyerInquiryListDTO buyerInquiryListDTO,Map<Long, Long> productCategoryMap) {
        List<BuyerInquiryVO> upsellList = inquiryAllDao.getUpsellListByInquiryId(vo.getParentId(), buyerInquiryListDTO);
        vo.setChildren(null);
        vo.setChildrenUnreadCount(0);
        if (CollectionUtils.isNotEmpty(upsellList)) {
            upsellList.parallelStream().forEach(this::setNewMsgAndRepliedFlag);
            vo.setChildren(upsellList);
            vo.setChildrenUnreadCount((int) vo.getChildren().stream().filter(v -> !v.getReadFlag()).count());
            for (BuyerInquiryVO children : vo.getChildren()) {
                if(Objects.nonNull(supplierInfoMap.get(children.getSupplierId()))){
                    children.setCompanyName(supplierInfoMap.get(children.getSupplierId()).getCompanyDisplayName());
                    children.setMaxContractLevel(supplierInfoMap.get(children.getSupplierId()).getMaxContractLevel());
                    children.setSupplierType(supplierInfoMap.get(children.getSupplierId()).getSupplierType());
                }
                children.setPrincipalPhoto(supplierUserPhotoMap.get(children.getSupplierUserId()));
                children.setProductCategoryId(productCategoryMap.get(children.getProductId()));
                children.setParentId(vo.getParentId());
            }
        }
    }

    private void processInquiryIdJump(BuyerInquiryListDTO buyerInquiryListDTO, List<BuyerInquiryVO> records,Long buyerId) {
        // 传入inquireId   则判断是否存在列表  不存在多查询一次
        if (StringUtils.isNotBlank(buyerInquiryListDTO.getInquiryId())) {
            boolean isContain = records.stream().anyMatch(m -> m.getInquiryId().equals(buyerInquiryListDTO.getInquiryId()));
            if (!isContain) {
                // 不包含inquiry 查询一次
                buyerInquiryListDTO.setInquiryFlag("true");
                BuyerInquiryVO inquiryVO = inquiryAllDao.getBuyerInquiryByThread(buyerInquiryListDTO, buyerId);

                if (null != inquiryVO) {
                    records.add(0, inquiryVO);
                }
            }
        }
    }

    private void setNewMsgAndRepliedFlag(BuyerInquiryVO vo){
        List<InquireAllEmailChatVO> inquireAllEmailChatList = inquiryAllEmailChatDao.inquiryAllEmailChatList(vo.getInquiryId(),false);
        String newReplyMessage = Strings.EMPTY;
        boolean alreadyRepliedFlag = false;
        boolean buyerAlreadyRepliedFlag = false;

        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(inquireAllEmailChatList)){
            if(Objects.nonNull(inquireAllEmailChatList.get(0))){
                newReplyMessage = inquireAllEmailChatList.get(0).getMessage();
            }
            alreadyRepliedFlag = inquireAllEmailChatList.size()>1;
            buyerAlreadyRepliedFlag = inquireAllEmailChatList.stream().filter(chat -> Objects.equals(1, chat.getMessageType())).count() >1;
        }
        vo.setNewReplyMessage(newReplyMessage);
        vo.setAlreadyReplied(alreadyRepliedFlag);
        vo.setBuyerAlreadyReplied(buyerAlreadyRepliedFlag);
    }
    private Map<Long, SupplierCommonInfoDTO> getSupplierCompanyNameBatch(List<BuyerInquiryVO> list) {
        Map<Long, SupplierCommonInfoDTO> result = new HashMap<>(list.size());
        List<Long> supplierIdList = new ArrayList<>(list.size());
        try {
            for (BuyerInquiryVO vo : list) {
                supplierIdList.add(vo.getSupplierId());
                if (CollectionUtils.isNotEmpty(vo.getChildren())) {
                    for (BuyerInquiryVO children : vo.getChildren()) {
                        supplierIdList.add(children.getSupplierId());
                    }
                }
            }
            List<SupplierCommonInfoDTO> suppNameList = supplierService.getSupplierCommonInfos(supplierIdList);
            if (CollectionUtils.isNotEmpty(suppNameList)) {
                for (SupplierCommonInfoDTO s : suppNameList) {
                    result.put(s.getSupplierId(), s);
                }
            }
        } catch (Exception e) {
            log.error("getSupplierCompanyNameBatch error", e);
        }
        return result;
    }

    private Map<Long, Long> getProductCategoryMap(List<BuyerInquiryVO> list) {
        Map<Long, Long> result = new HashMap<>(list.size());
        List<Long> productIdLIst = new ArrayList<>(list.size());
        try {
            for (BuyerInquiryVO vo : list) {
                productIdLIst.add(vo.getProductId());
                if (CollectionUtils.isNotEmpty(vo.getChildren())) {
                    for (BuyerInquiryVO children : vo.getChildren()) {
                        productIdLIst.add(children.getProductId());
                    }
                }
            }
            List<ProductLiteVO> productList = productService.getLiteProductListByIds(productIdLIst);
            if (CollectionUtils.isNotEmpty(productList)) {
                for (ProductLiteVO s : productList) {
                    result.put(s.getProductId(), s.getCategoryId());
                }
            }
        } catch (Exception e) {
            log.error("getProductCategoryMap error", e);
        }
        return result;
    }

    private Map<Long, String> getSupplierUserPhotoBatch(List<BuyerInquiryVO> list) {
        Map<Long, String> result = new HashMap<>(list.size());
        List<Long> userIdList = new ArrayList<>(list.size());
        try {
            for (BuyerInquiryVO vo : list) {
                userIdList.add(vo.getSupplierUserId());
                if (CollectionUtils.isNotEmpty(vo.getChildren())) {
                    for (BuyerInquiryVO children : vo.getChildren()) {
                        userIdList.add(children.getSupplierUserId());
                    }
                }
            }
            List<UserVO> userList = userService.getUserInfos(userIdList);
            if(CollectionUtils.isEmpty(userList)){
                return result;
            }
            for (UserVO user : userList) {
                 result.put(user.getUserId(), user.getPhoto());
            }

        } catch (Exception e) {
            log.error("getSupplierUserPhotoBatch error id : {} error :{}", userIdList,JSON.toJSONString(e));
        }
        return result;
    }

    @Override
    public Result inquiryReply(InquiryChatDTO inquiryChatDTO, UserVO buyerUserVO,String replySource) {

        log.info("buyer-inquiryReply dto:{},buyer info:{}", inquiryChatDTO, buyerUserVO);

        InquiryAllItemEntity inquiryAllItemEntity = inquiryAllItemDao.selectOne(new LambdaQueryWrapper<InquiryAllItemEntity>()
                .eq(InquiryAllItemEntity::getThreadId, inquiryChatDTO.getInquiryId()));
        if (Objects.isNull(inquiryAllItemEntity)) {
            log.error("buyer-inquiryReply error,InquireAllItemEntity is null,dto:{}", inquiryChatDTO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }
        log.info("buyer-inquiryReply inquireAllItemEntity:{}", inquiryAllItemEntity);

        InquiryAllEntity inquiryAllEntity = inquiryAllDao.selectOne(new LambdaQueryWrapper<InquiryAllEntity>()
                .eq(InquiryAllEntity::getInquiryId, inquiryAllItemEntity.getInquiryId()));
        if (Objects.isNull(inquiryAllEntity)) {
            log.error("buyer-inquiryReply error,InquireAllEntity is null,dto:{}", inquiryChatDTO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }
        log.info("buyer-inquiryReply inquireAllEntity:{}", inquiryAllEntity);

        if (!inquiryAllEntity.getBuyerId().equals(buyerUserVO.getUserId())) {
            log.error("buyer-inquiryReply error, inquiry buyer and login buyer no match, inquiryId ={}, inquiryBuyer ={}, loginBuyer ={}",
                    inquiryAllEntity.getInquiryId(), inquiryAllEntity.getBuyerId(), buyerUserVO.getUserId());
            throw new BusinessException(ResultCode.RfiResultCode.RFI_REPLY_LIMIT);
        }

        InquirySupplierStatusEntity supplierInquireStateEntity = inquirySupplierStatusDao.selectOne(new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .eq(InquirySupplierStatusEntity::getThreadId, inquiryChatDTO.getInquiryId()));
        if (Objects.isNull(supplierInquireStateEntity)) {
            log.error("buyer-inquiryReply error,supplierInquireStateEntity is null,dto:{}", inquiryChatDTO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }
        log.info("buyer-inquiryReply supplierInquireStateEntity:{}", supplierInquireStateEntity);

        String replyId = String.valueOf(IdGenerator.getStringId());

        UserVO supplierUserVO = userService.getUserInfo(supplierInquireStateEntity.getSupplierUserId());

        //假如用户没改标题，前端传的null，这里确保邮件标题不为空,发邮件 & 保存email_chat.subject用
        String subject = inquiryChatDTO.getSubject();
        if(StringUtils.isEmpty(subject)){
            subject = StringUtils.isEmpty(inquiryAllItemEntity.getSubject()) ? inquiryAllEntity.getSubject():inquiryAllItemEntity.getSubject();
        }

        //保存附件
        saveAttachment(inquiryChatDTO.getAttachmentList(), buyerUserVO.getUserId(), replyId);

        //保存回复 InquiryAllEmailChat,校验是否包含敏感词
        InquiryAllEmailChatEntity inquiryAllEmailChatEntity = saveInquiryAllEmailChat(false, subject, inquiryChatDTO, replyId, buyerUserVO, supplierUserVO,replySource);

        //买家更新已回复
        updateBuyerStatus(inquiryChatDTO.getInquiryId());

        //s30需求，供应商消息设置为已读
        markMessageRead(inquiryChatDTO.getInquiryId());

        //保存邮箱发送记录
        inquiryEmailAddressUtil.saveEmailAddress(inquiryAllEmailChatEntity.getSubject(), Arrays.asList(inquiryChatDTO.getCc()), "CC", inquiryAllEmailChatEntity.getReplyId());

        //黑名单或者是敏感词，不双写/发邮件
        if (inquiryAllEmailChatEntity.getBlacklistFlag() || inquiryAllEmailChatEntity.getSuspiciousContentFlag()) {
            log.info("buyer-inquiryReply filter:{}", inquiryAllEmailChatEntity);
            return Result.success();
        }
        release(inquiryAllItemEntity, inquiryAllEntity,supplierInquireStateEntity,inquiryAllEmailChatEntity,buyerUserVO,supplierUserVO,Arrays.asList(inquiryChatDTO.getCc()));

        //记录状态 @InquiryStatus(status = "reply")
        InquiryStatusDTO statusDTO = InquiryStatusDTO.builder()
                .inquiryId(inquiryAllEntity.getInquiryId())
                .threadId(inquiryChatDTO.getInquiryId())
                .userId(buyerUserVO.getUserId())
                .status(InquiryStatusEnum.REPLY.getStatus())
                .reviewType("")
                .email(buyerUserVO.getEmail())
                .upsellFlag(inquiryAllEntity.getUpsellFlag())
                .build();
        inquiryStatusService.saveInquiryStatus(statusDTO);
        return Result.success();
    }

    @Override
    public void releaseAfterReview(InquiryAllEmailChatEntity inquiryAllEmailChatEntity) {
        try {
            InquiryAllItemEntity inquiryAllItemEntity = inquiryAllItemDao.selectOne(new LambdaQueryWrapper<InquiryAllItemEntity>()
                    .eq(InquiryAllItemEntity::getThreadId, inquiryAllEmailChatEntity.getInquiryId()));

            log.info("buyer-inquiryReply inquireAllItemEntity:{}", inquiryAllItemEntity);

            InquiryAllEntity inquiryAllEntity = inquiryAllDao.selectOne(new LambdaQueryWrapper<InquiryAllEntity>()
                    .eq(InquiryAllEntity::getInquiryId, inquiryAllItemEntity.getInquiryId()));

            log.info("buyer-inquiryReply inquireAllEntity:{}", inquiryAllEntity);

            InquirySupplierStatusEntity supplierInquireStateEntity = inquirySupplierStatusDao.selectOne(new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                    .eq(InquirySupplierStatusEntity::getThreadId, inquiryAllEmailChatEntity.getInquiryId()));

            UserVO supplierUserVO = userService.getUserInfo(supplierInquireStateEntity.getSupplierUserId());

            UserVO buyerUserVO = userService.getUserInfo(inquiryAllEmailChatEntity.getSenderUserId());

            // refactor message审核 - release-查询cc列表后发送邮件
            List<String> ccEmailList = inquiryAllEmailChatDao.selectEmailList(RfiCommonConstants.CC, inquiryAllEmailChatEntity.getReplyId());

            // refactor remove 旧查询
            if (CollectionUtils.isEmpty(ccEmailList)) {
                ccEmailList = inquiryAllEmailChatDao.getCCEmailList(inquiryAllEmailChatEntity.getReplyId());
            }

            release(inquiryAllItemEntity, inquiryAllEntity,supplierInquireStateEntity,inquiryAllEmailChatEntity,buyerUserVO,supplierUserVO,ccEmailList);
        }catch (Exception e){
            log.error("releaseAfterReview error,param:{},error:{}",inquiryAllEmailChatEntity,JSON.toJSONString(e));
        }
    }

    /**
     * 提取，便于审核后复用逻辑
     *
     * @param inquiryAllItemEntity
     * @param inquiryAllEntity
     * @param supplierInquireStateEntity
     * @param inquiryAllEmailChatEntity
     * @param buyerUserVO
     * @param supplierUserVO
     * @param cc
     */
    private void release(InquiryAllItemEntity inquiryAllItemEntity, InquiryAllEntity inquiryAllEntity,
                         InquirySupplierStatusEntity supplierInquireStateEntity, InquiryAllEmailChatEntity inquiryAllEmailChatEntity,
                         UserVO buyerUserVO, UserVO supplierUserVO, List<String> cc){

        log.info("buyer-inquiryReply release logic,inquiryAllEmailChatEntity:{}", inquiryAllEmailChatEntity);

        //更新未读状态，同时是待回复状态
        updateSupplierStatus(inquiryAllItemEntity.getThreadId());

        //s31,修改标题
        if (StringUtils.isNotEmpty(inquiryAllEmailChatEntity.getSubject()) && !inquiryAllEmailChatEntity.getSubject().equals(inquiryAllItemEntity.getSubject())) {
            updateSubject(inquiryAllItemEntity.getThreadId(), inquiryAllEmailChatEntity.getSubject());
        }

        //更新thread l_upd_date
        inquiryAllItemDao.update(InquiryAllItemEntity.builder().lUpdDate(new Date()).build(), new LambdaQueryWrapper<InquiryAllItemEntity>().eq(InquiryAllItemEntity::getThreadId, inquiryAllItemEntity.getThreadId()));

        //发邮件
        sendEmail(supplierInquireStateEntity.getSupplierId(), buyerUserVO, supplierUserVO, cc, inquiryAllItemEntity, inquiryAllEntity, inquiryAllEmailChatEntity.getSubject());

        //webSocket通知
        sendRfiMsg(supplierInquireStateEntity.getSupplierId(), supplierInquireStateEntity.getSupplierUserId());
    }

    /**
     * 更新supplier status
     *
     * @param inquiryId
     */
    private void updateSupplierStatus(String inquiryId) {
        log.info("buyer-inquiryReply updateSupplierStatus inquiryId:{}", inquiryId);

        inquirySupplierStatusDao.update(null, new LambdaUpdateWrapper<InquirySupplierStatusEntity>().eq(InquirySupplierStatusEntity::getThreadId, inquiryId)
                .set(InquirySupplierStatusEntity::getMasterAcctReadFlag, false)
                .set(InquirySupplierStatusEntity::getReadFlag, false)
                .set(InquirySupplierStatusEntity::getReplyFlag, false)
                .set(InquirySupplierStatusEntity::getLUpdDate, new Date()));
    }

    /**
     * 更新buyer status
     *
     * @param inquiryId
     */
    private void updateBuyerStatus(String inquiryId) {
        log.info("buyer-inquiryReply updateBuyerStatus inquiryId:{}", inquiryId);

        inquiryBuyerStatusDao.update(null, new LambdaUpdateWrapper<InquiryBuyerStatusEntity>().eq(InquiryBuyerStatusEntity::getThreadId, inquiryId)
                .set(InquiryBuyerStatusEntity::getReadFlag, true)
                .set(InquiryBuyerStatusEntity::getReplyFlag, true)
                .set(InquiryBuyerStatusEntity::getLUpdDate, new Date()));
    }

    private void sendRfiMsg(Long supplierId, Long supplierUserId) {
        log.info("buyer-inquiryReply sendRfiMsg supplierId:{},supplierUserId:{}", supplierId, supplierUserId);
        try {
            //webSocket通知
            rfiMsgService.sendRfiMsg(RfiMsgVO.builder().supplierId(supplierId).userId(supplierUserId).build());
        } catch (Exception e) {
            log.error("buyer-inquiryReply webSocket rfi notice error, supplierId = {}, userId = {},error:{}", supplierId, supplierUserId, JSON.toJSONString(e));
        }
    }

    /**
     * s31,修改标题
     */
    private void updateSubject(String inquiryId, String subject) {
        log.info("buyer-inquiryReply updateSubject inquiryId:{},subject:{}", inquiryId, subject);

        inquiryAllItemDao.update(null, new LambdaUpdateWrapper<InquiryAllItemEntity>().eq(InquiryAllItemEntity::getThreadId, inquiryId)
                .set(InquiryAllItemEntity::getSubject, subject)
                .set(InquiryAllItemEntity::getLUpdDate, new Date()));

    }

    /**
     * s30需求，供应商消息设置为已读
     * 标记一条或多条消息已读
     * 当供应商读/回复了 messageA，则messageA之前的buyer发送的消息都应被视为已读，buyer端同理
     */
    private void markMessageRead(String inquiryId) {
        log.info("buyer-inquiryReply markMessageRead inquiryId:{}", inquiryId);

        inquiryAllEmailChatDao.update(null, new LambdaUpdateWrapper<InquiryAllEmailChatEntity>().eq(InquiryAllEmailChatEntity::getInquiryId, inquiryId)
                .eq(InquiryAllEmailChatEntity::getMessageType, 2)
                .lt(InquiryAllEmailChatEntity::getCreateDate, new Date())
                .set(InquiryAllEmailChatEntity::getReadFlag, true));
    }

    /**
     * 保存回复 InquiryAllEmailChat,校验是否包含敏感词
     */
    private InquiryAllEmailChatEntity saveInquiryAllEmailChat(boolean blackListFlag, String subject, InquiryChatDTO inquiryChatDTO, String replyId,
                                                              UserVO buyerUserVO, UserVO supplierUserVO,String replySource) {

        InquiryAllEmailChatEntity chatEntity = new InquiryAllEmailChatEntity();
        chatEntity.setInquiryId(inquiryChatDTO.getInquiryId());
        chatEntity.setReplyId(replyId);
        chatEntity.setMessage(inquiryChatDTO.getMessage());
        chatEntity.setMessageType(1);
        //发件人
        chatEntity.setSenderUserId(buyerUserVO.getUserId());
        chatEntity.setSenderEmailAddr(buyerUserVO.getEmail());
        chatEntity.setFirstName(buyerUserVO.getFirstName());
        chatEntity.setLastName(buyerUserVO.getLastName());
        //接收人
        chatEntity.setUserId(supplierUserVO.getUserId());
        chatEntity.setRecipientEmailAddr(supplierUserVO.getEmail());
        chatEntity.setBlacklistFlag(blackListFlag);
        chatEntity.setSuspiciousContentFlag(Boolean.FALSE);
        if (!blackListFlag) {
            chatEntity.setSubject(subject);
            //校验是否包含敏感词
            validSensitive(chatEntity);
        }
        if (StringUtils.isNotEmpty(inquiryChatDTO.getLang())) {
            chatEntity.setLangCode(inquiryChatDTO.getLang());
        }
        chatEntity.setReplySource(replySource);
        log.info("buyer-inquiryReply saveInquiryAllEmailChat entity:{}", chatEntity);
        inquiryAllEmailChatDao.insert(chatEntity);
        return chatEntity;
    }

    /**
     * 校验是否包含敏感词或敏感url
     *
     * @param chatEntity
     */
    private void validSensitive(InquiryAllEmailChatEntity chatEntity) {
        if (Objects.isNull(chatEntity) || StringUtils.isEmpty(chatEntity.getMessage())) {
            log.error("buyer-inquiryReply validSensitive null error:{}", chatEntity);
            return;
        }

        boolean sensitiveFlag = false;

        try {
            sensitiveFlag = ResultUtil.getData(sensitiveKeywordFeign.detectV2(chatEntity.getMessage()));
        } catch (Exception e) {
            log.error("buyer-inquiryReply validSensitive keyword error,param:{}", chatEntity, e);
        }
        try {
            List<String> sensitiveUrlList = googleUtil.checkAndGetUrl(chatEntity.getMessage());
            log.info("buyer-inquiryReply validSensitive, inquiryId:{},sensitiveKeyFlag:{},sensitiveUrl:{}", chatEntity.getInquiryId(), sensitiveFlag, sensitiveUrlList);
            boolean sensitiveUrlFlag = CollectionUtils.isNotEmpty(sensitiveUrlList);
            if (sensitiveUrlFlag) {
                chatEntity.setSuspiciousUrl(StringUtils.join(sensitiveUrlList, ","));
            }
            if (sensitiveFlag || sensitiveUrlFlag) {
                chatEntity.setSuspiciousContentFlag(Boolean.TRUE);
                chatEntity.setReviewStatus(RfiCommonConstants.PENDING);
            } else {
                chatEntity.setSuspiciousContentFlag(Boolean.FALSE);
            }
        } catch (Exception e) {
            log.error("buyer-inquiryReply validSensitive error,param:{},{}", chatEntity, JSON.toJSONString(e));
        }
    }

    /**
     * 发送邮件
     */
    private void sendEmail(Long supplierId, UserVO buyerUserVO, UserVO supplierUserVO, List<String> cc, InquiryAllItemEntity inquiryAllItemEntity, InquiryAllEntity inquiryAllEntity, String subject) {

        String templateId = RfxEmailTemplateEnum.RFI_PROD_SELLER_REPLY.getKey();
        if (InquiryTypeEnum.SUPPLIER.getKey().equals(inquiryAllEntity.getInquiryType())) {
            templateId = RfxEmailTemplateEnum.RFI_SUPPLIER_SELLER_REPLY.getKey();
        }
        //查询询盘对应编号，查询出用户，根据用户获取到发送邮箱
        HashMap<String, Object> templateParams = new HashMap<>(2);
        templateParams.put(templateId, JSON.toJSONString(buildEmailParam(inquiryAllItemEntity, inquiryAllEntity, supplierId, buyerUserVO, supplierUserVO, subject)));
        log.info("buyer-inquiryReply sendEmail templateParams:{}", templateParams);

        String fromEmail = inquireAllService.getMessageEmail(buyerUserVO.getUserId(), 0L,
                buyerUserVO.getFirstName(), buyerUserVO.getLastName(), buyerUserVO.getEmail());
        EmailModelPO emailModelPO = new EmailModelPO();
        emailModelPO.setTemplateParams(templateParams);
        emailModelPO.setTemplateId(templateId);
        emailModelPO.setForm(fromEmail);
        emailModelPO.setTo(supplierUserVO.getEmail());
        //邮件抄送
        if (CollectionUtils.isNotEmpty(cc)) {
            String email = StringUtils.join(cc, ",");
            emailModelPO.setCc(email);
        }
        log.info("buyer-inquiryReply sendEmail emailModelPO:{}", emailModelPO);
        try {
            rabbitService.sendMsg(emailModelPO);
        } catch (Exception e) {
            log.error("buyer-inquiryReply sendEmail error:{}", JSON.toJSONString(e));
        }
    }

    private InquiryReplyDetailVO buildEmailParam(InquiryAllItemEntity inquiryAllItemEntity, InquiryAllEntity inquiryAllEntity, Long supplierId, UserVO buyerUserVO, UserVO supplierUserVO, String subject) {
        //买家公司名称
        String buyerCompanyName = buyerUserVO.getCompanyName();
        //卖家公司名称
        OrganizationDTO companyInfo = supplierService.getSupplierNameAndCsoUserId(supplierId);
        String supplierCompanyName = Objects.nonNull(companyInfo) ? companyInfo.getOrgName() : null;

        //发送通知
        InquiryReplyDetailVO inquiryReplyDetailVO = new InquiryReplyDetailVO();
        inquiryReplyDetailVO.setInquiryId(inquiryAllItemEntity.getThreadId());
        inquiryReplyDetailVO.setSubmitDate(inquiryAllItemEntity.getCreateDate());
        //发送方的姓名
        inquiryReplyDetailVO.setFirstName(buyerUserVO.getFirstName());
        inquiryReplyDetailVO.setLastName(buyerUserVO.getLastName());
        inquiryReplyDetailVO.setUserId(supplierUserVO.getUserId());
        inquiryReplyDetailVO.setOrgId(supplierId);
        inquiryReplyDetailVO.setCompanyName(buyerCompanyName);
        //客户服务经理
        if (Objects.nonNull(companyInfo)) {
            UserVO gsolCsoUserVO = userService.getUserInfo(companyInfo.getCsoUserId());
            if (Objects.nonNull(gsolCsoUserVO)) {
                inquiryReplyDetailVO.setGSOLEmail(gsolCsoUserVO.getEmail());
                inquiryReplyDetailVO.setGSOLFirstName(gsolCsoUserVO.getFirstName());
                inquiryReplyDetailVO.setGSOLLastName(gsolCsoUserVO.getLastName());
            }
        }
        //假如用户没改标题，前端传的null，这里需要查库，确保邮件标题不为空
        inquiryReplyDetailVO.setSubject(subject);


        if (Objects.nonNull(inquiryAllItemEntity.getProductId())) {
            InquiryProductVO inquiryProductVO = new InquiryProductVO();
            inquiryProductVO.setProductId(inquiryAllItemEntity.getInquiryId());
            inquiryProductVO.setProductTitle(inquiryAllItemEntity.getProductName());
            inquiryProductVO.setProductImage(inquiryAllItemEntity.getProductImageUrl());
            inquiryProductVO.setModelNumber(inquiryAllItemEntity.getModelNumber());
            inquiryProductVO.setProductNum(inquiryAllItemEntity.getExpectedOrderQty());
            inquiryProductVO.setProductUnit(inquiryAllItemEntity.getExpectedOrderQtyUom());
            inquiryReplyDetailVO.setInquiryProduct(inquiryProductVO);
        }
        List<InquiryAllEmailChatEntity> chatEntityList = new ArrayList<>();
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.setMasterRouteOnly();
            chatEntityList = inquiryAllEmailChatDao.selectList(new LambdaQueryWrapper<InquiryAllEmailChatEntity>()
                    .eq(InquiryAllEmailChatEntity::getInquiryId, inquiryAllItemEntity.getThreadId())
                    .eq(InquiryAllEmailChatEntity::getBlacklistFlag, false)
                    .and(wrapper -> wrapper.eq(InquiryAllEmailChatEntity::getSuspiciousContentFlag, Boolean.FALSE).or().eq(InquiryAllEmailChatEntity::getReviewStatus, ReviewResultEnum.PASS.getValue()))
                    .orderByDesc(InquiryAllEmailChatEntity::getCreateDate));        }
        catch (Exception e){
            log.error("buildEmailParam error,inquiryAllItemEntity={},error:{}",inquiryAllItemEntity,JSON.toJSONString(e));
        }

        String message = "";
        String newReplyMessageSendTimeStr = LocalDateUtils.dateToYYDDMM(inquiryAllItemEntity.getCreateDate());
        if (CollectionUtils.isNotEmpty(chatEntityList)) {
            if (Objects.nonNull(chatEntityList.get(0))) {
                message = chatEntityList.get(0).getMessage();
                newReplyMessageSendTimeStr = LocalDateUtils.dateToYYDDMM(chatEntityList.get(0).getCreateDate());
            }
        } else {
            message = inquiryAllEntity.getBuyerMessage();
        }
        inquiryReplyDetailVO.setNewInquiryReply(InquiryReplyVO.builder()
                .firstName(inquiryAllEntity.getFirstName())
                .lastName(inquiryAllEntity.getLastName())
                .message(message)
                .companyName(buyerCompanyName)
                .createDate(newReplyMessageSendTimeStr)
                .build());
        //接收方的userId

        List<InquiryReplyVO> inquiryReplyVOList = new ArrayList<>();
        for (int i = 0; i < chatEntityList.size(); i++) {
            if (i < 3) {
                InquiryReplyVO inquiryReplyVO = InquiryReplyVO.builder()
                        .firstName(chatEntityList.get(i).getFirstName())
                        .lastName(chatEntityList.get(i).getLastName())
                        .photo(chatEntityList.get(i).getMessageType() == 2 ? supplierUserVO.getPhoto() : buyerUserVO.getPhoto())
                        .message(chatEntityList.get(i).getMessage())
                        .companyName(chatEntityList.get(i).getMessageType() == 2 ? supplierCompanyName : buyerCompanyName)
                        .createDate(LocalDateUtils.dateToYYDDMM(chatEntityList.get(i).getCreateDate())).build();
                inquiryReplyVOList.add(inquiryReplyVO);
            }

            List<InquiryReplyVO> compareSortList = inquiryReplyVOList.stream().sorted(Comparator.comparing(InquiryReplyVO::getCreateDate)).collect(Collectors.toList());
            inquiryReplyDetailVO.setInquiryReplyVOS(compareSortList);
        }

        return inquiryReplyDetailVO;
    }

    /**
     * 保存回复附件
     */
    private List<AttachmentCoreDTO> saveAttachment(String attachment, Long userId, String replyId) {
        List<AttachmentCoreDTO> attachmentCoreDTOList = new ArrayList<>();
        if (StringUtils.isNotBlank(attachment)) {
            //保存记录附件
            log.info("inquiry reply attachment json data = {}", attachment);
            List<AttachmentAggDTO> inquiryRFIRedistributePOList = JSON.parseArray(attachment, AttachmentAggDTO.class);
            attachmentCoreDTOList = JSON.parseArray(attachment, AttachmentCoreDTO.class);
            inquireAllFileAttachmentService.saveInquiryFile(inquiryRFIRedistributePOList, replyId, userId);
        }
        return attachmentCoreDTOList;
    }

    @Override
    public Result<String> blackReply(InquiryChatDTO inquiryChatDTO, UserVO buyerUserVO,String replySource) {
        log.info("buyer-blackReply dto:{},buyer info:{}", inquiryChatDTO, buyerUserVO);

        InquiryAllItemEntity inquiryAllItemEntity = inquiryAllItemDao.selectOne(new LambdaQueryWrapper<InquiryAllItemEntity>()
                .eq(InquiryAllItemEntity::getThreadId, inquiryChatDTO.getInquiryId()));
        if (Objects.isNull(inquiryAllItemEntity)) {
            log.error("buyer-blackReply error,InquireAllItemEntity is null,dto:{}", inquiryChatDTO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }
        log.info("buyer-blackReply inquireAllItemEntity:{}", inquiryAllItemEntity);

        InquiryAllEntity inquiryAllEntity = inquiryAllDao.selectOne(new LambdaQueryWrapper<InquiryAllEntity>()
                .eq(InquiryAllEntity::getInquiryId, inquiryAllItemEntity.getInquiryId()));
        if (Objects.isNull(inquiryAllEntity)) {
            log.error("buyer-blackReply error,InquireAllEntity is null,dto:{}", inquiryChatDTO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }
        log.info("buyer-blackReply inquireAllEntity:{}", inquiryAllEntity);

        if (!inquiryAllEntity.getBuyerId().equals(buyerUserVO.getUserId())) {
            log.error("buyer-blackReply error, inquiry buyer and login buyer no match, inquiryId ={}, inquiryBuyer ={}, loginBuyer ={}",
                    inquiryAllEntity.getInquiryId(), inquiryAllEntity.getBuyerId(), buyerUserVO.getUserId());
            throw new BusinessException(ResultCode.RfiResultCode.RFI_REPLY_LIMIT);
        }
        InquirySupplierStatusEntity supplierInquireStateEntity = inquirySupplierStatusDao.selectOne(new LambdaQueryWrapper<InquirySupplierStatusEntity>()
                .eq(InquirySupplierStatusEntity::getThreadId, inquiryChatDTO.getInquiryId()));
        if (Objects.isNull(supplierInquireStateEntity)) {
            log.error("buyer-blackReply error,supplierInquireStateEntity is null,dto:{}", inquiryChatDTO);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }
        log.info("buyer-blackReply supplierInquireStateEntity:{}", supplierInquireStateEntity);

        String replyId = String.valueOf(IdGenerator.getStringId());

        //买家发给卖家消息
        UserVO supplierUserVO = userService.getUserInfo(supplierInquireStateEntity.getSupplierUserId());

        saveAttachment(inquiryChatDTO.getAttachmentList(), buyerUserVO.getUserId(), replyId);

        saveInquiryAllEmailChat(true, null, inquiryChatDTO, replyId, buyerUserVO, supplierUserVO,replySource);

        //买家更新已回复
        updateBuyerStatus(inquiryChatDTO.getInquiryId());

        //s30需求，供应商消息设置为已读
        markMessageRead(inquiryChatDTO.getInquiryId());
        //保存邮箱发送记录
        inquiryEmailAddressUtil.saveEmailAddress(inquiryAllEntity.getSubject(), Arrays.asList(inquiryChatDTO.getCc()), "CC", replyId);

        //记录状态 @InquiryStatus(status = "reply")
        InquiryStatusDTO statusDTO = InquiryStatusDTO.builder()
                .inquiryId(inquiryAllEntity.getInquiryId())
                .threadId(inquiryChatDTO.getInquiryId())
                .userId(buyerUserVO.getUserId())
                .status(InquiryStatusEnum.REPLY.getStatus())
                .reviewType("")
                .email(buyerUserVO.getEmail())
                .upsellFlag(inquiryAllEntity.getUpsellFlag())
                .build();
        inquiryStatusService.saveInquiryStatus(statusDTO);
        return Result.success();
    }

    @SneakyThrows
    @Override
    public Result<com.globalsources.rfi.agg.response.APPInquiryReplyChatVO> appInquiryDetail(Long buyerId,String inquiryId) {
        InquirySupplierStatusVO supplierInquireStateEntity = supplierInquiryService.inquiryStateInfo(inquiryId);
        //买家关系
        InquiryBuyerStatusVO inquiryBuyerStatusVO = inquiryStateInfo(inquiryId);
        InquirySupplierStatusVO inquirySupplierStatusVO = supplierInquiryService.inquiryStateInfo(inquiryId);

        if (ObjectUtils.isEmpty(supplierInquireStateEntity)) {
            return Result.success(new com.globalsources.rfi.agg.response.APPInquiryReplyChatVO());
        }
        InquireAllVO inquireAllVO = inquiryAllDao.findOneByThreadId(inquiryId);
        if (ObjectUtils.isEmpty(inquireAllVO)) {
            return Result.success(new com.globalsources.rfi.agg.response.APPInquiryReplyChatVO());
        }
        if (!Objects.equals(buyerId,123L) && !Objects.equals(buyerId,inquireAllVO.getBuyerId())) {
            log.warn("appInquiryDetail warn get other's inquiry, buyerId:{},inquiryId:{}",buyerId,inquiryId);
            return Result.success(new com.globalsources.rfi.agg.response.APPInquiryReplyChatVO());
        }
        com.globalsources.rfi.agg.response.APPInquiryReplyChatVO appInquiryReplyChatVO = new com.globalsources.rfi.agg.response.APPInquiryReplyChatVO();
        com.globalsources.rfi.agg.response.APPInquiryDetailVO result = new com.globalsources.rfi.agg.response.APPInquiryDetailVO();
        BeanUtils.copyProperties(inquireAllVO, result);
        result.setSenderFirstName(supplierInquireStateEntity.getFirstName());
        result.setSenderLastName(supplierInquireStateEntity.getLastName());
        result.setSenderEmail(supplierInquireStateEntity.getEmailAddr());
        result.setEmailAddress(inquiryBuyerStatusVO.getEmailAddr());
        result.setMessage(inquireAllVO.getBuyerMessage());
        result.setInquiryType(com.globalsources.rfi.agg.enums.InquiryTypeEnum.getEnumByKey(inquireAllVO.getInquiryType().getKey()));
        //查询所有的回复记录
        List<InquireAllEmailChatVO> chatEntityList = inquiryAllEmailChatDao.inquiryAllEmailChatList(inquiryId,false);

        if (CollectionUtils.isNotEmpty(chatEntityList)) {
            List<com.globalsources.rfi.agg.response.APPInquiryDetailVO> inquiryDetailVOList = new ArrayList<>(chatEntityList.size());
            for (int i = 0; i < chatEntityList.size(); i++) {
                com.globalsources.rfi.agg.response.APPInquiryDetailVO entity = new com.globalsources.rfi.agg.response.APPInquiryDetailVO();
                //自己的邮箱
                entity.setEmailAddress(chatEntityList.get(i).getMessageType() == 1 ? inquiryBuyerStatusVO.getEmailAddr() : supplierInquireStateEntity.getEmailAddr());
                //发件人的邮箱
                entity.setSenderEmail(chatEntityList.get(i).getSenderEmailAddr());
                //app From
                entity.setFirstName(chatEntityList.get(i).getFirstName());
                entity.setLastName(chatEntityList.get(i).getLastName());

                entity.setMessage(InquiryReplyUtil.replyRepMessage(chatEntityList.get(i).getMessage()));
                entity.setCreateDate(chatEntityList.get(i).getCreateDate());
                entity.setInquiryType(com.globalsources.rfi.agg.enums.InquiryTypeEnum.getEnumByKey(inquireAllVO.getInquiryType().getKey()));
                entity.setSenderFirstName(inquirySupplierStatusVO.getFirstName());
                entity.setSenderLastName(inquirySupplierStatusVO.getLastName());

                // refactor add
                List<InquiryAttachmentVO> attachment = inquiryAllDao.getInquiryAttachmentArrayByReplyId(chatEntityList.get(i).getReplyId());

                // refactor remove 旧查询
                if (CollectionUtils.isEmpty(attachment)) {
                    attachment = inquiryAllDao.getInquiryFileArrayByReplyId(chatEntityList.get(i).getReplyId());
                }

                if (CollectionUtils.isNotEmpty(attachment)) {
                    List<RfiFileVO> rfiFileVOList = amazonUtil.appRfiFileList(attachment, i);
                    entity.setAttachment(rfiFileVOList);
                    if (i == chatEntityList.size() - 1) {
                        result.setAttachment(rfiFileVOList);
                    }
                }
                inquiryDetailVOList.add(entity);
            }
            inquiryDetailVOList = inquiryDetailVOList.stream().sorted(Comparator.comparing(com.globalsources.rfi.agg.response.APPInquiryDetailVO::getCreateDate).reversed()).collect(Collectors.toList());
            inquiryDetailVOList.remove(inquiryDetailVOList.size() - 1);

            appInquiryReplyChatVO.setInquiryDetailList(inquiryDetailVOList);
        }

        if (!InquiryTypeEnum.SUPPLIER.equals(inquireAllVO.getInquiryType())) {
            com.globalsources.rfi.agg.response.InquiryProdVO inquiryProdVO = com.globalsources.rfi.agg.response.InquiryProdVO.builder()
                    .productId(inquireAllVO.getInquireAllItemList().getProductId().toString())
                    .productImage(inquireAllVO.getInquireAllItemList().getProductImageUrl())
                    .productName(inquireAllVO.getInquireAllItemList().getProductName())
                    .productNum(inquireAllVO.getInquireAllItemList().getExpectedOrderQty())
                    .productUnit(inquireAllVO.getInquireAllItemList().getExpectedOrderQtyUom())
                    .categoryId(getCategoryIdByProductId(inquireAllVO.getInquireAllItemList().getProductId()))
                    .modelNumber(inquireAllVO.getInquireAllItemList().getModelNumber())
                    .build();
            result.setProducts(Collections.singletonList(inquiryProdVO));
        }
        appInquiryReplyChatVO.setInquiryDetailVO(result);
        //消息已读状态
        InquiryBuyerStatusDTO entity = InquiryBuyerStatusDTO.builder().readFlag(true).build();
        log.info("[询盘详情]{}", entity);
        updateInquiryState(entity, inquiryId);
        /**
         * s30需求，供应商消息设置为已读
         */
        inquireAllEmailChatService.markAsRead(inquiryId, 2);
        //查询买家公司信息

        com.globalsources.rfi.agg.response.detail.InquiryCompanyVO inquiryCompanyVO = new com.globalsources.rfi.agg.response.detail.InquiryCompanyVO();
        UserBaseProfileVO userBaseProfileVO = userService.getUserBaseProfile(supplierInquireStateEntity.getSupplierUserId());
        SupplierCommonInfoDTO supplierInfo = supplierService.getSupplierCommonInfo(supplierInquireStateEntity.getSupplierId());
        if (ObjectUtils.isNotEmpty(userBaseProfileVO)) {
            appInquiryReplyChatVO.setBuyerId(inquiryBuyerStatusVO.getBuyerId());
            appInquiryReplyChatVO.setVerifiedFlag(userBaseProfileVO.getContactInfo().getVerifiedFlag());
            appInquiryReplyChatVO.setVipFlag(userBaseProfileVO.getContactInfo().getVipFlag());
            inquiryCompanyVO.setSupplierId(String.valueOf(supplierInquireStateEntity.getSupplierId()));
            inquiryCompanyVO.setEmailAddress(userBaseProfileVO.getContactInfo().getEmail());
            inquiryCompanyVO.setFirstName(userBaseProfileVO.getContactInfo().getFirstName());
            inquiryCompanyVO.setLastName(userBaseProfileVO.getContactInfo().getLastName());
            inquiryCompanyVO.setRoleName(userBaseProfileVO.getContactInfo().getJobTitle());
            StringBuilder phone = new StringBuilder();
            phone.append("(" + userBaseProfileVO.getContactInfo().getTelCountryCode() + ")");
            phone.append(userBaseProfileVO.getContactInfo().getTelAreaCode());
            phone.append(userBaseProfileVO.getContactInfo().getPhone());
            inquiryCompanyVO.setPhone(phone.toString());
            inquiryCompanyVO.setCountry(dictCountryUtils.convertMapForOrderStatus(userBaseProfileVO.getContactInfo().getCountryCode()));
            inquiryCompanyVO.setUserId(supplierInquireStateEntity.getSupplierUserId());
            if (Objects.nonNull(supplierInfo)) {
                inquiryCompanyVO.setLogo(supplierInfo.getLogoUrl());
                inquiryCompanyVO.setSupplierId(String.valueOf(supplierInfo.getSupplierId()));
                inquiryCompanyVO.setSupplierVerified(supplierInfo.getVerifiedSupplierFlag());
                inquiryCompanyVO.setCompanyName(supplierInfo.getCompanyDisplayName());
                inquiryCompanyVO.setMaxContractLevel(supplierInfo.getMaxContractLevel());
                inquiryCompanyVO.setSupplierType(supplierInfo.getSupplierType());
            }
            appInquiryReplyChatVO.setCompanyModel(inquiryCompanyVO);
        }
        return Result.success(appInquiryReplyChatVO);
    }

    private Long getCategoryIdByProductId(Long productId) {
        try {
            OnlineProductEntityDTO productEntity = productService.getOnlineProductEntity(productId, true);
            return Objects.nonNull(productEntity) ? productEntity.getCategoryId() : null;
        } catch (Exception e) {
            log.error("getCategoryIdByProductId error:{}", productId);
        }
        return null;
    }

    @SneakyThrows
    @Override
    public Result<InquiryReplyDetailChatVO> inquiryDetail(String inquiryId, UserVO userVO, String lang) {
        //result code,msg
        IResultCode resultCode = ResultCode.CommonResultCode.SUCCESS;
        String resultMsg = "";

        InquirySupplierStatusVO inquirySupplierStatusVO = supplierInquiryService.inquiryStateInfo(inquiryId);
        if (ObjectUtils.isEmpty(inquirySupplierStatusVO)) {
            log.error("invoke supplierInquiryFeign.inquiryStateInfo is empty, inquiry = {}", inquiryId);
            return Result.success(new InquiryReplyDetailChatVO());
        }
        //rfi 基本信息
        InquireAllVO inquireAllVO = inquiryAllDao.findOneByThreadId(inquiryId);
        if(Objects.isNull(inquireAllVO)){
            log.error("invoke inquireAllDao.findOneByThreadId is empty, inquiry = {}", inquiryId);
            return Result.failed(ResultCode.RfiResultCode.BUYER_RFI_CHECK_LIMIT);
        }
        //校验是否查看的是自己的rfi
        if (!Objects.equals(inquireAllVO.getBuyerId(), userVO.getUserId())) {
            log.warn("inquiryDetail get other inquiry data, inquiryId:{},user:{},lang:{}", inquiryId, userVO, lang);
            return Result.failed(ResultCode.RfiResultCode.BUYER_RFI_CHECK_LIMIT);
        }

        //chat online status
        Long supplierId = inquireAllVO.getInquireAllItemList().getSupplierId();
        if (Objects.isNull(supplierId)) {
            log.warn("buyerInquiryDetail, supplierId of inquiry[" + inquiryId + "] is null");
        }
        String supplierChatOnlineStatus = chatService.getSupplierChatOnlineStatus(userVO, supplierId);
        if (Objects.isNull(supplierChatOnlineStatus)) {
            log.warn("buyerInquiryDetail, chatOnlineStatus is null, supplierId[" + supplierId + "] of inquiry[" + inquiryId + "]");
        }
        //查询所有的回复记录
        List<InquireAllEmailChatVO> chatEntityList = inquiryAllEmailChatDao.inquiryAllEmailChatList(inquiryId, false);

        //最新一条回复记录
        InquiryBuyerDetailVO newInquiryDetailVO = new InquiryBuyerDetailVO();
        //回复记录列表，按时间顺序排序，最新的回复在最后一条。包括第一条回复记录
        List<InquiryBuyerDetailVO> inquiryDetailVOList = new ArrayList<>(chatEntityList.size() + 1);
        List<ProductCategoryAttributeVO> pcAttrVOS = null;
        if (!InquiryTypeEnum.SUPPLIER.equals(inquireAllVO.getInquiryType()) && Objects.nonNull(inquireAllVO)) {
            List<InquiryProductCategoryAttrCoreDTO> pcAttrDtoList = inquiryProductCategoryAttrService.getAttrDtoByInquiryId(Objects.nonNull(inquireAllVO.getInquiryId()) ? Lists.newArrayList(inquireAllVO.getInquiryId()) : null);
            pcAttrVOS = Optional.ofNullable(pcAttrDtoList).orElse(Lists.newArrayList()).stream().map(pcAttrDto -> {
                ProductCategoryAttributeVO productCategoryAttributeVO = OrikaMapperUtil.coverObject(pcAttrDto, ProductCategoryAttributeVO.class);
                productCategoryAttributeVO.setAttrValue(pcAttrDto.getAttrValues());
                return productCategoryAttributeVO;
            }).collect(Collectors.toList());
        }

        //chatEntityList 回复消息
        for (int i = 0; i < chatEntityList.size(); i++) {
            InquiryBuyerDetailVO entity = new InquiryBuyerDetailVO();
            //messageType:1 买家发送的消息messageType:2 卖家发送的消息
            entity.setShowRole(chatEntityList.get(i).getMessageType() == 1);
            //对方是否已读
            entity.setFlagHaveRead(chatEntityList.get(i).getMessageType() == 1 ? chatEntityList.get(i).getReadFlag() : Boolean.FALSE);
            //发件人的邮箱
            entity.setEmailAddress(chatEntityList.get(i).getSenderEmailAddr());

            //发件人username
            entity.setFirstName(chatEntityList.get(i).getFirstName());
            entity.setLastName(chatEntityList.get(i).getLastName());
            if (i == chatEntityList.size() - 1) {
                entity.setProductCategoryAttrInfos(pcAttrVOS);
                entity.setProductFlag(true);
            }
            //收件人邮箱
            entity.setSendEmailModel(chatEntityList.get(i).getRecipientEmailAddr());

            entity.setRecipientEmailAddr(inquirySupplierStatusVO.getEmailAddr());
            entity.setRecipientFirstName(inquirySupplierStatusVO.getFirstName());
            entity.setRecipientLastName(inquirySupplierStatusVO.getLastName());
            //回复消息
            entity.setMessage(InquiryReplyUtil.replyRepMessage(chatEntityList.get(i).getMessage()));
            entity.setCreateDate(chatEntityList.get(i).getCreateDate());
            entity.setInquiryType(inquireAllVO.getInquiryType().getKey());

            //产品信息
            entity.setAttachmentType(inquireAllVO.getAttachmentType());
            if (!InquiryTypeEnum.SUPPLIER.equals(inquireAllVO.getInquiryType())) {
                //增加产品desktopProductDetailUrl
                String desktopProductDetailUrl = productService.getProductUrl(inquireAllVO.getInquireAllItemList().getProductId());
                entity.setDesktopProductDetailUrl(desktopProductDetailUrl);
                entity.setProductId(inquireAllVO.getInquireAllItemList().getProductId().toString());
                entity.setProductNum(inquireAllVO.getInquireAllItemList().getExpectedOrderQty());
                entity.setProductUnit(inquireAllVO.getInquireAllItemList().getExpectedOrderQtyUom());
                entity.setProductImage(inquireAllVO.getInquireAllItemList().getProductImageUrl());
                entity.setProductId(inquireAllVO.getInquireAllItemList().getProductId().toString());
                entity.setProductTitle(inquireAllVO.getInquireAllItemList().getProductName());
                entity.setModelNumber(inquireAllVO.getInquireAllItemList().getModelNumber());
                entity.setAttachmentType("new");
                entity.setSourceName("request");
                //区分new gsol 和 mc附件类型
                if ("old".equals(inquireAllVO.getAttachmentType())) {
                    Boolean flag = inquireAllVO.getInquireAllItemList().getProductImageUrl().indexOf(HOST_NAME) != -1;
                    entity.setSourceName(SourceNameEnum.REQUEST.getKey());
                    if (Boolean.TRUE.equals(flag)) {
                        entity.setAttachmentType("new");
                    }
                }
            }

            // refactor add
            List<InquiryAttachmentVO> attachment = inquiryAllDao.getInquiryAttachmentArrayByReplyId(chatEntityList.get(i).getReplyId());

            // refactor remove 旧查询
            if (CollectionUtils.isEmpty(attachment)) {
                attachment = inquiryAllDao.getInquiryFileArrayByReplyId(chatEntityList.get(i).getReplyId());
            }

            if (CollectionUtils.isNotEmpty(attachment)) {
                String sign = CipherUtil.getSign(secretKey, "rfi");
                int finalI = i;
                attachment.stream().forEach(att -> {
                    FileReqDTO fileReqDTO = new FileReqDTO();
                    fileReqDTO.setFileKey(att.getUrl());
                    fileReqDTO.setSign(sign);
                    fileReqDTO.setType(1);

                    //获取最大下标
                    if (!"old".equals(att.getAttachmentType()) && finalI != 0) {
                        String url = attachmentService.getFileUrl(fileReqDTO);
                        att.setUrl(url);
                    }
                    if ("new".equals(att.getAttachmentType())) {
                        String url = attachmentService.getFileUrl(fileReqDTO);
                        att.setUrl(url);
                    }
                    att.setType(FileUtil.fileMimeType(att.getName()));
                    att.setSourceType(SourceNameEnum.MESSAGE.getKey());
                });
                entity.setAttachment(OrikaMapperUtil.coverList(attachment, ResponseAttachmentVO.class));
            }
            //抄送邮件
            // refactor 查询 询盘详情
            List<RfiInquiryEmailAddressCoreVO> rfiInquiryEmailAddressCoreVOS = inquiryAllEmailService.selectInquireEmailList(RfiCommonConstants.CC, chatEntityList.get(i).getReplyId());

            // refactor remove 旧查询
            if (CollectionUtils.isEmpty(rfiInquiryEmailAddressCoreVOS)) {
                rfiInquiryEmailAddressCoreVOS = inquiryAllEmailAddressService.emailAddress(chatEntityList.get(i).getReplyId());
            }

            if (CollectionUtils.isNotEmpty(rfiInquiryEmailAddressCoreVOS)) {
                entity.setRfiInquiryEmailAddressList(OrikaMapperUtil.coverList(rfiInquiryEmailAddressCoreVOS, RfiInquiryEmailAddressAggVO.class));
            }

            //回复邮箱
            // refactor 查询 询盘详情
            List<RfiInquiryEmailAddressCoreVO> rfiInquiryToEmailAddressCoreVOS = inquiryAllEmailService.selectInquireEmailList(RfiCommonConstants.TO, chatEntityList.get(i).getReplyId());

            // refactor remove 旧查询
            if (CollectionUtils.isEmpty(rfiInquiryEmailAddressCoreVOS)) {
                rfiInquiryToEmailAddressCoreVOS = inquiryAllEmailAddressService.emailAddr(chatEntityList.get(i).getReplyId(), "TO");
            }

            if (CollectionUtils.isNotEmpty(rfiInquiryToEmailAddressCoreVOS)) {
                entity.setRfiInquiryToEmailAddressList(OrikaMapperUtil.coverList(rfiInquiryToEmailAddressCoreVOS, RfiInquiryEmailAddressAggVO.class));
            }
            inquiryDetailVOList.add(entity);
        }
        //查询询盘消息

        if (CollectionUtils.isNotEmpty(inquiryDetailVOList)) {
            //按照最早的回复时间重排序，第一条为最早的回复
            inquiryDetailVOList = inquiryDetailVOList.stream().sorted(Comparator.comparing(InquiryBuyerDetailVO::getCreateDate)).collect(Collectors.toList());
            // 获取最新一条回复
            newInquiryDetailVO = inquiryDetailVOList.get(inquiryDetailVOList.size() - 1);
            // 如果是产品询盘则在最新一条显示产品信息
            newInquiryDetailVO.setProductFlag(true);
        }
        //消息已读状态
        InquiryBuyerStatusDTO entity = InquiryBuyerStatusDTO.builder().readFlag(true).lUpdDate(new Date()).build();
        updateInquiryState(entity, inquiryId);
        /**
         * s30需求,供应商消息设置为已读
         */
        inquireAllEmailChatService.markAsRead(inquiryId, 2);

        //获取rfi buyer 详情中的supplier info
        InquiryCompanyVO inquiryCompanyVO = getSupplierDynaInfo(inquirySupplierStatusVO);

        if (Objects.nonNull(inquireAllVO)) {
            newInquiryDetailVO.setInquiryId(inquireAllVO.getInquiryId());
            if (inquireAllVO.getInquireAllItemList().getProductId() != null) {
                newInquiryDetailVO.setProductId(inquireAllVO.getInquireAllItemList().getProductId().toString());
                newInquiryDetailVO.setProductImage(inquireAllVO.getInquireAllItemList().getProductImageUrl());
            }
            newInquiryDetailVO.setSupplierId(inquireAllVO.getInquireAllItemList().getSupplierId());
            newInquiryDetailVO.setChatOnlineStatus(supplierChatOnlineStatus);
        }
        InquiryReplyDetailChatVO replyChatVO = InquiryReplyDetailChatVO.builder()
                .inquiryList(inquiryDetailVOList)
                .newInquiry(newInquiryDetailVO)
                .companyModel(inquiryCompanyVO)
                .chatReplyFlag(inquirySupplierStatusVO.getChatReplyFlag())
                .subject(inquireAllVO.getSubject())
                .build();
        return Result.success(resultCode.getCode(), resultMsg, replyChatVO);
    }

    @SneakyThrows
    @Override
    public BuyerInquiryDetailVO inquiryDetailV2(InquiryDetailDTO inquiryDetailDTO) {

        log.info("inquiryDetailV2-buyer dto:{}", inquiryDetailDTO);

        //rfi 基本信息
        InquireAllVO inquireAllVO = inquiryAllDao.findOneByThreadId(inquiryDetailDTO.getInquiryId());
        if (Objects.isNull(inquireAllVO)) {
            log.error("inquiryDetailV2-buyer inquiry is null, inquiryId ={}", inquiryDetailDTO.getInquiryId());
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }

        InquiryBuyerStatusVO inquiryBuyerStatusVO = inquiryStateInfo(inquiryDetailDTO.getInquiryId());
        if(Objects.isNull(inquiryBuyerStatusVO) || !inquiryBuyerStatusVO.getBuyerId().equals(inquiryDetailDTO.getBuyerId())){
            log.error("inquiryDetailV2-buyer inquiry buyer and login buyer no match inquiryId ={}, loginBuyer ={}",
                    inquiryDetailDTO.getInquiryId(), inquiryDetailDTO.getBuyerId());
            throw new BusinessException(ResultCode.RfiResultCode.RFI_VIEW_LIMIT);
        }

        InquirySupplierStatusVO inquirySupplierStatusVO = supplierInquiryService.inquiryStateInfo(inquiryDetailDTO.getInquiryId());
        if (Objects.isNull(inquirySupplierStatusVO)) {
            log.error("inquiryDetailV2-buyer inquiry supplier status is null, inquiryId = {}", inquiryDetailDTO.getInquiryId());
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }

        //产品相关信息
        InquiryProductInfo inquiryProductInfo = getInquiryProductInfo(inquireAllVO);

        boolean chatReplyFlag = inquiryBuyerStatusVO.getChatReplyFlag() || inquirySupplierStatusVO.getChatReplyFlag();

        //update status
        updateStatus(inquiryDetailDTO.getInquiryId());

        return BuyerInquiryDetailVO.builder()
                .chatReplyFlag(chatReplyFlag)
                .subject(inquireAllVO.getSubject())
                .productInfo(inquiryProductInfo)
                .replyEmailAddr(inquirySupplierStatusVO.getEmailAddr())
                .replyFirstName(inquirySupplierStatusVO.getFirstName())
                .replyLastName(inquirySupplierStatusVO.getLastName())
                .inquiryType(inquireAllVO.getInquiryType())
                .build();
    }

    private void updateStatus(String inquiryId) {
        //消息已读状态
        InquiryBuyerStatusDTO entity = InquiryBuyerStatusDTO.builder().readFlag(true).lUpdDate(new Date()).build();
        updateInquiryState(entity, inquiryId);
        /**
         * s30需求,供应商消息设置为已读
         */
        inquireAllEmailChatService.markAsRead(inquiryId, 2);
    }

    private InquiryProductInfo getInquiryProductInfo(InquireAllVO inquireAllVO) {

        InquiryProductInfo productInfo = null;
        try {
            ProductLiteVO product = null;
            if (Objects.nonNull(inquireAllVO.getInquireAllItemList()) && Objects.nonNull(inquireAllVO.getInquireAllItemList().getProductId()) && !InquiryTypeEnum.SUPPLIER.equals(inquireAllVO.getInquiryType())) {
                product = productService.getLiteProductById(inquireAllVO.getInquireAllItemList().getProductId());
            }
            if (Objects.isNull(product)) {
                product = new ProductLiteVO();
            }
            productInfo = OrikaMapperUtil.coverObject(product, InquiryProductInfo.class);
            productInfo.setProductNum(inquireAllVO.getInquireAllItemList().getExpectedOrderQty());
            productInfo.setProductCategoryAttrInfos(getProductCategoryAttrList(inquireAllVO.getInquiryType(), inquireAllVO.getInquiryId()));
            productInfo.setProductId(inquireAllVO.getInquireAllItemList().getProductId());
            productInfo.setProductPrimaryImage(inquireAllVO.getInquireAllItemList().getProductImageUrl());
            productInfo.setProductName(inquireAllVO.getInquireAllItemList().getProductName());
            productInfo.setMinOrderUnit(inquireAllVO.getInquireAllItemList().getExpectedOrderQtyUom());
            productInfo.setModelNumber(inquireAllVO.getInquireAllItemList().getModelNumber());
            productInfo.setDesktopProductDetailUrl(productInfo.getProductSEOUrl());
        } catch (Exception e) {
            log.error("get buyer inquiry detail product info error,param:{},error:{}", inquireAllVO, JSON.toJSONString(e));
        }
        return productInfo;
    }

    @SneakyThrows
    private List<ProductCategoryAttributeVO> getProductCategoryAttrList(InquiryTypeEnum inquiryType, String inquiryId) {
        List<ProductCategoryAttributeVO> pcAttrVOS = null;
        if (InquiryTypeEnum.SUPPLIER.equals(inquiryType)) {
            return pcAttrVOS;
        }

        List<InquiryProductCategoryAttrCoreDTO> pcAttrDtoList = inquiryProductCategoryAttrService.getAttrDtoByInquiryId(Objects.nonNull(inquiryId) ? Lists.newArrayList(inquiryId) : null);
        pcAttrVOS = Optional.ofNullable(pcAttrDtoList).orElse(Lists.newArrayList()).stream().map(pcAttrDto -> {
            ProductCategoryAttributeVO productCategoryAttributeVO = OrikaMapperUtil.coverObject(pcAttrDto, ProductCategoryAttributeVO.class);
            productCategoryAttributeVO.setAttrValue(pcAttrDto.getAttrValues());
            return productCategoryAttributeVO;
        }).collect(Collectors.toList());
        return pcAttrVOS;
    }

    @Override
    public PageResult<InquiryMessageVO> getInquiryMessagePageList(InquiryMessageListDTO dto){
        InquiryBuyerStatusVO buyerInquiry = inquiryStateInfo(dto.getInquiryId());
        if (Objects.isNull(buyerInquiry)) {
            log.error("getInquiryMessagePageList inquiry supplier status is null, inquiryId = {},dto:{}", dto.getInquiryId(), dto);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_NOT_EXIST);
        }

        if(!buyerInquiry.getBuyerId().equals(dto.getUserId())){
            log.error("getInquiryMessagePageList inquiry view limit, inquiryId ={} ,dto:{}", dto.getInquiryId(), dto);
            throw new BusinessException(ResultCode.RfiResultCode.RFI_VIEW_LIMIT);
        }
        IPage<InquireAllEmailChatVO> pageResult = inquiryAllEmailChatDao.getEmailChatListByThreadId(new Page<>(dto.getPageNum(), dto.getPageSize()), dto.getInquiryId(), false,true);
        InquireAllVO inquireAllVO = inquiryAllDao.findOneByThreadId(dto.getInquiryId());
        List<InquiryMessageVO> messageList = getMessageList(inquireAllVO, pageResult.getRecords());
        return PageResult.restPage(messageList, new Page<InquiryMessageVO>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal()));
    }

    @Override
    public Boolean searchResultSupplierInquiryLimit(Long buyerId) {
        Date date = new DateTime().minusHours(supplierInquirySearchResultHourLimit).toDate();
        Integer count = inquiryAllDao.searchResultSupplierInquiryCount(date, buyerId);
        log.info("searchResultSupplierInquiryLimit buyerId:{},countLimit:{},timeLimit:{},result:{}", buyerId, supplierInquirySearchResultCountLimit, supplierInquirySearchResultHourLimit, count);

        return count < supplierInquirySearchResultCountLimit;
    }

    @Override
    public Boolean relatedProductInquiryCount(Long buyerId) {
        Date date = new DateTime().minusHours(relatedProductHourLimit).toDate();
        Integer count = inquiryAllDao.relatedProductInquiryCount(date,buyerId);
        log.info("relatedProductInquiryCount buyerId:{},countLimit:{},timeLimit:{},result:{}",buyerId,relatedProductCountLimit,relatedProductHourLimit,count);

        return count<relatedProductCountLimit;
    }

    public APPInquireBuyerStatusTotalVO appInquiryBuyerListStatusTotal(Long buyerId) {
        APPInquireBuyerStatusTotalVO result = new APPInquireBuyerStatusTotalVO();
        result.setAll(inquiryBuyerStatusDao.buyerListAllCount(buyerId));
        result.setUnread(inquiryBuyerStatusDao.buyerListCount(buyerId, 1 , 2));
        result.setUnreplied(inquiryBuyerStatusDao.buyerListCount(buyerId, 1 , 3));
        return result;
    }

    @Override
    public BuyerInquiryTotalVO inquiryCount(Long buyerId) {
        BuyerInquiryTotalVO result = new BuyerInquiryTotalVO();
        List<StateInquiryVO> stateInquiryVOList = inquiryBuyerStatusDao.buyerInquireTotal(buyerId);
        if (CollectionUtils.isNotEmpty(stateInquiryVOList)) {
            for (StateInquiryVO stateInquiryVO : stateInquiryVOList) {
                switch (stateInquiryVO.getState()) {
                    case "all":
                        result.setAll(stateInquiryVO.getTotal());
                        break;
                    case "starred":
                        result.setStarred(stateInquiryVO.getTotal());
                        break;
                    case "bin":
                        result.setBin(stateInquiryVO.getTotal());
                        break;
                    default:break;
                }
            }
        }
        return result;

    }

    @Override
    public int delInquiry(Long buyerId, IdsVO ids) {
        InquiryBuyerStatusEntity entity = InquiryBuyerStatusEntity.builder()
                .deleteFlag(true).build();
        return inquiryBuyerStatusDao.update(entity,new LambdaQueryWrapper<InquiryBuyerStatusEntity>()
                .in(InquiryBuyerStatusEntity:: getThreadId,ids.getInquiryIds()).eq(InquiryBuyerStatusEntity::getBuyerId, buyerId));
    }

    @Override
    public BuyerInquiryListTotalVO inquiryStatusCount(Long buyerId, Integer tabType) {
        BuyerInquiryListTotalVO result = new BuyerInquiryListTotalVO();
        List<StateInquiryVO> stateInquiryVOList = inquiryBuyerStatusDao.buyerInquiryListTotal(buyerId, tabType);
        if (CollectionUtils.isNotEmpty(stateInquiryVOList)) {
            for (StateInquiryVO stateInquiryVO : stateInquiryVOList) {
                switch (stateInquiryVO.getState()) {
                    case "all":
                        result.setAll(stateInquiryVO.getTotal());
                        break;
                    case "new":
                        result.setInquiryNew(stateInquiryVO.getTotal());
                        break;
                    case "reply":
                        result.setReply(stateInquiryVO.getTotal());
                        break;
                    default:
                        break;
                }
            }
        }
        return result;
    }

    @Override
    public InquiryBuyerStatusVO inquiryStateInfo(String inquiryId) {
        InquiryBuyerStatusEntity buyerInquireStateEntity = inquiryBuyerStatusDao.selectOne(new LambdaQueryWrapper<InquiryBuyerStatusEntity>()
                .eq(InquiryBuyerStatusEntity:: getThreadId, inquiryId));
        InquiryBuyerStatusVO buyerInquireStateVO = new InquiryBuyerStatusVO();
        BeanUtils.copyProperties(buyerInquireStateEntity, buyerInquireStateVO);
        return buyerInquireStateVO;
    }

    @Override
    public boolean updateInquiryState(InquiryBuyerStatusDTO buyerInquireStateDTO, String inquiryId) {
        log.info("BuyerInquiryController updateInquiryState inquiryId:{},dto:{}",inquiryId,buyerInquireStateDTO);

        InquiryBuyerStatusEntity inquiryBuyerStatusEntity = new InquiryBuyerStatusEntity();
        BeanUtils.copyProperties(buyerInquireStateDTO, inquiryBuyerStatusEntity, MyBeanUtils.getNullPropertyNames(buyerInquireStateDTO));

        return inquiryBuyerStatusDao.update(inquiryBuyerStatusEntity, new LambdaQueryWrapper<InquiryBuyerStatusEntity>()
                .eq(InquiryBuyerStatusEntity:: getThreadId, inquiryId))>0;
    }

    @Override
    public boolean inquiryMarkRead(RequestRfiReadDTO requestRfiReadDTO) {
        Boolean flag = requestRfiReadDTO.getReadFlag() == 1;
        InquiryBuyerStatusEntity entity = InquiryBuyerStatusEntity.builder()
                .readFlag(flag).build();
        return inquiryBuyerStatusDao.update(entity,new LambdaQueryWrapper<InquiryBuyerStatusEntity>()
                .in(InquiryBuyerStatusEntity:: getThreadId,requestRfiReadDTO.getThreadId()).eq(InquiryBuyerStatusEntity::getBuyerId, requestRfiReadDTO.getUserId()))>0;
    }

    @Override
    public boolean inquiryStarred(List<String> inquiryIdList, Long userId, Integer type) {

        log.info("BuyerInquiryController inquiryStarred ids:{},buyerId:{},starredFlag:{}",inquiryIdList,userId,type);

        Boolean flag = type == 1;
        InquiryBuyerStatusEntity entity = InquiryBuyerStatusEntity.builder()
                .starredFlag(flag).build();
        return inquiryBuyerStatusDao.update(entity, new LambdaQueryWrapper<InquiryBuyerStatusEntity>()
                .in(InquiryBuyerStatusEntity::getThreadId, inquiryIdList).eq(InquiryBuyerStatusEntity::getBuyerId, userId))>0;
    }

    @Override
    public Result<Boolean> inquiryPin(String threadId,long userId,boolean pinFlag) {
        return Result.success(inquiryBuyerStatusDao.update(null, new LambdaUpdateWrapper<InquiryBuyerStatusEntity>()
                .eq(InquiryBuyerStatusEntity::getThreadId, threadId)
                .eq(InquiryBuyerStatusEntity::getBuyerId, userId)
                .set(InquiryBuyerStatusEntity::getPinTopDate,pinFlag?new Date():null))>0);
    }

    private List<InquiryMessageVO> getMessageList(InquireAllVO inquireAllVO, List<InquireAllEmailChatVO> chatEntityList){

        List<InquiryMessageVO> messageList = new ArrayList<>(chatEntityList.size() *2);

        Map<String, List<RfiInquiryEmailAddressCoreVO>> emailAddrMap=getEmailAddressBatch(chatEntityList.stream().filter(Objects::nonNull).map(InquireAllEmailChatVO::getReplyId).collect(Collectors.toList()));

        InquiryMessageVO message = null;

        //chatEntityList 回复消息
        for (int i = 0; i < chatEntityList.size(); i++) {
            if (Objects.isNull(chatEntityList.get(i))) {
                continue;
            }
            message = new InquiryMessageVO();
            //messageType:1 买家发送的消息messageType:2 卖家发送的消息
            message.setShowRole(chatEntityList.get(i).getMessageType() == 1);
            //对方是否已读
            message.setFlagHaveRead(chatEntityList.get(i).getMessageType() == 1 ? chatEntityList.get(i).getReadFlag() : Boolean.FALSE);
            //发件人信息
            message.setFromEmailAddress(chatEntityList.get(i).getSenderEmailAddr());
            message.setFromUserFirstName(chatEntityList.get(i).getFirstName());
            message.setFromUserLastName(chatEntityList.get(i).getLastName());

            //收件人信息
            message.setToEmailAddress(chatEntityList.get(i).getRecipientEmailAddr());

            //回复消息
            message.setMessage(chatEntityList.get(i).getMessage());

            message.setSendDate(chatEntityList.get(i).getCreateDate());

            //产品信息
            message.setAttachmentType(inquireAllVO.getAttachmentType());

            //  2022/8/11
            if (!InquiryTypeEnum.SUPPLIER.equals(inquireAllVO.getInquiryType())) {
                message.setAttachmentType("new");
                //区分new gsol 和 mc附件类型
                if ("old".equals(inquireAllVO.getAttachmentType())) {
                    Boolean flag = inquireAllVO.getInquireAllItemList().getProductImageUrl().indexOf(HOST_NAME) != -1;
                    if (Boolean.TRUE.equals(flag)) {
                        message.setAttachmentType("new");
                    }
                }
            }

            // refactor add
            List<InquiryAttachmentVO> attachmentList = inquiryAllDao.getInquiryAttachmentArrayByReplyId(chatEntityList.get(i).getReplyId());

            // refactor remove 旧查询
            if (CollectionUtils.isEmpty(attachmentList)) {
                attachmentList = inquiryAllDao.getInquiryFileArrayByReplyId(chatEntityList.get(i).getReplyId());
            }

            if (CollectionUtils.isNotEmpty(attachmentList)) {
                //s3附件
                //  2022/8/11 index?
                message.setAttachment(fileUpload(attachmentList, i));
            }

            //抄送邮箱
            List<RfiInquiryEmailAddressCoreVO> rfiInquiryEmailAddressCoreVOS = emailAddrMap.get(chatEntityList.get(i).getReplyId());

            if (CollectionUtils.isNotEmpty(rfiInquiryEmailAddressCoreVOS)) {
                message.setCcEmailAddressList(OrikaMapperUtil.coverList(rfiInquiryEmailAddressCoreVOS, RfiInquiryEmailAddressAggVO.class));
            }

            messageList.add(message);
        }
        return messageList.stream().sorted(Comparator.comparing(InquiryMessageVO::getSendDate).reversed()).collect(Collectors.toList());
    }

    private Map<String, List<RfiInquiryEmailAddressCoreVO>> getEmailAddressBatch(List<String> replyIds) {
        if (CollectionUtils.isEmpty(replyIds)) {
            return new HashMap<>();
        }
        Map<String, List<RfiInquiryEmailAddressCoreVO>> addrMap = new HashMap<>(replyIds.size());
        try {
            // refactor 买家中心 message list
            List<RfiInquiryEmailAddressCoreVO> list = inquiryAllEmailService.selectInquireEmailListBatch(Lists.newArrayList(RfiCommonConstants.CC), replyIds);

            // refactor remove
            // 没有结果的replyId, 再查一次
            if (CollectionUtils.isNotEmpty(list)) {
                List<String> findIds = list.stream().map(RfiInquiryEmailAddressCoreVO::getReplyId).distinct().collect(Collectors.toList());
                List<String> noFindIds = replyIds.stream().filter(item -> !findIds.contains(item)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(noFindIds)) {
                    List<RfiInquiryEmailAddressCoreVO> addrList = inquiryAllEmailAddressService.emailAddressBatch(noFindIds);
                    if (CollectionUtils.isNotEmpty(addrList)) {
                        list.addAll(addrList);
                    }
                }
            } else {
                list = inquiryAllEmailAddressService.emailAddressBatch(replyIds);
            }

            if (CollectionUtils.isEmpty(list)) {
                return addrMap;
            }

            for (RfiInquiryEmailAddressCoreVO addr : list) {
                if (Objects.nonNull(addr)) {
                    List<RfiInquiryEmailAddressCoreVO> itemList = addrMap.get(addr.getReplyId());
                    if (CollectionUtils.isEmpty(itemList)) {
                        itemList = new ArrayList<>();
                        addrMap.put(addr.getReplyId(), itemList);
                    }
                    itemList.add(addr);
                }
            }
        } catch (Exception e) {
            log.error("getEmailAddressBatch error,param:{},exception:{}", replyIds, JSON.toJSONString(e));
        }
        return addrMap;
    }

    @SneakyThrows
    private List<ResponseAttachmentVO> fileUpload(List<InquiryAttachmentVO> files, int index) {
        try {
            List<String> fileKeys = new ArrayList<>();
            files.forEach(att -> {
                if (!"old".equals(att.getAttachmentType()) && index != 0) {
                    fileKeys.add(att.getUrl());
                }
                if ("new".equals(att.getAttachmentType())) {
                    fileKeys.add(att.getUrl());
                }
            });
            JSONObject jsonObject = attachmentService.batchGetFileUrl(fileKeys, "rfi", 1);
            if (Objects.isNull(jsonObject) || jsonObject.isEmpty()) {
                return Collections.emptyList();
            }
            files.forEach(att -> {
                att.setUrl(jsonObject.get(att.getUrl()).toString());
                att.setType(FileUtil.fileMimeType(att.getName()));
                att.setSourceType(SourceNameEnum.MESSAGE.getKey());
            });

            return OrikaMapperUtil.coverList(files, ResponseAttachmentVO.class);
        } catch (Exception e) {
            log.error("fileUpload error,param:{},exception:{}", files, JSON.toJSONString(e));
        }
        return Collections.emptyList();
    }

    @Override
    public InquiryCompanyVO getSupplierInfoByInquiryId(InquiryDetailDTO dto){
        String inquiryId = dto.getInquiryId();
        InquirySupplierStatusVO inquirySupplierStatusVO = supplierInquiryService.inquiryStateInfo(inquiryId);

        if(Objects.nonNull(dto.getBuyerId())){
            InquiryBuyerStatusVO inquiryBuyerStatusVO = inquiryStateInfo(inquiryId);
            if(Objects.nonNull(inquiryBuyerStatusVO)  && !inquiryBuyerStatusVO.getBuyerId().equals(dto.getBuyerId())){
                log.error("getSupplierInfoByInquiryId inquiry view limit, inquiryId ={} ,dto:{}", dto.getInquiryId(), dto);
                throw new BusinessException(ResultCode.RfiResultCode.RFI_VIEW_LIMIT);
            }
        }

        //supplier 信息
        InquiryCompanyVO inquiryCompanyVO = new InquiryCompanyVO();
        inquiryCompanyVO.setSupplierId(String.valueOf(inquirySupplierStatusVO.getSupplierId()));
        try {
            SupplierCommonInfoDTO supplierMainInfoDto = supplierService.getSupplierCommonInfo(inquirySupplierStatusVO.getSupplierId());
            SupplierDynamicVO supplierGSOLDynamicVO = new SupplierDynamicVO();
            List<SectionVerifiableFieldDTO> sectionVerifiableFieldDTOList = null;
            //获取供应商信息
            if (Objects.nonNull(supplierMainInfoDto)) {
                log.info("==============================supplier service supplier info===============================");
                log.info("supplier service supplier info = {}", supplierMainInfoDto);

                //公司名称
                inquiryCompanyVO.setCompanyName(supplierMainInfoDto.getCompanyDisplayName());
                inquiryCompanyVO.setMaxContractLevel(ContractCodeEnum.getLevel(supplierMainInfoDto.getContractCode()));
                inquiryCompanyVO.setMemberTypeNum(supplierMainInfoDto.getMemberTypeNum());
                inquiryCompanyVO.setContractGroupCode(supplierMainInfoDto.getContractGroupCode());
                inquiryCompanyVO.setContractCode(supplierMainInfoDto.getContractCode());
                //公司认证信息
                supplierGSOLDynamicVO.setVerifiedManufacturerFlag(supplierMainInfoDto.getVerifiedSupplierFlag());
                supplierGSOLDynamicVO.setVerifiedSupplierFlag(supplierMainInfoDto.getVerifiedSupplierFlag());
                supplierGSOLDynamicVO.setO2oFlag(supplierMainInfoDto.getO2oFlag());
                supplierGSOLDynamicVO.setMaxContractLevel(ContractCodeEnum.getLevel(supplierMainInfoDto.getContractCode()));
                supplierGSOLDynamicVO.setContractCode(supplierMainInfoDto.getContractCode());
                supplierGSOLDynamicVO.setMemberTypeNum(supplierMainInfoDto.getMemberTypeNum());
                supplierGSOLDynamicVO.setContractGroupCode(supplierMainInfoDto.getContractGroupCode());
                supplierGSOLDynamicVO.setMemberSince(supplierMainInfoDto.getMemberSince());
                inquiryCompanyVO.setCompanyName(supplierMainInfoDto.getCompanyDisplayName());
                inquiryCompanyVO.setSupplierType(supplierMainInfoDto.getSupplierType());
                sectionVerifiableFieldDTOList = supplierMainInfoDto.getBusinessTypes();
            } else {
                log.info("===========================invoke supplierService.getSupplierCommonInfo and return empty==================================");
                log.error("invoke supplierService.getSupplierCommonInfo and return empty, supplier = {}, supplierUserId={}", inquirySupplierStatusVO.getSupplierId(), inquirySupplierStatusVO.getSupplierUserId());
                log.error("inquiry get supplier empty = {}", inquirySupplierStatusVO.getThreadId());
            }
            UserBaseProfileVO userBaseProfileVOResult = userService.getUserBaseProfile(inquirySupplierStatusVO.getSupplierUserId());
            if (Objects.nonNull(userBaseProfileVOResult)) {
                log.info("userService getUserBaseProfile user info = {}", userBaseProfileVOResult);

                //公司基本信息
                inquiryCompanyVO.setUserId(inquirySupplierStatusVO.getSupplierUserId());
                if (Objects.nonNull(userBaseProfileVOResult.getContactInfo())) {
                    inquiryCompanyVO.setEmailAddress(userBaseProfileVOResult.getContactInfo().getEmail());
                    inquiryCompanyVO.setFirstName(userBaseProfileVOResult.getContactInfo().getFirstName());
                    inquiryCompanyVO.setLastName(userBaseProfileVOResult.getContactInfo().getLastName());
                    inquiryCompanyVO.setRoleName(userBaseProfileVOResult.getContactInfo().getJobTitle());
                    StringBuilder phone = new StringBuilder();
                    if (StringUtils.isNotEmpty(userBaseProfileVOResult.getContactInfo().getTelCountryCode())) {
                        phone.append("(" + userBaseProfileVOResult.getContactInfo().getTelCountryCode() + ")");
                    }
                    if (StringUtils.isNotEmpty(userBaseProfileVOResult.getContactInfo().getTelAreaCode())) {
                        phone.append(userBaseProfileVOResult.getContactInfo().getTelAreaCode());
                    }
                    if (StringUtils.isNotEmpty(userBaseProfileVOResult.getContactInfo().getPhone())) {
                        phone.append(userBaseProfileVOResult.getContactInfo().getPhone());
                    }
                    inquiryCompanyVO.setPhone(phone.toString());
                    inquiryCompanyVO.setCountry(dictCountryUtils.convertMapForOrderStatus(userBaseProfileVOResult.getContactInfo().getCountryCode()));
                    inquiryCompanyVO.setSupplierUserPhoto(userBaseProfileVOResult.getContactInfo().getAvatar());
                }

            } else {
                log.error("userService getUserBaseProfile fail supplier = {}, supplierUserId={}", inquirySupplierStatusVO.getSupplierId(), inquirySupplierStatusVO.getSupplierUserId());
                log.error("inquiry get supplier info empty = {}", inquirySupplierStatusVO.getThreadId());
            }

            //供应商证书
            supplierGSOLDynamicVO.setCertificateList(getSupplierCertificateVOS(inquirySupplierStatusVO.getSupplierId()));

            //业务类型
            if (CollectionUtils.isNotEmpty(sectionVerifiableFieldDTOList)) {
                supplierGSOLDynamicVO.setBusinessType(StringUtils.join(sectionVerifiableFieldDTOList.stream().filter(Objects::nonNull).map(SectionVerifiableFieldDTO::getDisplayValue).collect(Collectors.toList()), ","));
            }

            //主要产品
            SuppProductCategoryLinkAggDTO suppProductCategoryLink = categoryService.getMostProdCategoryId(inquirySupplierStatusVO.getSupplierId(), 5);
            if (Objects.nonNull(suppProductCategoryLink) && CollectionUtils.isNotEmpty(suppProductCategoryLink.getCategoryIds())) {
                //主要产品（在线产品的最多的前5个L4类别）
                List<ProductCategoryVO> categoryList = categoryService.getCategoryListByIds(suppProductCategoryLink.getCategoryIds());
                if (CollectionUtils.isNotEmpty(categoryList)) {
                    supplierGSOLDynamicVO.setMainProducts(StringUtils.join(categoryList.stream().filter(Objects::nonNull).map(ProductCategoryVO::getDescEn).collect(Collectors.toList()), ","));
                }
            }
            supplierGSOLDynamicVO.setMajorCustomers(null);
            inquiryCompanyVO.setSupplierGSOLDynamic(supplierGSOLDynamicVO);

        } catch (Exception e) {
            log.error("inquiry buyer detail get supplier dynamic info error,supplierId:{},error:{}", inquirySupplierStatusVO.getSupplierId(), JSON.toJSONString(e));
        }
        return inquiryCompanyVO;
    }

    /**
     * 获取rfi buyer 详情中的supplier info
     *
     * @param inquirySupplierStatusVO
     * @return
     */
    private InquiryCompanyVO getSupplierDynaInfo(InquirySupplierStatusVO inquirySupplierStatusVO) {
        //supplier 信息
        InquiryCompanyVO inquiryCompanyVO = new InquiryCompanyVO();

        try {
            SupplierCommonInfoDTO supplierMainInfoDto = supplierService.getSupplierCommonInfo(inquirySupplierStatusVO.getSupplierId());
            SupplierDynamicVO supplierGSOLDynamicVO = new SupplierDynamicVO();
            List<SectionVerifiableFieldDTO> sectionVerifiableFieldDTOList = null;
            //获取供应商信息
            if (Objects.nonNull(supplierMainInfoDto)) {
                log.info("==============================supplier service supplier info===============================");
                log.info("supplier service supplier info = {}", supplierMainInfoDto);

                //公司名称
                inquiryCompanyVO.setCompanyName(supplierMainInfoDto.getCompanyDisplayName());

                //公司认证信息
                supplierGSOLDynamicVO.setVerifiedManufacturerFlag(supplierMainInfoDto.getVerifiedSupplierFlag());
                supplierGSOLDynamicVO.setVerifiedSupplierFlag(supplierMainInfoDto.getVerifiedSupplierFlag());
                supplierGSOLDynamicVO.setO2oFlag(supplierMainInfoDto.getO2oFlag());
                supplierGSOLDynamicVO.setMaxContractLevel(supplierMainInfoDto.getMaxContractLevel());
                supplierGSOLDynamicVO.setMemberSince(supplierMainInfoDto.getMemberSince());
                inquiryCompanyVO.setCompanyName(supplierMainInfoDto.getCompanyDisplayName());
                sectionVerifiableFieldDTOList = supplierMainInfoDto.getBusinessTypes();
            } else {
                log.info("===========================invoke supplierService.getSupplierCommonInfo and return empty==================================");
                log.error("invoke supplierService.getSupplierCommonInfo and return empty, supplier = {}, supplierUserId={}", inquirySupplierStatusVO.getSupplierId(), inquirySupplierStatusVO.getSupplierUserId());
                log.error("inquiry get supplier empty = {}", inquirySupplierStatusVO.getThreadId());
            }

            setBaseProfile(inquiryCompanyVO,inquirySupplierStatusVO.getSupplierUserId());

            //供应商证书
            supplierGSOLDynamicVO.setCertificateList(getSupplierCertificateVOS(inquirySupplierStatusVO.getSupplierId()));

            //业务类型
            if(CollectionUtils.isNotEmpty(sectionVerifiableFieldDTOList)){
                supplierGSOLDynamicVO.setBusinessType(StringUtils.join(sectionVerifiableFieldDTOList.stream().filter(Objects::nonNull).map(SectionVerifiableFieldDTO::getDisplayValue).collect(Collectors.toList()), ","));
            }

            //主要产品
            SuppProductCategoryLinkAggDTO suppProductCategoryLinkAggDTOS = categoryService.getMostProdCategoryId(inquirySupplierStatusVO.getSupplierId(),5);
            if(Objects.nonNull(suppProductCategoryLinkAggDTOS) && CollectionUtils.isNotEmpty(suppProductCategoryLinkAggDTOS.getCategoryIds())){
                //主要产品（在线产品的最多的前5个L4类别）
                List<ProductCategoryVO> categoryList = categoryService.getCategoryListByIds(suppProductCategoryLinkAggDTOS.getCategoryIds());
                if(CollectionUtils.isNotEmpty(categoryList)){
                    supplierGSOLDynamicVO.setMainProducts(StringUtils.join(categoryList.stream().filter(Objects::nonNull).map(ProductCategoryVO::getDescEn).collect(Collectors.toList()), ","));
                }
            }
            supplierGSOLDynamicVO.setMajorCustomers(null);
            inquiryCompanyVO.setSupplierGSOLDynamic(supplierGSOLDynamicVO);

        }catch (Exception e){
            log.error("inquiry buyer detail get supplier dynamic info error,supplierId:{},error:{}",inquirySupplierStatusVO.getSupplierId(),JSON.toJSONString(e));
        }
        return inquiryCompanyVO;
    }

    private void setBaseProfile(InquiryCompanyVO inquiryCompanyVO, Long supplierUserId){
        UserBaseProfileVO userBaseProfileVOResult = userService.getUserBaseProfile(supplierUserId);
        if (Objects.nonNull(userBaseProfileVOResult)) {
            log.info("userService getUserBaseProfile user info = {}", userBaseProfileVOResult);

            //公司基本信息
            inquiryCompanyVO.setUserId(supplierUserId);
            if(Objects.nonNull(userBaseProfileVOResult.getContactInfo())){
                inquiryCompanyVO.setEmailAddress(userBaseProfileVOResult.getContactInfo().getEmail());
                inquiryCompanyVO.setFirstName(userBaseProfileVOResult.getContactInfo().getFirstName());
                inquiryCompanyVO.setLastName(userBaseProfileVOResult.getContactInfo().getLastName());
                inquiryCompanyVO.setRoleName(userBaseProfileVOResult.getContactInfo().getJobTitle());
                StringBuilder phone = new StringBuilder();
                if(StringUtils.isNotEmpty(userBaseProfileVOResult.getContactInfo().getTelCountryCode())){
                    phone.append("(" + userBaseProfileVOResult.getContactInfo().getTelCountryCode() + ")");
                }
                if(StringUtils.isNotEmpty(userBaseProfileVOResult.getContactInfo().getTelAreaCode())){
                    phone.append(userBaseProfileVOResult.getContactInfo().getTelAreaCode());
                }
                if(StringUtils.isNotEmpty(userBaseProfileVOResult.getContactInfo().getPhone())){
                    phone.append(userBaseProfileVOResult.getContactInfo().getPhone());
                }
                inquiryCompanyVO.setPhone(phone.toString());
                inquiryCompanyVO.setCountry(dictCountryUtils.convertMapForOrderStatus(userBaseProfileVOResult.getContactInfo().getCountryCode()));
            }
        }
    }
    private List<CertificateVO> getSupplierCertificateVOS(Long supplierId){
        //供应商证书
        List<CertificateVO> supplierCertificateVOS = new ArrayList<>();
        List<OnlineCertificateDTO> certificateDTOList = supplierService.getCertificateList(supplierId,"SECTION");
        if (CollectionUtils.isNotEmpty(certificateDTOList)) {
            supplierCertificateVOS = certificateDTOList.stream().filter(Objects::nonNull).sorted(Comparator.comparing(OnlineCertificateDTO::getExpiryDate, Comparator.nullsFirst(Date::compareTo)).reversed()).map(item -> {
                CertificateVO certificateVO = new CertificateVO();
                if(Objects.nonNull(item.getCertificateStandardItem())){
                    certificateVO.setTitle(item.getCertificateStandardItem().getRefCodeDescEn());
                }else{
                    certificateVO.setTitle(item.getOtherCertificateStandard());
                }
                certificateVO.setImage(item.getCertificateUrl());
                return certificateVO;
            }).collect(Collectors.toList());
        }
        return supplierCertificateVOS;
    }

    @Override
    public Integer inquiryUnreadCount(Long buyerId) {
        Integer total = null;
        if (Objects.nonNull(buyerId)) {
            total = inquiryBuyerStatusDao.buyerInquiryUnreadTotal(buyerId);
        }
        return Optional.ofNullable(total).orElse(BigInteger.ZERO.intValue());
    }

    @Override
    public String getInquiryMessage(Long buyerId,String inquiryId) {
        return inquiryAllDao.getInquiryMessage(buyerId,inquiryId);
    }

    @Override
    public List<ProductInfoVO> relatedProductSendInquiryFilter(FilterProductDTO dto){
        log.info("filterRecentProductIds dto:{}", dto);
        List<Long> productIds = dto.getProductIdList().stream().distinct().collect(Collectors.toList());

        InquiryMatchResultDTO matchResult =null;
        InquiryAllItemEntity item = null;
        if(StringUtils.isNotBlank(dto.getInquiryId())){
            matchResult =inquiryMatchResultDao.getMatchResultInquiryId(dto.getInquiryId());
            item = inquiryAllItemDao.selectOne(new LambdaQueryWrapper<InquiryAllItemEntity>().eq(InquiryAllItemEntity::getInquiryId,dto.getInquiryId()));
        }
        if(Objects.nonNull(matchResult) && Objects.nonNull(matchResult.getMatchResultJson())) {
            log.info("filterRecentProductIds matchResult:{}", matchResult);
            String json = JSON.parse(matchResult.getMatchResultJson().toString()).toString();
            List<Long> matchProductId = JSON.parseArray(json, Long.class);
            productIds.removeAll(matchProductId);
        }

        List<ProductLiteVO> productList = productService.getLiteProductListByIds(productIds);
        if (CollectionUtils.isEmpty(productList)) {
            return new ArrayList<>();
        }
        log.info("filterRecentProductIds productList:{}", productList);

        Set<Long> supplierIdSet = new HashSet<>(productList.size());
        if(Objects.nonNull(item)){
            supplierIdSet.add(item.getSupplierId());
        }
        List<ProductInfoVO> result = new ArrayList<>(productList.size());

        for (ProductLiteVO product : productList) {
            //过滤重复供应商
            if(supplierIdSet.contains(product.getSupplierId())){
                continue;
            }
            supplierIdSet.add(product.getSupplierId());
            //产品信息
            ProductInfoVO productInfoVO = new ProductInfoVO();
            productInfoVO.setProductId(product.getProductId());
            productInfoVO.setProductImage(product.getProductPrimaryImage());
            productInfoVO.setProductName(product.getProductName());
            productInfoVO.setQuantity(Objects.isNull(product.getMinOrderQuantity())?0:product.getMinOrderQuantity());
            productInfoVO.setUnit(product.getMinOrderSingleUnit());
            productInfoVO.setSupplierId(product.getSupplierId());
            productInfoVO.setCategoryId(product.getCategoryId());
            result.add(productInfoVO);
        }
        log.info("filterRecentProductIds productList result:{}", result);
        return result;
    }
}
