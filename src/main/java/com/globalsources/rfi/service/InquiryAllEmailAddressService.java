package com.globalsources.rfi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.rfi.agg.core.dto.rfi.InquireAllEmailCoreDTO;
import com.globalsources.rfi.agg.core.vo.rfi.RfiInquiryEmailAddressCoreVO;
import com.globalsources.rfi.data.entity.InquiryAllEmailAddressEntity;
import java.util.List;


public interface InquiryAllEmailAddressService extends IService<InquiryAllEmailAddressEntity> {


    Integer addEmailAddress(InquireAllEmailCoreDTO inquireAllEmailCoreDTO);

    List<RfiInquiryEmailAddressCoreVO> emailAddress(String replyId);


    List<RfiInquiryEmailAddressCoreVO> emailAddressBatch(List<String> replyIdList);


    List<RfiInquiryEmailAddressCoreVO> emailAddr(String replyId, String emailType);

}
