package com.globalsources.rfi.service;

import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfi.agg.core.dto.InquiryBuyerStatusDTO;
import com.globalsources.rfi.agg.core.dto.mc.RequestRfiReadDTO;
import com.globalsources.rfi.agg.core.vo.InquiryBuyerStatusVO;
import com.globalsources.rfi.agg.dto.product.FilterProductDTO;
import com.globalsources.rfi.agg.request.BuyerInquiryListDTO;
import com.globalsources.rfi.agg.request.InquiryChatDTO;
import com.globalsources.rfi.agg.request.InquiryDetailDTO;
import com.globalsources.rfi.agg.request.message.InquiryMessageListDTO;
import com.globalsources.rfi.agg.response.*;
import com.globalsources.rfi.agg.response.detail.BuyerInquiryDetailVO;
import com.globalsources.rfi.agg.response.detail.InquiryCompanyVO;
import com.globalsources.rfi.agg.response.detail.InquiryMessageVO;
import com.globalsources.rfi.agg.response.detail.InquiryReplyDetailChatVO;
import com.globalsources.rfi.agg.response.rfi.ProductInfoVO;
import com.globalsources.rfi.data.entity.InquiryAllEmailChatEntity;
import java.util.List;

public interface BuyerInquiryService {

    PageResult<BuyerInquiryVO> inquiryList(BuyerInquiryListDTO buyerInquiryListDTO, Long buyerId);

    Result<String> inquiryReply(InquiryChatDTO inquiryChatDTO, UserVO userVO,String replySource);

    /**
     * 买家钓鱼回复
     * @param inquiryChatDTO
     * @param userVO
     * @return
     */
    Result<String> blackReply(InquiryChatDTO inquiryChatDTO, UserVO userVO,String replySource);

    Result<InquiryReplyDetailChatVO> inquiryDetail(String inquiryId, UserVO userVO,String lang);

    BuyerInquiryDetailVO inquiryDetailV2(InquiryDetailDTO inquiryDetailDTO);

    Result<com.globalsources.rfi.agg.response.APPInquiryReplyChatVO> appInquiryDetail(Long buyerId,String inquiryId);

    Integer inquiryUnreadCount(Long buyerId);

    void releaseAfterReview(InquiryAllEmailChatEntity inquiryAllEmailChatEntity);

    PageResult<InquiryMessageVO> getInquiryMessagePageList(InquiryMessageListDTO dto);

    InquiryCompanyVO getSupplierInfoByInquiryId(InquiryDetailDTO dto);

    Boolean searchResultSupplierInquiryLimit(Long buyerId);

    String getInquiryMessage(Long userId, String inquiryId);

    List<ProductInfoVO> relatedProductSendInquiryFilter(FilterProductDTO dto);

    Boolean relatedProductInquiryCount(Long buyerId);

    APPInquireBuyerStatusTotalVO appInquiryBuyerListStatusTotal(Long buyerId);

    BuyerInquiryTotalVO inquiryCount(Long buyerId);

    int delInquiry(Long buyerId, IdsVO ids);

    BuyerInquiryListTotalVO inquiryStatusCount(Long buyerId, Integer tabType);

    InquiryBuyerStatusVO inquiryStateInfo(String inquiryId);

    boolean updateInquiryState(InquiryBuyerStatusDTO buyerInquireStateDTO, String inquiryId);

    boolean inquiryMarkRead(RequestRfiReadDTO requestRfiReadDTO);

    boolean inquiryStarred(List<String> inquiryIdList, Long userId, Integer type);

    Result<Boolean> inquiryPin(String threadId,long userId,boolean pinFlag);
}
