package com.globalsources.rfi.service;


import com.globalsources.rfi.agg.response.DistRuleVO;

import java.util.List;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.buyerrfi.repository
 * @date:2021/5/10
 */
public interface DistRuleService {

    DistRuleVO getTargetUserIdWithCache(Long supplierId, Long productId, Long  buyerId);

    Long getTargetProductId(List<Long> productIdList);
}
