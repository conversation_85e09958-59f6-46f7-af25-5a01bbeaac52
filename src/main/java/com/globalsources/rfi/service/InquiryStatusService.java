package com.globalsources.rfi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.globalsources.rfi.agg.dto.inquiry.InquiryStatusDTO;
import com.globalsources.rfi.agg.enums.InquiryStatusEnum;
import com.globalsources.rfi.data.entity.InquiryAllEntity;
import com.globalsources.rfi.data.entity.InquiryAllItemEntity;
import com.globalsources.rfi.data.entity.InquiryStatusEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/11/2
 */
public interface InquiryStatusService extends IService<InquiryStatusEntity> {
    Integer saveInquiryStatus(InquiryStatusDTO statusDTO);

    Boolean batchSaveInquiryStatus(Map<String, InquiryAllEntity> allEntityMap, Map<String, InquiryAllItemEntity> itemEntityMap, InquiryStatusEnum statusEnum);

    Boolean batchSaveInquiryStatus(List<String> threadIds, Long userId, String email, InquiryStatusEnum statusEnum);

    List<InquiryStatusEntity> getInquiryStatusListByThreadId(String inquiryId, String threadId);

}
