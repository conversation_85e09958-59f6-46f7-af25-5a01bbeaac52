package com.globalsources.rfi.service;

import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.rfi.agg.dto.admin.AdminInquireDetailAggDTO;
import com.globalsources.rfi.agg.dto.admin.AdminSearchInquiryDTO;
import com.globalsources.rfi.agg.dto.admin.AdminSearchInquiryFaqDTO;
import com.globalsources.rfi.agg.request.TsQueryDTO;
import com.globalsources.rfi.agg.request.admin.AdminInquiryEDMDTO;
import com.globalsources.rfi.agg.request.admin.AdminInquiryQueryDTO;
import com.globalsources.rfi.agg.request.admin.AdminOnSiteRfiQueryDTO;
import com.globalsources.rfi.agg.request.admin.AdminProductQueryDTO;
import com.globalsources.rfi.agg.request.admin.AdminSearchInquiryQueryDTO;
import com.globalsources.rfi.agg.request.admin.AdminTradeshowZoneProductSaveDTO;
import com.globalsources.rfi.agg.response.RfiConvertRfqCountVO;
import com.globalsources.rfi.agg.response.TsOnSiteZoneGroupVO;
import com.globalsources.rfi.agg.response.admin.AdminOnSiteRfiListVO;
import com.globalsources.rfi.agg.response.admin.AdminTradeshowZoneProductVO;
import com.globalsources.rfi.agg.response.console.RfiThreadDetailVO;
import com.globalsources.rfi.agg.response.rfi.InquiryEDMAnalysisVO;
import com.globalsources.rfi.agg.response.rfi.InquiryTradeshowZoneProductVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> Chen
 * @date 2021/8/2 18:52
 */
public interface AdminRfiService {
    AdminInquireDetailAggDTO detail(String inquiryId);

    RfiThreadDetailVO rfiDetail(String threadId);

    PageResult<AdminSearchInquiryDTO> searchRfiList(Integer pageNum, Integer pageSize, String keyword);

    PageResult<AdminSearchInquiryFaqDTO> searchFaqRfiList(AdminSearchInquiryQueryDTO query);

    List<InquiryEDMAnalysisVO> edmAnalysis(AdminInquiryEDMDTO dto);

    List<RfiConvertRfqCountVO> getConvertRfqCountList(AdminInquiryQueryDTO query);

    PageResult<AdminTradeshowZoneProductVO> searchTradeshowZoneProductList(AdminSearchInquiryQueryDTO dto);

    Result<Boolean> saveTradeshowZoneProduct(AdminTradeshowZoneProductSaveDTO dto);

    Boolean deleteTradeshowZoneProduct(Long rfiTszpId, Long userId);

    Boolean deleteAllTradeshowZoneProduct(Long userId);

    Boolean batchUploadTradeshowZoneProduct(Long userId, MultipartFile uploadFile);

    List<AdminTradeshowZoneProductVO> downloadTradeshowZoneProductList();

    List<String> getTradeshowZoneProductGroupCodeList(String rfiType);

    List<InquiryTradeshowZoneProductVO> getTradeshowZoneProductInfo(AdminProductQueryDTO dto);

    PageResult<AdminOnSiteRfiListVO> searchOnSiteRfiList(AdminOnSiteRfiQueryDTO dto);

    List<AdminOnSiteRfiListVO> downloadOnSiteRfiList(AdminOnSiteRfiQueryDTO dto);

    List<TsOnSiteZoneGroupVO> searchZoneProductList(TsQueryDTO dto);
}
