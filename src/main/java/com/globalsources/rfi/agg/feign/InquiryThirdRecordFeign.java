package com.globalsources.rfi.agg.feign;

import com.globalsources.framework.result.Result;
import com.globalsources.rfi.agg.core.dto.record.InquiryThirdRecordDTO;
import com.globalsources.rfi.agg.core.vo.record.InquiryThirdRecordVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <a>Title: InquiryTmxFeign </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/8/9-14:22
 */
@FeignClient(value = "gsol-rfi-agg", path = "/gsol-rfi-agg/rfi-third-record")
public interface InquiryThirdRecordFeign {

    @PostMapping(value = "/save")
    @ApiOperation(value = "保存审核数据")
    Result<String> save(@RequestBody InquiryThirdRecordDTO inquiryThirdRecordDTO);

    @GetMapping(value = "/getRfiRequest/{businessId}/{businessType}")
    @ApiOperation(value = "根据业务编号业务类型获取审核数据")
    Result<InquiryThirdRecordVO> getRfiRequest(@PathVariable String businessId, @PathVariable String businessType);
}
