package com.globalsources.rfi.agg.feign;

import com.globalsources.framework.result.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <a>Title: RfiSocketFeign </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2022/1/14-15:00
 */
@FeignClient(value = "gsol-rfi-agg", path = "/rfi-socket")
public interface RfiSocketFeign {

    @PostMapping(value = "v1/rfi-supplier-msg/{supplierId}/{userId}")
    @ApiOperation(value = "press inquiry")
    Result<String> rfiSupplierMsg(@PathVariable Long supplierId, @PathVariable Long userId);
}
