/**
 * <a>Title: MessageFeign </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/22-17:26
 */
package com.globalsources.rfi.agg.feign;

import com.globalsources.framework.result.Result;
import com.globalsources.rfi.agg.core.dto.match.InquiryMatchResultDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "gsol-rfi-agg", path = "/gsol-rfi-agg/match")
public interface InquiryMatchResultFeign {

    @ApiOperation(value = "保存match匹配数据")
    @PostMapping(value = "/save-match-result")
    Result<Boolean> saveMatchResult(@RequestBody InquiryMatchResultDTO inquiryMatchResultDTO);

    @ApiOperation(value = "保存match匹配数据")
    @PostMapping(value = "/save-match-result-inquiry/{matchId}/{inquiryId}")
    Result<Boolean> saveMatchResultInquiry(@PathVariable String matchId, @PathVariable String inquiryId);

    @ApiOperation(value = "获取匹配生成的匹配数据")
    @GetMapping(value = "/get-match-result-match-id/{matchId}")
    Result<InquiryMatchResultDTO> getMatchResultByMatchId(@PathVariable String matchId);

    @ApiOperation(value = "获取匹配生成的匹配数据")
    @GetMapping(value = "/get-match-result-inquiry-id/{inquiryId}")
    Result<InquiryMatchResultDTO> getMatchResultInquiryId(@PathVariable String inquiryId);

    @ApiOperation(value = "获取upsell某个时间段的匹配数据")
    @GetMapping(value = "/get-match-upsell-list")
    Result<InquiryMatchResultDTO> getMatchUpsellResult();

    @ApiOperation(value = "获取category某个时间段的匹配数据")
    @GetMapping(value = "get-match-category-result/{categoryId}")
    Result<InquiryMatchResultDTO> getMatchCategoryResult(@PathVariable Long categoryId);
}
