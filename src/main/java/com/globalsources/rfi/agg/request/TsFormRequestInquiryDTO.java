package com.globalsources.rfi.agg.request;

import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import com.globalsources.rfi.agg.request.user.UserProfileDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TsFormRequestInquiryDTO implements Serializable {

    @ApiModelProperty(value = "消息")
    private String message;

    private Long categoryId;

    private Long userId;

    @ApiModelProperty(value = "商品數量")
    @Range(min = 1, max = 999999999, message = "商品數量")
    private Integer productNum;

    @ApiModelProperty(value = "Quantity默认为对应产品的MOQ（最小订单量）标记")
    private Boolean flexibleFlag;

    private Integer upsellCount;

    private Long tradeshowId;

    private String tradeshowName;

    private String pubCode;

    private List<Long> productIdList;

    @ApiModelProperty("询盘类型")
    private InquiryTypeEnum inquiryType;

}
