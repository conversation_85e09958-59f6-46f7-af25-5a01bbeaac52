package com.globalsources.rfi.agg.request.admin;

import com.globalsources.framework.page.BasePage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class L4UpsellMatchQueryDTO extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    private String  matchId;

    private String  matchType;

    private List<Long> excludeIds;

    private Boolean rowSupplierFlag;
}
