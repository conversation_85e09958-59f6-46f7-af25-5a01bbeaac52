/**
 * <a>Title: InquiryDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/6/30-15:45
 */
package com.globalsources.rfi.agg.request;

import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class InquiryDTO implements Serializable {

    @ApiModelProperty(value = "公司ID")
    private Long orgId;

    @ApiModelProperty(value = "message")
    private String message;

    @ApiModelProperty(value = "附件")
    private List<AttachmentEmailDTO> attachmentList;

    @ApiModelProperty(value = "商品编号")
    private Long productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品图片")
    private String productImage;

    @ApiModelProperty(value = "型号")
    private String modelNumber;

    @ApiModelProperty(value = "询盘category desc")
    private String inquiryCatDesc;

    @ApiModelProperty(value = "询盘类型")
    private InquiryTypeEnum inquiryType;

    @ApiModelProperty(value = "商品數量")
    private Integer productNum;

    @ApiModelProperty(value = "商品单位")
    private String productUnit;

    @ApiModelProperty(value = "sessionId")
    private String sessionId;

    @ApiModelProperty(value = "recommend matching suppliers and send this inquiry to them")
    public Boolean recommendFlag;
}
