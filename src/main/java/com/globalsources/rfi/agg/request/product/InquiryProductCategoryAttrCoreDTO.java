package com.globalsources.rfi.agg.request.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-11
 */
@Data
@Valid
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InquiryProductCategoryAttr对象", description="")
public class InquiryProductCategoryAttrCoreDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    private Integer inqPcaId;

    @NotNull
    @ApiModelProperty(value = "inquiry_id")
    private String inquiryId;

    @NotNull
    @ApiModelProperty(value = "冗余产品属性id")
    private Long attrId;

    @ApiModelProperty(value = "产品属性名称")
    private String attrName;

    @ApiModelProperty(value = "Category L4 id")
    private Long categoryId;

    @ApiModelProperty(value = "supplier_id")
    private Long supplierId;

    @ApiModelProperty(value = "Buyer user id")
    private Long buyerId;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    @ApiModelProperty(value = "更新人id")
    private Long lUpdBy;

    private List<String> attrValues;

}
