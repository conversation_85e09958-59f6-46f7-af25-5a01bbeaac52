package com.globalsources.rfi.agg.request.template;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InquiryReplyTemplate对象", description="")
public class InquiryReplyTemplateSaveAggDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "auto increment primary key")
    @TableId(value = "irt_id", type = IdType.AUTO)
    private Long irtId;

    @ApiModelProperty(value = "模板名")
    private String templateName;

    @ApiModelProperty(value = "模板内容")
    private String content;

    @ApiModelProperty(value = "supplier id")
    private Long supplierId;

    @ApiModelProperty(value = "user id")
    private Long userId;

    @ApiModelProperty(value = "default template")
    private Boolean defaultFlag;

    @ApiModelProperty(value = "修改人")
    private Long lUpdBy;



}
