/**
 * <a>Title: InquiryDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/6/30-15:45
 */
package com.globalsources.rfi.agg.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryDetailDTO implements Serializable {

    private Long supplierUserId;

    private String inquiryId;

    private Boolean adminFlag;

    private Long buyerId;

    private Long supplierId;
}
