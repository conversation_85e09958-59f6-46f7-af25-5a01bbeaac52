package com.globalsources.rfi.agg.request.template;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import springfox.documentation.annotations.ApiIgnore;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InquiryReplyTemplate对象", description="")
public class InquiryReplyTemplateRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long irtId;

    @ApiModelProperty(value = "模板名")
    private String templateName;

    @ApiModelProperty(value = "模板内容")
    private String content;

    @ApiModelProperty(hidden = true)
    private Long supplierId;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(value = "default template")
    private Boolean defaultFlag;

    @ApiModelProperty(hidden = true)
    private Long lUpdBy;



}
