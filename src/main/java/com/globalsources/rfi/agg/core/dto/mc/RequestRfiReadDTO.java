package com.globalsources.rfi.agg.core.dto.mc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: RequestRfiReadDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/8/17-18:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestRfiReadDTO implements Serializable {

    private Long userId;

    private String [] threadId;

    private Integer readFlag;
}
