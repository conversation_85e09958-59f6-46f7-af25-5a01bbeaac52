package com.globalsources.rfi.agg.core.vo.rfi;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/4/11
 */
@Data
public class RfiProductDwdVO {

    private static final long serialVersionUID = 1L;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * L4类别ID
     */
    private Integer categoryId;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商公司名称
     */
    private String companyName;

    /**
     * 合同等级
     */
    private String showroomType;

    /**
     * EDW 需求指数
     */
    private String supplierNeedinessIndex;

    /**
     * 首次上架时间
     */
    private Date firstonlinedate;

    /**
     * 更新时间
     */
    private Date lUpdDate;

}
