package com.globalsources.rfi.agg.core.dto.attachment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: AttachmentEmailDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/9/13-21:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttachmentCoreDTO implements Serializable {

    private String fileKey;

    private String name;

    private String reqId;

    private Long size;

    private Integer storeType;

    private String thumbImg;

    private Integer type;

    private String url;

    private Boolean whetherThumbImg;
}
