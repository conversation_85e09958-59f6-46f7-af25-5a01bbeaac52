package com.globalsources.rfi.agg.core.dto.admin;


import com.globalsources.rfi.agg.core.vo.InquiryAttachmentVO;
import com.globalsources.rfi.agg.dto.user.BuyerUserInfoDTO;
import com.globalsources.rfi.agg.dto.user.SellerUserInfoDTO;
import com.globalsources.rfi.agg.response.InquiryProdVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <a>Title: AdminInquireDetailDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/12/29-20:11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "fi详情(admin console)")
public class AdminInquireDetailDTO implements Serializable {

    private static final long serialVersionUID = -4138913670027590203L;
    @ApiModelProperty("买家用户信息")
    private BuyerUserInfoDTO buyerUserInfo;
    @ApiModelProperty("卖家用户信息")
    private SellerUserInfoDTO sellerUserInfo;
    @ApiModelProperty("产品信息")
    private InquiryProdVO inquiryProd;
    @ApiModelProperty("附件list")
    private List<InquiryAttachmentVO> attachments;
    @ApiModelProperty("消息")
    private String message;
    @ApiModelProperty("主题")
    private String subject;
    @ApiModelProperty("询盘id")
    private String inquiryId;
    @ApiModelProperty("submit date")
    private Date createDate;
    @ApiModelProperty("询盘类型 SUPPLIER, SUPPLIER, PRODUCT_UPSELL")
    private String inquiryType;
    @ApiModelProperty("最新状态 枚举：1：Send，2：Under Review，3：Release，4：Reply，5：Reassign，6：Pass，7：Block，8：Reject")
    private Integer threadStatus;
    @ApiModelProperty("最新状态：Send，Under Review，Release，Reply，Reassign，Pass，Block, Reject")
    private String status;
    @ApiModelProperty("Upsell from")
    private String parInquiryId;
    @ApiModelProperty("rif来源(平台)")
    private String rfiSources;
    @ApiModelProperty("状态记录")
    private List<AdminRfiThreadStatusDTO> statusRecords;
}
