package com.globalsources.rfi.agg.core.dto;

import com.globalsources.rfi.agg.core.dto.attachment.AttachmentCoreDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquireAllEmailChatDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long supplierId;

    /**
     * 询盘编号
     */
    private String inquiryId;

    /**
     * 回复人编号
     */
    private Long userId;

    /**
     * 询盘回复编号，对应附件
     */
    private String replyId;

    /**
     * 回复类型：1，buyer回复，2，supplier回复
     */
    private Integer messageType;

    /**
     * 回复消息
     */
    private String message;

    /**
     * 发送的电子邮箱
     */
    private String senderEmailAddr;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 最后修改时间
     */
    private Date lUpdDate;

    /**
     * 主键编号
     */
    private Integer id;

    /**
     * first name
     */
    private String firstName;

    /**
     * last name
     */
    private String lastName;

    /**
     * 回复人的电子邮箱
     */
    private String recipientEmailAddr;

    /**
     * 接收人的userId
     */
    private Long senderUserId;

    private Boolean offlineFlag;

    /** 1：master supplier , 2:supplier, 3:buyer*/
    private Integer supplierType = 3;

    /**
     * buyer reply black message
     */
    private Boolean blacklistFlag;

    private List<AttachmentCoreDTO> attachmentCoreDTOList;

    private String replySource;


}
