/**
 * <a>Title: InquiryProductDetailVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/5/21-10:48
 */
package com.globalsources.rfi.agg.core.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class InquiryProductDetailVO implements Serializable {

    @ApiModelProperty(value = "商品id")
    private Long productId;

    @ApiModelProperty(value = "商品标题")
    private String productTitle;

    @ApiModelProperty(value = "商品描述")
    private String productDesc;

    @ApiModelProperty(value = "商品图片")
    private String productImage;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "categoryId")
    private Long categoryId;

    @ApiModelProperty(value = "orgId")
    private Long orgId;

    @ApiModelProperty(value = "categoryName")
    private String categoryName;

    @ApiModelProperty(value = "供应商最大等级，为null/-1 标识不存在")
    private Integer maxContractLevel;

    @ApiModelProperty(value = "是否有o2o标志")
    private Boolean o2oFlag;

    @ApiModelProperty(value = "与GS合作年限")
    private Integer memberSince;

    @ApiModelProperty(value = "是否已认证制造商")
    private Boolean verifiedManufacturerFlag;

    @ApiModelProperty(value = "是否为已认证供应商")
    private Boolean verifiedSupplierFlag;

    @ApiModelProperty(value = "quantity")
    private Integer quantity;

    @ApiModelProperty(value = "unit")
    private String unit;

}
