package com.globalsources.rfi.agg.core.dto;

import com.globalsources.rfi.agg.request.product.ProductCategoryAttributeRequestDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquireAllItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 线索
     */
    private String threadId;

    /**
     * 询盘id
     */
    private String inquiryId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 主题
     */
    private String subject;

    /**
     * 产品图片
     */
    private String productImageUrl;

    /**
     * 产品型号
     */
    private String modelNumber;

    /**
     * 最小订单量
     */
    private Integer expectedOrderQty;

    /**
     * 最小订单单位
     */
    private String expectedOrderQtyUom;

    /**
     * 请求时间
     */
    private LocalDate inqResponseByDate;

    /**
     * 供应商公司ID
     */
    private Long supplierId;

    /**
     * 供应商类型
     */
    private String supplierType;

    /**
     * 创建时间
     */
    private Date createDate = new Date();

    /**
     * 最后修改时间
     */
    private Date lUpdDate = new Date();

    /**
     * upsellFlag
     */
    private Boolean upsellFlag;

    private List<ProductCategoryAttributeRequestDTO> productCategoryAttrInfos;

}
