package com.globalsources.rfi.agg.core.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierInquireStateVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer id;
    private String inquiryId;
    private Long supplierId;
    private Long supplierUserId;
    private Boolean distFlag;
    private Boolean starredFlag;
    private Boolean replyFlag;
    private Boolean chatReplyFlag;
    private Boolean readFlag;
    private Boolean masterAcctReadFlag;
    private Boolean masterAcctStarredFlag;
    private Boolean deleteFlag;
    private Boolean rubbishFlag;
    private String emailAddress;
    private String firstName;
    private String lastName;
    private Date createDate;
    private Date lUpdDate;
}