package com.globalsources.rfi.agg.core.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchInquiryStatusVO implements Serializable {

    @ApiModelProperty(value = "是否加星标注")
    private Boolean starredFlag;

    @ApiModelProperty(value = "是否已经回复")
    private Boolean replyFlag;

    @ApiModelProperty(value = "是否已读")
    private Boolean readFlag;

    @ApiModelProperty(value = "是否删除")
    private Boolean deleteFlag;

    @ApiModelProperty(value = "是否垃圾询盘")
    private Boolean rubbishFlag;
}
