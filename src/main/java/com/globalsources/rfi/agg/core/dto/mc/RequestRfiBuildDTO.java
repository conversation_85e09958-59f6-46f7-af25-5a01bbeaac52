package com.globalsources.rfi.agg.core.dto.mc;

import com.globalsources.rfi.agg.core.dto.rfi.InquireAllEmailAddressCoreDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: RequestRfiBuildDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/8/13-9:33
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestRfiBuildDTO implements Serializable {

    private String inquiryId;

    private String threadId;

    private String inquiryType;

    private Boolean hasAttachmentStatus;

    private String supplierReplyCnt;

    private String buyerDetailOutStatus;

    private BuyerProfile buyerProfile;

    private SupplierProfile supplierProfile;

    private ProductInfo productInfo;

    private InquireAllEmailAddressCoreDTO cc;

    private ThreadMessage threadMessage;

}
