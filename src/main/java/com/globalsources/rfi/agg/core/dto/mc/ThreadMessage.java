package com.globalsources.rfi.agg.core.dto.mc;

import com.globalsources.rfi.agg.request.AttachmentEmailDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThreadMessage implements Serializable {

    private Long userId;

    private Long msgId;

    private String subject;

    private Long requestId;

    private Long threadId;

    private String buyerMessage;

    private List<AttachmentEmailDTO> attachmentEmailDTOList;
}