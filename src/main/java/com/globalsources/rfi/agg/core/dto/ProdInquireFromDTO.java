/**
 * <a>Title: InquiryFromDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/20-19:30
 */
package com.globalsources.rfi.agg.core.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value="询盘提交对象", description="")
public class ProdInquireFromDTO implements Serializable {

    @ApiModelProperty(value = "商品编号")
    @NotNull(message = "inquiryProductId is not null")
    private Long inquiryProductId;

    @ApiModelProperty(value = "templateId")
    @NotNull(message = "templateId is not null")
    private Integer templateId;

    @ApiModelProperty(value = "message")
    @NotNull(message = "message is not null")
    @Size(max = 1500)
    private String message;

    @ApiModelProperty(value = "附件")
    private List<AttachmentEmailDTO> attachmentList;

    @ApiModelProperty(value = "商品數量")
    @NotNull(message = "productNum is not null")
    @Range(min = 1, max = 1000, message = "商品數量")
    private Integer productNum;

    @ApiModelProperty(value = "商品单位")
    @NotNull(message = "productUnit is not null")
    @Size(min = 1, max = 100)
    private String productUnit;

    @ApiModelProperty(value = "inquiryKeyword")
    private String inquiryKeyword;

    @ApiModelProperty(value = "query")
    private String query;

    @ApiModelProperty(value = "sessionId")
    @NotNull(message = "sessionId is not null")
    private String sessionId;

    @ApiModelProperty(value = "recommend matching suppliers and send this inquiry to them")
    public Boolean recommendFlag;
}
