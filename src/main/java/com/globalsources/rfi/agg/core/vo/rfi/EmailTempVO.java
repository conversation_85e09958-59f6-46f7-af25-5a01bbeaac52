package com.globalsources.rfi.agg.core.vo.rfi;

import lombok.Data;

import java.util.Date;

@Data
public class EmailTempVO {

    /**
     * 线索
     */
    private String threadId;

    /**
     * 询盘id
     */
    private String inquiryId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品图片
     */
    private String productImageUrl;

    /**
     * 产品型号
     */
    private String modelNumber;

    /**
     * 供应商公司ID
     */
    private Long supplierId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 最小订单量
     */
    private Integer expectedOrderQty;

    /**
     * 最小订单单位
     */
    private String expectedOrderQtyUom;


    /**
     * buyer status
     */
    private Long buyerId;

    private String emailAddr;

    private String firstName;

    private String lastName;


    /**
     * supplier status
     */
    private Long supplierUserId;

    private String supplierEmailAddress;

    private String supplierFirstName;

    private String supplierLastName;

}
