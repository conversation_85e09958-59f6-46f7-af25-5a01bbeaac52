package com.globalsources.rfi.agg.core.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquirySupplierStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 询盘编号
     */
    private String threadId;

    /**
     * 供应商编号
     */
    private Long supplierUserId;

    private Date createDate;

    private Date lUpdDate;

    /**
     * 是否加星
     */
    private Boolean starFlag;

    /**
     * 是否已回复
     */
    private Boolean replyFlag;

    /**
     * 是否已读
     */
    private Boolean readFlag;

    /**
     * 是否已删除
     */
    private Boolean deleteFlag;

    /**
     * 是否是垃圾询盘
     */
    private Boolean rubbishFlag;

    private Integer id;

    /**
     * 已分配，未分配
     */
    private Boolean distFlag;

    /**
     * 卖家电子邮箱
     */
    private String emailAddr;

    private String firstName;

    private String lastName;

    /**
     * 公司ID
     */
    private Long supplierId;

    /**
     * 主账号已读
     */
    private Boolean masterAcctReadFlag;

    /**
     * 主账号标记加星
     */
    private Boolean masterAcctStarredFlag;

}
