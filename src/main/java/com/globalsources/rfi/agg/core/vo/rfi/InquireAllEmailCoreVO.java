package com.globalsources.rfi.agg.core.vo.rfi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 电子邮箱发送记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquireAllEmailCoreVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 询盘编号
     */
    private String inquiryId;

    /**
     * 电子邮箱发送编号
     */
    private Long emailId;

    /**
     * 主题
     */
    private String subject;

    /**
     * 是否发送
     */
    private String sentFlag;

    /**
     * 链接
     */
    private String inquireUrl;

    private String addlRfiDistReason;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date lUpdDate;

    private Integer inquiryEmailId;

    private List<InquireAllEmailAddressCoreVO> emailAddressList;

}
