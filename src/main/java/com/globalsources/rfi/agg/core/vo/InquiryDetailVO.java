/**
 * <a>Title: BuyerInquiryEmailDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/5/6-10:17
 */
package com.globalsources.rfi.agg.core.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class InquiryDetailVO implements Serializable {

    private String firstName;

    private String lastName;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "主题")
    private String subject;

    @ApiModelProperty(value = "电子邮件")
    private String emailAddress;

    @ApiModelProperty(value = "模板消息")
    private String message;

    @ApiModelProperty(value = "发送方")
    private String senderEmail;

    @ApiModelProperty(value = "当前用户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean currentRole;

    @ApiModelProperty(value = "对方是否已读")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean flagHaveRead;

    @ApiModelProperty(value = "附件")
    private List<InquiryAttachmentVO> attachment;

    @ApiModelProperty(value = "是否第一条显示商品")
    private Boolean productFlag = false;

    @ApiModelProperty(value = "询盘类型")
    private InquiryTypeEnum inquiryType;

    @ApiModelProperty(value = "supplierId")
    private Long supplierId;

    @ApiModelProperty(value = "商品id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String productId;

    @ApiModelProperty(value = "型号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String modelNumber;

    @ApiModelProperty(value = "图片")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String productImage;

    @ApiModelProperty(value = "商品标题")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String productTitle;

    @ApiModelProperty(value = "商品数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer productNum;

    @ApiModelProperty(value = "商品单位")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String productUnit;

    @ApiModelProperty(value = "询盘发送时间")
    private Date createDate;

    @ApiModelProperty(value = "是否upsell询盘")
    private Boolean recommendFlag;
}
