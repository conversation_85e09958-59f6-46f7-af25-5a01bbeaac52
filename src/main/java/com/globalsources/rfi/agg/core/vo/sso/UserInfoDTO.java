package com.globalsources.rfi.agg.core.vo.sso;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: UserInfo </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2022/3/10-14:27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoDTO implements Serializable {

    private Long userId;

    private String firstName;

    private String lastName;

    private String email;

    private Boolean doiStatus;

    private String companyName;

}
