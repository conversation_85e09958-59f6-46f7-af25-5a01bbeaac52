package com.globalsources.rfi.agg.core.dto.record;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InquiryThirdRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键id
     */
    private Integer iralId;

    /**
     * 业务类型
     */
    private String assessSystem;

    /**
     * 业务主键
     */
    private String inquiryId;

    /**
     * 请求数据
     */
    private String reqData;

    /**
     * 业务结果code
     */
    private String businessCode;

    /**
     * 响应结果
     */
    private String respData;

    /**
     * 调用结果code
     */
    private String resultCode;

    /**
     * 调用结果msg
     */
    @TableField("result_msg")
    private String resultMsg;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

}
