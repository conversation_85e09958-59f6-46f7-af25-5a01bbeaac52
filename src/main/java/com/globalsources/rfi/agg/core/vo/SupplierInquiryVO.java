/**
 * <a>Title: InquiryVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/22-13:35
 */
package com.globalsources.rfi.agg.core.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.globalsources.rfi.agg.core.dto.rfi.RfiSupplierDetailCoreVO;
import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierInquiryVO implements Serializable {

    @ApiModelProperty(value = "询盘编号")
    private String inquiryId;

    @ApiModelProperty(value = "询盘类型")
    private InquiryTypeEnum inquiryType;

    @ApiModelProperty(value = "文件类型")
    private String attachmentType;

    @ApiModelProperty(value = "文件类型")
    private String sourceName;

    @ApiModelProperty(value = "商品编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long productId ;

    @ApiModelProperty(value = "型号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String modelNumber;

    @ApiModelProperty(value = "主题")
    private String subject;

    @ApiModelProperty(value = "图片")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String productImage;

    @ApiModelProperty(value = "商品标题")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String productTitle;

    @ApiModelProperty(value = "最新的询盘回复消息")
    private String newReplyMessage;

    @ApiModelProperty(value = "消息")
    private String buyerMessage;

    @ApiModelProperty(value = "是否重新分配")
    private Boolean redistribution;

    @ApiModelProperty(value = "买家头像(app)")
    private String buyerAvatar;

    @ApiModelProperty(value = "是否有附件(app)")
    private Boolean hasAttachmentFlag;

    @ApiModelProperty(value = "买家编号")
    private Long buyerId;

    @ApiModelProperty(value = "供应商编号")
    private Long supplierId;

    @ApiModelProperty(value = "供应商卖家编号")
    private Long supplierUserId;

    @ApiModelProperty(value = "名")
    private String firstName;

    @ApiModelProperty(value = "姓")
    private String lastName;

    @ApiModelProperty(value = "负责人 firstName")
    private String principalFirstName;

    @ApiModelProperty(value = "负责人 LastName")
    private String principalLastName;

    @ApiModelProperty(value = "负责人电子邮箱")
    private String principalEmail;

    @ApiModelProperty(value = "买家国籍")
    private String country;

    @ApiModelProperty(value = "买家公司")
    private String companyName;

    @ApiModelProperty(value = "产品数量")
    private Integer num;

    @ApiModelProperty(value = "产品单位")
    private String unit;

    @ApiModelProperty(value = "是否已经分配")
    private Boolean distFlag;

    @ApiModelProperty(value = "是否加星标注")
    private Boolean starredFlag;

    @ApiModelProperty(value = "是否已经回复")
    private Boolean replyFlag;

    @ApiModelProperty(value = "是否已读")
    private Boolean readFlag;

    @ApiModelProperty(value = "是否删除")
    private Boolean deleteFlag;

    @ApiModelProperty(value = "是否删除标记")
    private Boolean markDelFlag;

    @ApiModelProperty(value = "是否垃圾询盘")
    private Boolean rubbishFlag;

    @ApiModelProperty(value = "询盘创建时间")
    private Timestamp createDate;

    @ApiModelProperty(value = "询盘最后更新时间")
    private Timestamp lastDate;

    @ApiModelProperty(value = "是否upsell询盘")
    private Boolean recommendFlag;

    @ApiModelProperty(value = "chat 供应商信息")
    private RfiSupplierDetailCoreVO rfiSupplierDetail;

    @ApiModelProperty(value = "消息类型")
    private String msgType;

    @ApiModelProperty(value = "买家注册时间")
    private Date buyerRegisterDate;

    @ApiModelProperty(value = "买家邮箱")
    private String buyerEmailAddress;

    @ApiModelProperty(value = "买家已核实")
    private String buyerVerified;

    @ApiModelProperty(value = "买家类型")
    private Integer[] buyerType;

    @ApiModelProperty(value = "inquire_all_item.supplier_type")
    private String supplierType;

    @ApiModelProperty(value = "firstInquiryFlag")
    private Boolean firstInquiryFlag;
}
