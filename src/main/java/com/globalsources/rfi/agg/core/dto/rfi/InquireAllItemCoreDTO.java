package com.globalsources.rfi.agg.core.dto.rfi;

import com.globalsources.rfi.agg.request.AttachmentEmailDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> Li
 * @since 2021-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquireAllItemCoreDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 线索
     */
    private String threadId;

    /**
     * 询盘id
     */
    private String inquiryId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品图片
     */
    private String productImageUrl;

    /**
     * 产品型号
     */
    private String modelNumber;

    /**
     * 最小订单量
     */
    private Integer expectedOrderQty;

    /**
     * 最小订单单位
     */
    private String expectedOrderQtyUom;

    /**
     * 请求时间
     */
    private Date inqResponseByDate;

    /**
     * 供应商公司ID
     */
    private Long supplierId;

    /**
     * 供应商类型
     */
    private String supplierType;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 最后修改时间
     */
    private Date lUpdDate;

    /**
     * 询盘是否重新分配
     */
    private Boolean redistributionFlag;

    /**
     * 询盘是否重新分配
     */
    private Boolean upsellFlag;


    private List<AttachmentEmailDTO> attachmentEmailDTOList;

}
