package com.globalsources.rfi.agg.core.dto.rfi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquireAllItemDetailCoreDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 询盘编号
     */
    private String inquiryId;

    /**
     * 文章编号
     */
    private Integer articleId;

    /**
     * 目标国家清单
     */
    private String targetCountryList;

    /**
     * keyword 历史清单
     */
    private String keywordHistList;

    /**
     * 类别历史清单
     */
    private String l4CategoryHistList;

    /**
     * 文章类别清单
     */
    private String articleCategoryHistList;

    private Boolean showAddlBuyerInfoFlag;

    /**
     * 资源服务器
     */
    private String sourceWebServer;

    /**
     * 询盘类型方法
     */
    private String inquiryTypeMethod;

    /**
     * 询盘类型路径
     */
    private String inquiryTypePath;

    /**
     * 展会编号
     */
    private Long tradeshowId;

    /**
     * 推荐标识
     */
    private Boolean emagReferralFlag;

    /**
     * 最后发送消息标识
     */
    private Boolean lastSentMessageFlag;

    /**
     * 保存消息标识
     */
    private Boolean savedMessageFlag;

    /**
     * 网站编程语言
     */
    private String siteLangCode;

    /**
     * 自动注册标识
     */
    private Boolean autoRegFlag;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 最后修改时间
     */
    private Date lUpdDate;

    /**
     * 主键
     */
    private Integer id;

}
