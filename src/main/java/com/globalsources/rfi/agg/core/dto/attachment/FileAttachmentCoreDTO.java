package com.globalsources.rfi.agg.core.dto.attachment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <a>Title: FileAttachmentDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/9/2-9:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileAttachmentCoreDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件编号
     */
    private String fileId;

    /**
     * 文件路径
     */
    private String url;

    /**
     * 上传用户编号
     */
    private Long ownerId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件类型：附件文件类型 1 图片 2文件 4压缩包 5其他键入消息
     */
    private Integer mimeType;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 最后修改时间
     */
    private Date lUpdDate;

    /**
     * 主键
     */
    private Integer id;
}
