package com.globalsources.rfi.agg.core.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: SupplierReassignVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/8/10-9:47
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierReassignDTO implements Serializable {

    private String[] inquiryIds;

    private Long supplierId;

    private Long supplierUserId;

    private String firstName;

    private String lastName;

    private String email;

    @ApiModelProperty("潜在商机询盘迁出来源: 1=web ; 2=H5 ; 4=IOS ; 5=Android")
    private String source;

}
