package com.globalsources.rfi.agg.core.vo;

import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquireAllVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 询盘编号
     */
    private String inquiryId;

    /**
     * threadId
     */
    private String threadId;

    /**
     * query参数
     */
    private String query;

    /**
     * 类别详情
     */
    private String inquiryCatDesc;

    /**
     * keyword
     */
    private String inquiryKeyword;

    /**
     * sessionId
     */
    private String sessionId;

    /**
     * 询盘类型：当前只有PRODUCT,老数据存在SUPPLIER
     */
    private InquiryTypeEnum inquiryType;

    /**
     * 完成状态
     */
    private String formCompleteStatus;

    /**
     * 完成时间
     */
    private Date formCompleteDate;

    /**
     * 产品填充状态
     */
    private String itemPopulateStatus;

    /**
     * 填充时间
     */
    private Date itemPopulateDate;

    /**
     * 主题
     */
    private String subject;

    /**
     * 卖家留言
     */
    private String buyerMessage;

    /**
     * 是否抄送给自己
     */
    private Boolean ccSelfFlag;

    /**
     * 客户编号
     */
    private Long buyerId;

    private Boolean mcUserFlag;

    /**
     * 称号
     */
    private String title;

    private String firstName;

    private String lastName;

    /**
     * 工作
     */
    private String jobTitle;

    /**
     * 电子邮箱
     */
    private String emailAddr;

    /**
     * 抄送电子邮箱
     */
    private String ccEmailAddr;

    /**
     * 城市
     */
    private String city;

    /**
     * 州
     */
    private String state;

    /**
     * 邮政编码
     */
    private String zipCode;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * LINKEDIN个人资料网址
     */
    private String linkedinProfileUrl;

    /**
     * 创建时间
     */
    private Timestamp createDate;

    /**
     * 最后修改时间
     */
    private Timestamp lUpdDate;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 询盘是否重新分配
     */
    private Boolean redistributionFlag;

    /**
     * upsell 勾选
     */
    private Boolean recommendFlag;

    /**
     * 是否上传附件
     */
    private Boolean hasAttachmentFlag;

    /**
     * 供应商数量
     */
    private Integer supplierCnt;

    /**
     * 产品数量
     */
    private Integer productCnt;

    /**
     * 来源
     */
    private Integer rfiSource;

    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 询盘详情：一对一，一对多
     */
    private InquireAllItemVO inquireAllItemList;

    /**
     * upsell 勾选
     */
    private Boolean upSellFlag;

    /**
     * inquiryPath
     */
    private String inquiryPath;

    private String tmxStatus;

    private String eblockStatus;

    private boolean upsellMatchFlag;

    private String buyerIp;

    @ApiModelProperty("买家类型")
    private Integer[] buyerType;

    private Boolean doiStatus;

    private String doiSource;

    private String langCode;

    private String buyerCompanyName;

    private Date doiDate;

    private boolean potentialOpportunityConvertFlag;

}
