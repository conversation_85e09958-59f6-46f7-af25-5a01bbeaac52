package com.globalsources.rfi.agg.core.dto.rfi;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class L4UpsellDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "categoryId not null")
    private Long categoryId;

    @NotNull(message = "limitCnt not null")
    private Integer limitCnt = 200;

    @ApiModelProperty(value = "offset")
    private Integer offsetCnt = 0;

    private Long supplierId;

    private List<String> countryNameList;

    private List<String> stateNameList;

    @ApiModelProperty(value = "exhibitors标记：指含有有效Tradeshow展会合同（OSFSTD合同）")
    private Boolean exhibitorsFlag;

    @ApiModelProperty(value = "exhibitors查询开始天数")
    private Integer exhibitorsStartDays;

    @ApiModelProperty(value = "exhibitors查询结束天数")
    private Integer exhibitorsEndDays;

    @ApiModelProperty(value = "o2o标记：指O2O标识的供应商")
    private Boolean o2oFlag;

    @ApiModelProperty(value = "排除的ID列表：supplier id")
    private List<Long> excludeIds;

    @ApiModelProperty(value = "非中国大陆供应商")
    private Boolean rowSupplierFlag;
}
