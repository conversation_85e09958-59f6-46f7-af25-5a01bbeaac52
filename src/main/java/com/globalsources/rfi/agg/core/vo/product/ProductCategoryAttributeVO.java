package com.globalsources.rfi.agg.core.vo.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/9 10:11
 */
@Data
@ApiModel(description = "产品属性")
public class ProductCategoryAttributeVO implements Serializable {

    private static final long serialVersionUID = 441397992600104890L;

    @ApiModelProperty("产品属性名称")
    private String attrName;
    @ApiModelProperty("产品属性值")
    private List<String> attrValue;
}
