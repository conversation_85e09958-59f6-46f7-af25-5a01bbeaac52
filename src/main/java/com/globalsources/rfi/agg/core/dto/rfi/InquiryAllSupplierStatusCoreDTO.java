package com.globalsources.rfi.agg.core.dto.rfi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: InquireAllSupplierStatusCoreDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/9/1-20:26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryAllSupplierStatusCoreDTO implements Serializable {

    private String threadId;

    private Long supplierId;

    private Long supplierUserId;

    private String firstName;

    private String lastName;

    private String emailAddr;
}
