/**
 * <a>Title: EmailTemplateDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 *
 * <AUTHOR>
 * @date 2021/6/28-19:57
 */
package com.globalsources.rfi.agg.core.dto;

import com.globalsources.rfi.agg.enums.TemplateEnums;
import lombok.Data;

import java.io.Serializable;

@Data
public class EmailTemplateDTO implements Serializable {

    /**模板类型*/
    public TemplateEnums type;

    /**商品ID*/
    public Long productId;

    /**姓名*/
    public String username;
}
