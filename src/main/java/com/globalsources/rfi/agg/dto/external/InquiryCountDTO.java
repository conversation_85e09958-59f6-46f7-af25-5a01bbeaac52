package com.globalsources.rfi.agg.dto.external;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class InquiryCountDTO implements Serializable {

    private Long userId;

    private Long supplierId;

    private Long roleId;

    private Date startDate;

    private Date endDate;

    private List<Long> productIdList;

    private int day;
}
