package com.globalsources.rfi.agg.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "admin console 查询rfi常见问题列表 列表dto")
public class AdminSearchInquiryFaqAggDTO implements Serializable {
    private static final long serialVersionUID = -3666019861004638625L;

    @ApiModelProperty(value = "询盘id:thread_id")
    private String rfiId;

    @ApiModelProperty(value = "询盘id:inquiry_id")
    private String inquiryId;

    @ApiModelProperty(value = "产品id:product_id")
    private String ppId;

    @ApiModelProperty("supplierId")
    private Long supplierId;

    @ApiModelProperty(value = "l4分类名")
    private String categoryName;
}
