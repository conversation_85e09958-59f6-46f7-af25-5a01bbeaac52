package com.globalsources.rfi.agg.dto.inquiry;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/2
 */
@Data
@Builder
@ApiModel(description = "询盘删除DTO")
@NoArgsConstructor
@AllArgsConstructor
public class InquiryDeleteDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> threadIdList;
    private Long supplierId;
    private Long userId;
    private Boolean adminFlag;
}
