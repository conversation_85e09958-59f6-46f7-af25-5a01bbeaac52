package com.globalsources.rfi.agg.dto.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/8/3 10:45
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "询盘状态变更记录")
public class AdminInquiryStatusAggDTO implements Serializable {

    private static final long serialVersionUID = 539799138830825434L;

    @ApiModelProperty(value = "邮箱")
    private String emailAddr;

    @ApiModelProperty(value = "最新状态：状态：PENDING，REPLY，REASSIGN，TMX_PASS，TMX_REVIEW，TMX_REJECT，EBLOCK_BLOCK，EBLOCK_RELEASE，EBLOCK_REJECT")
    private String status;

    @ApiModelProperty(value = "最新状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "旧的状态码，不再使用，保留避免异常")
    private Integer threadStatus = 0;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;
}
