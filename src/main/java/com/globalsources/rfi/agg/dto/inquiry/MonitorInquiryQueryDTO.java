package com.globalsources.rfi.agg.dto.inquiry;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MonitorInquiryQueryDTO {

    private Date startDate;

    private Date endDate;

    private Integer offset = 0;

    private Integer pageSize = 10;
    
}
