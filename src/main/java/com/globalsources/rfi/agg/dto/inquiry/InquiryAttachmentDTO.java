package com.globalsources.rfi.agg.dto.inquiry;

import com.globalsources.rfi.agg.request.AttachmentAggDTO;
import com.globalsources.rfi.agg.request.product.ProductCategoryAttributeRequestDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/9 10:11
 */
@Data
@ApiModel(description = "询盘附件")
@NoArgsConstructor
@AllArgsConstructor
public class InquiryAttachmentDTO implements Serializable {

    private List<AttachmentAggDTO> attachmentList;

    private String threadId;

    private Long buyerId;

}
