/**
 * <a>Title: SupplierInquiryTotalEnum </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/26-9:39
 */
package com.globalsources.rfi.agg.enums;

public enum InquiryLogEnum {

    OFFLINE_EMAIL("OFFLINE_EMAIL", "离线邮件报错");

    private String key;

    private String value;

    InquiryLogEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String match(String name) {
        InquiryLogEnum[] values = InquiryLogEnum.values();
        for (InquiryLogEnum emailEnum : values) {
            if (emailEnum.key.equals(name)) {
                return emailEnum.value;
            }
        }
        return null;
    }

    public static String matchSwitch(String name) {
        InquiryLogEnum[] values = InquiryLogEnum.values();
        for (InquiryLogEnum emailEnum : values) {
            if (emailEnum.key.equals(name)) {
                return emailEnum.value;
            }
        }
        return null;
    }
    public static InquiryLogEnum getEnumByKey(String key) {
        InquiryLogEnum[] values = InquiryLogEnum.values();
        for (InquiryLogEnum emailEnum : values) {
            if (emailEnum.key.equals(key)) {
                return emailEnum;
            }
        }
        return null;
    }
}
