/**
 * <a>Title: SupplierInquiryTotalEnum </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/26-9:39
 */
package com.globalsources.rfi.agg.enums;

public enum SourceNameEnum {

    MESSAGE("message", "产品询盘"),
    QUOTATION("quotation", "供应商询盘"),
    REQUEST("request", "类目询盘");

    private String key;

    private String value;

    SourceNameEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
