/**
 * <a>Title: InquiryVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/22-13:35
 */
package com.globalsources.rfi.agg.response.rfi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InquiryTradeshowZoneProductVO implements Serializable {

    private String productPrimaryImage;

    private String productName;

    private String groupCode;

    private String productInquiryCode;

    private Long productId;

    private String productSEOUrl;

}
