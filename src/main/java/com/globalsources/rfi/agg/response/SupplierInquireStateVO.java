package com.globalsources.rfi.agg.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierInquireStateVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer id;
    private String inquiryId;
    private Long supplierId;
    private Long orgId;
    private Boolean distFlag;
    private Boolean starredFlag;
    private Boolean replyFlag;
    private Boolean readFlag;
    private Boolean masterAcctReadFlag;
    private Boolean deleteFlag;
    private Boolean rubbishFlag;
    private String emailAddress;
    private String firstName;
    private String lastName;
    private Date createDate;
    private Date lUpdDate;
}