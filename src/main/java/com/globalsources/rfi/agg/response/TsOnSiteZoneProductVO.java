package com.globalsources.rfi.agg.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/9/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TsOnSiteZoneProductVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("rfiTszpId")
    private Long rfiTszpId;

    @ApiModelProperty("分组 code")
    private String groupCode;

    @ApiModelProperty("产品二维码code")
    private String productInquiryCode;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty(value = "商品标题 product name")
    private String productName;

    @ApiModelProperty("最小订单量")
    private Integer minOrderQuantity;

    @ApiModelProperty("最小订单量单位")
    private String minOrderUnit;

    @ApiModelProperty("给前端显示的价格")
    private String listVoShowPriceStr;

    @ApiModelProperty("Product primary image")
    private String productPrimaryImage;

    @ApiModelProperty(value = "categoryId L4 数据追踪")
    private Long categoryId;

    @ApiModelProperty(value = "categoryName")
    private String categoryName;



}
