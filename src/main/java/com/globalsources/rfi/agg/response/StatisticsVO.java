/**
 * <a>Title: StatisticsVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/7/19-9:30
 */
package com.globalsources.rfi.agg.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class StatisticsVO implements Serializable {

    @ApiModelProperty("回复率")
    private Integer responseRate;

    @ApiModelProperty("平均回复时间")
    private String avgResponseTime;
}
