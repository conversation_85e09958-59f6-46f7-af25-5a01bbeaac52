package com.globalsources.rfi.agg.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/10/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierPotentialOpportunityListTotalVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "全部")
    private Integer all = 0;

    @ApiModelProperty(value = "未读")
    private Integer noRead = 0;
}
