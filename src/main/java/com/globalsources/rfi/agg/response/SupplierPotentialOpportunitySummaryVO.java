package com.globalsources.rfi.agg.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @since 2023/10/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierPotentialOpportunitySummaryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "询盘编号")
    private String inquiryId;

    @ApiModelProperty(value = "主题")
    private String subject;

    @ApiModelProperty(value = "买家编号")
    private Long buyerId;

    @ApiModelProperty(value = "名")
    private String firstName;

    @ApiModelProperty(value = "姓")
    private String lastName;

    @ApiModelProperty(value = "买家国籍")
    private String country;

    @ApiModelProperty(value = "买家邮箱")
    private String buyerEmailAddress;

    @ApiModelProperty(value = "询盘创建时间")
    private Timestamp createDate;

    @ApiModelProperty(value = "邮箱是否验证: true=已验证，false=未验证，默认空=全部")
    private Boolean doiStatus;

    @ApiModelProperty(value = "doi时间")
    private Timestamp doiDate;

    @ApiModelProperty(value = "供应商编号")
    private Long supplierId;

    @ApiModelProperty(value = "供应商卖家编号")
    private Long supplierUserId;

    @ApiModelProperty(value = "inquire_all_item.supplier_type")
    private String supplierType;

    @ApiModelProperty(value = "firstInquiryFlag，true:ONE_TO_ONE，false:ONE_TO_MANY")
    private Boolean firstInquiryFlag;
}
