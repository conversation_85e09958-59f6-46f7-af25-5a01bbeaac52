/**
 * <a>Title: SupplierCertificateVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/5/17-15:26
 */
package com.globalsources.rfi.agg.response.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CertificateVO implements Serializable {

    @ApiModelProperty(value = "证书图片路径")
    private String image;

    @ApiModelProperty(value = "证书标题")
    private String title;
}
