/**
 * <a>Title: ProdSupplierInquiryVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/6/29-19:50
 */
package com.globalsources.rfi.agg.response.rfi;

import com.globalsources.rfi.agg.response.product.ProductCategoryAttributeDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProductInfoVO implements Serializable {

    @ApiModelProperty(value = "产品编号")
    private Long productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品图片")
    private String productImage;

    @ApiModelProperty(value = "订单量")
    private Integer quantity;

    @ApiModelProperty(value = "订单量单位")
    private String unit;

    @ApiModelProperty("L4Category id")
    private Long categoryId;

    @ApiModelProperty("L1Category id")
    private Long l1CategoryId;

    @ApiModelProperty("L2Category id")
    private Long l2CategoryId;

    @ApiModelProperty("L3Category id")
    private Long l3CategoryId;

    @ApiModelProperty("L4Category id")
    private Long l4CategoryId;

    @ApiModelProperty(value = "产品属性")
    private List<ProductCategoryAttributeDetailVO> productCategoryAttrInfos;

    @ApiModelProperty(value = "公司Id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商类型")
    private String supplierType;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "供应商最大等级，为null/-1 标识不存在")
    private Integer maxContractLevel;

    @ApiModelProperty(value = "是否有o2o标志")
    private Boolean o2oFlag;

    @ApiModelProperty(value = "与GS合作年限")
    private Integer memberSince;

    @ApiModelProperty(value = "是否已认证制造商")
    private Boolean verifiedManufacturerFlag;

    @ApiModelProperty(value = "是否为已认证供应商")
    private Boolean verifiedSupplierFlag;

    @ApiModelProperty("SEO, 供应商自定义域名")
    private String supplierShortName;

    //新合同相关
    @ApiModelProperty("供应商contractCode")
    private String contractCode;
    @ApiModelProperty("供应商memberTypeNum")
    private Integer memberTypeNum;
    @ApiModelProperty("供应商contractGroupCode")
    private String contractGroupCode;

}
