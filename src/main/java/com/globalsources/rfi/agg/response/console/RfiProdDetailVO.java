package com.globalsources.rfi.agg.response.console;

import com.globalsources.rfi.agg.response.product.ProductCategoryAttributeVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <a>Title: RfiProdDetailVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2022/2/17-16:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RfiProdDetailVO implements Serializable {

    @ApiModelProperty("产品Id")
    private Long productId;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品图片链接")
    private String productUrl;

    @ApiModelProperty("产品型号")
    private String modelNumber;

    @ApiModelProperty("订单数量")
    private Long expectedOrderQty;

    @ApiModelProperty("单位")
    private String expectedOrderQtyUom;

    @ApiModelProperty("SEO 产品链接")
    private String desktopProductDetailUrl;

    @ApiModelProperty("product category")
    private List<ProductCategoryAttributeVO> productCategoryAttributeVOS;

}
