/**
 * <a>Title: BuyerInquiryChatVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/5/6-10:45
 */
package com.globalsources.rfi.agg.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.globalsources.rfi.agg.response.detail.InquiryCompanyVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class APPInquiryReplyChatVO implements Serializable {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "卖家是否已回复此询盘")
    private Boolean replyFlag;

    @ApiModelProperty(value = "第一次发送消息")
    private APPInquiryDetailVO inquiryDetailVO;

    @ApiModelProperty(value = "询盘全部集合")
    private List<APPInquiryDetailVO> inquiryDetailList;

    @ApiModelProperty(value = "卖家公司信息")
    private InquiryCompanyVO companyModel;

    @ApiModelProperty(value = "buyerId")
    public Long buyerId;

    @ApiModelProperty(value = "此买家为环球资源已核实买家")
    public Boolean verifiedFlag;

    @ApiModelProperty(value = "此买家为环球资源VIP买家")
    public Boolean vipFlag;

    @ApiModelProperty(value = "threadId")
    public String threadId;

    @ApiModelProperty(value = "是否已删除")
    public Boolean deleteFlag;

    @ApiModelProperty(value = "是否是潜在商机转换的询盘")
    public Boolean potentialOpportunityConvertFlag;

    @ApiModelProperty(value = "是否是潜在商机询盘")
    public Boolean potentialOpportunityFlag;

    @ApiModelProperty(value = "是否是推荐买家")
    public Boolean recommendBuyerFlag;
}
