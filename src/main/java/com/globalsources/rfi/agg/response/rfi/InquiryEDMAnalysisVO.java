/**
 * <a>Title: InquiryVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/22-13:35
 */
package com.globalsources.rfi.agg.response.rfi;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InquiryEDMAnalysisVO implements Serializable {
    public InquiryEDMAnalysisVO(String createDate){
        this.createDate=createDate;
    }
    private String createDate;

    @ApiModelProperty(value = "当天提交的RFI Form总数")
    private Integer totalInquiryCount = 0;

    @ApiModelProperty(value = "当天提交的所有RFI Form中，未勾选Upsell选项的数量")
    private Integer noClickUpsellCount = 0;

    @ApiModelProperty(value = "推送的邮件数量")
    private Integer sentEmailCount = 0;

    @ApiModelProperty(value = "点击邮件中“Send Inquiries in Batch”按钮并且成功提交RFI的数量")
    private Integer submitCount = 0;

    @ApiModelProperty(value = "已回复的一对多RFI数量")
    private Integer repliedCount = 0;

    @ApiModelProperty(value = "所有邮件提交的询盘中，一共涉及到的供应商数量")
    private Integer supplierCount = 0;

    @ApiModelProperty(value = "通过询盘回复的供应商的数量")
    private Integer repliedSupplierCount = 0;

    @ApiModelProperty(value = "通过Chat回复的供应商的数量")
    private Integer repliedByChatSupplierCount = 0;

}
