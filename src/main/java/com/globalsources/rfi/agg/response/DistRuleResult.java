package com.globalsources.rfi.agg.response;

import com.globalsources.search.api.dto.supplier.AdditionalRuleDto;
import lombok.Data;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.buyerrfi.controller
 * @date:2021/5/18
 */
@Data
public class DistRuleResult {
    // 路由之后的目标账号
    private Long userId = 0L;
    private String targetEmailAddress = "";
    //兜底账号
    private String supplierRfiEmail = "";
    private AdditionalRuleDto additionalRuleDto;
    private String routeType;
}
