/**
 * <a>Title: BuyerInquiryTotalVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/22-13:18
 */
package com.globalsources.rfi.agg.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BuyerInquiryTotalVO implements Serializable {

    @ApiModelProperty(value = "买家未回复的询盘")
    private Integer all = 0;

    @ApiModelProperty(value = "买家标星的询盘")
    private Integer starred = 0;

    @ApiModelProperty(value = "买家删除的询盘")
    private Integer bin = 0;
}
