package com.globalsources.rfi.agg.response.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a>Title: ProductCategoryAttributeValueVO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description:  <a>
 *
 * <AUTHOR>
 * @date 2021/7/26 16:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@ApiModel(description = "产品属性值")
public class ProductCategoryAttributeValueDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("Attribute Id")
    private Long attrId;

    @ApiModelProperty("Attribute Value")
    private String attrValue;

    @ApiModelProperty("Attribute Value Position")
    private Integer attrValuePos;
}
