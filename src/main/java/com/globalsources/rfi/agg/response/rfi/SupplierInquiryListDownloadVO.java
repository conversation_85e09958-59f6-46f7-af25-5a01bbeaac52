/**
 * <a>Title: InquiryVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/22-13:35
 */
package com.globalsources.rfi.agg.response.rfi;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierInquiryListDownloadVO implements Serializable {

    @ApiModelProperty(value = "询盘编号")
    private String inquiryId;

    @ApiModelProperty(value = "商品编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long productId ;

    @ApiModelProperty(value = "型号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String modelNumber;

    @ApiModelProperty(value = "主题")
    private String subject;

    @ApiModelProperty(value = "买家编号")
    private Long buyerId;

    @ApiModelProperty(value = "买家电话")
    private String buyerPhoneNumber;

    @ApiModelProperty(value = "供应商编号")
    private Long supplierId;

    @ApiModelProperty(value = "供应商卖家编号")
    private Long supplierUserId;

    @ApiModelProperty(value = "名")
    private String firstName;

    @ApiModelProperty(value = "姓")
    private String lastName;

    @ApiModelProperty(value = "负责人 firstName")
    private String principalFirstName;

    @ApiModelProperty(value = "负责人 LastName")
    private String principalLastName;

    @ApiModelProperty(value = "买家国籍")
    private String country;

    @ApiModelProperty(value = "买家公司")
    private String companyName;

    @ApiModelProperty(value = "买家web url")
    private String websiteUrl;

    @ApiModelProperty(value = "询盘创建时间")
    private Timestamp createDate;

    @ApiModelProperty(value = "询盘最后更新时间")
    private Timestamp lastDate;

    @ApiModelProperty(value = "是否upsell询盘")
    private Boolean recommendFlag;

    @ApiModelProperty(value = "买家注册时间")
    private Date buyerRegisterDate;

    @ApiModelProperty(value = "买家邮箱")
    private String buyerEmailAddress;

    @ApiModelProperty(value = "买家已核实")
    private String buyerVerified;

    private String productName;

}
