package com.globalsources.rfi.agg.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.globalsources.rfi.agg.enums.InquiryTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2021/7/27 16:21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(description = "询盘列表-询盘对象VO")
public class SupplierAppInquiryListSimpleVO implements Serializable {
    private static final long serialVersionUID = 2673667817831684217L;

    @ApiModelProperty("询盘编号")
    private String inquiryId;
    @ApiModelProperty("主题")
    private String subject = "subject";
    @ApiModelProperty("询盘类型 PRODUCT:产品类型, SUPPLIER:供应商询盘")
    private InquiryTypeEnum inquiryType;
    @ApiModelProperty("商品标题")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String productTitle;
    @ApiModelProperty("买家头像(app)")
    private String buyerAvatar;
    @ApiModelProperty("是否有附件(app)")
    private Boolean hasAttachmentFlag;
    @ApiModelProperty("买家编号")
    private Long buyerId;
    @ApiModelProperty("名")
    private String firstName;
    @ApiModelProperty("姓")
    private String lastName;
    @ApiModelProperty("负责人 firstName")
    private String principalFirstName;
    @ApiModelProperty("负责人 LastName")
    private String principalLastName;
    @ApiModelProperty("负责人电子邮箱")
    private String principalEmail;
    @ApiModelProperty("买家国籍")
    private String country;
    @ApiModelProperty("是否已经回复")
    private Boolean replyFlag;
    @ApiModelProperty("是否已读")
    private Boolean readFlag;
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @ApiModelProperty("询盘创建时间")
    private Timestamp createDate;
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @ApiModelProperty("询盘最后更新时间")
    private Timestamp lastDate;
}
