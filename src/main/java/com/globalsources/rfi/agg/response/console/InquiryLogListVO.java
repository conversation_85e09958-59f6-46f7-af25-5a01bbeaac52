package com.globalsources.rfi.agg.response.console;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InquiryLogListVO implements Serializable {

    @ApiModelProperty("错误说明")
    private String logData;

    @ApiModelProperty("thread / inquiry id")
    private String businessId;

}
