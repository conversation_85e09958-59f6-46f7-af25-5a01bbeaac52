/**
 * <a>Title: IdsVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/23-16:10
 */
package com.globalsources.rfi.agg.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdminUpsellVO implements Serializable {

    private String matchId;

    private Long productId;
}
