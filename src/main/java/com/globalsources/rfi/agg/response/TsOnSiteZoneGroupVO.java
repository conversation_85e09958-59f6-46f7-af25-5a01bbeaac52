package com.globalsources.rfi.agg.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TsOnSiteZoneGroupVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分组 code")
    private String groupCode;

    @ApiModelProperty("Product list")
    private List<TsOnSiteZoneProductVO> productList;
}
