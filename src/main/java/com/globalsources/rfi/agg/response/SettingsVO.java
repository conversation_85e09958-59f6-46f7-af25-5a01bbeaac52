/**
 * <a>Title: SettingsVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/7/23-9:38
 */
package com.globalsources.rfi.agg.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SettingsVO implements Serializable {

    @ApiModelProperty("tmx配置参数")
    private BuyerSettingsVO tmx;
}
