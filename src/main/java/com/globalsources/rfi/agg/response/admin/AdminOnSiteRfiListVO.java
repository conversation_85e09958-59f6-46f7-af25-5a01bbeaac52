package com.globalsources.rfi.agg.response.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdminOnSiteRfiListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("rfi id")
    private String rfiId;

    @ApiModelProperty("RFI的发送时间")
    private String sendTime;

    @ApiModelProperty("Tradeshow Name")
    private String tradeshowName;

    @ApiModelProperty("专区，来源于Send RFIs的RFI Type")
    private String onSiteZone;

    @ApiModelProperty("rfi type: 1:1 RFI，1:Many RFI")
    private String rfiType;

    @ApiModelProperty(value = "buyer name")
    private String buyerName;

    @ApiModelProperty(value = "邮箱")
    private String emailAddr;

    @ApiModelProperty(value = "countryCode", hidden = true)
    private String countryCode;

    @ApiModelProperty(value = "买家国籍")
    private String country;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("L1Category")
    private String l1Category;

    @ApiModelProperty("L4Category")
    private String l4Category;

    @ApiModelProperty("supplier id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商公司")
    private String supplierName;

    @ApiModelProperty(value = "来源", hidden = true)
    private Integer rfiSource;

    @ApiModelProperty(value = "supplier id", hidden = true)
    private String supplierIdStr;

    @ApiModelProperty("买家所在国家/地区所属的大洲")
    private String buyerContinent;

    @ApiModelProperty("商家收到询盘后是否回复买家——Yes、No")
    private String supplierReplied;

    @ApiModelProperty("商家回复询盘后买家是否二次回复——Yes、No")
    private String buyerReplied;
}
