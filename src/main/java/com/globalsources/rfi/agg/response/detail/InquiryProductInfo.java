/**
 * <a>Title: SupplierInquiryCompanyDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/5/6-10:26
 */
package com.globalsources.rfi.agg.response.detail;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.globalsources.rfi.agg.response.product.ProductCategoryAttributeVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class InquiryProductInfo implements Serializable {

    @ApiModelProperty("product id")
    private Long productId;

    @ApiModelProperty("supplier id")
    private Long supplierId;

    @ApiModelProperty("category id")
    private Long categoryId;

    @ApiModelProperty("category name")
    private String categoryName;

    @ApiModelProperty("Model Number")
    private String modelNumber;

    @ApiModelProperty("Product Name")
    private String productName;

    @ApiModelProperty("商品单位")
    private String minOrderUnit;

    @ApiModelProperty("Product primary image")
    private String productPrimaryImage;

    @ApiModelProperty("Product SEO url")
    private String productSEOUrl;

    private String desktopProductDetailUrl;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "商品数量")
    private Integer productNum;

    @ApiModelProperty(value = "产品类别属性信息")
    private List<ProductCategoryAttributeVO> productCategoryAttrInfos;

}
