package com.globalsources.rfi.agg.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/9/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ProductChatInfoVO implements Serializable {

    private static final long serialVersionUID = -3060476340257062533L;

    @ApiModelProperty(value = "product id")
    private Long productId;

    @ApiModelProperty(value = "产品类别(L1)")
    private Long l1CategoryId;

    @ApiModelProperty(value = "产品类别(L2)")
    private Long l2CategoryId;

    @ApiModelProperty(value = "产品类别(L3)")
    private Long l3CategoryId;

    @ApiModelProperty(value = "产品类别(L4)")
    private Long l4CategoryId;

    @ApiModelProperty(value = "Product Name")
    private String productName;

    @ApiModelProperty(value = "Product primary image")
    private String productPrimaryImage;

    @ApiModelProperty(value = "Product SEO url")
    private String productSEOUrl;

    @ApiModelProperty(value = "给前端显示的价格")
    private String listVoShowPriceStr;

    @ApiModelProperty(value = "最小订单量")
    private Integer minOrderQuantity;

    @ApiModelProperty(value = "最小订单量单位")
    private String minOrderUnit;

    @ApiModelProperty(value = "单数单位，注意是单数的")
    private String minOrderSingleUnit;

}
