package com.globalsources.rfi.utils;

import com.alibaba.fastjson.JSON;
import com.google.api.client.googleapis.GoogleUtils;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.safebrowsing.Safebrowsing;
import com.google.api.services.safebrowsing.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.security.GeneralSecurityException;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2019/11/26
 */
@Slf4j
@Component
public class GoogleUtil {

    @Value("${google.api_key}")
    private String googleApiKeyStr;

    @Value("${google.platform_types}")
    private String platformTypeStr;

    @Value("${google.threat_entry}")
    private String threatEntryTypeStr;

    @Value("${google.client_id}")
    private String clientId;

    @Value("${google.client_version}")
    private String clientVersion;

    @Autowired
    private RedisTemplate redisTemplate;

    private String[] platformTypes;

    private String[] threatEntryTypes;

    private String[] googleApiKey;

    private static final Pattern pattern = Pattern
            .compile("(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:.;]+[-A-Za-z0-9+&@#/%=~_|]");

    @Value("${gsol.proxy.enable}")
    public Boolean enable;

    private static final long CACHE_EXPIRE = 3600L * 24L;

    private static final Integer THREAT = 1;

    private static final Integer NOT_THREAT = 0;

    private static final String THREAT_TYPES = "threatTypes";

    private static Random random = new Random();

    private NetHttpTransport httpTransport;

    private static final JacksonFactory GOOGLE_JSON_FACTORY = JacksonFactory.getDefaultInstance();

    @PostConstruct
    public void googleDefault() throws NumberFormatException, GeneralSecurityException, IOException {
        this.googleApiKey = googleApiKeyStr.split(",");
        this.platformTypes = platformTypeStr.split(",");
        this.threatEntryTypes = threatEntryTypeStr.split(",");
        if(Boolean.TRUE.equals(enable)) {
            httpTransport = new NetHttpTransport.Builder()
                    .setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("proxy.globalsources.com", 3333)))
                    .trustCertificates(GoogleUtils.getCertificateTrustStore()).build();
        }else {
             httpTransport = GoogleNetHttpTransport.newTrustedTransport();
        }

    }

    public void test(String email) throws GeneralSecurityException, IOException {
        String api = googleApiKey[random.nextInt(googleApiKey.length)];
        // all urls from email content

        Set<String> urls = getUrlsFromEmail(email).keySet();
        FindThreatMatchesRequest findThreatMatchesRequest = createFindThreatMatchesRequest(urls);

        Safebrowsing.Builder safeBrowsingBuilder = new Safebrowsing.Builder(httpTransport, GOOGLE_JSON_FACTORY, null)
                .setApplicationName(clientId);
        Safebrowsing safebrowsing = safeBrowsingBuilder.build();
        FindThreatMatchesResponse findThreatMatchesResponse = safebrowsing.threatMatches()
                .find(findThreatMatchesRequest).setKey(api).execute();

        List<ThreatMatch> threatMatches = findThreatMatchesResponse.getMatches();

        if (CollectionUtils.isNotEmpty(threatMatches)) {
            for (ThreatMatch threatMatch : threatMatches) {
                log.info("---------------------------");
                log.info(threatMatch.getThreat().getUrl());
                log.info("---------------------------");
            }
        }
    }

    public Boolean checkUrl(String email) {
        // random choose a key
        String api = googleApiKey[random.nextInt(googleApiKey.length)];
        // all urls from email content
        Map<String, Integer> allUrl = getUrlsFromEmail(email);
        // url need check(not in redis)
        Map<String, Integer> urlCheck = getUrlsNeedCheck(allUrl);
        if (MapUtils.isNotEmpty(urlCheck)) {
            return true;
        }

        try {
            FindThreatMatchesRequest findThreatMatchesRequest = createFindThreatMatchesRequest(allUrl.keySet());

            Safebrowsing.Builder safeBrowsingBuilder = new Safebrowsing.Builder(httpTransport, GOOGLE_JSON_FACTORY,
                    null).setApplicationName(clientId);
            Safebrowsing safebrowsing = safeBrowsingBuilder.build();

            FindThreatMatchesResponse findThreatMatchesResponse = safebrowsing.threatMatches()
                    .find(findThreatMatchesRequest).setKey(api).execute();

            List<ThreatMatch> threatMatches = findThreatMatchesResponse.getMatches();
            log.info("invoke google api post url = {}", allUrl.keySet());
            log.info("invoke google api result = {}", threatMatches);

            if (CollectionUtils.isNotEmpty(threatMatches)) {
                for (ThreatMatch threatMatch : threatMatches) {
                    urlCheck.put(threatMatch.getThreat().getUrl(), THREAT);
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            e.printStackTrace();
        }
        List<String> result = new ArrayList<>(urlCheck.size());
        for (Entry<String, Integer> entry : urlCheck.entrySet()) {
            if (THREAT.equals(entry.getValue())) {
                result.add(entry.getKey());
            }
        }

        if (CollectionUtils.isNotEmpty(result)) {
            saveUrl(urlCheck);
            return true;
        }
        return false;
    }

    public List<String> checkAndGetUrl(String email) {
        // random choose a key
        String api = googleApiKey[random.nextInt(googleApiKey.length)];
        // all urls from email content
        Set<String> allUrl = getUrlListFromEmail(email);
        // url need check(not in redis)
        List<String> threatUrl = new ArrayList<>();

        try {
            FindThreatMatchesRequest findThreatMatchesRequest = createFindThreatMatchesRequest(allUrl);

            Safebrowsing.Builder safeBrowsingBuilder = new Safebrowsing.Builder(httpTransport, GOOGLE_JSON_FACTORY,
                    null).setApplicationName(clientId);
            Safebrowsing safebrowsing = safeBrowsingBuilder.build();

            FindThreatMatchesResponse findThreatMatchesResponse = safebrowsing.threatMatches()
                    .find(findThreatMatchesRequest).setKey(api).execute();

            List<ThreatMatch> threatMatches = findThreatMatchesResponse.getMatches();
            log.info("invoke google api post url = {}", allUrl);
            log.info("invoke google api result = {}", threatMatches);

            if (CollectionUtils.isNotEmpty(threatMatches)) {
                for (ThreatMatch threatMatch : threatMatches) {
                    threatUrl.add(threatMatch.getThreat().getUrl());
                }
            }
        } catch (Exception e) {
            log.error("google util checkAndGetUrl error:{}", JSON.toJSONString(e.getMessage()));
        }

        if (CollectionUtils.isNotEmpty(threatUrl)) {
            saveUrl(threatUrl);
        }
        return threatUrl;
    }

    private Map<String, Integer> getUrlsNeedCheck(Map<String, Integer> urlAll) {
        Map<String, Integer> needCheck = new HashMap<>();
        Integer value = null;
        for (Entry<String, Integer> entry : urlAll.entrySet()) {
            value = (Integer)redisTemplate.opsForValue().get(entry.getKey());
            if (Objects.nonNull(value)) {
                needCheck.put(entry.getKey(), NOT_THREAT);
            }
        }
        return needCheck;
    }

    private void saveUrl(Map<String, Integer> urlMap) {
        for (Entry<String, Integer> entry : urlMap.entrySet()) {
            redisTemplate.opsForValue().increment(entry.getKey(), CACHE_EXPIRE);
        }
    }

    private void saveUrl(List<String> urlList) {
        for (String url : urlList) {
            redisTemplate.opsForValue().increment(url, CACHE_EXPIRE);
        }
    }

    private FindThreatMatchesRequest createFindThreatMatchesRequest(Set<String> urls) {
        FindThreatMatchesRequest findThreatMatchesRequest = new FindThreatMatchesRequest();

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setClientId(clientId);
        clientInfo.setClientVersion(clientVersion);
        findThreatMatchesRequest.setClient(clientInfo);

        ThreatInfo threatInfo = new ThreatInfo();
        threatInfo.setThreatTypes(getThreatTypes());
        threatInfo.setPlatformTypes(Arrays.asList(platformTypes));
        threatInfo.setThreatEntryTypes(Arrays.asList(threatEntryTypes));

        List<ThreatEntry> threatEntries = new ArrayList<>();

        for (String url : urls) {
            ThreatEntry threatEntry = new ThreatEntry();
            threatEntry.set("url", url);
            threatEntries.add(threatEntry);
        }
        threatInfo.setThreatEntries(threatEntries);
        findThreatMatchesRequest.setThreatInfo(threatInfo);

        return findThreatMatchesRequest;
    }

    /**
     * @return
     */
    @SuppressWarnings("unchecked")
    private List<String> getThreatTypes() {
        List<String> threatTypes = (List<String>) redisTemplate.opsForValue().get(THREAT_TYPES);
        if (CollectionUtils.isNotEmpty(threatTypes)) {
            return threatTypes;
        }
        Set<String> threatTypeSet = new HashSet<>();
        try {
            if(Boolean.TRUE.equals(enable)) {
                httpTransport = new NetHttpTransport.Builder()
                        .setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("proxy.globalsources.com", 3333)))
                        .trustCertificates(GoogleUtils.getCertificateTrustStore()).build();
            }else {
                httpTransport = GoogleNetHttpTransport.newTrustedTransport();
            }

            String api = googleApiKey[random.nextInt(googleApiKey.length)];

            Safebrowsing.Builder safeBrowsingBuilder = new Safebrowsing.Builder(httpTransport, GOOGLE_JSON_FACTORY,
                    null).setApplicationName(clientId);
            Safebrowsing safebrowsing = safeBrowsingBuilder.build();
            ListThreatListsResponse types = safebrowsing.threatLists().list().setKey(api).execute();
            List<ThreatListDescriptor> list = types.getThreatLists();
            for (ThreatListDescriptor desc : list) {
                threatTypeSet.add(desc.getThreatType());
            }
            redisTemplate.opsForValue().set(THREAT_TYPES, new ArrayList<>(threatTypeSet));
        } catch (IOException | GeneralSecurityException e) {
            log.error(e.getMessage());
            e.printStackTrace();
        }
        return new ArrayList<>(threatTypeSet);
    }

    private Map<String, Integer> getUrlsFromEmail(String email) {
        Map<String, Integer> result = new HashMap<>();
        Matcher matcher = pattern.matcher(email);
        while (matcher.find()) {
            result.put(matcher.group(), null);
        }
        return result;
    }

    private Set<String> getUrlListFromEmail(String email) {
        Set<String> result = new HashSet<>();
        Matcher matcher = pattern.matcher(email);
        while (matcher.find()) {
            result.add(matcher.group());
        }
        return result;
    }
}
