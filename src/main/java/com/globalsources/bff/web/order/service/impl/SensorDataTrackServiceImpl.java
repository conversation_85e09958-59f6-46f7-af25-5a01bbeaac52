package com.globalsources.bff.web.order.service.impl;

import com.globalsources.bff.web.order.service.SensorDataTrackService;
import com.globalsources.framework.result.Result;
import com.globalsources.order.api.dto.OrderSensorTackDTO;
import com.globalsources.order.api.feign.OrderSensorTackFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SensorDataTrackServiceImpl implements SensorDataTrackService {

    @Autowired
    private OrderSensorTackFeign orderSensorTackFeign;

    @Override
    public Boolean receivePush(OrderSensorTackDTO orderSensorTackDTO) {
        Result<Boolean> booleanResult = orderSensorTackFeign.receivePush(orderSensorTackDTO);
        return booleanResult.getData();
    }
}
