package com.globalsources.bff.web.order.service.impl;

import com.globalsources.bff.web.order.service.ShoppingCartService;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.translator.Translator;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.order.api.dto.ShoppingCartOrgDTO;
import com.globalsources.order.api.dto.ShoppingCartRespDTO;
import com.globalsources.order.api.feign.ShoppingCartAggFeign;
import com.globalsources.order.api.vo.ShoppingCartItemVO;
import com.globalsources.order.api.vo.ShoppingCartOrgVO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/7/8
 */
@Service
@Slf4j
public class ShoppingCartServiceImpl implements ShoppingCartService {

    @Autowired
    private ShoppingCartAggFeign shoppingCartAggFeign;

    @Resource
    private Translator translator;

    @Override
    public List<ShoppingCartOrgVO> getShoppingCart(String ul2Cookie, String language) {
        List<ShoppingCartOrgDTO> shoppingCartOrgDTOList = ResultUtil.getData(shoppingCartAggFeign.getShoppingCart(ul2Cookie));
        List<ShoppingCartOrgVO> shoppingCartOrgVOList = null;
        if (!CollectionUtils.isEmpty(shoppingCartOrgDTOList)) {
            shoppingCartOrgVOList = OrikaMapperUtil.coverList(shoppingCartOrgDTOList, ShoppingCartOrgVO.class);

            if (!LanguageDicEnum.EN_US.getValue().equals(language)) {
                List<String> trans = new ArrayList<>();
                shoppingCartOrgVOList.forEach(shoppingCartOrgVO -> {
                    List<ShoppingCartItemVO> productList = shoppingCartOrgVO.getProductList();
                    productList.forEach(product -> {
                        String productName = product.getProductName();
                        String orderUom = product.getOrderUom();
                        trans.add(productName);
                        trans.add(orderUom);
                    });
                });
                Map<String, String> map = translator.litePerform(trans, LanguageDicEnum.getLanguageDicEnumByValue(language));

                for (ShoppingCartOrgVO shoppingCartOrgVO : shoppingCartOrgVOList) {
                    List<ShoppingCartItemVO> productList = shoppingCartOrgVO.getProductList();
                    for (ShoppingCartItemVO product : productList) {
                        String productName = product.getProductName();
                        String orderUom = product.getOrderUom();
                        product.setProductName(map.get(productName));
                        product.setOrderUom(map.get(orderUom));
                    }
                }
            }


            // reset priceRangeList
            Map<Long, List<Map<String, Object>>> productPriceRangeMap = Maps.newHashMap();
            shoppingCartOrgDTOList.forEach(shoppingCartOrgDTO -> shoppingCartOrgDTO.getProductList().forEach(shoppingCartItemDTO -> productPriceRangeMap.put(shoppingCartItemDTO.getProductId(), shoppingCartItemDTO.getPriceRangeList())));
            shoppingCartOrgVOList.forEach(shoppingCartOrgVO -> shoppingCartOrgVO.getProductList().forEach(shoppingCartItemVO -> shoppingCartItemVO.setPriceRangeList(productPriceRangeMap.get(shoppingCartItemVO.getProductId()))));
        }

        return shoppingCartOrgVOList;
    }

    @Override
    public ShoppingCartRespDTO addShoppingCartItem(String ul2Cookie, Long productId, Integer quantity) {
        return ResultUtil.getData(shoppingCartAggFeign.addShoppingCartItem(ul2Cookie, productId, quantity));
    }

    @Override
    public ShoppingCartRespDTO updateShoppingCartItem(String ul2Cookie, Long productId, Integer quantity) {
        return ResultUtil.getData(shoppingCartAggFeign.updateShoppingCartItem(ul2Cookie, productId, quantity));
    }

    @Override
    public Boolean deleteShoppingCartItem(String ul2Cookie, Long productId) {
        return ResultUtil.getData(shoppingCartAggFeign.deleteShoppingCartItem(ul2Cookie, productId));
    }

    @Override
    public Integer getShoppingCartItemCount(String ul2Cookie) {
        return ResultUtil.getData(shoppingCartAggFeign.getShoppingCartItemCount(ul2Cookie));
    }

}
