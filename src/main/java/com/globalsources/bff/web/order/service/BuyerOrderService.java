package com.globalsources.bff.web.order.service;

import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.order.api.dto.BuyerListQueryDTO;
import com.globalsources.order.api.dto.CancelDTO;
import com.globalsources.order.api.dto.PlaceOrderDTO;
import com.globalsources.order.api.dto.cart.BuyerRefundDTO;
import com.globalsources.order.api.vo.*;

import java.util.List;

public interface BuyerOrderService {
    Result<PlaceOrderVO> placeOrder(PlaceOrderDTO placeOrderDTO);

    Result<PreOrderVO> preOrder(List<Long> productIdList, List<Integer> productQtyList, Long userId, String language);

    Result<Boolean> complete(String orderId, Long userId);

    Result<Boolean> cancel(CancelDTO cancelDTO, Long userId);

    Result<PageResult<BuyerListVO>> list(BuyerListQueryDTO queryDTO, Long userId, String language);

    Result<BuyerDetailVO> detail(String orderId, String language);

    Result<Boolean> refund(BuyerRefundDTO buyerRefundDTO);

    Result<OrderLiteVO> getOrderLite(String orderId, String language, Long userId);

    Result<DOStatusCountVO> getOrderCountByBuyer(Long userId);

    Result delayedShipmentConfirmed(String orderId);

    Result delayedShipmentRejected(String orderId);
}
