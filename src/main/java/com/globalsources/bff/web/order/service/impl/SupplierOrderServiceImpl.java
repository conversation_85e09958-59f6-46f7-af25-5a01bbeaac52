package com.globalsources.bff.web.order.service.impl;

import com.globalsources.bff.web.order.service.SupplierOrderService;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.order.api.dto.DeliveryDTO;
import com.globalsources.order.api.dto.QuoteDTO;
import com.globalsources.order.api.dto.SupplierListQueryDTO;
import com.globalsources.order.api.feign.SupplierOrderAggFeign;
import com.globalsources.order.api.vo.DOStatusCountVO;
import com.globalsources.order.api.vo.PreQuoteVO;
import com.globalsources.order.api.vo.SupplierDetailVO;
import com.globalsources.order.api.vo.SupplierListVO;
import com.globalsources.order.core.api.enmus.DirectOrderStatus;
import com.globalsources.order.core.api.feign.OrderCoreFeign;
import com.globalsources.order.core.api.order.po.DirectOrderStatusPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> liu
 * @date 2022/4/18
 */
@Slf4j
@Service
public class SupplierOrderServiceImpl implements SupplierOrderService {

    @Autowired
    private SupplierOrderAggFeign supplierOrderAggFeign;

    @Autowired
    private OrderCoreFeign orderCoreFeign;

    @Override
    public Result<PreQuoteVO> preQuote(String orderId) {
        return supplierOrderAggFeign.preQuote(orderId);
    }

    @Override
    public Result<Boolean> quote(QuoteDTO quoteDTO) {
        Result<Boolean> result = supplierOrderAggFeign.quote(quoteDTO);
        if (!Result.success().getCode().equals(result.getCode())) {
            throw new BusinessException(result.getCode(), result.getMsg(), result.getData());
        }
        return result;
    }

    @Override
    public Result<Boolean> delivery(DeliveryDTO deliveryDTO) {
        Result<Boolean> result = supplierOrderAggFeign.delivery(deliveryDTO);
        if (!Result.success().getCode().equals(result.getCode())) {
            throw new BusinessException(result.getCode(), result.getMsg(), result.getData());
        }
        return result;
    }

    @Override
    public Result<PageResult<SupplierListVO>> list(SupplierListQueryDTO queryDTO, Long userId, Long supplierId, Boolean mainAccount, String language) {
        Result<PageResult<SupplierListVO>> result = supplierOrderAggFeign.list(queryDTO, userId, supplierId, mainAccount, language);
        if (!Result.success().getCode().equals(result.getCode())) {
            throw new BusinessException(result.getCode(), result.getMsg());
        }

        PageResult<SupplierListVO> pageResult = result.getData();
        List<SupplierListVO> list = pageResult.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(supplierListVO -> {
                BigDecimal thirdEstimateShippingCost = supplierListVO.getThirdEstimateShippingCost();
                boolean addEstimateShippingCost = false;
                if (supplierListVO.getQuotedFlag() == null || !supplierListVO.getQuotedFlag()) {
                    addEstimateShippingCost = true;
                }
                if (thirdEstimateShippingCost != null && addEstimateShippingCost) {
                    BigDecimal totalOrderPrice = supplierListVO.getTotalOrderPrice();
                    supplierListVO.setTotalOrderPrice(totalOrderPrice.add(thirdEstimateShippingCost));
                }
            });
        }
        return result;
    }

    @Override
    public Result<DOStatusCountVO> getOrderCountBySupplier(Long userId, Long supplierId, Boolean mainAccount) {
        return supplierOrderAggFeign.getOrderCountBySupplier(userId, supplierId, mainAccount);
    }

    @Override
    public Result<SupplierDetailVO> detail(String orderId, String language) {
        SupplierDetailVO supplierDetailVO = supplierOrderAggFeign.detail(orderId, language).getData();
        if (Objects.nonNull(supplierDetailVO)) {
            BigDecimal thirdEstimateShippingCost = supplierDetailVO.getThirdEstimateShippingCost();
            boolean addEstimateShippingCost = false;
            if (supplierDetailVO.getQuotedFlag() == null || !supplierDetailVO.getQuotedFlag()) {
                addEstimateShippingCost = true;
            }
            if (thirdEstimateShippingCost != null && addEstimateShippingCost) {
                BigDecimal totalOrderPrice = supplierDetailVO.getTotalOrderPrice();
                supplierDetailVO.setTotalOrderPrice(totalOrderPrice.add(thirdEstimateShippingCost));
            }
        }
        return Result.success(supplierDetailVO);
    }

    @Override
    public Result refundConfirmed(String orderId) {
        return supplierOrderAggFeign.refundConfirmed(orderId);
    }

    @Override
    public Result refundRejected(String orderId, String remark) {
        return supplierOrderAggFeign.refundRejected(orderId, remark);
    }

    @Override
    public Result delayedShipment(String orderId, Integer delayDays) {
        DirectOrderStatusPO directOrderStatusPO = new DirectOrderStatusPO();
        directOrderStatusPO.setOrderId(orderId);
        directOrderStatusPO.setOrderStatus(DirectOrderStatus.DELAY_SHIPMENT_ISSUE.name());
        directOrderStatusPO.setRemark(String.valueOf(delayDays));
        directOrderStatusPO.setStatusSource("APP");
        directOrderStatusPO.setCreateDate(new Date());
        directOrderStatusPO.setLUpdDate(new Date());
        Result result = orderCoreFeign.insertDirectOrderStatus(directOrderStatusPO);
        if (!Result.success().getCode().equals(result.getCode())) {
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        return Result.success();
    }
}
