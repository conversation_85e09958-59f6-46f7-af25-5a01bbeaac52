package com.globalsources.bff.web.order.controller;

import com.alibaba.fastjson.JSON;
import com.globalsources.bff.web.order.service.BuyerOrderService;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.LanguageUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.order.api.dto.BuyerListQueryDTO;
import com.globalsources.order.api.dto.CancelDTO;
import com.globalsources.order.api.dto.PlaceOrderDTO;
import com.globalsources.order.api.dto.cart.BuyerRefundDTO;
import com.globalsources.order.api.vo.BuyerDetailVO;
import com.globalsources.order.api.vo.BuyerListVO;
import com.globalsources.order.api.vo.DOStatusCountVO;
import com.globalsources.order.api.vo.OrderLiteVO;
import com.globalsources.order.api.vo.PlaceOrderVO;
import com.globalsources.order.api.vo.PreOrderVO;
import com.globalsources.order.core.api.feign.OrderCoreFeign;
import com.globalsources.order.core.api.order.po.DirectOrderPO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> liu
 * @date 2022/3/10
 */
@Api(tags = {"买家订单"})
@Slf4j
@RestController
@RequestMapping(value = "/buyer-order")
public class BuyerOrderController {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private BuyerOrderService buyerOrderService;

    @Autowired
    private OrderCoreFeign orderCoreFeign;

    @Login(validLogin = false)
    @GetMapping(value = "pre-order")
    @ApiOperation(value = "预下单")
    public Result<PreOrderVO> preOrder(@RequestParam List<Long> productIdList, @RequestParam List<Integer> productQtyList, @ApiIgnore UserVO userVO) {
        log.info("pre order --------------productIdList :{} ---------------------productQtyList: {}", productIdList, productQtyList);
        Long userId = null;
        if (Objects.nonNull(userVO)) {
            userId = userVO.getUserId();
        }
        String language = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.EN_US);
        return buyerOrderService.preOrder(productIdList, productQtyList, userId, language);
    }

    @Login
    @PostMapping(value = "place-order")
    @ApiOperation(value = "下单")
    public Result<PlaceOrderVO> placeOrder(@RequestBody PlaceOrderDTO placeOrderDTO, @ApiIgnore UserVO userVO) {
        placeOrderDTO.setOrderSource("WEB");
        placeOrderDTO.setUserId(userVO.getUserId());
        log.info("place order dto is ---------------------:{}", JSON.toJSONString(placeOrderDTO));
        return buyerOrderService.placeOrder(placeOrderDTO);
    }

    @Login
    @GetMapping(value = "complete")
    @ApiOperation(value = "手动完成订单")
    public Result<Boolean> complete(@RequestParam String orderId, @ApiIgnore UserVO userVO) {
        log.info("complete order id is ---------------------:{}", orderId);
        if (checkBuyerOrderSecurity(orderId, userVO)) {
            return Result.failed(ResultCode.OrderResultCode.USER_NOT_MATCH);
        }
        return buyerOrderService.complete(orderId, userVO.getUserId());
    }


    @Login
    @PostMapping(value = "cancel")
    @ApiOperation(value = "手动取消订单")
    public Result<Boolean> cancel(@RequestBody CancelDTO cancelDTO, @ApiIgnore UserVO userVO) {
        log.info("cancel order dto is ------------------:{}", JSON.toJSONString(cancelDTO));
        if (checkBuyerOrderSecurity(cancelDTO.getOrderId(), userVO)) {
            return Result.failed(ResultCode.OrderResultCode.USER_NOT_MATCH);
        }
        return buyerOrderService.cancel(cancelDTO, userVO.getUserId());
    }

    @Login
    @PostMapping(value = "list")
    @ApiOperation(value = "订单列表")
    public Result<PageResult<BuyerListVO>> list(@RequestBody BuyerListQueryDTO queryDTO, @ApiIgnore UserVO userVO) {
        log.info("query order list dto is :{}", JSON.toJSONString(queryDTO));
        String language = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.EN_US);
        return buyerOrderService.list(queryDTO, userVO.getUserId(), language);
    }

    @Login
    @GetMapping(value = "detail")
    @ApiOperation(value = "订单详情")
    public Result<BuyerDetailVO> detail(@RequestParam String orderId, @ApiIgnore UserVO userVO) {
        log.info("query order detail orderId is ------:{}", orderId);
        String language = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.EN_US);
        if (checkBuyerOrderSecurity(orderId, userVO)) {
            return Result.failed(ResultCode.OrderResultCode.USER_NOT_MATCH);
        }
        return buyerOrderService.detail(orderId, language);
    }

    @Login
    @PostMapping(value = "refund")
    @ApiOperation(value = "申请退款")
    public Result<Boolean> refund(@RequestBody BuyerRefundDTO buyerRefundDTO, @ApiIgnore UserVO userVO) {
        log.info("request refund param dto is-------:{}", buyerRefundDTO);
        if (checkBuyerOrderSecurity(buyerRefundDTO.getOrderId(), userVO)) {
            return Result.failed(ResultCode.OrderResultCode.USER_NOT_MATCH);
        }
        return buyerOrderService.refund(buyerRefundDTO);
    }

    @Login
    @GetMapping(value = "create-success")
    @ApiOperation(value = "创建订单成功页面接口")
    public Result<OrderLiteVO> getOrderLite(@RequestParam String orderId, @ApiIgnore UserVO userVO) {
        log.info("get lite order order is ----------:{}", orderId);
        String language = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.EN_US);
        return buyerOrderService.getOrderLite(orderId, language, userVO.getUserId());
    }

    @Login
    @ApiOperation(value = "买家端Orders订单数量查询接口")
    @GetMapping("/order-count-buyer")
    public Result<DOStatusCountVO> getOrderCountByBuyer(@ApiIgnore UserVO userVO) {
        Long userId = userVO.getUserId();
        return buyerOrderService.getOrderCountByBuyer(userId);
    }

    @Login
    @ApiOperation(value = "买家同意延迟发货")
    @GetMapping("/delayed-shipment-confirmed")
    public Result delayedShipmentConfirmed(@RequestParam String orderId) {
        log.info("delay shipment confirmed order id is---:{}", orderId);
        return buyerOrderService.delayedShipmentConfirmed(orderId);
    }

    @Login
    @ApiOperation(value = "买家拒绝延迟发货")
    @GetMapping("/delayed-shipment-rejected")
    public Result delayedShipmentRejected(@RequestParam String orderId) {
        log.info("delay shipment rejected order id is---:{}", orderId);
        return buyerOrderService.delayedShipmentRejected(orderId);
    }

    private boolean checkBuyerOrderSecurity(String orderId, UserVO userVO) {
        DirectOrderPO directOrderPO = orderCoreFeign.queryDirectOrder(orderId).getData();
        if (Objects.isNull(directOrderPO)) {
            log.error("check Buyer Order Security query direct OrderPO is null");
            return true;
        }
        if (Objects.isNull(userVO) || !directOrderPO.getCustomerId().equals(userVO.getUserId())) {
            log.error("check Buyer Order Security user id error");
            return true;
        }
        return false;
    }
}
