package com.globalsources.bff.web.order.controller;

import com.globalsources.bff.web.order.service.ShoppingCartService;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.LanguageUtil;
import com.globalsources.framework.utils.TokenUtil;
import com.globalsources.order.api.dto.ShoppingCartRespDTO;
import com.globalsources.order.api.vo.ShoppingCartOrgVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/7/28
 */
@Api(tags = {"购物车相关"})
@Slf4j
@RestController
@RequestMapping("/shopping-cart")
public class ShoppingCartController {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private ShoppingCartService shoppingCartService;

    @Login()
    @ApiOperation(value = "获取购物车信息", notes = "获取购物车信息")
    @GetMapping("v1/get")
    public Result<List<ShoppingCartOrgVO>> getShoppingCart() {

        String language = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.EN_US);

        String ul2Cookie = getUl2CookieValue();
        log.info("getShoppingCart: ul2Cookie={}", ul2Cookie);

        return Result.success(shoppingCartService.getShoppingCart(ul2Cookie, language));
    }

    @Login()
    @ApiOperation(value = "添加购物车产品", notes = "添加购物车产品")
    @PostMapping("v1/add-item")
    public Result<ShoppingCartRespDTO> addShoppingCartItem(@RequestParam Long productId, @RequestParam Integer quantity) {
        return Result.success(shoppingCartService.addShoppingCartItem(getUl2CookieValue(), productId, quantity));
    }

    @Login()
    @ApiOperation(value = "更新购物车产品数量", notes = "更新购物车产品数量")
    @PostMapping("v1/update-item")
    public Result<ShoppingCartRespDTO> updateShoppingCartItem(@RequestParam Long productId, @RequestParam Integer quantity) {
        return Result.success(shoppingCartService.updateShoppingCartItem(getUl2CookieValue(), productId, quantity));
    }

    @Login()
    @ApiOperation(value = "删除购物车产品", notes = "删除购物车产品")
    @PostMapping("v1/delete-item")
    public Result<Boolean> deleteShoppingCartItem(@RequestParam Long productId) {
        return Result.success(shoppingCartService.deleteShoppingCartItem(getUl2CookieValue(), productId));
    }

    @Login()
    @ApiOperation(value = "获取购物车产品数量", notes = "获取购物车产品数量")
    @GetMapping("v1/get-item-count")
    public Result<Integer> getShoppingCartItemCount() {
        return Result.success(shoppingCartService.getShoppingCartItemCount(getUl2CookieValue()));
    }

    protected String getUl2CookieValue() {
        String authorization = request.getHeader(TokenUtil.AUTHORIZE_TOKEN);
        String[] data = TokenUtil.getData(authorization);
        return data[1];
    }

}
