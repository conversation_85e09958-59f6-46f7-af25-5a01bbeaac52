package com.globalsources.bff.web.order.controller;

import com.globalsources.agg.admin.api.feign.RfqBlackListFeign;
import com.globalsources.bff.web.order.service.OrderService;
import com.globalsources.chat.feign.ChatFeign;
import com.globalsources.chat.util.ChatUtil;
import com.globalsources.framework.annotation.AutoIdempotent;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.LanguageUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.order.api.dto.CancelOrderDTO;
import com.globalsources.order.api.dto.DOSubmitDTO;
import com.globalsources.order.api.dto.PostDeliveryDTO;
import com.globalsources.order.api.dto.PostQuoteDTO;
import com.globalsources.order.api.model.DOListDetail;
import com.globalsources.order.api.model.DOOrderDetail;
import com.globalsources.order.api.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR> liu
 * @date 2021/7/7
 */
@RestController
@RequestMapping("/order")
@Slf4j
@Api(tags = {"订单相关"})
public class OrderController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private ChatFeign chatFeign;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private RfqBlackListFeign rfqBlackListFeign;

    @Login(validLogin = false)
    @ApiOperation(value = "初始化orderFrom页面")
    @GetMapping("order-from")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "quantityList", value = "产品ID列表", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "quantityList", value = "数量列表", required = true, dataType = "Integer")
    })
    public Result<DOPlaceOrderVO> orderFromInit(@RequestParam List<Long> productIdList, @RequestParam List<Integer> quantityList, @ApiIgnore UserVO user) {
        DOPlaceOrderVO doPlaceOrderVO = orderService.orderFromInit(productIdList, quantityList);
        Long buyerId = 0L;
        if (Objects.nonNull(user) && Objects.nonNull(user.getUserId())) {
            buyerId = user.getUserId();
        }
        doPlaceOrderVO.setChatOnlineStatus(ChatUtil.getChatOnlineStatus(buyerId, doPlaceOrderVO.getSupplierId(), productIdList, chatFeign));
        return Result.success(doPlaceOrderVO);
    }

    @Login
    @ApiOperation(value = "提交订单")
    @PostMapping(value = "submit-order")
    @AutoIdempotent
    public Result<DOSubmitVO> submitOrder(@RequestBody DOSubmitDTO doSubmitDTO, @ApiIgnore UserVO userVO) throws UnsupportedEncodingException {
        Boolean isBlackListUser = rfqBlackListFeign.isBlackListUser(userVO.getUserId(), userVO.getEmail()).getData();
        if (isBlackListUser) {
            throw new BusinessException(ResultCode.OrderResultCode.BLACK_USER);
        }
        DOSubmitVO doSubmitVO = orderService.submitOrder(doSubmitDTO);
        return Result.success(doSubmitVO);
    }

    @Login
    @GetMapping("/buyer-order-list")
    @ApiOperation(value = "买家端订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "第几页", required = true, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "多少条", required = true, dataType = "Integer")
    })
    public Result<PageResult<DOListDetail>> buyerOrderList(Integer pageNum, Integer pageSize) {
        String language = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.EN_US);
        PageResult<DOListDetail> pageResult = orderService.buyerOrderList(pageNum, pageSize, language);
        return Result.success(pageResult);
    }

    @Login
    @GetMapping("/supplier-order-list")
    @ApiOperation(value = "卖家端订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "第几页", required = true, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "多少条", required = true, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "language", value = "语言", dataType = "String")
    })
    public Result<PageResult<DOListDetail>> supplierOrderList(Integer pageNum, Integer pageSize, String orderStatus, @ApiIgnore UserVO userVO) {
        String language = request.getHeader("lang");
        if (StringUtils.isEmpty(language)) {
            language = LanguageDicEnum.ZH_CN.getValue();
        }
        PageResult<DOListDetail> pageResult = orderService.supplierOrderList(pageNum, pageSize, orderStatus, language);
        return Result.success(pageResult);
    }

    @Login
    @GetMapping("/buyer-order-detail")
    @ApiOperation(value = "买家端订单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "orderId", value = "订单Id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "language", value = "语言", dataType = "String")
    })
    public Result<OrderDetailVO> buyerOrderDetail(@RequestParam("orderId") String orderId, @ApiIgnore UserVO user) {
        String language = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.EN_US);
        return orderService.buyerOrderDetail(orderId, language, user.getUserId());
    }

    @Login
    @GetMapping("/supplier-order-detail")
    @ApiOperation(value = "卖家端订单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "orderId", value = "订单Id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "language", value = "语言", dataType = "String")
    })
    public Result<OrderDetailVO> supplierOrderDetail(@RequestParam("orderId") String orderId, @ApiIgnore UserVO user) {
        String language = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.ZH_CN);
        return orderService.supplierOrderDetail(orderId, language, user);
    }

    @GetMapping("/order-created")
    @ApiOperation(value = "创建订单成功页")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "orderId", value = "订单id", required = true, dataType = "Long"),
    })
    public Result<DOOrderDetail> orderCreated(Long orderId) {
        String language = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.EN_US);
        DOOrderDetail orderCreated = orderService.orderCreated(orderId, language);
        if (Objects.nonNull(orderCreated)) {
            Long productId = 0L;
            if (!CollectionUtils.isEmpty(orderCreated.getProducts())) {
                productId = orderCreated.getProducts().get(0).getProductId();
            }
            orderCreated.setChatOnlineStatus(ChatUtil.getChatOnlineStatus(orderCreated.getCustomerId(), orderCreated.getSupplierId(), productId, chatFeign));
        }
        return Result.success(orderCreated);
    }

    @GetMapping("/supplier-refund")
    @ApiOperation(value = "接收退款或拒绝退款")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "orderId", value = "订单id", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "orderStatus", value = "修改的状态", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "remark", value = "备注", dataType = "String")
    })
    public Result<Boolean> supplierRefund(Long orderId, String orderStatus, String remark) {
        Boolean aBoolean = orderService.supplierRefund(orderId, orderStatus, remark);
        return Result.success(aBoolean);
    }

    @GetMapping("/quotation")
    @ApiOperation(value = "供应商报价回显接口")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "orderId", value = "订单id", required = true, dataType = "Long"),
    })
    public Result<OrderQuotationVO> quotation(Long orderId) {
        OrderQuotationVO quotationVO = orderService.quotation(orderId);
        return Result.success(quotationVO);
    }

    @PostMapping("/post-quotation")
    @ApiOperation(value = "供应商提交报价接口")
    public Result<Boolean> postQuotation(@RequestBody PostQuoteDTO postQuoteDTO) {
        boolean data = orderService.postQuotation(postQuoteDTO);
        if (!data) {
            return new Result<>(ResultCode.CommonResultCode.PSC_ERROR.getCode(), ResultCode.CommonResultCode.PSC_ERROR.getMsg(), false);
        }
        return Result.success(true);
    }

    @PostMapping("/post-delivery")
    @ApiOperation(value = "提交送货订单接口（新建和修改同一个）")
    public Result<Boolean> postDelivery(@RequestBody PostDeliveryDTO postDeliveryDTO) {
        boolean aBoolean = orderService.postDelivery(postDeliveryDTO);
        if (!aBoolean) {
            return new Result<>(ResultCode.OrderResultCode.SUPPLIER_PAYPAL_ACCOUNT_RESTRICTED.getCode(), ResultCode.OrderResultCode.SUPPLIER_PAYPAL_ACCOUNT_RESTRICTED.getMsg(), false);
        }
        return Result.success(true);
    }

    @GetMapping("/refund-issue")
    @ApiOperation(value = "买家申请退款接口")
    public Result<Boolean> refundIssue(String orderId, String remark) {
        return Result.success(orderService.refundIssue(orderId, remark));
    }


    @ApiOperation(value = "买家端Orders订单数量查询接口")
    @GetMapping("/order-count-buyer")
    @Login
    public Result<DOStatusCountVO> getOrderCountByBuyer() {
        DOStatusCountVO doStatusCountVO = orderService.getOrderCountByBuyer();
        return Result.success(doStatusCountVO);
    }

    @Login
    @ApiOperation(value = "卖家端Orders订单数量查询接口")
    @GetMapping("/order-count-seller")
    public Result<DOStatusCountVO> getOrderCountBySeller(@ApiIgnore UserVO user) {
        if (user != null && user.getCurrentSupplier() != null && user.getCurrentSupplier().getSupplierId() != null) {
            DOStatusCountVO doStatusCountVO = orderService.getOrderCountBySeller(user.getCurrentSupplier().getSupplierId());
            return Result.success(doStatusCountVO);
        } else {
            return Result.failed("未获取用户信息");
        }
    }

    @Login
    @ApiOperation(value = "手动完成订单接口")
    @GetMapping("/complete-order")
    public Result<Boolean> completeOrder(Long orderId) {
        return Result.success(orderService.completeOrder(orderId));
    }

    @Login
    @ApiOperation(value = "手动取消订单接口")
    @PostMapping("/cancel-order")
    public Result<Boolean> cancelOrder(@RequestBody CancelOrderDTO cancelOrderDTO) {
        return Result.success(orderService.cancelOrder(cancelOrderDTO));
    }
}
