package com.globalsources.bff.web.order.service;

import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.order.api.dto.CancelOrderDTO;
import com.globalsources.order.api.dto.DOSubmitDTO;
import com.globalsources.order.api.dto.PostDeliveryDTO;
import com.globalsources.order.api.dto.PostQuoteDTO;
import com.globalsources.order.api.model.DOListDetail;
import com.globalsources.order.api.model.DOOrderDetail;
import com.globalsources.order.api.vo.*;

import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * <AUTHOR> liu
 * @date 2021/7/7
 */
public interface OrderService {
    DOPlaceOrderVO orderFromInit(List<Long> productIdList, List<Integer> quantityList);

    DOSubmitVO submitOrder(DOSubmitDTO doSubmitDTO) throws UnsupportedEncodingException;

    PageResult<DOListDetail> buyerOrderList(Integer pageNum, Integer pageSize, String language);

    PageResult<DOListDetail> supplierOrderList(Integer pageNum, Integer pageSize, String orderStatus, String language);

    Result<OrderDetailVO> supplierOrderDetail(String orderId, String language, UserVO user);

    Result<OrderDetailVO> buyerOrderDetail(String orderId, String language, Long userId);

    DOOrderDetail orderCreated(Long orderId, String language);

    Boolean supplierRefund(Long orderId, String orderStatus, String remark);

    OrderQuotationVO quotation(Long orderId);

    Boolean postQuotation(PostQuoteDTO postQuoteDTO);

    Boolean postDelivery(PostDeliveryDTO postDeliveryDTO);

    Boolean refundIssue(String orderId, String remark);

    DOStatusCountVO getOrderCountByBuyer();

    DOStatusCountVO getOrderCountBySeller(Long supplierId);

    boolean completeOrder(Long orderId);

    Boolean cancelOrder(CancelOrderDTO cancelOrderDTO);
}
