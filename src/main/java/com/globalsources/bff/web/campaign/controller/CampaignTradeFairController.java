package com.globalsources.bff.web.campaign.controller;

import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.operation.agg.api.dto.campaigntradefair.CampaignTradeFairListQueryDTO;
import com.globalsources.operation.agg.api.feign.CampaignTradeFairFeign;
import com.globalsources.operation.agg.api.vo.CommonLiteVO;
import com.globalsources.operation.agg.api.vo.campaigntradefair.CampaignTradeFairVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/28
 */
@Slf4j
@Api(tags = "Campaign-Trade-Fair")
@RestController
@RequestMapping("/campaign-trade-fair")
public class CampaignTradeFairController {

    @Autowired
    private CampaignTradeFairFeign campaignTradeFairFeign;

    @ApiOperation(value = "get campaign trade fair page list", notes = "get campaign trade fair page list")
    @PostMapping("v1/get-campaign-trade-fair-page-list")
    public Result<PageResult<CampaignTradeFairVO>> getCampaignTradeFairPageList(@RequestBody CampaignTradeFairListQueryDTO queryDTO) {
        queryDTO.setActiveFlag(Boolean.TRUE);
        return campaignTradeFairFeign.getCampaignTradeFairPageList(queryDTO);
    }

    @ApiOperation(value = "get campaign trade fair view info", notes = "get campaign trade fair view info")
    @GetMapping("/v1/get-campaign-trade-fair-view-info/{urlShowTitle}")
    public Result<CampaignTradeFairVO> getCampaignTradeFairViewInfo(@PathVariable String urlShowTitle) {
        return campaignTradeFairFeign.getCampaignTradeFairViewInfo(urlShowTitle);
    }

    @ApiOperation(value = "get campaign trade fair show type", notes = "get campaign trade fair show type")
    @GetMapping("/v1/get-campaign-trade-fair-show-type")
    public Result<List<String>> getCampaignTradeFairShowType() {
        return campaignTradeFairFeign.getCampaignTradeFairShowType();
    }

    @ApiOperation(value = "get campaign trade fair category list", notes = "get campaign trade fair category list")
    @GetMapping("/v1/get-campaign-trade-fair-category-list")
    public Result<List<CommonLiteVO>> getCampaignTradeFairCategoryList() {
        return campaignTradeFairFeign.getCampaignTradeFairCategoryList();
    }

    @ApiOperation(value = "get campaign trade fair country list", notes = "get campaign trade fair country list")
    @GetMapping("/v1/get-campaign-trade-fair-country-list")
    public Result<List<CommonLiteVO>> getCampaignTradeFairCountryList() {
        return campaignTradeFairFeign.getCampaignTradeFairCountryList();
    }

}
