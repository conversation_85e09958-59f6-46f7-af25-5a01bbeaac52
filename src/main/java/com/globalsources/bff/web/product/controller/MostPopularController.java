package com.globalsources.bff.web.product.controller;

import com.globalsources.bff.web.product.service.ProductService;
import com.globalsources.bff.web.product.vo.WebHotProductVO;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.product.agg.api.feign.BuyerProductFeign;
import com.globalsources.product.agg.api.vo.DOL1CategoryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Optional;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.bff.web.product.controller
 * @date:2021/10/19
 */
@Api(tags = "MostPopularController")
@RequestMapping("/most-popular/product")
@RestController
public class MostPopularController {

    @Autowired
    private BuyerProductFeign productFeign;

    @Autowired
    private ProductService productService;

    @Login(validLogin = false)
    @ApiOperation(value = "MostPopular落地页头部类别导航栏查询", notes = "MostPopular落地页头部类别导航栏查询")
    @GetMapping("most-popular-category")
    public Result<List<DOL1CategoryVO>> mostPopularCategory(@ApiIgnore UserVO user) {
        return Result.success(productFeign.mostPopularCategory());
    }

    @ApiOperation("Most Popular Product Top List, like home most hot product")
    @Login(validLogin = false)
    @GetMapping("/v1/top/list")
    public Result<List<WebHotProductVO>> getMostPopularProductTopList(@RequestParam(required = false) String locationCode,
                                                                      @RequestParam(required = false) Integer topNum) {
        return Result.success(productService.getMostPopularProductTopList(locationCode, Optional.ofNullable(topNum).orElse(5)));
    }

}
