package com.globalsources.bff.web.product.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.buyer.product.vo
 * @date:2021/6/17
 */
@Data
public class ProductDetailShippingInfoVo {

    @ApiModelProperty(value = "FOB港口（选项）")
    private String fobPort;

    @ApiModelProperty(value = "lead time 范围")
    private String leadTimeRange;

    @ApiModelProperty(value = "产品（带包装）重量")
    private String weightPerUnit;

    @ApiModelProperty(value = "产品（带包装）尺寸")
    private String dimensionsPerUnit;

    @ApiModelProperty(value = "每个出口包装箱可装单位数量")
    private String unitsPerExportCarton;

    @ApiModelProperty(value = "出口包装箱尺寸")
    private String exportCartonDimensions;

    @ApiModelProperty(value = "出口包装箱重量")
    private String exportCartonWeight;

    @ApiModelProperty(value = "美国海关编码")
    private String usHTSCode;

}
