package com.globalsources.bff.web.product.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 产品分类表	来源 psc product_category
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ProductCategory对象")
public class ProductCategoryConstructVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分类id")
    @TableId(value = "category_id", type = IdType.AUTO)
    private Long categoryId;

    @ApiModelProperty(value = "父级id")
    private Long parentCategoryId;

    @ApiModelProperty(value = "层级")
    private Integer categoryLevel;

    @ApiModelProperty(value = "名称")
    private String descEn;

    @ApiModelProperty(value = "名称_中文")
    private String descZh;

    @ApiModelProperty(value = "排序")
    private Integer displaySort;

    @ApiModelProperty(value = "旧的id，改造给前端使用")
    @TableField(exist = false)
    private Long id;

    @ApiModelProperty(value = "合并中英文的描述")
    @TableField(exist = false)
    private String mergeDesc;

    @ApiModelProperty(value = "分类名字，兼容vo")
    @TableField(exist = false)
    private String categoryName;

    @ApiModelProperty(value = "seo category point id")
    @TableField(exist = false)
    private Long pointId;

    @ApiModelProperty(value = "seo singular Category Name 单数分类名")
    @TableField(exist = false)
    private String singularCategoryName;

    @ApiModelProperty(value = "类别等级，兼容vo")
    @TableField(exist = false)
    private Long level;

    @ApiModelProperty(value = "上级分类")
    @TableField(exist = false)
    private ProductCategoryConstructVO parentCategory;

    @ApiModelProperty("无效 标签， 此标签为true 的category ，只进行显示， 选择的树中 不提供。")
    private Boolean disableFlag;

    public Long getId() {
        return categoryId;
    }


}
