package com.globalsources.bff.web.product.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.globalsources.product.agg.api.vo.ProductTrackingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/17 21:13
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "Recommended for your business vo")
public class BuyerOrientCornerProductRecommendWebVO implements Serializable {
    private static final long serialVersionUID = 5600332051624562601L;

    @ApiModelProperty(value = "产品ID",required = false)
    private Long productId;

    @ApiModelProperty(value = "产品名称",required = false)
    private String productName;

    @ApiModelProperty(value = "fob价格最小值",required = false)
    private String productFOBMin;

    @ApiModelProperty(value = "fob价格最大值",required = false)
    private String productFOBMax;

    @ApiModelProperty(value = "最小订单量单位",required = false)
    private String minOrderUnit;

    @ApiModelProperty(value = "最小订单量单数单位",required = false)
    private String minOrderSingleUnit;

    @ApiModelProperty(value = "产品主图",required = false)
    private String primaryImageUrl;

    @ApiModelProperty(value = "最小订单量",required = false)
    private Integer minOrderQuantity;

    @ApiModelProperty(value = "fob价格类型")
    private String fobPriceShowType;

    @ApiModelProperty(value = "给前端显示的价格")
    private String listVoShowPriceStr;

    @ApiModelProperty(value = "SEO优化使用的产品详情地址")
    private String desktopProductDetailUrl;

    @ApiModelProperty(value = "发布天数")
    private Integer newDays;

    @ApiModelProperty(value = "NEW_PRODUCT, HOT_RFI, FREE_SAMPLE, VERY_POSITIVE")
    private String recommendType;

    @ApiModelProperty("productTracking vo")
    private ProductTrackingVO productTrackingVO;

}
