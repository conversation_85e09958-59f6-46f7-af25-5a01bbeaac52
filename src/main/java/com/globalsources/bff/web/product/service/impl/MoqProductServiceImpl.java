package com.globalsources.bff.web.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.globalsources.bff.web.home.service.LowRankCacheService;
import com.globalsources.bff.web.product.service.MoqProductService;
import com.globalsources.bff.web.product.vo.WebMoqProductVO;
import com.globalsources.bff.web.utils.ProductConvertUtil;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.product.agg.api.feign.MoqProductFeign;
import com.globalsources.product.agg.api.vo.AggCategoryVO;
import com.globalsources.product.agg.api.vo.AggProductVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class MoqProductServiceImpl implements MoqProductService {
    @Autowired
    private MoqProductFeign moqProductFeign;

    @Autowired
    private LowRankCacheService lowRankCacheService;

    private static final String CACHE_KEY_HOME_LOW_MOQ = "HOME_LOW_MOQ";

    @Override
    public Result<List<WebMoqProductVO>> getTopProduct(Integer topNum, String locationCode, String language) {
        List<WebMoqProductVO> productVOList = null;
        String cacheKeyWithLanguage = ProductConvertUtil.getLanguageKey(CACHE_KEY_HOME_LOW_MOQ, language);
        try {
            List<AggProductVO> aggProductVOList = moqProductFeign.queryTopProduct(topNum, locationCode).getData();
            if (!CollectionUtils.isEmpty(aggProductVOList)) {
                productVOList = OrikaMapperUtil.coverList(aggProductVOList, WebMoqProductVO.class);

                if (!lowRankCacheService.checkCache(cacheKeyWithLanguage)) {
                    lowRankCacheService.save(cacheKeyWithLanguage, productVOList);
                }
            }
        } catch (Exception e) {
            log.error("get low moq product occur exception, topNum:{}", topNum, e);
        }

        if (CollectionUtils.isEmpty(productVOList)) {
            log.error("get low moq product from cache!");
            productVOList = lowRankCacheService.getCacheByMethodName(cacheKeyWithLanguage, WebMoqProductVO.class);
        }

        return Result.success(productVOList);
    }

    @Override
    public Result<PageResult<WebMoqProductVO>> getProductList(Long categoryId, Long productId, Integer pageNum, Integer pageSize) {
        try {
            Result<PageResult<AggProductVO>> resp = moqProductFeign.getProductList(categoryId, productId, pageNum, pageSize);
            if (!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("query moq product list occur fail, categoryId:{}, pageNum:{} , pageSize:{}, resp:{}", categoryId,
                        pageNum, pageSize, JSON.toJSONString(resp));
                return Result.error();
            }

            PageResult<AggProductVO> rawPageResult = resp.getData();
            if (CollectionUtils.isEmpty(rawPageResult.getList())) {
                return Result.success();
            }

            //数据格式转换
            PageResult<WebMoqProductVO> pageResult = new PageResult<>();
            BeanUtils.copyProperties(rawPageResult, pageResult);
            pageResult.setList(OrikaMapperUtil.coverList(rawPageResult.getList(), WebMoqProductVO.class));

            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("get moq product list occur exception, categoryId:{}, pageNum:{} , pageSize:{}", categoryId, pageNum, pageSize, e);
            return Result.error();
        }
    }

    @Override
    public Result<List<AggCategoryVO>> getProductCategoryList(String userId) {
        return moqProductFeign.getProductCategoryList(userId);
    }

    @Override
    public List<WebMoqProductVO> getMoqProductTopList(String locationCode, Integer topNum) {
        List<AggProductVO> list = ResultUtil.getData(moqProductFeign.queryTopProduct(topNum, locationCode), "--->>> call MoqProductFeign.queryTopProduct failed");
        if (CollUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }

        return OrikaMapperUtil.coverList(list, WebMoqProductVO.class);
    }
}
