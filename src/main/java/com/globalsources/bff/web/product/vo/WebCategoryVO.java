package com.globalsources.bff.web.product.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/9
 */
@Data
public class WebCategoryVO {
    @ApiModelProperty(value = "分类id")
    private Long categoryId;

    @ApiModelProperty(value = "父级id")
    private Long parentCategoryId;

    @ApiModelProperty(value = "层级")
    private Integer categoryLevel;

    @ApiModelProperty(value = "名称")
    private String descEn;

    @ApiModelProperty(value = "名称_中文")
    private String descZh;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    public String getCategoryName() {
        return this.descEn;
    }
}
