package com.globalsources.bff.web.product.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/12
 */
@Data
public class WebServicesSupplierCardVO {

    @ApiModelProperty(value = "公司id")
    private Long supplierId;

    @ApiModelProperty(value = "公司名")
    private String companyName;

    @ApiModelProperty(value = "Supplier Short Name")
    private String supplierShortName;

    @ApiModelProperty("会员类型编号, agg:500,p0:1000,basic会员:2000,标准会员:3000,高级会员:4000,超级会员:5000,至尊会员:6000")
    private Integer memberTypeNum;

    @ApiModelProperty(value = "o2o图标flag")
    private Boolean o2oFlag;

    @ApiModelProperty(value = "合作年限")
    private Integer memberSince;

    @ApiModelProperty(value = "是否为已认证供应商")
    private Boolean verifiedSupplierFlag;

    @ApiModelProperty(value = "是否为已认证制造商")
    private Boolean verifiedManufacturerFlag;

    @ApiModelProperty(value = "供应商类型，包括：ADV/AGG/FL")
    private String supplierType;


    @ApiModelProperty("rfi平均回复时间评级，枚举，1:<=24h, 2:24-28h, 3:48-72h, 4:>72h")
    private Integer avgResponseTimeRatingEnum;

    /**
     * rfi回复率评级
     * - High
     * - Medium
     */
    @ApiModelProperty("rfi回复率评级")
    private String responseRateRating;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "业务类型")
    private String businessTypes;

    @ApiModelProperty("VR图标flag")
    private Boolean vrFlag;

    @ApiModelProperty("公司视频 url")
    private String videoUrl;

    @ApiModelProperty("公司视频 cover url")
    private String videoCoverUrl;

    @ApiModelProperty("公司司标 url")
    private String logoUrl;

    @ApiModelProperty("Main Products")
    private String mainProducts;

    @ApiModelProperty("Export Market")
    private String exportMarkets;

    @ApiModelProperty("网站主标题")
    private String displayMessage;

    @ApiModelProperty("网站副标题")
    private String subDisplayMessage;

    @ApiModelProperty("产品列表")
    private List<WebServicesSupplierCardProductVO> productList;
}
