package com.globalsources.bff.web.tradeshow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.globalsources.bff.web.search.feign.ISearchFeign;
import com.globalsources.bff.web.search.model.dto.request.FilterTerm;
import com.globalsources.bff.web.search.model.dto.request.ISearchRequest;
import com.globalsources.bff.web.search.model.dto.response.ISearchResponse;
import com.globalsources.bff.web.tradeshow.constants.TradeShowConstants;
import com.globalsources.bff.web.tradeshow.service.TradeShowProductService;
import com.globalsources.bff.web.tradeshow.vo.TSProductDetailVO;
import com.globalsources.bff.web.tradeshow.vo.TradeShowProductVO;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ProductPriceUtil;
import com.globalsources.framework.vo.ProductTrackingVO;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.product.agg.api.feign.BuyerProductFeign;
import com.globalsources.search.api.vo.ProductDetailVo;
import com.globalsources.search.api.vo.ProductSearchVo;
import com.globalsources.search.api.vo.detail.ProductCategoryInfo;
import com.globalsources.search.api.vo.detail.ProductVo;
import com.globalsources.search.api.vo.detail.SupplierInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: create by Corin Chen
 * @version: v1.0
 * @description: com.globalsources.bff.web.tradeshow.service.impl
 * @date:2021/7/20
 */
@Slf4j
@Service
public class TradeShowProductServiceImpl implements TradeShowProductService {


    @Autowired
    private BuyerProductFeign productFeign;

    @Autowired
    private ISearchFeign iSearchFeign;

    private static final String CONNECTOR_CHAR = " - " ;

    private static final String PRODUCT_ID = "productId";

    private static String fullDomain;
    private static String http;

    @Value("${website.full.domain}")
    public void setFullDomain(String fullDomain) {
        TradeShowProductServiceImpl.fullDomain = fullDomain;
    }

    @Value("${website.http}")
    public void setHttp(String http) {
        TradeShowProductServiceImpl.http = http;
    }

    @Override
    public List<TradeShowProductVO> queryProductByIds(List<Long> productIds) {
        List<TradeShowProductVO> resultList = new ArrayList<>();
        List<Map<String,Object>> productDetailDTOList = getList(productIds);

        for (Map<String,Object> sourceProduct : productDetailDTOList) {
            TradeShowProductVO productVO = JSON.parseObject(JSON.toJSONString(sourceProduct), TradeShowProductVO.class);
            productVO.setVideoFlag(MapUtil.getLong(sourceProduct, "videoId", 0L) > 0);
            productVO.setDesktopProductDetailUrl(http + fullDomain + MapUtil.getStr(sourceProduct, "desktopProductDetailUrl"));
            productVO.setInquireUrl(http + fullDomain + MessageFormat.format(TradeShowConstants.RFI_FORM_URL, MapUtil.getStr(sourceProduct, PRODUCT_ID)));
            productVO.setProductName(MapUtil.getStr(sourceProduct, "productName"));
            productVO.setMinOrderSingleUnit(MapUtil.getStr(sourceProduct,"orderUom"));
            productVO.setPrimaryImageUrl(MapUtil.getStr(sourceProduct,"imageUrl"));
            productVO.setListVoShowPriceStr(productPrice(new BigDecimal(MapUtil.getStr(sourceProduct,"minOrderPrice", "0")),
                    new BigDecimal(MapUtil.getStr(sourceProduct,"maxOrderPrice","0"))));

            List<String> productCertLinks = MapUtil.get(sourceProduct, "productCertificate", new TypeReference<List<String>>() {
            });
            List<String> verifiedCompanyCertificates = MapUtil.get(sourceProduct, "verifiedCompanyCertificate", new TypeReference<List<String>>() {
            });

            List<String> productCertificates = new ArrayList<>();
            if(CollUtil.isNotEmpty(productCertLinks)) {
                productCertificates.addAll(productCertLinks);
            }
            if(CollUtil.isNotEmpty(verifiedCompanyCertificates)) {
                productCertificates.addAll(verifiedCompanyCertificates);
            }

            if(CollUtil.isNotEmpty(productCertificates)) {
                productCertificates = new ArrayList<>(new HashSet<>(productCertificates));
            }

            if (CollUtil.isNotEmpty(productCertificates)) {
                productVO.setCertificates(String.join(",",productCertificates));
            }

            resultList.add(productVO);
        }
        return resultList;
    }

    private List<Map<String, Object>> getList(List<Long> productIds) {
        if(CollUtil.isEmpty(productIds)) {
            return Collections.emptyList();
        }

        ISearchRequest request = new ISearchRequest();
        request.setIndicesName("spu");
        request.setPageNum(1);
        request.setPageSize(productIds.size());
        request.setPlatform("DESKTOP");
        request.setVersion("v2");
        request.setFilters(Collections.singletonList(
                new FilterTerm("COMMON", "productId", false, productIds.stream().map(String::valueOf).collect(Collectors.toList())
                )));
        request.setSourceFields(Arrays.asList("productId", "productName", "minOrderQuantity", "orderUom",
                "shortDescription", "categories", "videoFlag",
                "minOrderPrice", "maxOrderPrice", "fobPriceFlag",
                "videoUrl", "imageUrl", "productCertificate", "verifiedCompanyCertificate"));

        Result<ISearchResponse> result = iSearchFeign.genericSearchWithObject(request);
        if(result.getData() != null && CollUtil.isNotEmpty(result.getData().getData())) {
            return result.getData().getData();
        }
        return Collections.emptyList();
    }

    public static String productPrice(BigDecimal min, BigDecimal max) {
        DecimalFormat df = new DecimalFormat("#0.00");
        if (Objects.isNull(max) || BigDecimal.ZERO.compareTo (max) == 0) {
            if(BigDecimal.ZERO.compareTo (min) == 0 || Objects.isNull(min)) {
                return CharSequenceUtil.EMPTY;
            }
            return df.format(min).trim();
        }
        return Optional.of(max).map(u -> CharSequenceUtil.join(CONNECTOR_CHAR, df.format(min), df.format(u)).trim()).orElse(df.format(min).trim());
    }

    @Override
    public TSProductDetailVO productDetail(Long productId) {
        ProductDetailVo productDetail = productFeign.productInfo(productId).getData();
        ProductVo product = productDetail.getProduct();
        if(Boolean.TRUE.equals(productDetail.getExpiredFlag())) {
            log.warn("\n--------------product is expired! productId:{}", productId);
            TSProductDetailVO expiredProduct = new TSProductDetailVO();
            expiredProduct.setExpiredFlag(true);
            return expiredProduct;
        }
        SupplierInfoVo supplierSnippetInfo = productDetail.getSupplierSnippetInfo();
        TSProductDetailVO resultDetail = OrikaMapperUtil.coverObject(product, TSProductDetailVO.class);
        resultDetail.setProductUrl(http + fullDomain + product.getDesktopProductDetailUrl());
        resultDetail.setProductName(productDetail.getProductInfoMultiLan().getProductName());
        resultDetail.setPrimaryImageUrl(productDetail.getProductPrimaryImage());
        resultDetail.setModelNumber(productDetail.getProductInfoMultiLan().getModelNumber());
        resultDetail.setInquireUrl(http + fullDomain + MessageFormat.format(TradeShowConstants.RFI_FORM_URL, productId + ""));

        resultDetail.setListVoShowPriceStr(ProductPriceUtil.convertProductPrice(product.getFobPriceShowType()
                , product.getSpecifyFobPriceRangeUp(), product.getSpecifyFobPriceRangeLow()
                , product.getProductFOBMax(), product.getProductFOBMin()));

        if (Objects.nonNull(supplierSnippetInfo)) {
            resultDetail.setTotalAnnualSales(supplierSnippetInfo.getTotalAnnualSales());
            resultDetail.setYearEstablished(supplierSnippetInfo.getYearEstablished());
            resultDetail.setExportMarkets(supplierSnippetInfo.getExportMarkets());
            resultDetail.setTotalNoEmployees(supplierSnippetInfo.getTotalNoEmployees());
        }
        resultDetail.setProductTrackingVO(completeTrackingInfo(productDetail));

        if(Boolean.TRUE.equals(productDetail.getUnverifiedFlag())) {
            resultDetail.setUnverifiedFlag(true);
        }
        return resultDetail;
    }

    private ProductTrackingVO completeTrackingInfo(ProductDetailVo product) {
        ProductTrackingVO trackingVO = new ProductTrackingVO();
        if (Objects.isNull(product)) {
            return trackingVO;
        }
        trackingVO.setSupplierId(product.getProduct().getOrgId());

        ProductCategoryInfo categoryInfo = product.getCategoryInfo();
        if (Objects.nonNull(categoryInfo)) {
            trackingVO.setL1CategoryId(categoryInfo.getL1CategoryVo().getCategoryId());
            trackingVO.setL2CategoryId(categoryInfo.getL2CategoryVo().getCategoryId());
            trackingVO.setL3CategoryId(categoryInfo.getL3CategoryVo().getCategoryId());
            trackingVO.setL4CategoryId(categoryInfo.getL4CategoryVo().getCategoryId());
            trackingVO.setProductName(product.getProductInfoMultiLan().getProductName());
        }
        if (Objects.nonNull(product.getSupplierSnippetInfo())) {
            trackingVO.setSupplierType(product.getSupplierSnippetInfo().getSupplierType());
            trackingVO.setSupplierPackage(product.getSupplierSnippetInfo().getMaxStartLevel()+"");
        }
        return trackingVO;
    }

    @Override
    public List<TradeShowProductVO> recommendProduct(UserVO userVO, Long categoryId,String anonymousId) {
        Long userId = null;
        if (Objects.nonNull(userVO)) {
            userId = userVO.getUserId();
        }
        List<ProductSearchVo> searchVos = productFeign.tradeShowRecommendProduct(categoryId, Objects.nonNull(userId)?userId.toString():anonymousId).getData();
        List<TradeShowProductVO> tradeShowProductVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(searchVos)) {
            return tradeShowProductVOList;
        }
        for (ProductSearchVo searchVo : searchVos) {
            TradeShowProductVO tradeShowProductVO = OrikaMapperUtil.coverObject(searchVo, TradeShowProductVO.class);
            tradeShowProductVO.setDesktopProductDetailUrl(http + fullDomain + searchVo.getDesktopProductDetailUrl());
            tradeShowProductVOList.add(tradeShowProductVO);
        }

        return tradeShowProductVOList;
    }

}
