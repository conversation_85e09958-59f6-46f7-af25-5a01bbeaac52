package com.globalsources.bff.web.tradeshow.service.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.agg.supplier.api.feign.TradeshowAggFeign;
import com.globalsources.agg.supplier.api.model.dto.TradeshowAggDTO;
import com.globalsources.agg.supplier.api.model.dto.tradeshow.SeasonTradeShowDTO;
import com.globalsources.agg.supplier.api.model.dto.tradeshow.TsPavilionDTO;
import com.globalsources.awesome.logging.annotation.Logging;
import com.globalsources.bff.web.enums.TsGEIMSProductPrefEnums;
import com.globalsources.bff.web.tradeshow.dto.EdmTsRecommendDTO;
import com.globalsources.bff.web.tradeshow.dto.TsVietnamRegDTO;
import com.globalsources.bff.web.tradeshow.dto.TsVietnamRegV2DTO;
import com.globalsources.bff.web.tradeshow.dto.WebTsGEIMSUserRegDTO;
import com.globalsources.bff.web.tradeshow.dto.WebTsIdUserRegDTO;
import com.globalsources.bff.web.tradeshow.dto.WebTsIdUserRegV2DTO;
import com.globalsources.bff.web.tradeshow.dto.WebTsIdUserRegV3DTO;
import com.globalsources.bff.web.tradeshow.dto.WebTsRecommendEmailDTO;
import com.globalsources.bff.web.tradeshow.dto.WebTsUserOneClickRegDTO;
import com.globalsources.bff.web.tradeshow.dto.WebTsUserRegDTO;
import com.globalsources.bff.web.tradeshow.dto.WebTsUserRegEdmDTO;
import com.globalsources.bff.web.tradeshow.dto.WebVnAdditionalInfoDTO;
import com.globalsources.bff.web.tradeshow.service.TradeShowService;
import com.globalsources.bff.web.tradeshow.vo.SeasonTradeShowVO;
import com.globalsources.bff.web.tradeshow.vo.TradeShowByGrpCodeBffVO;
import com.globalsources.bff.web.tradeshow.vo.TsPavilionVO;
import com.globalsources.bff.web.tradeshow.vo.TsRegConfirmVO;
import com.globalsources.bff.web.utils.QRCodeUtil;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.enums.SourceEnum;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.IResultCode;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.rfi.agg.feign.TradeShowFeign;
import com.globalsources.rfi.agg.response.email.TradeQuestionVO;
import com.globalsources.ts.api.enums.TradeShowTypeEnums;
import com.globalsources.ts.api.feign.HongKongTradeShowFeign;
import com.globalsources.ts.api.feign.RegionalOnlineHongKongFeign;
import com.globalsources.ts.api.feign.TsCommonFeign;
import com.globalsources.ts.api.model.TradeShowQueryDTO;
import com.globalsources.ts.api.model.dto.desktop.TradeShowListDTO;
import com.globalsources.ts.api.model.vo.HongKongSeasonTradeShowVO;
import com.globalsources.ts.api.model.vo.SeasonTradeShowV2VO;
import com.globalsources.ts.api.model.vo.TsInfoVO;
import com.globalsources.ts.api.model.vo.desktop.TradeShowVO;
import com.globalsources.ts.api.model.vo.desktop.TsContactGlobalSourcesVO;
import com.globalsources.ts.api.model.vo.desktop.TsPressInquiryVO;
import com.globalsources.user.api.dto.HongKongSeasonTradeShowDTO;
import com.globalsources.user.api.dto.ResendMailDTO;
import com.globalsources.user.api.dto.TsRecommendEmailDTO;
import com.globalsources.user.api.dto.TsRegPaDTO;
import com.globalsources.user.api.dto.TsRegSuccessDTO;
import com.globalsources.user.api.dto.TsUserHKRegDTO;
import com.globalsources.user.api.dto.TsUserOneClickRegDTO;
import com.globalsources.user.api.dto.TsUserRegDTO;
import com.globalsources.user.api.dto.TsUserRegEdmDTO;
import com.globalsources.user.api.dto.TsUserVnRegDTO;
import com.globalsources.user.api.dto.VnAdditionalInfoDTO;
import com.globalsources.user.api.enums.TsRegSourceEnum;
import com.globalsources.user.api.enums.TsSSOGSRegEnum;
import com.globalsources.user.api.enums.UserErrorEnum;
import com.globalsources.user.api.feign.TsUserFeign;
import com.globalsources.user.api.vo.TsReferralInfoVO;
import com.globalsources.user.api.vo.TsUserVO;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Chen
 * @date 2021/7/13 20:10
 */
@Slf4j
@Service
@RefreshScope
public class TradeShowServiceImpl implements TradeShowService {
    @Value("${ts.season.timeTitle:October,2022}")
    private String seasonTimeTitle;
    @Value("${ts.season.region}")
    private String seasonTsRegion;
    @Value("${ts.season.startTime}")
    private Long seasonTsStartTime;
    @Value("${ts.season.endTime}")
    private Long seasonTsEndTime;
    @Value("${ts.season.recommend.startTime}")
    private Long recommendStartTime;
    @Value("${ts.season.recommend.endTime}")
    private Long recommendEndTime;
    @Value("${ts.hkshow.hkTsGrpCode:EC,ELC,ME,SHA,GH,HK,SP,FA,BCP}")
    private String hkTsGrpCode;
    @Value("${ts.season.hightouch.recommend.startTime}")
    private Long htRecommendStartTime;
    @Value("${ts.season.hightouch.recommend.endTime}")
    private Long htRecommendEndTime;
    @Value("${hk.bcp.tsId}")
    private Long hkBcpTsId;
    private static final String ERR_CODE = "E1";

    @Autowired
    private TradeShowFeign tradeShowFeign;
    @Resource
    private TsUserFeign tsUserFeign;

    @Autowired
    private TradeshowAggFeign tradeshowAggFeign;

    @Autowired
    private RegionalOnlineHongKongFeign onlineHongKongFeign;
    @Autowired
    private TsCommonFeign tsCommonFeign;

    @Autowired
    private HongKongTradeShowFeign hongKongTradeShowFeign;

    @Override
    public Result<PageResult<TsPavilionVO>> getTsPavilionListByTsIds(List<Long> tsIds, Integer pageIndex, Integer pageSize) {
        Result<PageResult<TsPavilionDTO>> tsPavilistionList = tradeshowAggFeign.getTsPavilionListByTsIds(tsIds, pageIndex, pageSize);

        PageResult<TsPavilionDTO> tsPavilionDTOPageResult = tsPavilistionList.getData();
        List<TsPavilionDTO> tsPavilionDTOList = tsPavilionDTOPageResult.getList();

        List<TsPavilionVO> tsPavilionVOList = OrikaMapperUtil.coverList(tsPavilionDTOList, TsPavilionVO.class);

        PageResult<TsPavilionVO> newPageResult = new PageResult<>();
        newPageResult.setPageNum(tsPavilionDTOPageResult.getPageNum());
        newPageResult.setTotal(tsPavilionDTOPageResult.getTotal());
        newPageResult.setPageSize(tsPavilionDTOPageResult.getPageSize());
        newPageResult.setTotalPage(tsPavilionDTOPageResult.getTotalPage());
        newPageResult.setList(tsPavilionVOList);

        return Result.success(newPageResult);
    }

    @Override
    public List<TradeShowByGrpCodeBffVO> getTradeShowWithPavilionByTsGroupCodeList(List<String> tsGroupCodeList) {
        List<TradeShowByGrpCodeBffVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(tsGroupCodeList)) {
            tsGroupCodeList = ImmutableList.of("EC", "ME", "SHA", "HK", "FA");
        }
        Result<List<TradeshowAggDTO>> tradeShowListResult = tradeshowAggFeign.getTradeShowWithPavilionByTsGroupCodeList(tsGroupCodeList);
        List<TradeshowAggDTO> tsAggDtoList = ResultUtil.getData(tradeShowListResult, "failed to getTradeShowWithPavilionByTsGroupCodeList, tsGroupCodeList:  " + tsGroupCodeList);
        if (CollectionUtils.isNotEmpty(tsAggDtoList)) {
            for (TradeshowAggDTO tsAggDto : tsAggDtoList) {
                if (Objects.nonNull(tsAggDto)) {
                    TradeShowByGrpCodeBffVO tradeShowByGrpCodeVo = OrikaMapperUtil.coverObject(tsAggDto, TradeShowByGrpCodeBffVO.class);
                    List<TsPavilionVO> tsPavilionVoList = OrikaMapperUtil.coverList(tsAggDto.getTsPavilionList(), TsPavilionVO.class);
                    tradeShowByGrpCodeVo.setTsPavilionVOList(tsPavilionVoList);
                    result.add(tradeShowByGrpCodeVo);
                }
            }
        }
        return result;
    }

    @Override
    public Result<String> pressInquiry(TsPressInquiryVO tradeShowPressInquiryVO) {
        return onlineHongKongFeign.pressInquiry(tradeShowPressInquiryVO);
    }

    @Override
    public Result<String> contactGlobalSources(TsContactGlobalSourcesVO tradeShowContactGlobalSourcesVO) {
        return onlineHongKongFeign.contactGlobalSources(tradeShowContactGlobalSourcesVO);
    }

    @Override
    public Result<String> submitTradeQuestion(TradeQuestionVO tradeQuestionVO) {
        return tradeShowFeign.submitTradeQuestion(tradeQuestionVO);
    }

    @Override
    public Result<List<SeasonTradeShowVO>> getSeasonTradeShow(String lang) {
        String[] regionArr=seasonTsRegion.split("-");

        try {
            //查询指定季度展会
            Result<List<SeasonTradeShowDTO>> resp = tradeshowAggFeign.getSeasonTradeShow(regionArr[0], regionArr[1], seasonTsStartTime, seasonTsEndTime,lang);
            if (!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.warn("query season trade show return error, region:{}, resp:{}",seasonTsRegion,resp);
                return Result.failed(resp.getCode(), resp.getMsg());
            }

            if (CollectionUtils.isEmpty(resp.getData())) {
                return Result.success(new ArrayList<>());
            }

            //查询每个展会关联的product preference 列表
            List<SeasonTradeShowVO> data = new ArrayList<>();
            for (SeasonTradeShowDTO item : resp.getData()) {
                SeasonTradeShowVO vo = OrikaMapperUtil.coverObject(item, SeasonTradeShowVO.class);
                if ("GH".equals(vo.getTsGrpCode())) {
                    vo.setTsGrpCode("FA");
                }

                vo.setRecommendStartDate(new Date(recommendStartTime));
                vo.setRecommendEndDate(new Date(recommendEndTime));
                vo.setHtRecommendStartDate(new Date(htRecommendStartTime));
                vo.setHtRecommendEndDate(new Date(htRecommendEndTime));
                data.add(vo);
            }

            return Result.success(data);
        }catch (Exception e){
            log.error("query season trade show occur exception, detail:{}",e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<List<HongKongSeasonTradeShowVO>> getSeasonTradeShowV2(String lang) {
        return hongKongTradeShowFeign.hkSeasonList(lang);
    }

    @Override
    @Logging(businessType = "TS_REG", errorTag = "WEB_TS_REG", logOnError = false)
    @Deprecated
    public Result<TsRegConfirmVO> register(WebTsUserRegDTO dto) {
        try{
            boolean sendEmail=true;
            //注册展会用户
            TsUserRegDTO regDTO= OrikaMapperUtil.coverObject(dto,TsUserHKRegDTO.class);
            regDTO.setSsoGsSources(TsSSOGSRegEnum.OSF_Online_D.name());
            Result<TsUserVO> resp=tsUserFeign.register(regDTO);

            //用户名字不匹配
            if(resp.getCode().equals(UserErrorEnum.TS_USER_NAME_NOT_MATCH.getCode())){
                throw new BusinessException(UserErrorEnum.TS_USER_NAME_NOT_MATCH);
            }

            if (resp.getCode().equals(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_EMPTY.getCode())
                    || resp.getCode().equals(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_FAIL.getCode())) {
                throw new BusinessException(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_FAIL);
            }

            if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode()) && !resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())){
                log.error("register trade show user return error, dto:{} ,resp:{}",dto,resp);
                throw new BusinessException(resp.getCode(), resp.getMsg());
            }

            IResultCode resultCode=ResultCode.CommonResultCode.SUCCESS;
            if(resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())){
                resultCode=UserErrorEnum.ALREADY_ALL_REG_TS;
            }

            //格式转换
            TsRegConfirmVO data = OrikaMapperUtil.coverObject(resp.getData(),TsRegConfirmVO.class);
            if(CollectionUtils.isEmpty(resp.getData().getTsId())){
                return Result.success(data);
            }

            //查询展会信息
            List<TsRegConfirmVO.TsInfo> tsInfoList=queryTradeShowList(resp.getData().getTsId(),dto.getLanguage());
            data.setTsList(tsInfoList);

            //查询展会分组列表
            List<HongKongSeasonTradeShowVO> listResult = getSeasonTradeShowV2("enus").getData();
            //发送注册成功邮件
            if(sendEmail) {
                TsRegSuccessDTO mailInfo = OrikaMapperUtil.coverObject(data, TsRegSuccessDTO.class);
                mailInfo.setSeasonName(seasonTimeTitle);
                mailInfo.setEmail(dto.getEmail());
                mailInfo.setBuyerType(data.getBuyerType());
                mailInfo.setQrCode(QRCodeUtil.generateAsBase64(data.getVisitorId() + "|" + data.getCountryName()));
                mailInfo.setSource(data.getSource());
                if (CollectionUtils.isNotEmpty(listResult) && CollectionUtils.isNotEmpty(tsInfoList)) {
                    Map<Long, String> map = tsInfoList.stream().collect(Collectors.toMap(TsRegConfirmVO.TsInfo::getTsId, TsRegConfirmVO.TsInfo::getName));

                    List<HongKongSeasonTradeShowVO> list = new ArrayList<>();
                    for (HongKongSeasonTradeShowVO seasonTradeShowVO: listResult) {
                        List<SeasonTradeShowV2VO> voList = new ArrayList<>();
                        for (SeasonTradeShowV2VO seasonTradeShowV2VO: seasonTradeShowVO.getList()) {
                            if (map.containsKey(seasonTradeShowV2VO.getTradeshowId())) {
                                SeasonTradeShowV2VO showV2VO = new SeasonTradeShowV2VO();
                                showV2VO.setTradeshowId(seasonTradeShowV2VO.getTradeshowId());
                                showV2VO.setTsName(seasonTradeShowV2VO.getTsName());
                                voList.add(showV2VO);
                            }
                        }
                        if (!voList.isEmpty()) {
                            HongKongSeasonTradeShowVO hongKongSeasonTradeShowVO = new HongKongSeasonTradeShowVO();
                            hongKongSeasonTradeShowVO.setSeasonDate(seasonTradeShowVO.getSeasonDate());
                            hongKongSeasonTradeShowVO.setExhibitionStage(seasonTradeShowVO.getExhibitionStage());
                            hongKongSeasonTradeShowVO.setList(voList);
                            list.add(hongKongSeasonTradeShowVO);
                        }
                    }
                    List<HongKongSeasonTradeShowDTO> tradeShowList = OrikaMapperUtil.coverList(list, HongKongSeasonTradeShowDTO.class);
                    mailInfo.setTradeShowList(tradeShowList);
                }
                if (!CollectionUtils.isEmpty(tsInfoList)) {
                    mailInfo.setTsList(tsInfoList.stream().map(TsRegConfirmVO.TsInfo::getName).collect(Collectors.toList()));
                }
                tsUserFeign.sendRegSuccessMail(mailInfo);
            }

            return new Result<>(resultCode.getCode(), resultCode.getMsg(), data);
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            log.error("register trade show user occur exception, dto:{} ,detail:{}", dto, e.getMessage(), e);
            throw new BusinessException(ERR_CODE, e.getMessage());
        }
    }

    @Override
    public Result<TsRegConfirmVO> oneClickRegister(WebTsUserOneClickRegDTO dto) {
        try {
            //注册展会用户
            TsUserOneClickRegDTO regDTO = OrikaMapperUtil.coverObject(dto, TsUserOneClickRegDTO.class);
            Result<TsUserVO> resp = tsUserFeign.oneClickRegister(regDTO);

            //格式转换
            TsRegConfirmVO data = OrikaMapperUtil.coverObject(resp.getData(), TsRegConfirmVO.class);

            //查询展会信息
            List<Long> tsIdList = Lists.newArrayList();
            tsIdList.addAll(dto.getNeedToRegTsIds());
            tsIdList.addAll(dto.getRegedTsIds());

            //发送注册成功邮件
            TsRegSuccessDTO mailInfo = OrikaMapperUtil.coverObject(data, TsRegSuccessDTO.class);
            mailInfo.setSeasonName(seasonTimeTitle);
            mailInfo.setEmail(resp.getData().getEmail());
            mailInfo.setBuyerType(data.getBuyerType());
            mailInfo.setQrCode(QRCodeUtil.generateAsBase64(data.getVisitorId() + "|" + dto.getCountryName()));
            mailInfo.setSource(data.getSource());
            mailInfo.setConfirmId(Long.parseLong(dto.getRegId()));
            mailInfo.setCompanyName(dto.getCompanyName());

            List<HongKongSeasonTradeShowVO> tsInfo = ResultUtil.getData(hongKongTradeShowFeign.hkSeasonList(LanguageDicEnum.EN_US.getValue()));
            log.info("oneClickRegister tsInfo :{}",tsInfo);

            List<TradeShowVO> regTsInfoList=ResultUtil.getData(hongKongTradeShowFeign.tradeShowList(TradeShowListDTO.builder().tradeShowIdList(tsIdList).build()));
            log.info("oneClickRegister regTsInfoList :{}",tsInfo);

            mailInfo.setTradeShowList(getRegTsInfoForMail(tsInfo, regTsInfoList));

            if (CollectionUtils.isNotEmpty(regTsInfoList)) {
                mailInfo.setTsList(regTsInfoList.stream().map(TradeShowVO::getTsName).collect(Collectors.toList()));
            }
            tsUserFeign.sendRegSuccessMail(mailInfo);
            data.setTsList(getTsInfoList(regTsInfoList));
            return Result.success(data);

        } catch (Exception e) {
            log.error("One-Click Register trade show user occur exception, dto:{} ,detail:{}", dto, e.getMessage(), e);
            return Result.error();
        }
    }

    private List<TsRegConfirmVO.TsInfo> getTsInfoList(List<TradeShowVO> regTsInfoList) {
        List<TsRegConfirmVO.TsInfo> result = new ArrayList<>();
        try {
            for(TradeShowVO ts : regTsInfoList){
                TsRegConfirmVO.TsInfo info = new TsRegConfirmVO.TsInfo();
                info.setTsId(ts.getTradeshowId());
                info.setName(ts.getTsName());
                info.setTsGrpCode(ts.getTsGrpCode());
                info.setStartDate(ts.getTsStartDate());
                info.setEndDate(ts.getTsEndDate());
                result.add(info);
            }
        }catch (Exception e){
            log.error("getTsInfoList occur exception, detail:{}", e.getMessage(), e);
        }
        return result;
    }

    private List<HongKongSeasonTradeShowDTO> getRegTsInfoForMail(List<HongKongSeasonTradeShowVO> tsInfo,List<TradeShowVO> regTsInfoList) {
        try{
            List<Long> regTsIds = regTsInfoList.stream().map(TradeShowVO::getTradeshowId).collect(Collectors.toList());
            log.info("getRegTsInfoForMail regTsIds :{}",regTsIds);

            Iterator<HongKongSeasonTradeShowVO> it = tsInfo.iterator();
            while (it.hasNext()) {
                HongKongSeasonTradeShowVO next = it.next();
                next.getList().removeIf(nextTs -> !regTsIds.contains(nextTs.getTradeshowId()));
                if(CollectionUtils.isEmpty(next.getList())){
                    it.remove();
                }
            }
            return OrikaMapperUtil.coverList(tsInfo, HongKongSeasonTradeShowDTO.class);
        }catch (Exception e){
            log.error("getRegTsInfoForMail occur exception, tsInfo:{} ,regTsInfoList:{}", tsInfo, regTsInfoList, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Result<TsRegConfirmVO> geimsRegister(WebTsGEIMSUserRegDTO dto) {
        try {
            boolean sendEmail = true;
            //注册展会用户
            TsUserRegDTO regDTO = OrikaMapperUtil.coverObject(dto, TsUserHKRegDTO.class);
            regDTO.setSsoGsSources(TsSSOGSRegEnum.OSF_Online_D.name());

            if ("h5".equalsIgnoreCase(dto.getFrom())) {
                regDTO.setSsoGsSources(TsSSOGSRegEnum.OSF_Online_M.name());
            } else if ("app".equalsIgnoreCase(dto.getFrom()) || "ios".equalsIgnoreCase(dto.getFrom()) || "android".equalsIgnoreCase(dto.getFrom())) {
                regDTO.setSsoGsSources(TsSSOGSRegEnum.OSF_Online_APP.name());
            }

            regDTO.setRegSource(TsRegSourceEnum.get(dto.getFrom()));

            //set category ids
            List<Long> prodCatIdList = Lists.newArrayList();
            for (List<String> list : dto.getProductCategoryList()) {
                list.forEach(code -> prodCatIdList.addAll(TsGEIMSProductPrefEnums.match(code)));
            }

            List<TsUserRegDTO.ProductPref> productPrefList = Lists.newArrayList();
            prodCatIdList.stream().distinct().forEach(catId -> {
                TsUserRegDTO.ProductPref pref = new TsUserRegDTO.ProductPref();
                pref.setCodeId(String.valueOf(catId));

                productPrefList.add(pref);
            });
            regDTO.setProductPrefList(productPrefList);

            Result<TsUserVO> resp = tsUserFeign.geimsRegister(regDTO);

            //用户名字不匹配
            if (resp.getCode().equals(UserErrorEnum.TS_USER_NAME_NOT_MATCH.getCode())) {
                return Result.failed(UserErrorEnum.TS_USER_NAME_NOT_MATCH);
            }

            if (!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode()) && !resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())) {
                log.error("register GEIMS user return error, dto:{} ,resp:{}", dto, resp);
                return Result.error();
            }

            IResultCode resultCode = ResultCode.CommonResultCode.SUCCESS;
            if (resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())) {
                sendEmail = false;
                resultCode = UserErrorEnum.ALREADY_ALL_REG_TS;
            }

            //格式转换
            TsRegConfirmVO data = OrikaMapperUtil.coverObject(resp.getData(), TsRegConfirmVO.class);
            if (CollectionUtils.isEmpty(resp.getData().getTsId())) {
                return Result.success(data);
            }

            //查询展会信息
            List<TsRegConfirmVO.TsInfo> tsInfoList = queryTradeShowList(resp.getData().getTsId(), dto.getLanguage());
            data.setTsList(tsInfoList);

            //发送注册成功邮件
            String qrCode = QRCodeUtil.generateAsBase64(data.getVisitorId() + "|" + data.getCountryName());
            if (sendEmail) {
                TsRegSuccessDTO mailInfo = OrikaMapperUtil.coverObject(data, TsRegSuccessDTO.class);
                mailInfo.setEmail(dto.getEmail());
                mailInfo.setQrCode(qrCode);
                if (!CollectionUtils.isEmpty(tsInfoList)) {
                    mailInfo.setTsList(tsInfoList.stream().map(TsRegConfirmVO.TsInfo::getName).collect(Collectors.toList()));
                }

                tsUserFeign.sendGEIMSRegSuccessMail(mailInfo);
            }

            data.setQrCodeImg(qrCode);

            return new Result<>(resultCode.getCode(), resultCode.getMsg(), data);
        } catch (Exception e) {
            log.error("register GEIMS user occur exception, dto:{} ,detail:{}", dto, e.getMessage(), e);
            return Result.error();
        }
    }

    @Override
    @Logging(businessType = "TS_ID_REG", errorTag = "WEB_TS_ID_REG", logOnError = false)
    public Result<TsRegConfirmVO> indonesiaRegister(WebTsIdUserRegDTO dto) {
        try{
            //注册展会用户
            TsUserRegDTO regDTO= OrikaMapperUtil.coverObject(dto,TsUserHKRegDTO.class);
            regDTO.setSsoGsSources(TsSSOGSRegEnum.OSF_Online_D.name());
            Result<TsUserVO> resp=tsUserFeign.idRegister(regDTO);

            //用户名字不匹配
            if(resp.getCode().equals(UserErrorEnum.TS_USER_NAME_NOT_MATCH.getCode())){
                throw new BusinessException(UserErrorEnum.TS_USER_NAME_NOT_MATCH);
            }

            if (resp.getCode().equals(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_EMPTY.getCode())
                    || resp.getCode().equals(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_FAIL.getCode())) {
                throw new BusinessException(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_FAIL);
            }

            if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode()) && !resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())){
                log.error("register trade show user return error, dto:{} ,resp:{}",dto,resp);
                throw new BusinessException(resp.getCode(), resp.getMsg());
            }

            IResultCode resultCode=ResultCode.CommonResultCode.SUCCESS;
            if(resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())){
                resultCode= UserErrorEnum.ALREADY_ALL_REG_TS;
            }

            //格式转换
            TsRegConfirmVO data = OrikaMapperUtil.coverObject(resp.getData(),TsRegConfirmVO.class);
            if(CollectionUtils.isEmpty(resp.getData().getTsId())){
                return Result.success(data);
            }

            //查询展会信息
            List<TsRegConfirmVO.TsInfo> tsInfoList=queryTradeShowList(resp.getData().getTsId(),dto.getLanguage());
            data.setTsList(tsInfoList);

            //发送注册成功邮件
            TsRegSuccessDTO mailInfo = OrikaMapperUtil.coverObject(data, TsRegSuccessDTO.class);
            mailInfo.setEmail(dto.getEmail());
            if (!CollectionUtils.isEmpty(tsInfoList)) {
                mailInfo.setTsList(tsInfoList.stream().map(TsRegConfirmVO.TsInfo::getName).collect(Collectors.toList()));
            }
            mailInfo.setQrCode(QRCodeUtil.generateAsBase64(data.getVisitorId() + "|" + data.getCountryName()));
            tsUserFeign.sendIndonesiaRegSuccessMail(mailInfo);
            return new Result<>(resultCode.getCode(), resultCode.getMsg(), data);
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e){
            log.error("register trade show user occur exception, dto:{} ,detail:{}",dto,e.getMessage(),e);
            throw new BusinessException(ERR_CODE, e.getMessage());
        }
    }

    @Override
    @Logging(businessType = "TS_ID_REG", errorTag = "WEB_TS_ID_REG", logOnError = false)
    public Result<TsRegConfirmVO> indonesiaRegisterV2(WebTsIdUserRegV2DTO dto) {
        try{
            //注册展会用户
            TsUserRegDTO regDTO= OrikaMapperUtil.coverObject(dto,TsUserHKRegDTO.class);
            regDTO.setSsoGsSources(TsSSOGSRegEnum.OSF_Online_D.name());
            Result<TsUserVO> resp=tsUserFeign.idRegister(regDTO);

            //用户名字不匹配
            if(resp.getCode().equals(UserErrorEnum.TS_USER_NAME_NOT_MATCH.getCode())){
                throw new BusinessException(UserErrorEnum.TS_USER_NAME_NOT_MATCH);
            }

            if (resp.getCode().equals(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_EMPTY.getCode())
                    || resp.getCode().equals(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_FAIL.getCode())) {
                throw new BusinessException(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_FAIL);
            }

            if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode()) && !resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())){
                log.error("register trade show user return error, dto:{} ,resp:{}",dto,resp);
                throw new BusinessException(resp.getCode(), resp.getMsg());
            }

            IResultCode resultCode=ResultCode.CommonResultCode.SUCCESS;
            if(resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())){
                resultCode= UserErrorEnum.ALREADY_ALL_REG_TS;
            }

            //格式转换
            TsRegConfirmVO data = OrikaMapperUtil.coverObject(resp.getData(),TsRegConfirmVO.class);
            if(CollectionUtils.isEmpty(resp.getData().getTsId())){
                return Result.success(data);
            }

            //查询展会信息
            List<TsRegConfirmVO.TsInfo> tsInfoList=queryTradeShowList(resp.getData().getTsId(),dto.getLanguage());
            data.setTsList(tsInfoList);

            //发送注册成功邮件
            TsRegSuccessDTO mailInfo = OrikaMapperUtil.coverObject(data, TsRegSuccessDTO.class);
            mailInfo.setEmail(dto.getEmail());
            if (!CollectionUtils.isEmpty(tsInfoList)) {
                mailInfo.setTsList(tsInfoList.stream().map(TsRegConfirmVO.TsInfo::getName).collect(Collectors.toList()));
            }
            mailInfo.setQrCode(QRCodeUtil.generateAsBase64(data.getVisitorId() + "|" + data.getCountryName()));
            tsUserFeign.sendIndonesiaRegSuccessMail(mailInfo);
            return new Result<>(resultCode.getCode(), resultCode.getMsg(), data);
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e){
            log.error("register trade show user occur exception, dto:{} ,detail:{}",dto,e.getMessage(),e);
            throw new BusinessException(ERR_CODE, e.getMessage());
        }
    }

    @Override
    @Logging(businessType = "TS_ID_REG", errorTag = "WEB_TS_ID_REG", logOnError = false)
    public Result<TsRegConfirmVO> indonesiaRegisterV3(WebTsIdUserRegV3DTO dto) {
        try{
            //注册展会用户
            TsUserRegDTO regDTO= OrikaMapperUtil.coverObject(dto,TsUserHKRegDTO.class);
            regDTO.setSsoGsSources(TsSSOGSRegEnum.OSF_Online_D.name());
            Result<TsUserVO> resp=tsUserFeign.idRegister(regDTO);

            //用户名字不匹配
            if(resp.getCode().equals(UserErrorEnum.TS_USER_NAME_NOT_MATCH.getCode())){
                throw new BusinessException(UserErrorEnum.TS_USER_NAME_NOT_MATCH);
            }

            if (resp.getCode().equals(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_EMPTY.getCode())
                    || resp.getCode().equals(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_FAIL.getCode())) {
                throw new BusinessException(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_FAIL);
            }

            if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode()) && !resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())){
                log.error("register trade show user return error, dto:{} ,resp:{}",dto,resp);
                throw new BusinessException(resp.getCode(), resp.getMsg());
            }

            IResultCode resultCode=ResultCode.CommonResultCode.SUCCESS;
            if(resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())){
                resultCode= UserErrorEnum.ALREADY_ALL_REG_TS;
            }

            //格式转换
            TsRegConfirmVO data = OrikaMapperUtil.coverObject(resp.getData(),TsRegConfirmVO.class);
            if(CollectionUtils.isEmpty(resp.getData().getTsId())){
                return Result.success(data);
            }

            //查询展会信息
            List<TsRegConfirmVO.TsInfo> tsInfoList=queryTradeShowList(resp.getData().getTsId(),dto.getLanguage());
            data.setTsList(tsInfoList);

            //发送注册成功邮件
            TsRegSuccessDTO mailInfo = OrikaMapperUtil.coverObject(data, TsRegSuccessDTO.class);
            mailInfo.setEmail(dto.getEmail());
            if (!CollectionUtils.isEmpty(tsInfoList)) {
                mailInfo.setTsList(tsInfoList.stream().map(TsRegConfirmVO.TsInfo::getName).collect(Collectors.toList()));
            }
            mailInfo.setQrCode(QRCodeUtil.generateAsBase64(data.getVisitorId() + "|" + data.getCountryName()));
            tsUserFeign.sendIndonesiaRegSuccessMail(mailInfo);
            return new Result<>(resultCode.getCode(), resultCode.getMsg(), data);
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e){
            log.error("register trade show user occur exception, dto:{} ,detail:{}",dto,e.getMessage(),e);
            throw new BusinessException(ERR_CODE, e.getMessage());
        }
    }

    @Override
    @Logging(businessType = "TS_ID_REG", errorTag = "WEB_TS_ID_REG", logOnError = false)
    public Result<TsRegConfirmVO> indonesiaRegisterV4(WebTsIdUserRegV3DTO dto) {
        try{
            //注册展会用户
            TsUserRegDTO regDTO= OrikaMapperUtil.coverObject(dto,TsUserHKRegDTO.class);
            regDTO.setSsoGsSources(TsSSOGSRegEnum.OSF_Online_D.name());
            regDTO.setRegSource(TsRegSourceEnum.WEB.getSourceName());
            Result<TsUserVO> resp=tsUserFeign.idRegisterV1(regDTO);

            log.info("------ indonesiaRegisterV4, resp:{}", JSON.toJSONString(resp));
            //用户名字不匹配
            if(resp.getCode().equals(UserErrorEnum.TS_USER_NAME_NOT_MATCH.getCode())){
                throw new BusinessException(UserErrorEnum.TS_USER_NAME_NOT_MATCH);
            }

            if (resp.getCode().equals(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_EMPTY.getCode())
                    || resp.getCode().equals(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_FAIL.getCode())) {
                throw new BusinessException(UserErrorEnum.TS_REG_MAIL_VERIFICATION_CODE_FAIL);
            }

            if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode()) && !resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())){
                log.error("register trade show user return error, dto:{} ,resp:{}",dto,resp);
                throw new BusinessException(resp.getCode(), resp.getMsg());
            }

            IResultCode resultCode=ResultCode.CommonResultCode.SUCCESS;
            if(resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())){
                resultCode= UserErrorEnum.ALREADY_ALL_REG_TS;
            }

            //格式转换
            TsRegConfirmVO data = OrikaMapperUtil.coverObject(resp.getData(),TsRegConfirmVO.class);
            if(CollectionUtils.isEmpty(resp.getData().getTsId())){
                return Result.success(data);
            }

            log.info("------ indonesiaRegisterV4, getData:{}", JSON.toJSONString(resp.getData()));
            //查询展会信息
            List<TsRegConfirmVO.TsInfo> tsInfoList = getSimpleTsList(TradeShowQueryDTO.builder().lang(dto.getLanguage()).showType(TradeShowTypeEnums.ID).tsIds(resp.getData().getTsId()).build());
            data.setTsList(tsInfoList);

            //发送注册成功邮件
            TsRegSuccessDTO mailInfo = OrikaMapperUtil.coverObject(data, TsRegSuccessDTO.class);
            mailInfo.setEmail(dto.getEmail());
            if (!CollectionUtils.isEmpty(tsInfoList)) {
                mailInfo.setTsList(tsInfoList.stream().map(TsRegConfirmVO.TsInfo::getName).collect(Collectors.toList()));
            }
            mailInfo.setQrCode(QRCodeUtil.generateAsBase64(data.getVisitorId() + "|" + data.getCountryName()));
            log.info("------ indonesiaRegisterV4, mailInfo:{}", JSON.toJSONString(mailInfo));
            tsUserFeign.sendIndonesiaRegSuccessMail(mailInfo);
            return new Result<>(resultCode.getCode(), resultCode.getMsg(), data);
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e){
            log.error("register trade show user occur exception, dto:{} ,detail:{}",dto,e.getMessage(),e);
            throw new BusinessException(ERR_CODE, e.getMessage());
        }
    }

    @Override
    public Result<TsRegConfirmVO> getJoinTsInfo(Long confirmId,String lang) {
        Result<TsUserVO> resp = tsUserFeign.getJoinTsInfo(confirmId);
        if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
            log.error("get join trade show info return error, confirmId:{} ,resp:{}",confirmId,resp);
            return Result.error();
        }

        //格式转换
        TsRegConfirmVO data = OrikaMapperUtil.coverObject(resp.getData(),TsRegConfirmVO.class);
        if(CollectionUtils.isEmpty(resp.getData().getTsId())){
            return Result.success(data);
        }

        //查询展会信息
        List<TsRegConfirmVO.TsInfo> tsInfoList=queryTradeShowList(resp.getData().getTsId(), lang);
        data.setTsList(tsInfoList);
        return Result.success(data);
    }

    @Override
    public Result<TsUserVO> subscribe(TsRegPaDTO dto) {
        return tsUserFeign.subscribe(dto);
    }

    @Override
    public Result<List<String>> sendRecommendEmail(List<WebTsRecommendEmailDTO> dto) {
        List<TsRecommendEmailDTO> list=OrikaMapperUtil.coverList(dto,TsRecommendEmailDTO.class);

        return tsUserFeign.sendTsRecommendEmail(null, SourceEnum.WEB.name(), list);
    }

    @Override
    public Result<String> sendTsEdmRegEmail(WebTsUserRegEdmDTO tsUserRegEdmDTO, String lang) {
        log.info("edm reg send mail dto = {}, lang = {}", tsUserRegEdmDTO, lang);
        TsUserRegEdmDTO userRegEdmDTO = OrikaMapperUtil.coverObject(tsUserRegEdmDTO, TsUserRegEdmDTO.class);
        if (CollectionUtils.isEmpty(tsUserRegEdmDTO.getTradeShowIds())) {
            log.info("edm reg send mail dto = {}, tsIds is empty, lang = {}", tsUserRegEdmDTO, lang);
            Result<List<SeasonTradeShowVO>> listResult = getSeasonTradeShow(lang);
            if (ObjectUtils.isNotEmpty(listResult) && ObjectUtils.isNotEmpty(listResult.getData())) {
                List<Long> longList = listResult.getData().stream().map(SeasonTradeShowVO::getTradeshowId).collect(Collectors.toList());
                userRegEdmDTO.setTradeShowIds(longList);
            }
        }
        return tsUserFeign.sendTsEdmRegEmail(userRegEdmDTO);
    }

    @Override
    public Result<List<String>> sendEdmRecommendEmail(EdmTsRecommendDTO dto) {
        List<TsRecommendEmailDTO> list=OrikaMapperUtil.coverList(dto.getRecommendList(),TsRecommendEmailDTO.class);

        return tsUserFeign.sendTsRecommendEmail(dto.getInviteEmail(), SourceEnum.WEB.name(), list);
    }

    @Override
    public Result<Void> batchImportEdmRecommendEmail(MultipartFile file) {
        //导入格式:inviteEmail,lang,invitedEmail,title,firstName,lastName,companyName
        try {
            BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()));

            String row;
            int index=0;
            String recommendUri="/tradeShows/HK/register?source=TS_REG_REFERRAL";
            EdmTsRecommendDTO dto=new EdmTsRecommendDTO();
            WebTsRecommendEmailDTO recommend=new WebTsRecommendEmailDTO();
            dto.setRecommendList(Arrays.asList(recommend));

            while((row=reader.readLine())!=null){
                index+=1;
                //防止频繁调用
                if(index%10==0){
                    Thread.sleep(1000);
                }

                String[] data=row.split(",");
                dto.setInviteEmail(data[0]);
                recommend.setEmail(data[2]);
                recommend.setLang("EN");
                recommend.setTitle(data[3]);
                recommend.setFirstName(data[4]);
                recommend.setLastName(data[5]);
                recommend.setCompanyName(data[6]);
                recommend.setRecommendUri(recommendUri);
                sendEdmRecommendEmail(dto);
            }

            return Result.success();
        }catch (Exception e){
            log.error("batch send eDM ts reg recommend occur exception, detail:{}",e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<TsRegConfirmVO> vietnamRegister(TsVietnamRegDTO dto) {
        try {
            //注册展会用户
            TsUserRegDTO regDTO = OrikaMapperUtil.coverObject(dto, TsUserVnRegDTO.class);
            return vietnamRegister(regDTO);
        } catch (Exception e) {
            log.error("register trade show user occur exception, dto:{} ,detail:{}", dto, e.getMessage(), e);
            return Result.error();
        }
    }

    @Override
    public Result<TsRegConfirmVO> vietnamRegisterV2(TsVietnamRegV2DTO dto) {
        try {
            //注册展会用户
            TsUserRegDTO regDTO = OrikaMapperUtil.coverObject(dto, TsUserHKRegDTO.class);
            return vietnamRegister(regDTO);
        } catch (Exception e) {
            log.error("vietnam Register V2 occur exception, dto:{} ,detail:{}", dto, e.getMessage(), e);
            return Result.error();
        }
    }

    private Result<TsRegConfirmVO> vietnamRegister(TsUserRegDTO regDTO){
        try {
            boolean sendEmail = true;
            //注册展会用户
            regDTO.setSsoGsSources(TsSSOGSRegEnum.OSF_Online_D.name());
            regDTO.setRegSource(TsRegSourceEnum.WEB.getSourceName());
            Result<TsUserVO> resp = tsUserFeign.vietnamRegister(regDTO);

            //用户名字不匹配
            if (resp.getCode().equals(UserErrorEnum.TS_USER_NAME_NOT_MATCH.getCode())) {
                return Result.failed(UserErrorEnum.TS_USER_NAME_NOT_MATCH);
            }

            //活动已经结束
            if (resp.getCode().equals(UserErrorEnum.TS_REG_ACT_ALREADY_OVER.getCode())) {
                return Result.failed(UserErrorEnum.TS_REG_ACT_ALREADY_OVER);
            }

            if (!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode()) && !resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())) {
                log.error("register trade show user return error, dto:{} ,resp:{}", regDTO, resp);
                return Result.error();
            }

            IResultCode resultCode = ResultCode.CommonResultCode.SUCCESS;
            if (resp.getCode().equals(UserErrorEnum.ALREADY_ALL_REG_TS.getCode())) {
                sendEmail = false;
                resultCode = UserErrorEnum.ALREADY_ALL_REG_TS;
            }

            //格式转换
            TsRegConfirmVO data = OrikaMapperUtil.coverObject(resp.getData(), TsRegConfirmVO.class);
            if (CollectionUtils.isEmpty(resp.getData().getTsId())) {
                return Result.success(data);
            }

            //查询展会信息
            List<TsRegConfirmVO.TsInfo> tsInfoList = getSimpleTsList(TradeShowQueryDTO.builder().lang(regDTO.getLanguage()).showType(TradeShowTypeEnums.VI).tsIds(resp.getData().getTsId()).build());
            data.setTsList(tsInfoList);

            //发送注册成功邮件
            if (sendEmail) {
                TsRegSuccessDTO mailInfo = OrikaMapperUtil.coverObject(data, TsRegSuccessDTO.class);
                mailInfo.setEmail(regDTO.getEmail());
                mailInfo.setQrCode(QRCodeUtil.generateAsBase64(data.getVisitorId() + "|" + data.getCountryName()));
                if (!CollectionUtils.isEmpty(tsInfoList)) {
                    SimpleDateFormat sFormat = new SimpleDateFormat("MMMM dd", Locale.US);
                    SimpleDateFormat eFormat = new SimpleDateFormat("dd, yyyy", Locale.US);
                    List<String> tsList = Lists.newArrayList();
                    tsInfoList.forEach(ts ->
                            tsList.add(ts.getName() + "|" + sFormat.format(ts.getStartDate()) + " - " + eFormat.format(ts.getEndDate()))
                    );
                    mailInfo.setTsList(tsList);
                }
                tsUserFeign.sendVietnamRegSuccessMail(mailInfo);
            }

            return new Result<>(resultCode.getCode(), resultCode.getMsg(), data);
        } catch (Exception e) {
            log.error("vietnam Register V2 occur exception, dto:{} ,detail:{}", regDTO, e.getMessage(), e);
            return Result.error();
        }
    }

    @Override
    public Result<Boolean> checkVietnamAct() {
        return tsUserFeign.checkVietnamRegTime();
    }

    @Override
    public Result<Void> updateVietnamUserAdditionalInfo(WebVnAdditionalInfoDTO dto) {
        VnAdditionalInfoDTO additionalInfo=OrikaMapperUtil.coverObject(dto,VnAdditionalInfoDTO.class);

        //更新越南展用户附加信息
        Result<Void> resp=tsUserFeign.updateVietnamAdditionalInfo(additionalInfo);
        if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
            log.error("update vietnam user additional info return error, dto:{}, resp:{}",dto,resp);
            return Result.error();
        }

        if(CollectionUtils.isEmpty(dto.getMagazineList())){
            return Result.success();
        }

        //更新杂志订阅
        TsRegPaDTO paParam=new TsRegPaDTO();
        paParam.setConfirmId(dto.getConfirmationId());
        paParam.setOpenPA(true);
        paParam.setMagazineList(dto.getMagazineList());
        Result<TsUserVO> subsResp= tsUserFeign.subscribe(paParam);
        if(!subsResp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
           log.error("vietnam user subscribe pa return error, paParam:{},subsResp:{}",paParam,subsResp);
        }

        return Result.success();
    }

    @Override
    public Result<TsReferralInfoVO> queryReferralInfo(String referralId) {
        return tsUserFeign.getRegReferralInfo(referralId);
    }

    @Override
    public Result<Boolean> resendMail(String emailAddr) {
        try{
            log.info("resendMail, emailAddr :{}",emailAddr);

            List<HongKongSeasonTradeShowVO> tsInfo = ResultUtil.getData(hongKongTradeShowFeign.hkSeasonList(LanguageDicEnum.EN_US.getValue()));
            log.info("resendMail tsInfo :{}",tsInfo);

            List<Long> allTsIds = tsInfo.stream().flatMap(phase -> phase.getList().stream()).map(SeasonTradeShowV2VO::getTradeshowId).collect(Collectors.toList());
            Result<TsUserVO> resp=tsUserFeign.resendMail(ResendMailDTO.builder().emailAddr(emailAddr).tsIds(allTsIds).build());
            log.info("resendMail TsUserVO :{}",resp);

            //格式转换
            TsRegConfirmVO data = OrikaMapperUtil.coverObject(resp.getData(),TsRegConfirmVO.class);
            if(ObjectUtils.isEmpty(resp.getData())){
                return Result.success(UserErrorEnum.TS_NO_REGISTER.getCode(), null, false);
            }

            //查询展会信息
            List<TradeShowVO> regTsInfoList=ResultUtil.getData(hongKongTradeShowFeign.tradeShowList(TradeShowListDTO.builder().tradeShowIdList(resp.getData().getTsId()).build()));

            log.info("resendMail regTsInfoList :{}",regTsInfoList);

            //发送注册成功邮件
            TsRegSuccessDTO mailInfo = OrikaMapperUtil.coverObject(data, TsRegSuccessDTO.class);
            mailInfo.setSeasonName(seasonTimeTitle);
            mailInfo.setEmail(emailAddr);
            mailInfo.setBuyerType(data.getBuyerType());
            mailInfo.setQrCode(QRCodeUtil.generateAsBase64(data.getVisitorId() + "|" + data.getCountryName()));
            mailInfo.setSource(data.getSource());
            mailInfo.setTradeShowList(getRegTsInfoForMail(tsInfo, regTsInfoList));
            if (CollectionUtils.isNotEmpty(regTsInfoList)) {
                mailInfo.setTsList(regTsInfoList.stream().map(TradeShowVO::getTsName).collect(Collectors.toList()));
            }
            log.info("resendMail mailInfo :{}",mailInfo);

            tsUserFeign.sendRegSuccessMail(mailInfo);
            return Result.success(true);
        }catch (Exception e){
            log.error("register trade show user occur exception, dto:{} ,detail:{}",emailAddr,e.getMessage(),e);
            return Result.error();
        }
    }

    private List<TsRegConfirmVO.TsInfo> queryTradeShowList(List<Long> list,String lang){
        //查找展会信息信息
        Result<List<SeasonTradeShowDTO>> queryResp=tradeshowAggFeign.getTradeShowByIds(list,lang);

        if(!queryResp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
            log.error("query trade show list return error, list:{},resp:{}",list,queryResp);
            return new ArrayList<>();
        }

        return queryResp.getData().stream().map(e->{
            TsRegConfirmVO.TsInfo vo=new TsRegConfirmVO.TsInfo();
            vo.setName(e.getTsName());
            vo.setTsId(e.getTradeshowId());
            vo.setTsGrpCode(e.getTsGrpCode());
            return vo;
        }).collect(Collectors.toList());
    }

    private List<TsRegConfirmVO.TsInfo> getSimpleTsList(TradeShowQueryDTO dto) {
        //查找展会信息信息
        Result<List<TsInfoVO>> queryResp = tsCommonFeign.getSimpleTsList(dto);

        if (!queryResp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
            log.error("------ getSimpleTsList error, dto:{},resp:{}", dto, queryResp);
            return new ArrayList<>();
        }

        return OrikaMapperUtil.coverList(queryResp.getData(), TsRegConfirmVO.TsInfo.class);
    }
}
