package com.globalsources.bff.web.tradeshow.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix= "ts.hk", ignoreInvalidFields = true)
public class HkShowProperties {
    private String title;
    private Long startTime;
    private Long endTime;
    private String closeTip;
    @Value("${ts.hk.recommend.startTime:}")
    private Long recommendStartTime;
    @Value("${ts.hk.recommend.endTime:}")
    private Long recommendEndTime;
}
