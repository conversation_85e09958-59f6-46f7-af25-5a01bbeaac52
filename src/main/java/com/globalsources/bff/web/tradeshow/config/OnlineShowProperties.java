package com.globalsources.bff.web.tradeshow.config;

import com.globalsources.bff.web.tradeshow.dto.OsTradeShowInfoDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/2 10:56
 */
@Component
@Data
@ConfigurationProperties(prefix= "ts.online-show", ignoreInvalidFields = true)
public class OnlineShowProperties {

    private Map<String, List<Long>> supplierIdByTsKeyMap;

    private Map<String, List<Long>> productIdByTsKeyMap;

    private Map<String, OsTradeShowInfoDTO> tsInfoMap;



}
