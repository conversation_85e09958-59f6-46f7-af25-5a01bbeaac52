package com.globalsources.bff.web.tradeshow.service;

import com.globalsources.bff.web.tradeshow.vo.TSProductDetailVO;
import com.globalsources.bff.web.tradeshow.vo.TradeShowProductVO;
import com.globalsources.framework.vo.UserVO;

import java.util.List;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.bff.web.tradeshow.service
 * @date:2021/7/20
 */
public interface TradeShowProductService {
    List<TradeShowProductVO> queryProductByIds(List<Long> productIds);

    TSProductDetailVO productDetail(Long productId);

    List<TradeShowProductVO> recommendProduct(UserVO userVO, Long categoryId,String anonymousId);
}
