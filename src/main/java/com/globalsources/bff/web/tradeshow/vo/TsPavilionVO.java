package com.globalsources.bff.web.tradeshow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/6/16 10:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="Pavilion VO", description="")
public class TsPavilionVO implements Serializable {

    @ApiModelProperty(value = "Tradeshow id")
    private Long tsId;

    @ApiModelProperty(value = "Pavilion Code")
    private String pavilionCode;

    @ApiModelProperty(value = "Pavilion English Description")
    private String pavilionDescEN;

    @ApiModelProperty(value = "Pavilion Chinese Description")
    private String pavilionDescCN;

    @ApiModelProperty(value = "Pavilion Image Url")
    private String imgUrl;

    private Date lUpdDate;

}
