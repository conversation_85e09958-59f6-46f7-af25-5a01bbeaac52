package com.globalsources.bff.web.tradeshow.vo.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Title: HighlightProduct
 * @Author: Johann
 * @Description: HighlightProduct
 * @date 2021/06/11 - 10:50
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OsHighlightProductVO {

    @ApiModelProperty(value = "商品id")
    private Long productId;

    @ApiModelProperty(value = "商品名称,会根据不同的语言,返回不同的")
    private String productName;

    @ApiModelProperty(value = "商品主图,如果有视频,则是视频地址")
    private String productPrimaryImage;

    @ApiModelProperty(value = "SEO优化使用的产品详情地址")
    private String desktopProductDetailUrl;

    private Boolean videoFlag;

    @ApiModelProperty("L4 category id")
    private Long categoryId;
}
