package com.globalsources.bff.web.tradeshow.vo.supplier;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Title: SupplierInfo
 * @Author: Johann
 * @Description: SupplierInfo
 * @date 2021/06/11 - 10:52
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OsSupplierInfoVO {

    @ApiModelProperty(value = "供应商id")
    private Long orgId;

    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商公司显示名称")
    private String websiteName;

    @ApiModelProperty(value = "供应商公司显示名称")
    private String companyName;

    @ApiModelProperty(value = "供应商")
    private String logoUrl;

    @ApiModelProperty(value = "供应商位置")
    private String supplierLocation;

    @ApiModelProperty(value = "供应商等级 (-2~6,前端显示不同的图标, >=0 付费供应商)")
    private Integer supplierRank;

    @ApiModelProperty(value = "环球资源年限")
    private String gsYears;

    @ApiModelProperty(value = "供应商主要卖点")
    private String usp;

    @ApiModelProperty(value = "供应商主营商品分类")
    private String mainProducts;

    @ApiModelProperty(value = "business type")
    private String businessTypes;

    @ApiModelProperty(value = "供应商公司员工数")
    private String employees;

    @ApiModelProperty(value = "出口市场")
    private String exportMarket;

    @ApiModelProperty(value = "供应商展会信息")
    private String tradeShow;

    @ApiModelProperty(value = "是否是历史展会状态,默认false")
    private Boolean historical;

    @ApiModelProperty("SEO, 供应商自定义域名")
    private String supplierShortName;

    @ApiModelProperty("SEO, supplierType: FL/AGG/ADV")
    private String supplierType;

    @ApiModelProperty("新合同类型, AGG: P0050, 旧P0-P6: P0100/P1100/P2100/P3400/P4100/P5100/P6100, 新P3-P6(标准 NEWPSD, 高级 NEWPAD, 超级 NEWPSP, 至尊 NEWPVP): P3200/P4500/P5500/P6500")
    private String contractCode;

    @ApiModelProperty("合同对应 group code，具体值看DB数据")
    private String contractGroupCode;

    @ApiModelProperty("会员类型编号, agg:500,p0:1000,basic会员:2000,标准会员:3000,高级会员:4000,超级会员:5000,至尊会员:6000")
    private Integer memberTypeNum;
}
