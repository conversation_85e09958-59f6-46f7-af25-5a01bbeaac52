package com.globalsources.bff.web.tradeshow.vo;

import com.globalsources.framework.vo.ProductTrackingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.bff.web.tradeshow.vo
 * @date:2021/7/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "TSProductDetailVO")
public class TSProductDetailVO {

    @ApiModelProperty(value = "产品id,自增")
    private Long productId;
    @ApiModelProperty(value = "new gsol 产品url")
    private String productUrl;
    @ApiModelProperty(value = "产品名称及简短描述")
    private String productName;

    @ApiModelProperty("RFI form 地址")
    private String inquireUrl = "";
    @ApiModelProperty(value = "产品型号")
    private String modelNumber;
    @ApiModelProperty(value = "环球资源产品类别")
    private Long categoryId;

    @ApiModelProperty("给前端显示的价格")
    private String listVoShowPriceStr = "";
    @ApiModelProperty("视频标签")
    private Boolean videoFlag;
    @ApiModelProperty("视频地址")
    private String productVideoUrl = "";
    @ApiModelProperty("产品主图")
    private String primaryImageUrl;

    @ApiModelProperty(value = "最小订单量")
    private Integer minOrderQuantity;

    @ApiModelProperty(value = "单位(最小订单量）")
    private String minOrderUnit;

    @ApiModelProperty(value = "单数单位(最小订单量）")
    private String minOrderSingleUnit;

    @ApiModelProperty("交货期 （天数下限）")
    private Integer orderLeadTimeRangeLow;
    @ApiModelProperty("货期（天数上限）")
    private Integer orderLeadTimeRangeUp;

    @ApiModelProperty(value = "上一年销售额")
    private String totalAnnualSales;
    @ApiModelProperty(value = "成立年份")
    private String yearEstablished;
    @ApiModelProperty(value = "出口市场")
    private String exportMarkets;
    @ApiModelProperty(value = "员工总人数")
    private String totalNoEmployees;
    @ApiModelProperty("产品是否过期， 是-true，否-false")
    private Boolean expiredFlag = false;
    @ApiModelProperty("产品供应商是否未认证， 是-true，否-false")
    private Boolean unverifiedFlag = false;

    @ApiModelProperty("产品列表中的tracking info")
    private ProductTrackingVO productTrackingVO;
}
