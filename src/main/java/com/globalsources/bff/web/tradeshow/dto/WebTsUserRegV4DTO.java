package com.globalsources.bff.web.tradeshow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class WebTsUserRegV4DTO {

    @ApiModelProperty("推荐id")
    private String referralId;

    @ApiModelProperty("来源渠道")
    @Size(max = 100, message = "UTM length cannot exceed 100 characters")
    private String utm;

    @ApiModelProperty("展会id列表")
    private List<Long> tradeShowIds;

    /*Registration Type*/
    @ApiModelProperty("buyer type")
    private String buyerType;

    /*Name */
    @ApiModelProperty("Name:Title")
    @NotBlank(message = "Title is required. Please enter information")
    @Size(max = 6, message = "Title length cannot exceed 6 characters")
    private String title;

    @ApiModelProperty("Name:FirstName")
    @NotBlank(message = "First name is required. Please enter information")
    @Size(max = 35, message = "First name length cannot exceed 35 characters")
    private String firstName;

    @ApiModelProperty("Name:LastName")
    @NotBlank(message = "Last name is required. Please enter information")
    @Size(max = 35, message = "Last name length cannot exceed 35 characters")
    private String lastName;

    /*Business Email */
    @ApiModelProperty("Business Email")
    @NotBlank(message = "Mailbox cannot be null")
    @Email(message = "The mailbox format is incorrect")
    @Size(max = 100, message = "mailbox length cannot exceed 100 characters")
    private String email;

    /*Country/Region */
    @ApiModelProperty("Country/Region")
    @NotBlank(message = "Country code is required. Please enter information")
    @Size(max = 6, message = "Country code length cannot exceed 6 characters")
    private String countryCode;


    @ApiModelProperty("Mobile dial Country Code")
    @NotBlank(message = "Mobile dial country code is required. Please enter information")
    @Size(max = 6, message = "Mobile dial country code length cannot exceed 6 characters")
    private String mobileCountryCode;

    /*Mobile Phone Number */
    @ApiModelProperty("Mobile country")
    @NotBlank(message = "Mobile country is required. Please enter information")
    @Size(max = 6, message = "Mobile country length cannot exceed 4 characters")
    private String mobileCountry;

    @ApiModelProperty("Mobile number")
    @NotBlank(message = "Mobile number is required. Please enter information")
    @Size(max = 20, message = "Mobile number length cannot exceed 20 characters")
    private String mobileNumber;

    /*Company Name */
    @ApiModelProperty("Company Name")
    @NotBlank(message = "Company name is required. Please enter information")
    @Size(max = 100, message = "Company name length cannot exceed 100 characters")
    private String companyName;

    /*What is your company's business type? (please choose all that apply)*/
    @ApiModelProperty("What is your company's business type:businessType")
    @NotEmpty(message = "What is your company's business type:businessType is required. Please enter information")
    private List<String> businessTypes;

    @ApiModelProperty("What is your company's business type:customBusinessType")
    @Size(max = 70, message = "Custom business type length cannot exceed 70 characters")
    private String customBusinessType;

    @ApiModelProperty("What is your company's business type:onlineStoreUrl")
    private String onlineStoreUrl;

    /*What is the major business type of your company? (please choose the most suitable answer) */
    @ApiModelProperty("What is the major business type of your company? (please choose the most suitable answer)")
    @NotBlank(message = "What is the major business type of your company is required. Please enter information")
    private String businessType;

    /*What is the annual sourcing amount of your company? (USD)  */
    @ApiModelProperty("What is the annual sourcing amount of your company")
    private String annualSourcingValue;

    /*What is the annual sourcing amount of your business unit/department? */
    @ApiModelProperty("What is the annual sourcing amount of your business unit/department")
    private String annualSourcingAmountOfBusinessUnit;

    @ApiModelProperty("语言")
    private String language;

    @ApiModelProperty("verification code")
    private Integer verificationCode;
}
