package com.globalsources.bff.web.tradeshow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class TsVietnamRegDTO {

    @ApiModelProperty("来源渠道")
    @Size(max = 100, message = "UTM length cannot exceed 100 characters")
    private String utm;

    @ApiModelProperty("语言")
    private String language;

    /*****  注册基本信息 *****/

    @ApiModelProperty("职位名字")
    @Size(max = 50, message = "Job function length cannot exceed 50 characters")
    private String jobFunction;

    @ApiModelProperty("职级别")
    @Size(max = 50, message = "Job level length cannot exceed 50 characters")
    private String jobLevel;

    @ApiModelProperty("自定义职称")
    @Size(max = 100, message = "Custom job title length cannot exceed 100 characters")
    private String customJobTitle;

    @ApiModelProperty("名字")
    @Size(max = 35, message = "First name length cannot exceed 35 characters")
    private String firstName;

    @ApiModelProperty("姓氏")
    @Size(max = 35, message = "Last name length cannot exceed 35 characters")
    private String lastName;

    @ApiModelProperty("邮箱")
    @Email(message = "The mailbox format is incorrect")
    @Size(max = 100, message = "mailbox length cannot exceed 100 characters")
    private String email;

    @ApiModelProperty("城市")
    @Size(max = 35, message = "City length cannot exceed 35 characters")
    private String city;

    @ApiModelProperty("性别称呼")
    @Size(max = 6, message = "Title length cannot exceed 6 characters")
    private String title;

    @ApiModelProperty("国家码")
    @Size(max = 6, message = "Country code length cannot exceed 6 characters")
    private String countryCode;

    @ApiModelProperty("省份")
    @Size(max = 6, message = "State length cannot exceed 6 characters")
    private String state;

    @ApiModelProperty("地址1")
    @Size(max = 100, message = "Address1 length cannot exceed 100 characters")
    private String address1;

    @ApiModelProperty("地址2")
    @Size(max = 100, message = "Address2 length cannot exceed 100 characters")
    private String address2;

    @ApiModelProperty("地址3")
    @Size(max = 100, message = "Address3 length cannot exceed 100 characters")
    private String address3;

    @ApiModelProperty("手机国家码")
    @Size(max = 4, message = "Mobile country length cannot exceed 4 characters")
    private String mobileCountry;

    @ApiModelProperty("手机号码")
    @Size(max = 20, message = "Mobile number length cannot exceed 30 characters")
    private String mobileNumber;

    @ApiModelProperty("公司名字")
    @Size(max = 100, message = "Company name length cannot exceed 100 characters")
    private String companyName;

    @ApiModelProperty("邮编号码")
    @Size(max = 100, message = "Post code length cannot exceed 100 characters")
    private String postCode;

    @ApiModelProperty("公司类型")
    @Size(max = 15, message = "Business type length cannot exceed 4 characters")
    private String businessType;

    @ApiModelProperty("自定义公司类型")
    @Size(max = 100, message = "Custom business type length cannot exceed 100 characters")
    private String customBusinessType;

    @ApiModelProperty("在线店铺url")
    @Size(max = 60, message = "Online store url length cannot exceed 60 characters")
    private String onlineStoreUrl;

    @ApiModelProperty("公司网址")
    @Size(max = 60, message = "Company website url length cannot exceed 60 characters")
    private String companyWebsiteURL;

    @ApiModelProperty("年度销售额")
    private String annualSourcingValue;

    @ApiModelProperty("出口区域列表")
    private List<String> marketYouSellTo;

    @ApiModelProperty("关注经销商类别列表")
    private List<String> supplierLookingFor;

    @ApiModelProperty("展会产品Pref列表")
    private List<ProductPref> productPrefList;

    @Data
    public static class ProductPref{
        @ApiModelProperty("产品prof codeId")
        private String codeId;
        @ApiModelProperty("codeId所对应的名字")
        private String name;
    }
}
