package com.globalsources.bff.web.tradeshow.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TsRegConfirmVO {
    @ApiModelProperty("名字")
    private String firstName;
    @ApiModelProperty("姓氏")
    private String lastName;
    @ApiModelProperty("公司名字")
    private String companyName;
    @ApiModelProperty("展会列表")
    private List<TsInfo> tsList;
    @ApiModelProperty("确认码")
    private Long confirmId;
    @ApiModelProperty("visitorId")
    private String visitorId;
    @ApiModelProperty("展会列表")
    private String barCodeImg;
    @ApiModelProperty("国家")
    private String countryName;
    @ApiModelProperty("SSO注册标志")
    private Boolean alreadyRegSSO;
    @ApiModelProperty("buyerType")
    private String buyerType;
    private String source;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;
    private Long regBatchId;
    @ApiModelProperty("二维码图片")
    private String qrCodeImg;

    @Data
    public static class TsInfo{
        @ApiModelProperty("展会id")
        private Long tsId;
        @ApiModelProperty("展会名字")
        private String name;
        @ApiModelProperty("展会组code")
        private String tsGrpCode;
        @ApiModelProperty("开始时间")
        private Date startDate;
        @ApiModelProperty("结束时间")
        private Date endDate;
    }
}
