package com.globalsources.bff.web.tradeshow.service.impl;

import com.globalsources.agg.supplier.api.feign.SupplierSeoAggFeign;
import com.globalsources.agg.supplier.api.feign.SupplierVideoAggFeign;
import com.globalsources.agg.supplier.api.model.dto.request.seo.SuppSeoInfoQueryAggDTO;
import com.globalsources.agg.supplier.api.model.dto.request.video.CompanyVideoQueryAggDTO;
import com.globalsources.agg.supplier.api.model.dto.seo.SuppSeoInfoAggDTO;
import com.globalsources.agg.supplier.api.model.dto.video.SimpleCompanyVideoDTO;
import com.globalsources.bff.web.tradeshow.service.TradeShowSupplierService;
import com.globalsources.bff.web.tradeshow.vo.TradeShowSupplierVO;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.utils.SupplierUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Chen
 * @date 2021/7/26 10:13
 */
@Slf4j
@Service
public class TradeShowSupplierServiceImpl implements TradeShowSupplierService {

    @Autowired
    private SupplierSeoAggFeign supplierSeoAggFeign;

    @Autowired
    private SupplierVideoAggFeign supplierVideoAggFeign;

    @Override
    public List<TradeShowSupplierVO> getSupplierInfoForTsWebsiteByIds(List<Long> supplierIds) {
        List<TradeShowSupplierVO> result = getTradeShowSupplierVos(supplierIds);
        result.stream().filter(Objects::nonNull).forEach(vo ->
                //orgId 200xxx -> 600xxx
                vo.setSupplierId(SupplierUtil.convertPscOrgIdToGsolOrgId(vo.getSupplierId()))
        );
        return result;
    }

    @Override
    public List<TradeShowSupplierVO> getTradeShowSupplierVos(List<Long> supplierIds) {
        List<TradeShowSupplierVO> result = new ArrayList<>();
        supplierIds = Optional.ofNullable(supplierIds).map(list -> list.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList())).orElse(null);
        if (CollectionUtils.isNotEmpty(supplierIds)) {
            //600前缀的orgId 转 200
            supplierIds = supplierIds.stream().filter(Objects::nonNull).map(SupplierUtil::convertGsolOrgIdToPscOrgId).collect(Collectors.toList());
            Result<List<SuppSeoInfoAggDTO>> suppSeoInfoDtoBatch = supplierSeoAggFeign.getSuppSeoInfoDtoBatch(SuppSeoInfoQueryAggDTO.builder().supplierIds(supplierIds).homepageUrlFlag(true).build());
            Result<List<SimpleCompanyVideoDTO>> companyVideoResult = supplierVideoAggFeign.getSimpleCompanyVideoBatch(CompanyVideoQueryAggDTO.builder().supplierIds(supplierIds).build());
            List<SimpleCompanyVideoDTO> videoDtoList = ResultUtil.getData(companyVideoResult);
            Map<Long, SimpleCompanyVideoDTO> videoMap = Optional.ofNullable(videoDtoList).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull)
                    .collect(Collectors.toMap(SimpleCompanyVideoDTO::getSupplierId, dto -> dto, (oldBean, newBean) -> oldBean));
            List<SuppSeoInfoAggDTO> data = ResultUtil.getData(suppSeoInfoDtoBatch);
            if (CollectionUtils.isNotEmpty(data)) {
                data.stream().forEach(dto -> {
                    SimpleCompanyVideoDTO simpleCompanyVideoDTO = videoMap.get(dto.getSupplierId());
                    if (Objects.nonNull(simpleCompanyVideoDTO) && StringUtils.isNotEmpty(simpleCompanyVideoDTO.getCoverImageUrl())
                            && StringUtils.isNotEmpty(simpleCompanyVideoDTO.getVideoUrl())) {
                        TradeShowSupplierVO vo = new TradeShowSupplierVO();
                        vo.setCompanyName(dto.getCompanyName());
                        vo.setSupplierId(dto.getSupplierId());
                        vo.setSupplierHomePageUrl(dto.getHomepageUrl());
                        vo.setSupplierType(dto.getSupplierType());
                        vo.setSupplierShortName(dto.getSupplierShortName());
                        vo.setMaxContractLevel(dto.getMaxContractLevel());
                        vo.setVideoUrl(simpleCompanyVideoDTO.getVideoUrl());
                        vo.setVideoCoverUrl(simpleCompanyVideoDTO.getCoverImageUrl());
                        result.add(vo);
                    }
                });
            }
        }
        return result;
    }
}
