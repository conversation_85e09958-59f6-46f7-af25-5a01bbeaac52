package com.globalsources.bff.web.tradeshow.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <a>Title: HongKongSeasonTradeShowVO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2022/11/28-16:27
 */
@Data
public class HongKongSeasonTradeShowVO implements Serializable {

    private String seasonDate;

    private String exhibitionStage;

    private Boolean isExhibitionFinish = false;

    private List<SeasonTradeShowV2VO> list;
}
