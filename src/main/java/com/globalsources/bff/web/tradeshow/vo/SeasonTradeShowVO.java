package com.globalsources.bff.web.tradeshow.vo;

import com.globalsources.user.api.vo.TsProductPrefVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SeasonTradeShowVO {
    private Long tradeshowId;
    private String tsName;
    private String tsGrpCode;
    private String tsStartTime;
    private String tsEndTime;
    private Date recommendStartDate;
    private Date recommendEndDate;
    private List<TsProductPrefVO> productPrefList;
    private Date htRecommendStartDate;
    private Date htRecommendEndDate;
}
