package com.globalsources.bff.web.comment.service.impl;

import com.globalsources.bff.web.comment.service.OrderCommentService;
import com.globalsources.comment.dto.OrderCommentDTO;
import com.globalsources.comment.feign.OrderCommentAggFeign;
import com.globalsources.comment.vo.CommentProductVO;
import com.globalsources.comment.vo.OrderCommentVO;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.translator.Translator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderCommentServiceImpl implements OrderCommentService {

    @Autowired
    private OrderCommentAggFeign orderCommentAggFeign;

    @Resource
    private Translator translator;

    @Override
    public Boolean submitOrderComment(List<OrderCommentDTO> orderCommentDTOList) {
        Result result = orderCommentAggFeign.submitOrderComment(orderCommentDTOList);
        if (!Result.success().getCode().equals(result.getCode())) {
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        return (Boolean) result.getData();
    }


    @Override
    public PageResult<OrderCommentVO> pendingReviewsByBuyer(Integer pageNum, Integer pageSize, String language) {
        PageResult<OrderCommentVO> pageResult = orderCommentAggFeign.pendingReviewsByBuyer(pageNum, pageSize);
        trans(language, pageResult);
        return pageResult;
    }

    @Override
    public PageResult<OrderCommentVO> reviewedByBuyer(Integer pageNum, Integer pageSize, String language) {
        PageResult<OrderCommentVO> pageResult = orderCommentAggFeign.reviewedByBuyer(pageNum, pageSize);
        trans(language, pageResult);
        return pageResult;
    }

    private void trans(String language, PageResult<OrderCommentVO> pageResult) {
        if (pageResult != null && !CollectionUtils.isEmpty(pageResult.getList()) && !LanguageDicEnum.EN_US.getValue().equals(language)) {
            List<OrderCommentVO> list = pageResult.getList();
            List<OrderCommentVO> collect = list.stream().filter(orderCommentVO -> orderCommentVO.getProductVOList().size() == 1).collect(Collectors.toList());
            List<String> strings = new ArrayList<>();
            collect.forEach(orderCommentVO -> strings.add(orderCommentVO.getProductVOList().get(0).getProductName()));

            Map<String, String> map = translator.litePerform(strings, LanguageDicEnum.getLanguageDicEnumByValue(language));

            for (OrderCommentVO commentVO : collect) {
                CommentProductVO commentProductVO = commentVO.getProductVOList().get(0);
                String productName = commentProductVO.getProductName();
                commentProductVO.setProductName(map.get(productName));
            }

        }
    }
}
