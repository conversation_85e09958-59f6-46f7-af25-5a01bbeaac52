package com.globalsources.bff.web.comment.service.impl;


import com.globalsources.bff.web.comment.service.CommentService;
import com.globalsources.bff.web.comment.vo.ProductDetailCommentReplyVO;
import com.globalsources.bff.web.comment.vo.ProductDetailCommentVO;
import com.globalsources.comment.dto.CommentAggSearchDTO;
import com.globalsources.comment.dto.CommentScoreSummarySearchAggDTO;
import com.globalsources.comment.feign.CommentAggFeign;
import com.globalsources.comment.vo.CommentAggVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.utils.ResultUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * <AUTHOR>
 * @since 2021-08-12
 */
@Slf4j
@Service
public class CommentServiceImpl implements CommentService {

    @Autowired
    private CommentAggFeign commentAggFeign;

    @Override
    public PageResult<ProductDetailCommentVO> getProductCommentList(Integer pageNum, Integer pageSize, Long productId) {
        CommentAggSearchDTO commentAggSearchDTO = new CommentAggSearchDTO();
        commentAggSearchDTO.setProductId(productId);
        commentAggSearchDTO.setPublishStatus("Published");

        PageResult<CommentAggVO> commentAggVOPageResult = ResultUtil.getData(commentAggFeign.search(pageNum, pageSize, commentAggSearchDTO));

        PageResult<ProductDetailCommentVO> productDetailCommentVOPageResult = new PageResult<>();
        BeanUtils.copyProperties(commentAggVOPageResult, productDetailCommentVOPageResult);

        List<ProductDetailCommentVO> productDetailCommentVOList = Lists.newArrayList();
        productDetailCommentVOPageResult.setList(productDetailCommentVOList);

        List<CommentAggVO> commentAggVOList = commentAggVOPageResult.getList();
        if (!CollectionUtils.isEmpty(commentAggVOList)) {
            commentAggVOList.forEach(commentAggVO -> {
                ProductDetailCommentVO productDetailCommentVO = new ProductDetailCommentVO();
                productDetailCommentVO.setCommentId(commentAggVO.getCommentId());
                productDetailCommentVO.setScore(commentAggVO.getScore());

                commentAggVO.getCommentMsgAggVOList().forEach(commentMsgAggVO -> {
                    if (commentMsgAggVO.getParentMsgId() == null) {
                        productDetailCommentVO.setContent(commentMsgAggVO.getMsgContent());
                        productDetailCommentVO.setCountryName(commentMsgAggVO.getCountryName());
                        productDetailCommentVO.setPhoto(commentMsgAggVO.getUserAvatarUrl());
                        productDetailCommentVO.setOriginalUserName(commentMsgAggVO.getUserName());
                        productDetailCommentVO.setUserName(Optional.ofNullable(commentMsgAggVO.getUserName()).map(name -> name.charAt(0) + "**********").orElse(""));
                        productDetailCommentVO.setCreateDate(commentMsgAggVO.getCreateDate());
                        productDetailCommentVO.setCreateBy(commentMsgAggVO.getCreateBy());
                        productDetailCommentVO.setCountryImageUrl(commentMsgAggVO.getCountryImageUrl());
                    } else {
                        ProductDetailCommentReplyVO productDetailCommentReplyVO = new ProductDetailCommentReplyVO();
                        productDetailCommentReplyVO.setSupplierId(commentAggVO.getSupplierId());
                        productDetailCommentReplyVO.setCompanyName(commentAggVO.getCompanyName());
                        productDetailCommentReplyVO.setCompanyLogoUrl(commentAggVO.getCompanyLogoUrl());
                        productDetailCommentReplyVO.setCreateDate(commentMsgAggVO.getCreateDate());
                        productDetailCommentReplyVO.setContent(commentMsgAggVO.getMsgContent());

                        productDetailCommentVO.setProductDetailCommentReplyVO(productDetailCommentReplyVO);
                    }
                });

                productDetailCommentVOList.add(productDetailCommentVO);
            });
        }

        return productDetailCommentVOPageResult;
    }

    @Override
    public Map<String, Object> getProductCommentSummaryBySupplierId(Long supplierId) {
        CommentScoreSummarySearchAggDTO commentScoreSummarySearchAggDTO = new CommentScoreSummarySearchAggDTO();
        commentScoreSummarySearchAggDTO.setSupplierId(supplierId);

        return ResultUtil.getData(commentAggFeign.getCommentScoreSummaryMap(commentScoreSummarySearchAggDTO));
    }
}
