package com.globalsources.bff.web.comment.service;

import com.globalsources.comment.dto.OrderCommentDTO;
import com.globalsources.comment.vo.OrderCommentVO;
import com.globalsources.framework.result.PageResult;

import java.util.List;

public interface OrderCommentService {

    Boolean submitOrderComment(List<OrderCommentDTO> orderCommentDTOList);

    PageResult<OrderCommentVO> pendingReviewsByBuyer(Integer pageNum, Integer pageSize, String language);

    PageResult<OrderCommentVO> reviewedByBuyer(Integer pageNum, Integer pageSize, String language);
}
