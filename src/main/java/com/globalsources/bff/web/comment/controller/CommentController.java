package com.globalsources.bff.web.comment.controller;

import com.globalsources.bff.web.comment.service.CommentService;
import com.globalsources.bff.web.comment.vo.ProductDetailCommentVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@Api(tags = {"评价操作"})
@Slf4j
@RestController
@RequestMapping("/comment")
public class CommentController {

    @Autowired
    private CommentService commentService;

    @ApiOperation(value = "获取Product Comment列表（分页）")
    @GetMapping(value = "product-comment-list/{productId}")
    public Result<PageResult<ProductDetailCommentVO>> getProductCommentList(@RequestParam(required = false) Integer pageNum, @RequestParam(required = false) Integer pageSize,
                                                                            @PathVariable Long productId) {
        log.info("Input param in getProductCommentList, productId = {}, pageNum = {}, pageSize = {}", productId, pageNum, pageSize);
        if (Objects.isNull(pageNum) || pageNum == 0) {
            pageNum = 1;
        }
        if (Objects.isNull(pageSize) || pageSize == 0) {
            pageSize = 10;
        }
        try {
            PageResult<ProductDetailCommentVO> result = commentService.getProductCommentList(pageNum, pageSize, productId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("get product comment list error, ", e);
            return Result.failed(e.getMessage());
        }
    }


}
