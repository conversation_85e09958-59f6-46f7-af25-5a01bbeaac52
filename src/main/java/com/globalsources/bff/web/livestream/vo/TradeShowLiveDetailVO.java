package com.globalsources.bff.web.livestream.vo;

import com.globalsources.activity.agg.api.vo.SupplierContactInfoVO;
import com.globalsources.activity.agg.api.vo.SwitchSupplierInfo;
import com.globalsources.video.api.dto.PauseMsgDTO;
import com.globalsources.video.api.vo.JoinLiveTokenVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TradeShowLiveDetailVO {
    @ApiModelProperty("活动id")
    private Long activityId;
    @ApiModelProperty("直播类型:PLATFORM,SUPPLIER")
    private String type;
    @ApiModelProperty("频道状态:READY,START,PAUSE,STOP")
    private String status;
    @ApiModelProperty("直播sdk")
    private JoinLiveTokenVO sdk;
    @ApiModelProperty("直播活动详情")
    private LiveDetailVO liveDetail;
    @ApiModelProperty("当前切换供应商状态")
    private SwitchSupplierInfo currentSwitchSupplierInfo;
    @ApiModelProperty("当前播放状态")
    private PauseMsgDTO currentPlayInfo;
    @ApiModelProperty("展会供应商列表")
    private List<TSSupplierVO> supplierList;
    @ApiModelProperty("优选供应商信息")
    private SwitchSupplierInfo bestSupplier;
    @ApiModelProperty("快捷交换名片开关")
    private Boolean shortcutExchangeCard;
    @ApiModelProperty("欢迎语")
    private String welcomeWord;
    @ApiModelProperty("评论开关开关")
    private Boolean commentSwitch;
    @Data
    public static class LiveDetailVO{
        @ApiModelProperty("活动id")
        private Long activityId;

        @ApiModelProperty("标题")
        private String title;

        @ApiModelProperty("pavilion名字")
        private String pavilionName;

        @ApiModelProperty("pavilion code")
        private String pavilionCode;

        @ApiModelProperty(value = "campaignId")
        private Long campaignId;

        @ApiModelProperty(value = "campaign名字")
        private String campaignName;

        @ApiModelProperty("活动描述")
        private String description;

        @ApiModelProperty("缩略图")
        private String thumbImgUrl;

        @ApiModelProperty("活动海报图")
        private String posterImgUrl;

        @ApiModelProperty(value = "活动分类id")
        private Long categoryId;

        @ApiModelProperty(value = "活动开始时间")
        private Date startDate;

        @ApiModelProperty(value = "活动结束时间")
        private Date endDate;

        @ApiModelProperty(value = "活动类型")
        private String type;

        @ApiModelProperty(value = "活动状态: READY(7)  STARTING(1) STOP(2) FINISH(8) UNSTART(9)")
        private Integer status;

        @ApiModelProperty(value = "直播url")
        private String liveUrl;

        @ApiModelProperty(value = "rtmp url")
        private String rtmpUrl;

        @ApiModelProperty(value = "预告片地址")
        private String trailerUrl;

        @ApiModelProperty(value = "创建日期")
        private Date createDate;
    }

    @Data
    public static class TSSupplierVO{
        @ApiModelProperty("供应商id")
        private Long supplierId;
        @ApiModelProperty("供应商logo")
        private String logoUrl;
        @ApiModelProperty("vs标志")
        private Boolean vsFlag;
        @ApiModelProperty("vm标志")
        private Boolean vmFlag;
        @ApiModelProperty("o2o标志")
        private Boolean o2oFlag;
        @ApiModelProperty("供应商等级")
        private Integer maxContractLevel;
        @ApiModelProperty("年份")
        private String year;
        @ApiModelProperty("公司名")
        private String companyName;
        @ApiModelProperty("展会列表")
        private List<String> boothNum;
        @ApiModelProperty("供应商描述")
        private String description;
        @ApiModelProperty("主账号信息")
        private SupplierContactInfoVO mainAccountInfo;
    }
}
