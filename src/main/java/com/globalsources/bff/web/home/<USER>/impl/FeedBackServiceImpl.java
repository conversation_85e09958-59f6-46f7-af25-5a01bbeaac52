package com.globalsources.bff.web.home.service.impl;

import com.globalsources.bff.web.home.service.FeedBackService;
import com.globalsources.common.api.dto.FeedBackSubmitDTO;
import com.globalsources.common.api.feign.FeedBackAggFeign;
import com.globalsources.framework.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class FeedBackServiceImpl implements FeedBackService {

    @Autowired
    private FeedBackAggFeign feedBackAggFeign;

    @Override
    public Result submit(FeedBackSubmitDTO feedBackSubmitDTO) {
        return feedBackAggFeign.submit(feedBackSubmitDTO);
    }
}
