package com.globalsources.bff.web.home.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SimpleProductVO {
    @ApiModelProperty(value = "id")
    private Long productId;
    @ApiModelProperty(value = "主图")
    private String primaryImageUrl;
    @ApiModelProperty("SEO优化使用的产品详情地址")
    private String productDetailUrl;
    @ApiModelProperty(
            value = "产品名称及简短描述",
            required = true
    )
    private String productName;
}
