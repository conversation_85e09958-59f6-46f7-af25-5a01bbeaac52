package com.globalsources.bff.web.home.controller;

import com.globalsources.bff.web.feign.AdminFeign;
import com.globalsources.bff.web.home.vo.AcAnnouncementMessageVO;
import com.globalsources.framework.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2021/11/3
 */
@Slf4j
@RestController
@RequestMapping("/announcement")
@Api(tags = {"走马灯公告"})
public class AcAnnouncementController {

    @Autowired
    private AdminFeign adminFeign;

    @ApiOperation("走马灯公告:roleType(s 供应商,b 买家), announcementType(desktop, mobile, app)")
    @GetMapping("/v1/notice")
    public Result<String> notice(@RequestParam String roleType, @RequestParam String announcementType, @RequestParam String lang) {
        List<AcAnnouncementMessageVO> result = adminFeign.notice(roleType, announcementType).getData();
        AtomicReference<String> message = new AtomicReference<>("");
        if (CollectionUtils.isNotEmpty(result)) {
            result.stream().forEach(item -> {
                if (lang.equals(item.getLangCode())) {
                    message.set(item.getDescription());
                }
            });
        }
        return Result.success(message.get());
    }

}
