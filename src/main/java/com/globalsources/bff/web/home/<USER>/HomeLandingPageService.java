package com.globalsources.bff.web.home.service;

import com.globalsources.agg.admin.api.model.vo.AcBannerVO;
import com.globalsources.bff.web.home.vo.HotProductDetailVO;
import com.globalsources.bff.web.home.vo.SupplierVO;
import com.globalsources.bff.web.home.vo.TextilePavilionVO;
import com.globalsources.bff.web.home.vo.TopAndBottomBannerVO;
import com.globalsources.framework.result.Result;


import java.util.List;


public interface HomeLandingPageService {

    List<HotProductDetailVO> getHotProductVideoList(String verticalCode, List<Long> categoryIds, Long userId, String location);

    List<SupplierVO> homeLandingPageTopBrandList(String vertical, Long categoryId, Long userId, List<Long> l1CategoryIds, String location);


    List<AcBannerVO> getBannerList(String bannerType, Long num);

    TopAndBottomBannerVO getTopAndBottomBanner();

    List<AcBannerVO> homeLandingPageBannerList(String vertical, Long categoryId);

    /**
     * 紡拓會落地页
     * @return
     */
    Result<List<TextilePavilionVO>> getTextilePavilion();
}
