package com.globalsources.bff.web.home.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.globalsources.agg.admin.api.model.vo.AcBannerVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/11 19:15
 */
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopAndBottomBannerVO implements Serializable {
    private static final long serialVersionUID = -8100945847026530447L;

    private AcBannerVO topBanner;

    private AcBannerVO bottomBanner;
}
