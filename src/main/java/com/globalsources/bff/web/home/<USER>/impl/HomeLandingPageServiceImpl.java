package com.globalsources.bff.web.home.service.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.agg.admin.api.constants.FieldFilterConstants;
import com.globalsources.agg.admin.api.feign.AcHotProductFeign;
import com.globalsources.agg.admin.api.feign.AcTopBrandFeign;
import com.globalsources.agg.admin.api.feign.BannerFeign;
import com.globalsources.agg.admin.api.model.dto.banner.BannerQueryDTO;
import com.globalsources.agg.admin.api.model.dto.recommend.AcHotProductDTO;
import com.globalsources.agg.admin.api.model.dto.recommend.DisplayHotProdEntityQueryDTO;
import com.globalsources.agg.admin.api.model.dto.recommend.HotProductVideoLandingQueryDTO;
import com.globalsources.agg.admin.api.model.vo.AcBannerVO;
import com.globalsources.agg.admin.api.model.vo.recommend.AcHotProductDetailVO;
import com.globalsources.agg.supplier.api.feign.SupplierAggFeign;
import com.globalsources.agg.supplier.api.model.dto.SupplierCommonInfoDTO;
import com.globalsources.bff.web.home.service.HomeLandingPageService;
import com.globalsources.bff.web.home.vo.HotProductDetailVO;
import com.globalsources.bff.web.home.vo.SimpleProductVO;
import com.globalsources.bff.web.home.vo.SupplierVO;
import com.globalsources.bff.web.home.vo.TextilePavilionVO;
import com.globalsources.bff.web.home.vo.TopAndBottomBannerVO;
import com.globalsources.bff.web.supplier.service.SupplierSeoService;
import com.globalsources.bff.web.utils.SupplierInfoUtils;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.product.agg.api.feign.SupplierProductListFeign;
import com.globalsources.product.agg.api.vo.SupplierProductVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigInteger;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HomeLandingPageServiceImpl implements HomeLandingPageService {

    @Autowired
    private AcTopBrandFeign acTopBrandFeign;

    @Autowired
    private SupplierAggFeign supplierAggFeign;

    @Autowired
    private SupplierProductListFeign supplierProductListFeign;

    @Value("${gsol.domain:}")
    private String domain;
    @Value("${landing.page.textile.pavilion}")
    private String textilePavilionConfig;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private SupplierSeoService supplierSeoService;

    @Autowired
    private BannerFeign bannerFeign;

    @Autowired
    private AcHotProductFeign acHotProductFeign;

    /**
     * 兜底vertical hero banner
     */
    private Map<String, List<AcBannerVO>> fakeVerticalHeroBannerMap;

    private static final Integer DEFAULT_PAGE_SIZE = 4;

    @PostConstruct
    public void init() {
        bannerFakeMapInit();
    }

    @Override
    public List<HotProductDetailVO> getHotProductVideoList(String verticalCode, List<Long> categoryIds, Long userId, String location) {
        location = StringUtils.isNotEmpty(location) ? location : getAcHotProductLocation(verticalCode);
        HotProductVideoLandingQueryDTO query = HotProductVideoLandingQueryDTO.builder()
                .appType("DESKTOP")
                .verticalCode(verticalCode)
                .categoryIds(categoryIds)
                .location(location)
                .size(6L)
                .build();
        List<AcHotProductDetailVO> voList = null;
        try {
            Result<List<AcHotProductDetailVO>> hotProductLandingPageList = acHotProductFeign.getHotProductLandingPageList(query, userId);
            voList = ResultUtil.getData(hotProductLandingPageList, "failed to getHotProductLandingPageList");
        } catch (Exception e) {
            log.error("failed to getHotProductLandingPageList, query: " + JSON.toJSONString(query) + ", userId: " + userId);
        }
        return OrikaMapperUtil.coverList(voList, HotProductDetailVO.class);
    }

    public List<Tuple2<Long, String>> queryAcTopBrandSupplierInfos(String verticalCode, Long categoryId, Long size, String location) {
        location = StringUtils.isNotEmpty(location) ? location : getAcHotProductLocation(verticalCode);
        size = Optional.ofNullable(size).orElse(Long.valueOf(DEFAULT_PAGE_SIZE));
        DisplayHotProdEntityQueryDTO query = DisplayHotProdEntityQueryDTO.builder()
                .appType("DESKTOP")
                .recomType("SUPPLIER")
                .filterFields(Lists.newArrayList(FieldFilterConstants.HotProductFiledEnum.ORG_ID, FieldFilterConstants.HotProductFiledEnum.DESCRIPTION))
                .verticalCode(verticalCode)
                .categoryId(categoryId)
                .location(location)
                .size(size)
                .build();
        List<AcHotProductDTO> acHotProductDTOS = queryAcProduct(query);
        return Optional.ofNullable(acHotProductDTOS).orElse(Lists.newArrayList())
                .stream().filter(Objects::nonNull).map(dto -> new Tuple2<>(dto.getOrgId(), dto.getDescription())).collect(Collectors.toList());
    }

    private String getAcHotProductLocation(String verticalCode) {
        return StringUtils.isNotEmpty(verticalCode) ? "VMP" : "L1P";
    }

    public List<AcHotProductDTO> queryAcProduct(DisplayHotProdEntityQueryDTO query) {
        if (Objects.isNull(query)) {
            return Lists.newArrayList();
        }
        List<AcHotProductDTO> data = null;
        try {
            Result<List<AcHotProductDTO>> listResult = acHotProductFeign.queryDisplayHotProductEntity(query);
            data = ResultUtil.getData(listResult, "queryDisplayHotProductEntity: " + query);
        } catch (Exception e) {
            log.error("failed to queryAcProduct, query: " + JSON.toJSONString(query) + ", errorMsg:" + e.getMessage(), e);
        }
        return Optional.ofNullable(data).orElse(Lists.newArrayList());
    }

    @Override
    public List<SupplierVO> homeLandingPageTopBrandList(String vertical, Long categoryId, Long userId, List<Long> l1CategoryIds, String location) {
        List<Long> supplierIdList = Lists.newArrayList();
        Map<Long, String> descriptionBySupplierIdMap = Maps.newHashMap();
        List<Tuple2<Long, String>> acTopBrandSupplierInfos = getAcTopBrandSupplierInfos(vertical, categoryId, l1CategoryIds, location);
        Optional.ofNullable(acTopBrandSupplierInfos).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull)
                .forEach(acInfo -> {
                    if (Objects.nonNull(acInfo._1())) {
                        supplierIdList.add(acInfo._1());
                        descriptionBySupplierIdMap.put(acInfo._1(), acInfo._2());
                    }
                });
        List<SupplierVO> result = getSupplierVoBySupplierIds(supplierIdList, l1CategoryIds);
        //最多4个supplier
        if (CollectionUtils.isNotEmpty(result)) {
            for (SupplierVO supplierVO : result) {
                String description = Optional.ofNullable(descriptionBySupplierIdMap.get(supplierVO.getSupplierId())).orElse(supplierVO.getDisplayMessage());
                supplierVO.setDisplayMessage(description);
            }
        }
        if (CollectionUtils.isEmpty(result) || result.size() < 4) {
            Integer num = 4 - Optional.ofNullable(result).map(List::size).orElse(BigInteger.ZERO.intValue());
            List<Long> categoryIds = Lists.newArrayList();
            if (Objects.nonNull(categoryId)) {
                categoryIds.add(categoryId);
            }
            if (CollectionUtils.isNotEmpty(l1CategoryIds)) {
                categoryIds.addAll(l1CategoryIds);
            }
            categoryIds = categoryIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(categoryIds)) {
                Result<List<Long>> recomSupplierIdOfTopBrand = acTopBrandFeign.getRecomSupplierIdOfTopBrand(categoryIds, num, userId, supplierIdList);
                List<Long> recomSupplierIds = ResultUtil.getData(recomSupplierIdOfTopBrand, "failed to getRecomSupplierIdOfTopBrand");
                recomSupplierIds = Optional.ofNullable(recomSupplierIds).map(list -> list.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList())).orElse(Lists.newArrayList());
                List<SupplierVO> recomSupplierList = getSupplierVoBySupplierIds(recomSupplierIds, categoryIds);
                result = Optional.ofNullable(result).map(list -> list.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList())).orElse(Lists.newArrayList());
                result.addAll(recomSupplierList);
            }
        }
        return result;
    }

    private List<Tuple2<Long, String>> getAcTopBrandSupplierInfos(String vertical, Long categoryId, List<Long> l1CategoryIds, String location) {
        Long firstL1CategoryId = categoryId;
        List<Tuple2<Long, String>> acTopBrandSupplierInfos = null;
        Long size = 4L;
        if (StringUtils.isEmpty(vertical)) {
            if (Objects.isNull(firstL1CategoryId) && CollectionUtils.isNotEmpty(l1CategoryIds)) {
                firstL1CategoryId = Optional.ofNullable(l1CategoryIds).flatMap(list -> list.stream().filter(Objects::nonNull).findFirst()).orElse(null);
            }
            if (Objects.nonNull(firstL1CategoryId)) {
                acTopBrandSupplierInfos = queryAcTopBrandSupplierInfos(null, firstL1CategoryId, size, location);
            }
        } else {
            acTopBrandSupplierInfos = queryAcTopBrandSupplierInfos(vertical, null, size, location);
        }
        return acTopBrandSupplierInfos;
    }

    private List<SupplierVO> getSupplierVoBySupplierIds(List<Long> supplierIdList, List<Long> l1CategoryIds) {
        List<SupplierVO> result = Lists.newArrayList();
        List<SupplierVO> tempList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(supplierIdList)) {
            return result;
        }
        List<SupplierCommonInfoDTO> supplierList = ResultUtil.getData(supplierAggFeign.getCommonInfos(supplierIdList));
        if (CollectionUtils.isNotEmpty(supplierList)) {
            for (SupplierCommonInfoDTO supplierInfo : supplierList) {
                //只显示在线supplier
                if (Objects.isNull(supplierInfo) || !Boolean.TRUE.equals(supplierInfo.getOnlineFlag())) {
                    continue;
                }
                SupplierVO supplier = OrikaMapperUtil.coverObject(supplierInfo, SupplierVO.class);
                supplier.setProductList(getProductListBySupplier(supplier.getSupplierId(), l1CategoryIds));
                Boolean vrFlag = SupplierInfoUtils.getVrFlag(supplierInfo);
                supplier.setVrFlag(vrFlag);
                supplier.setSupplierLocation(supplierInfo.getCountry());
                tempList.add(supplier);
            }
        }
        Map<Long, SupplierVO> supplierMap = tempList.stream().filter(Objects::nonNull).filter(vo -> Objects.nonNull(vo.getSupplierId())).collect(Collectors.toMap(SupplierVO::getSupplierId, vo -> vo));
        supplierIdList.stream().filter(Objects::nonNull).forEach(id -> {
            SupplierVO supplierVO = supplierMap.get(id);
            if (Objects.nonNull(supplierVO)) {
                result.add(supplierVO);
            }
        });
        return result;
    }

    private List<SimpleProductVO> getProductListBySupplier(Long supplierId, List<Long> l1CategoryIds) {
        List<SimpleProductVO> productList = new ArrayList<>();
        Result<PageResult<SupplierProductVO>> pageResultResult = supplierProductListFeign.initSupplierProductListPage(supplierId, 1, 4, null, null, l1CategoryIds, null, null, false, null, null, false);
        PageResult<SupplierProductVO> supplierProductVOPageResult = ResultUtil.getData(pageResultResult, "failed to get initSupplierProductListPage, supplierId:" + supplierId + ", categoryIds: " + l1CategoryIds);
        if (CollectionUtils.isNotEmpty(supplierProductVOPageResult.getList())) {
            for (SupplierProductVO product : supplierProductVOPageResult.getList()) {
                SimpleProductVO p = new SimpleProductVO();
                p.setProductId(product.getProductId());
                p.setPrimaryImageUrl(product.getProductPrimaryImage());
                // add product detail url by johann
                p.setProductDetailUrl(product.getDesktopProductDetailUrl());
                p.setProductName(product.getProductName());
                productList.add(p);
            }
        }
        return productList;
    }


    @Override
    public List<AcBannerVO> getBannerList(String bannerType, Long num) {
        //之前只有top/bottom Banner在用，现在应该暂时没有使用
        num = Optional.ofNullable(num).orElse(BigInteger.ONE.longValue());
        if (num > 20L) {
            num = 20L;
        }
        BannerQueryDTO query = BannerQueryDTO.builder()
                .limit(num)
                .displayFlag(Boolean.TRUE)
                .onlineFlag(Boolean.TRUE)
                .bannerTypes(Lists.newArrayList(bannerType))
                .build();
        return queryBanner(query);
    }

    @Override
    public TopAndBottomBannerVO getTopAndBottomBanner() {
        String topDesktop = "TOP_DESKTOP";
        String bottomDesktop = "BOTTOM_DESKTOP";
        BannerQueryDTO query = BannerQueryDTO.builder()
                .bannerTypeLimit(1L)
                .displayFlag(Boolean.TRUE)
                .onlineFlag(Boolean.TRUE)
                .bannerTypes(Lists.newArrayList(topDesktop, bottomDesktop))
                .build();
        List<AcBannerVO> acBannerVoList = queryBanner(query);
        Map<String, AcBannerVO> bannerByTypeMap = Optional.ofNullable(acBannerVoList).map(list -> list.stream().filter(Objects::nonNull)
                .filter(vo -> Objects.nonNull(vo.getBannerType()))
                .collect(Collectors.toMap(AcBannerVO::getBannerType, vo -> vo, (v1, v2) -> v1)))
                .orElse(Maps.newHashMap());
        AcBannerVO topBanner = bannerByTypeMap.get(topDesktop);
        AcBannerVO bottomBanner = bannerByTypeMap.get(bottomDesktop);

        return TopAndBottomBannerVO.builder()
                .topBanner(topBanner)
                .bottomBanner(bottomBanner)
                .build();

    }

    @Override
    public List<AcBannerVO> homeLandingPageBannerList(String vertical, Long categoryId) {
        if (MapUtils.isEmpty(fakeVerticalHeroBannerMap)) {
            bannerFakeMapInit();
        }
        BannerQueryDTO.BannerQueryDTOBuilder builder = BannerQueryDTO.builder()
                .limit(10L)
                .langFilterFlag(true)
                .displayFlag(Boolean.TRUE)
                .onlineFlag(Boolean.TRUE);
        if (StringUtils.isEmpty(vertical) && Objects.nonNull(categoryId)) {
            BannerQueryDTO query = builder
                    .bannerType("HERO_L1_DESKTOP")
                    .categoryId(categoryId)
                    .build();
            return queryBanner(query);
        } else if (StringUtils.isNotEmpty(vertical)) {
            BannerQueryDTO query = builder
                    .bannerType("HERO_VERTICAL_DESKTOP")
                    .verticalCode(vertical)
                    .build();
            List<AcBannerVO> acBannerVoList = queryBanner(query);
            if (CollectionUtils.isEmpty(acBannerVoList)) {
                acBannerVoList = fakeVerticalHeroBannerMap.get(vertical);
            }
            return acBannerVoList;
        }

        return Lists.newArrayList();

    }

    @Override
    public Result<List<TextilePavilionVO>> getTextilePavilion() {
        //读取供应商列表
        if (StringUtils.isEmpty(textilePavilionConfig)) {
            return Result.success();
        }

        //读取缓存信息
        final String cacheKey = "GSOL.LANDING.PAGE.TEXTILE.PAVILION." + textilePavilionConfig;
        String cacheJson = redisTemplate.opsForValue().get(cacheKey);
        if (!StringUtils.isEmpty(cacheJson)) {
            return Result.success(JSON.parseArray(cacheJson, TextilePavilionVO.class));
        }

        List<Long> supplierIds = Arrays.stream(textilePavilionConfig.split(","))
                .map(Long::parseLong).collect(Collectors.toList());


        //加载供应商基本信息
        Result<List<SupplierCommonInfoDTO>> supResp = supplierAggFeign.getCommonInfos(supplierIds);
        if (!supResp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
            log.error("query supplier common info fail, supplierIds:{},supResp:{}", supplierIds, supResp);
            return Result.failed(ResultCode.UserResultCode.QUERY_USER_SUPPLIER_ERROR);
        }
        Map<Long, SupplierCommonInfoDTO> supplierInfoMap = new HashMap<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(supResp.getData())) {
            for (SupplierCommonInfoDTO dto : supResp.getData()) {
                supplierInfoMap.put(dto.getSupplierId(), dto);
            }
        }

        List<TextilePavilionVO> data = new ArrayList<>();
        supplierIds.forEach(supplierId -> {
            TextilePavilionVO vo = new TextilePavilionVO();
            //读取供应商祖新产品4个
            Result<PageResult<SupplierProductVO>> prodResp = supplierProductListFeign.initSupplierProductListPage(supplierId, 1, 4, null, null, null, null, true);
            if (!prodResp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("query supplier 4 product return error, supplierId:{},prodResp:{}", supplierId, prodResp);
                return;
            }

            if (!CollectionUtils.isEmpty(prodResp.getData().getList())) {
                List<SimpleProductVO> productList = prodResp.getData().getList().stream().map(e -> {
                    SimpleProductVO product = new SimpleProductVO();
                    product.setProductId(e.getProductId());
                    product.setPrimaryImageUrl(e.getProductPrimaryImage());
                    product.setProductDetailUrl(e.getDesktopProductDetailUrl());
                    return product;
                }).collect(Collectors.toList());

                vo.setProductList(productList);
            }

            if (supplierInfoMap.containsKey(supplierId)) {
                SupplierCommonInfoDTO curSupplier = supplierInfoMap.get(supplierId);
                vo.setSupplierId(curSupplier.getSupplierId());
                vo.setCompanyName(curSupplier.getCompanyDisplayName());
                vo.setLogoUrl(curSupplier.getLogoUrl());
                vo.setMemberSince(curSupplier.getMemberSince());
                vo.setVmFlag(curSupplier.getVerifiedManufacturerFlag());
                vo.setVsFlag(curSupplier.getVerifiedSupplierFlag());
                vo.setO2oFlag(curSupplier.getO2oFlag());
                vo.setMaxContractLevel(curSupplier.getMaxContractLevel());
                vo.setCountry(curSupplier.getCountry());
                vo.setSupplierType(curSupplier.getSupplierType());
                vo.setSupplierShortName(curSupplier.getSupplierShortName());
                vo.setWebsiteUrl(curSupplier.getSupplierHomepageUrl());
                vo.setWebsiteName(curSupplier.getDisplayMessage());
            }

            data.add(vo);
        });

        //缓存数据,防止下次重复查询
        redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(data), Duration.ofDays(1));
        return Result.success(data);
    }

    public List<AcBannerVO> queryBanner(BannerQueryDTO query) {
        Result<List<AcBannerVO>> listResult = bannerFeign.queryBanner(query);
        List<AcBannerVO> data = ResultUtil.getData(listResult, "failed to queryBanner: " + query);
        return Optional.ofNullable(data).orElse(Lists.newArrayList());
    }

    private void bannerFakeMapInit() {
        String baseUrl = "https://s.globalsources.com/IMAGES/BANNER/VERTICAL/%s.jpg";

        fakeVerticalHeroBannerMap = Maps.newHashMap();
        String noJumpType = "NO";

        AcBannerVO defaultHw = new AcBannerVO();
        defaultHw.setImageUrl(String.format(baseUrl, "HW-DEFAULT"));
        defaultHw.setJumpType(noJumpType);
        fakeVerticalHeroBannerMap.put("HW", Lists.newArrayList(defaultHw));

        AcBannerVO defaultElec = new AcBannerVO();
        defaultElec.setImageUrl(String.format(baseUrl, "ELEC-DEFAULT"));
        defaultElec.setJumpType(noJumpType);
        fakeVerticalHeroBannerMap.put("EC", Lists.newArrayList(defaultElec));

        AcBannerVO defaultLsfa = new AcBannerVO();
        defaultLsfa.setImageUrl(String.format(baseUrl, "LSFA-DEFAULT"));
        defaultLsfa.setJumpType(noJumpType);
        fakeVerticalHeroBannerMap.put("LF", Lists.newArrayList(defaultLsfa));


    }

}
