package com.globalsources.bff.web.user.controller;

import com.globalsources.bff.web.base.BaseController;
import com.globalsources.bff.web.user.dto.WebAccountAppealDTO;
import com.globalsources.bff.web.user.dto.WebSecurityDTO;
import com.globalsources.bff.web.user.dto.WebVerifyCodeDTO;
import com.globalsources.bff.web.user.redis.LoginOtpRedis;
import com.globalsources.bff.web.user.service.UserService;
import com.globalsources.bff.web.vo.WebUserAuthorizationVO;
import com.globalsources.bff.web.vo.WebUserPhoneInfoVO;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.HttpUtil;
import com.globalsources.framework.utils.TokenUtil;
import com.globalsources.user.api.enums.TokenTypeEnum;
import com.globalsources.user.api.enums.UserErrorEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Api(tags = {"用户基础操作"})
@Validated
@Slf4j
@RequestMapping("/user")
@RestController
public class UserSecurityController extends BaseController {
    @Value("${third.sso.maxAge}")
    private int tokenMaxAge;
    @Value("${third.sso.login.trust.maxAge:2592000}")
    private int trustMaxAge;
    @Value("${website.domain}")
    private String websiteDomain;

    @Resource
    private LoginOtpRedis loginOtpRedis;
    @Resource
    private UserService userService;



    @ApiOperation(value = "查找用户手机认证状态")
    @GetMapping("/v2/phone/status")
    public Result<WebUserPhoneInfoVO> findUserPhoneStatus(HttpServletRequest request, @RequestParam String tFaToken){
        //登录没有使用, 可能已经没用了
        LoginOtpRedis.LoginOtpInfo loginOtpInfo=loginOtpRedis.getLoginOptInfo(tFaToken);
        if(loginOtpInfo==null){
            return Result.failed(UserErrorEnum.OTP_TOKEN_INVALID);
        }

        String trustMe=request.getHeader(HttpUtil.TRUST_ME);
        if(trustMe==null){
            trustMe="";
        }

        return userService.findUserPhoneStatus(loginOtpInfo.getEmail(), loginOtpInfo.getPassword(), trustMe);
    }

    @ApiOperation("发送doi码邮件")
    @GetMapping("/v2/doi/code/send")
    public Result<Void> sendDoiCodeMail(@RequestParam String token){
        return userService.sendDoiCodeMail(token);
    }

    @ApiOperation("验证doi邮件码")
    @PostMapping("/v2/doi/code/check")
    public Result<WebUserAuthorizationVO> checkDoiCode(@Valid @RequestBody WebVerifyCodeDTO dto,HttpServletResponse response){
        Result<WebUserAuthorizationVO> result= userService.checkDoiCode(dto);

        //设置登录cookie
        if(result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode()) ||
                result.getCode().equals(ResultCode.UserResultCode.NOT_SWITCH_SUPPLIER.getCode()) ||
                result.getCode().equals(ResultCode.UserResultCode.GOTO_SELLER_CENTER.getCode())){
            //s99 沒2fa不需要记住设备 有2fa会走otp流程
//            setTrustMeCookie(response,websiteDomain,tokenMaxAge,TokenUtil.getTrustMeText(result.getData().getLoginInfo().getEmail()))
            setUserLoginCookie(response,websiteDomain,result.getData().getToken(),tokenMaxAge);
        }

        return result;
    }

    @ApiOperation(value = "发送手机验证码",notes = "发送完后 返回token")
    @GetMapping("/v2/sms/verify-code/send")
    public Result<String> sendSmsVerifyCode(@RequestParam String tFaToken){
        return userService.sendSmsVerifyCode(tFaToken);
    }

    @ApiOperation(value = "校验手机验证码")
    @PostMapping("/v2/phone/verify-code/check")
    public Result<WebUserAuthorizationVO> checkPhoneVerifyCodeV2(HttpServletRequest request,@Valid @RequestBody WebVerifyCodeDTO dto , HttpServletResponse response){
        String userIp = HttpUtil.getUserIp(request);
        String userAgent=request.getHeader("user-agent");
        Result<WebUserAuthorizationVO> result = userService.checkPhoneVerifyCodeAndLogin(dto.getToken(),dto.getVerifyCode(), userIp, userAgent);

        //设置登录cookie
        if(result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode()) ||
                result.getCode().equals(ResultCode.UserResultCode.NOT_SWITCH_SUPPLIER.getCode()) ||
                result.getCode().equals(ResultCode.UserResultCode.GOTO_SELLER_CENTER.getCode())){
            WebUserAuthorizationVO data = result.getData();
            Boolean smsTfaExpireTipFlag = userService.getSmsTfaExpireTipFlagByUserId(data.getLoginInfo().getUserId());
            data.setSmsTfaExpireTipFlag(smsTfaExpireTipFlag);
            Boolean trustDevice = dto.getTrustDevice();
            String tfaType = data.getTfaType();
            if (Boolean.TRUE.equals(trustDevice) || !TokenTypeEnum.TOTP.getName().equals(tfaType)) {
//              !TokenTypeEnum.TOTP.getName().equals(tfaType)  这个条件是兼容buyer 短信验证 没有信任设备勾选框，默认记住设备， sms 2fa下线后删除
                setTrustMeCookie(response,websiteDomain,trustMaxAge,TokenUtil.getTrustMeText(data.getLoginInfo().getEmail()));
            } else {
                removeTrustMeCookie(response, websiteDomain);
            }
            setUserLoginCookie(response,websiteDomain, data.getToken(),tokenMaxAge);
        }

        return result;
    }

    @ApiOperation(value = "发送手机验证码",notes = "发送完后 返回token,这个未来废弃掉")
    @PostMapping("/v1/sms/verify-code/send")
    public Result<String> sendSmsVerifyCode(@Valid @RequestBody WebSecurityDTO dto){
        return userService.sendSmsVerifyCode(dto.getEmail(),dto.getPassword());
    }

    @ApiOperation(value = "校验手机验证码",notes = "这个未来废弃掉")
    @PostMapping("/v1/phone/verify-code/check")
    public Result<String> checkPhoneVerifyCode(@Valid @RequestBody WebVerifyCodeDTO dto , HttpServletResponse response){
        Result<String> result = userService.checkPhoneVerifyCode(dto.getToken(),dto.getVerifyCode());
        //检查是否还有使用 没有就准备删了
        log.warn("checkPhoneVerifyCode v1 ");

        //设置trust me
        if(result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
            setTrustMeCookie(response,websiteDomain,trustMaxAge,TokenUtil.getTrustMeText(result.getData()));
        }

        result.setData(null);
        return result;
    }

    @ApiOperation("提交账号申诉")
    @PostMapping("v1/account/appeal/submit")
    public Result<Void> submitAccountAppeal(@RequestBody WebAccountAppealDTO accountAppeal){
        return userService.submitAccountAppeal(accountAppeal);
    }


}
