package com.globalsources.bff.web.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.activity.agg.api.feign.LiveActivityQueryFeign;
import com.globalsources.activity.agg.api.vo.SupplierContactInfoVO;
import com.globalsources.bff.web.user.service.UserContactCardService;
import com.globalsources.bff.web.vo.WebContactCardVO;
import com.globalsources.bff.web.vo.WebSupplierContactInfoVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.BeanUtil;
import com.globalsources.framework.utils.HttpUtil;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.supplierconsole.agg.api.buyer.feign.BuyerLeadsFeign;
import com.globalsources.user.api.constants.UserConstant;
import com.globalsources.user.api.dto.UserSupplierBusCardSaveAggDTO;
import com.globalsources.user.api.enums.ExchangeTypeEnum;
import com.globalsources.user.api.enums.SourceTypeEnum;
import com.globalsources.user.api.feign.UserContactCardFeign;
import com.globalsources.user.api.vo.ContactCardVO;
import com.globalsources.user.api.vo.UserSupplierBusCardSimpleVO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class UserContactCardServiceImpl implements UserContactCardService {
    @Resource
    private UserContactCardFeign userContactCardFeign;
    @Resource
    private LiveActivityQueryFeign liveActivityQueryFeign;

    @Resource
    private BuyerLeadsFeign buyerLeadsFeign;

    @Override
    public Result<WebSupplierContactInfoVO> exchangeCard(Long userId, Long supplierId, String type,Long fromId, HttpServletRequest request) {
        try{
            UserSupplierBusCardSaveAggDTO dto = UserSupplierBusCardSaveAggDTO.builder()
                    .userId(userId)
                    .supplierId(supplierId)
                    .type(type)
                    .channel(SourceTypeEnum.Desktop.name())
                    .fromId(fromId)
                    .checkLimitFlag(true)
                    .ipAddr(HttpUtil.getUserIp(request))
                    .build();
            log.info("------ exchangeCardV2 dto:{}", dto);
            Result<UserSupplierBusCardSimpleVO> resp=userContactCardFeign.exchangeCardV2(dto);
            if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
                log.error("exchange supplier contact card return error, userId:{}, supplierId:{}, type:{}, fromId:{}, resp:{}",userId,supplierId,type,fromId,resp);
                return Result.error();
            }

            UserSupplierBusCardSimpleVO cardVO = resp.getData();
            WebSupplierContactInfoVO contact = new WebSupplierContactInfoVO();
            if (Objects.nonNull(cardVO)) {
                BeanUtil.copyProperties(cardVO, contact);
            }
            if (UserConstant.StatusEnum.LIMITED.name().equals(contact.getStatus())) {
                return Result.success(contact);
            }

            // CONTACTINFO 返回名片状态
            if(UserConstant.PENDING_EXCHANGE_TYPE_VALUE_SET.contains(type)) {
                if  (UserConstant.NO_COMPLETE_STATUS_VALUE_SET.contains(contact.getStatus())) {
                    contact.setStatus(UserConstant.StatusEnum.PENDING.name());
                }
                log.info("------ exchangeCardV2 cardVO:{}, contact:{}", cardVO, contact);
                return Result.success(contact);
            }

            //仅仅直播交换名片才返回联系人信息
            if(!type.equals(ExchangeTypeEnum.LIVE.name())){
                return Result.success();
            }

            //查询供应商负责人信息
            Result<SupplierContactInfoVO> mainAccountInfoResp=liveActivityQueryFeign.getSupplierMainAccountInfo(supplierId,false);
            if(!mainAccountInfoResp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
                log.error("get live supplier contact card info return error,supplierId:{}, resp:{}",supplierId, JSON.toJSONString(mainAccountInfoResp));
                return Result.success();
            }
            SupplierContactInfoVO supplierContactInfoVO= mainAccountInfoResp.getData();
            if(supplierContactInfoVO==null){
                return Result.success();
            }

            contact= OrikaMapperUtil.coverObject(supplierContactInfoVO,WebSupplierContactInfoVO.class);
            contact.setLimitCount(Optional.ofNullable(cardVO).map(UserSupplierBusCardSimpleVO::getLimitCount).orElse(null));
            return Result.success(contact);
        }catch (Exception e){
            log.error("exchange supplier contact card occur error, userId:{}, supplierId:{}, type:{}, detail:{}",userId,supplierId,type,e.getMessage(),e);
            return Result.error();
        }
    }


    @Override
    public Result<Map<Long, WebSupplierContactInfoVO>> exchangeCard(Long userId, List<Long> supplierIds, String type,Long fromId, HttpServletRequest request) {
        Map<Long, WebSupplierContactInfoVO> result = Maps.newHashMap();
        for (Long supplierId: supplierIds ) {
            //check blacklist
            Result<Boolean> blacklistRes = buyerLeadsFeign.isBlacklist(supplierId, userId);
            if (ResultCode.CommonResultCode.SUCCESS.getCode().equals(blacklistRes.getCode())) {
                Boolean blacklist = Optional.of(blacklistRes).map(Result::getData).orElse(false);
                if (BooleanUtils.isTrue(blacklist)) {
                    log.warn("skip exchangeCard since this user is a blacklist in supplier. userId=={}, supplierId={}", userId, supplierId);
                    continue;
                }
            }

            Result<WebSupplierContactInfoVO> scInfoRes = exchangeCard(userId, supplierId, type,fromId, request);
            if (ResultCode.CommonResultCode.SUCCESS.getCode().equals(scInfoRes.getCode())) {
                WebSupplierContactInfoVO scInfo = Optional.of(scInfoRes).map(Result::getData).orElse(null);
                if (Objects.nonNull(scInfo)) {
                    result.put(supplierId, scInfo);
                }
            }
        }
        return Result.success(result);
    }

    @Override
    public Result<PageResult<WebContactCardVO>> getContactCardList(Long userId, Long pageNum, Long pageSize) {
        try{
            //更新为已读
            userContactCardFeign.updateAllAsRead(userId);

            //查询供应商名片列表
            Result<PageResult<ContactCardVO>> resp=userContactCardFeign.getContactCardList(userId,pageNum,pageSize);
            if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
                log.error("get contact card list return error, userId:{}, pageNum:{}, pageSize:{}, resp:{}",userId,pageNum,pageSize,resp);
                return Result.error();
            }

            PageResult<WebContactCardVO> pageResult=new PageResult<>(pageNum,pageSize,0l,0l,null);
            if(resp.getData()==null || CollectionUtils.isEmpty(resp.getData().getList())){
                return Result.success(pageResult);
            }

            List<WebContactCardVO> list= OrikaMapperUtil.coverList(resp.getData().getList(),WebContactCardVO.class);
            pageResult.setTotalPage(resp.getData().getTotalPage());
            pageResult.setTotal(resp.getData().getTotal());
            pageResult.setList(list);

            return Result.success(pageResult);
        }catch (Exception e){
            log.error("get contact card list occur exception, userId:{}, pageNum:{}, pageSize:{}, detail:{}",userId,pageNum,pageSize,e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> deleteContactCard(Long userId, Long usbcId) {
        try {
            Result<Void> resp= userContactCardFeign.deleteContactCard(userId,usbcId);
            if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
                log.error("delete user contact card return error, userId:{}, usbcId:{}, resp:{}",userId,usbcId,resp);
                return Result.error();
            }

            return Result.success();
        }catch (Exception e){
            log.error("delete user contact card occur exception, userId:{}, usbcId:{}, detail:{}",userId,usbcId,e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<Long> getUnreadTotal(Long userId) {
        try {
            Result<Long> resp= userContactCardFeign.getUnreadCount(userId);
            if(!resp.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
                log.error("get item total of user unread contact card return error, userId:{}, resp:{}",userId,resp);
                return Result.error();
            }

            return Result.success(resp.getData());
        }catch (Exception e){
            log.error("get item total of user unread contact card occur exception, userId:{}, detail:{}",userId,e.getMessage(),e);
            return Result.error();
        }
    }
}
