package com.globalsources.bff.web.user.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class WebEditEmailDTO {
    @ApiModelProperty("验证码token")
    private String token;
    @NotBlank(message = "email cannot be null")
    @Email(message = "The mailbox format is incorrect")
    @Size(max = 100, message = "Mailbox length cannot exceed 100 characters")
    @ApiModelProperty("邮箱")
    private String newEmail;

    @NotNull(message = "Password cannot be null")
    @Length(min = 6,max = 100, message = "The password length should be at least 6 characters")
    @ApiModelProperty("密码")
    private String password;
}
