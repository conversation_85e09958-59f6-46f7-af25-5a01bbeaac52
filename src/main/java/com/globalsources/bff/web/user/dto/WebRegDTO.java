package com.globalsources.bff.web.user.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

/**
 * 注册dto
 * <AUTHOR>
 */
@Data
public class WebRegDTO {
    @NotBlank(message = "country code cannot be null")
    @ApiModelProperty(value = "国家码",required = true)
    private String countryCode;

    @NotBlank(message = "email cannot be null")
    @Email(message = "The mailbox format is incorrect")
    @Size(max = 100, message = "Mailbox length cannot exceed 100 characters")
    @ApiModelProperty(value = "邮箱",required = true)
    private String email;

    @NotNull(message = "Password cannot be null")
    @Length(min = 6,max = 100, message = "The password length should be at least 6 characters, and the maximum should be 100 characters")
    @ApiModelProperty(value = "密码",required = true)
    private String password;

    @ApiModelProperty(value = "确认密码")
    private String confirmPassword;

    @Length(max = 35, message = "Firstname length cannot exceed 35")
    @NotBlank(message = "firstname cannot be null")
    @ApiModelProperty(value = "名字",required = true)
    private String firstName;

    @Length(max = 35, message = "The length of LastName cannot exceed 35")
    @NotBlank(message = "lastname cannot be null")
    @ApiModelProperty(value = "姓氏",required = true)
    private String lastName;

    @ApiModelProperty(value = "电话国家代码")
    @Size(max = 4, message = "Tel country code length cannot exceed 4 characters")
    @Pattern(regexp = "\\d*",message = "tel country code must be number")
    private String telCountryCode;

    @ApiModelProperty(value = "电话区号")
    @Pattern(regexp = "\\d*",message = "tel area code must be number")
    @Size(max = 5, message = "Tel area code length cannot exceed 5 characters")
    private String telAreaCode;

    @ApiModelProperty(value = "电话号码")
    @Pattern(regexp = "\\d*",message = "phone number must be number")
    @Length(max = 20, message = "The phone number length maximum should be 20 characters")
    private String phoneNumber;

    @ApiModelProperty("职位标题")
    @Length(max=35,message = "The job title length maximum should be 35 characters")
    private String jobTitle;

    @NotBlank(message = "company name cannot be null")
    @Size(max = 60, message = "company name length cannot exceed 60 characters")
    @ApiModelProperty(value = "公司名称",required = true)
    private String companyName;

    @ApiModelProperty("公司网站")
    //@Pattern(regexp = "(?:https?://)?\\w+(?:\\.\\w+)*(?:\\.\\w{2,3}){1,2}/?",message = "url format is incorrect")
    @Length(max=70,message = "The company url length maximum should be 70 characters")
    private String companyWebsiteUrl;

    @ApiModelProperty("是否使用chat，默认是使用，欧盟下国家可以选择不使用")
    private Boolean chatFlag;

    @ApiModelProperty("功能标志: 0.正常业务流程(或者这个值为空) 1.忽略身份")
    private Integer flag;

    @ApiModelProperty("验证码token (目前用于图形验证码)")
    private String token;

    //invite userId(token)
    private String inviteToken;

    @ApiModelProperty("验证码")
    private String otpToken;

    @ApiModelProperty("验证码类型, 注册:REGISTER, 邮箱注册(邮箱doi,ab测试): REG_EMAIL")
    private String otpType;


}
