package com.globalsources.bff.web.user.controller;

import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCodeEnum;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.user.api.dto.SourcingClubTreasureReqDTO;
import com.globalsources.user.api.feign.UserSourcingClubTreasureFeign;
import com.globalsources.user.api.vo.SourcingClubTreasureNumVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/02/17
 */
@Slf4j
@RestController
@RequestMapping("/sc-treasure")
@Api(tags = "宝箱接口 - Sourcing Club")
public class SourcingClubTreasureController {

    @Autowired
    private UserSourcingClubTreasureFeign userSourcingClubTreasureFeign;

    @Login
    @ApiOperation(value = "收集任务信息", notes = "收集任务信息", tags = {"宝箱接口 - Sourcing Club"})
    @PostMapping(value = "/v1/save")
    public Result<Boolean> saveSourcingClubTreasure(@ApiIgnore UserVO userVO, @RequestBody SourcingClubTreasureReqDTO reqDTO) {
        try {
            Long userId = 0L;
            if (!Objects.isNull(userVO)) {
                userId = userVO.getUserId();
            }
            reqDTO.setUserId(userId);
            return userSourcingClubTreasureFeign.saveSourcingClubTreasure(reqDTO);
        } catch (Exception exp) {
            log.error("SourcingClubTreasureController BFF saveSourcingClubTreasure error, ReqDTO:{}, error:{}", reqDTO, exp);
            return Result.failed(ResultCodeEnum.FAILED);
        }
    }

    @Login
    @ApiOperation(value = "获取宝箱数量&DONE的标识", notes = "获取宝箱数量&DONE的标识")
    @GetMapping(value = "/v1/get-treasure-num")
    public Result<SourcingClubTreasureNumVO> getSourcingClubTreasureNum(@ApiIgnore UserVO userVO) {
        Long userId = 0L;
        if (!Objects.isNull(userVO)) {
            userId = userVO.getUserId();
        }
        return userSourcingClubTreasureFeign.getSourcingClubTreasureNum(userId);
    }
}
