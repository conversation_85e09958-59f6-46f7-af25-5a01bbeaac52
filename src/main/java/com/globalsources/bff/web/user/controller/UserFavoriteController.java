package com.globalsources.bff.web.user.controller;

import com.globalsources.agg.supplier.api.model.dto.seo.SuppSeoInfoAggDTO;
import com.globalsources.bff.web.supplier.service.SupplierSeoService;
import com.globalsources.bff.web.user.dto.UserFavoriteRemoveDTO;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.page.BasePage;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.user.api.dto.UserFavoriteCollectDTO;
import com.globalsources.user.api.dto.UserFavoriteListReqDTO;
import com.globalsources.user.api.dto.UserFavoriteRemoveReqDTO;
import com.globalsources.user.api.enums.SourceTypeEnum;
import com.globalsources.user.api.enums.UserFavoriteEnum;
import com.globalsources.user.api.feign.UserFavoriteCollectionFeign;
import com.globalsources.user.api.vo.FavoriteProductTrackingVO;
import com.globalsources.user.api.vo.SupplierInfoVO;
import com.globalsources.user.api.vo.UserFavoriteProductVO;
import com.globalsources.user.api.vo.UserFavoriteSupplierVO;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <a>Title: UserFavoriteController </a>
 * <a>Author: Mike Chen <a>
 * <a>Description:  <a>
 *
 * <AUTHOR> Chen
 * @date 2021/6/15 11:18
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@Api(tags = "用户收藏接口")
@RequestMapping(value = "/favorite")
public class UserFavoriteController {

    @Autowired
    private UserFavoriteCollectionFeign userFavoriteCollectionFeign;

    @Autowired
    private SupplierSeoService supplierSeoService;

    @Login
    @ApiOperation(value = "用户收藏产品或者供应商", notes = "用户收藏产品或者供应商", tags = {"User Favorite Collection"})
    @PostMapping(value = "v1/collect")
    public Result<FavoriteProductTrackingVO> addFavorite(@ApiIgnore UserVO userVO, @RequestBody @Valid UserFavoriteCollectDTO userFavoriteCollectDTO) {
        userFavoriteCollectDTO.setDeviceType(SourceTypeEnum.Desktop.name());
        return userFavoriteCollectionFeign.addFavorite(userVO.getUserId(), userFavoriteCollectDTO);
    }

    @Login
    @ApiOperation(value = "用户收藏产品列表", notes = "用户收藏产品列表", tags = {"User Favorite Collection"})
    @PostMapping(value = "v1/product-list")
    public Result<PageResult<UserFavoriteProductVO>> showProductFavorite(@ApiIgnore UserVO userVO, @RequestBody BasePage basePage) {
        UserFavoriteListReqDTO userFavoriteListReqDTO = UserFavoriteListReqDTO.builder()
                .userId(userVO.getUserId())
                .type(UserFavoriteEnum.FAVORITE_PRODUCT.type())
                .build();
        userFavoriteListReqDTO.setPageNum(basePage.getPageNum());
        userFavoriteListReqDTO.setPageSize(basePage.getPageSize());
        return Result.success(userFavoriteCollectionFeign.showProductFavorite(userFavoriteListReqDTO));
    }

    @Login
    @ApiOperation(value = "用户收藏供应商列表", notes = "用户收藏供应商列表", tags = {"User Favorite Collection"})
    @PostMapping(value = "v1/supplier-list")
    public Result<PageResult<UserFavoriteSupplierVO>> showSupplierFavorite(@ApiIgnore UserVO userVO, @RequestBody BasePage basePage) {
        UserFavoriteListReqDTO userFavoriteListReqDTO = UserFavoriteListReqDTO.builder()
                .userId(userVO.getUserId())
                .type(UserFavoriteEnum.FAVORITE_SUPPLIER.type())
                .build();
        userFavoriteListReqDTO.setPageNum(basePage.getPageNum());
        userFavoriteListReqDTO.setPageSize(basePage.getPageSize());
        PageResult<UserFavoriteSupplierVO> data = userFavoriteCollectionFeign.showSupplierFavorite(userFavoriteListReqDTO);
        List<UserFavoriteSupplierVO> userFavoriteSuppVoList = Optional.ofNullable(data).map(PageResult::getList).orElse(Lists.newArrayList());
        List<Long> supplierIds = userFavoriteSuppVoList.stream()
                .map(vo -> Optional.ofNullable(vo).map(UserFavoriteSupplierVO::getSupplier).map(SupplierInfoVO::getOrgId).orElse(null))
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Map<Long, SuppSeoInfoAggDTO> seoInfoMap = supplierSeoService.getSeoInfoMapBySupplierIds(supplierIds);
        userFavoriteSuppVoList.stream().filter(Objects::nonNull)
                .forEach(supp -> {
                    if (Objects.nonNull(supp.getSupplier())) {
                        SuppSeoInfoAggDTO suppSeoInfoAggDTO = seoInfoMap.get(supp.getSupplier().getOrgId());
                        if (Objects.nonNull(suppSeoInfoAggDTO)) {
                            supp.getSupplier().setSupplierType(suppSeoInfoAggDTO.getSupplierType());
                            supp.getSupplier().setSupplierShortName(suppSeoInfoAggDTO.getSupplierShortName());
                        }
                        if (Objects.nonNull(suppSeoInfoAggDTO) && supp.getSupplier().getSupplierType().equals("FL")) {
                            supp.setProducts(new ArrayList<>());
                        }
                    }
                });
        return Result.success(data);
    }

    @Login
    @ApiOperation(value = "移除产品/供应商收藏列表", notes = "移除产品/供应商收藏列表", tags = {"User Favorite Collection"})
    @PostMapping(value = "v1/deleted")
    public Result<Boolean> removeFavorite(@ApiIgnore UserVO userVO, @RequestBody @Valid UserFavoriteRemoveDTO userFavoriteRemoveReqDTO) {
        UserFavoriteRemoveReqDTO userFavorite = OrikaMapperUtil.coverObject(userFavoriteRemoveReqDTO, UserFavoriteRemoveReqDTO.class);
        userFavorite.setUserId(userVO.getUserId());
        return Result.success(userFavoriteCollectionFeign.removeFavorite(userFavorite));
    }

    @Login
    @ApiOperation(value = "用户收藏产品总数", notes = "用户收藏产品总数", tags = {"User Favorite Collection"})
    @GetMapping(value = "v1/product-count")
    public Result<Integer> favoriteCount(@ApiIgnore UserVO userVO) {
        return Result.success(userFavoriteCollectionFeign.getFavoriteProductCount(userVO.getUserId()));
    }
}