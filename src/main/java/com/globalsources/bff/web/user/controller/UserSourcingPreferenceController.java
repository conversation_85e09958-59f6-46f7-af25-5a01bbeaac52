package com.globalsources.bff.web.user.controller;

import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.user.api.dto.ProductCategory;
import com.globalsources.user.api.feign.UserSourcingPreferenceFeign;
import com.globalsources.user.api.feign.UserSourcingPreferenceScoreFeign;
import com.globalsources.user.api.vo.CategoryVO;
import com.globalsources.user.api.vo.SourcingPreferenceLevelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Li
 * @date 2021/7/5
 */
@Slf4j
@RestController
@RequestMapping("/sourcing-preference")
@Api(tags = "用户偏好接口")
public class UserSourcingPreferenceController {

    @Autowired
    private UserSourcingPreferenceFeign userSourcingPreferenceFeign;

    @Autowired
    private UserSourcingPreferenceScoreFeign userSourcingPreferenceScoreFeign;

    private static final Integer CATEGORY_LEVEL = 4;
    private static final Integer COUNT = 1;
    private static final Integer SORT_TYPE = 2;


    @Login
    @ApiOperation(value = "偏好分类列表", notes = "偏好分类列表", tags = {"UserSourcingPreferenceController"})
    @GetMapping("v1/list")
    public Result<SourcingPreferenceLevelVO> getSourcingPreference(@ApiIgnore UserVO user, @RequestHeader(required = false, value="AnonymousId")String anonymousId) {
        log.warn("：：：：：：：：：：：：sourcingPreference/list,userId:{},{}", user.getUserId(),anonymousId);
        if (user.getUserId() == null) {
            return Result.failed();
        }
        return Result.success(userSourcingPreferenceFeign.getSourcingPreference(user.getUserId()));
    }

    @Login
    @ApiOperation(value = "偏好分类新增", notes = "偏好分类新增", tags = {"UserSourcingPreferenceController"})
    @GetMapping("v1/add")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "categoryId", value = "categoryId", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "categoryLevel", value = "categoryLevel", required = true, dataType = "Integer")
    })
    public Result add(@ApiIgnore UserVO user, Long categoryId, Integer categoryLevel,@ApiIgnore @RequestHeader(required = false, value = "AnonymousId") String anonymousId) {
        log.warn("sourcingPreference/add,userId:{},anonymousId:{}", user.getUserId(),anonymousId);
        if (user.getUserId() == null || null == categoryId || null == categoryLevel) {
            return Result.failed();
        }
        try {
            return userSourcingPreferenceFeign.add(user.getUserId(), categoryId, categoryLevel);
        } catch (BusinessException e) {
            return Result.failed(e.getCode(), e.getMessage(), null);
        }
    }

    @Login
    @ApiOperation(value = "偏好分类删除", notes = "偏好分类删除", tags = {"UserSourcingPreferenceController"})
    @GetMapping("v1/remove")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "categoryId", value = "categoryId", required = true, dataType = "Long"),
    })
    public Result remove(@ApiIgnore UserVO user, Long categoryId) {
        log.warn("sourcingPreference/add,userId:{}", user.getUserId());
        if (user.getUserId() == null) {
            return Result.failed();
        }
        return userSourcingPreferenceFeign.remove(user.getUserId(), categoryId);
    }

    @Login
    @ApiOperation(value = "偏好分类修改", notes = "偏好分类修改", tags = {"UserSourcingPreferenceController"})
    @GetMapping("v1/update")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "categoryId", value = "categoryId", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "newCategoryId", value = "newCategoryId", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "newCategoryLevel", value = "1/2/3/4", required = true, dataType = "Integer")
    })
    public Result update(@ApiIgnore UserVO user, Long categoryId, Long newCategoryId, Integer newCategoryLevel,@ApiIgnore @RequestHeader(required = false, value = "AnonymousId") String anonymousId) {
        log.warn("sourcingPreference/update,userId:{},anonymousId:{}", user.getUserId(),anonymousId);
        if (user.getUserId() == null) {
            return Result.failed();
        }
        return userSourcingPreferenceFeign.update(user.getUserId(), categoryId, newCategoryId, newCategoryLevel);
    }

    @Login
    @ApiOperation(value = "偏好分类修改时，获取当前分类上级分类", notes = "用于偏好分类编辑", tags = {"UserSourcingPreferenceController"})
    @GetMapping("v1/categoryTrees")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "categoryId", value = "categoryId", required = true, dataType = "Long")
    })
    public Result<List<ProductCategory>> getCategoryTrees(Long categoryId) {
        if (categoryId == null) {
            return Result.failed();
        }
        return Result.success(userSourcingPreferenceFeign.getCategoryTrees(categoryId));
    }

    @Login
    @ApiOperation(value = "添加前检查是否达到上限", notes = "添加前检查是否达到上限", tags = {"UserSourcingPreferenceController"})
    @GetMapping("v1/addCheck")
    public Result addCheck(@ApiIgnore UserVO user) {
        if (user.getUserId() == null) {
            return Result.failed();
        }
        return userSourcingPreferenceFeign.checkLimit(user.getUserId());
    }

    @Login(validLogin = false)
    @ApiOperation(value = "获取匿名用户偏好", notes = "获取匿名用户偏好", tags = {"UserSourcingPreferenceController"})
    @GetMapping("v1/anonymous-user/preference")
    public Result<String> getAnonymousUserPreference(@ApiIgnore UserVO userVO, @ApiIgnore @RequestHeader(required = false, value = "AnonymousId") String anonymousId) {
        try {
            log.info("AnonymousId：{}",anonymousId);
            String userId = anonymousId;
            if (Objects.nonNull(userVO)) {
                userId = userVO.getUserId().toString();
            }
            //查询匿名用户最新的L4类别偏好
            List<CategoryVO> categoryList = userSourcingPreferenceFeign.getAnonymousUserPreference(CATEGORY_LEVEL, COUNT, SORT_TYPE, userId);
            log.info("Anonymous preference result:{}",categoryList);
            return Result.success(CollectionUtils.isNotEmpty(categoryList) ? categoryList.get(0).getCategoryName() : "");
        } catch (Exception e) {
            log.error("Anonymous preference error:{}",e);
            return Result.failed();
        }
    }
}
