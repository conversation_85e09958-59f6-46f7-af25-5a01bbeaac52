package com.globalsources.bff.web.user.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <a>Title: UserFavoriteRemoveDTO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description:  <a>
 *
 * <AUTHOR>
 * @date 2021/6/15 14:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFavoriteRemoveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "批量删除产品:product/批量删除供应商:supplier")
    private String type;

    @ApiModelProperty(value = "删除的id")
    private List<Long> ids;
}