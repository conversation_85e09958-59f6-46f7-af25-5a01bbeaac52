package com.globalsources.bff.web.user.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/21
 */
@Data
public class WebBuyerLoginDTO {
    @NotBlank(message = "The mailbox cannot be null")
    @Email(message = "The mailbox format is incorrect")
    @Size(max = 100, message = "Mailbox length cannot exceed 100 characters")
    @ApiModelProperty("邮箱")
    private String email;

    @NotNull(message = "Password cannot be null")
    @Length(min = 6,max = 100, message = "The password length should be at least 6 characters")
    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("功能标志: 0.正常业务流程(或者这个值为空) 1.忽略身份")
    private Integer flag;

    @ApiModelProperty("验证码token")
    private String token;

    @ApiModelProperty("tmx session id")
    private String tmxSessionId;
}
