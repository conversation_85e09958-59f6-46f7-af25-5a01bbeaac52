package com.globalsources.bff.web.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.bff.web.redis.VerifyCodeRedis;
import com.globalsources.bff.web.user.dto.WebBindingPhoneDTO;
import com.globalsources.bff.web.user.dto.WebEditEmailDTO;
import com.globalsources.bff.web.user.dto.WebEditPasswordDTO;
import com.globalsources.bff.web.user.dto.WebProfileCompanyInfoDTO;
import com.globalsources.bff.web.user.dto.WebProfileContactInfoDTO;
import com.globalsources.bff.web.user.dto.security.BindingTotpBffDTO;
import com.globalsources.bff.web.user.dto.security.TotpSecretBffVO;
import com.globalsources.bff.web.user.service.UserProfileService;
import com.globalsources.bff.web.vo.WebProfileInfoVO;
import com.globalsources.bff.web.vo.WebUserHomeInfoVO;
import com.globalsources.bff.web.vo.WebUserPhoneInfoVO;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.TokenUtil;
import com.globalsources.user.api.constants.SmsRoleTypeAggEnum;
import com.globalsources.user.api.dto.BindingPhoneDTO;
import com.globalsources.user.api.dto.EditEmailDTO;
import com.globalsources.user.api.dto.EditPasswordDTO;
import com.globalsources.user.api.dto.ProfileCompanyInfoDTO;
import com.globalsources.user.api.dto.ProfileContactInfoDTO;
import com.globalsources.user.api.enums.UserAppTypeEnum;
import com.globalsources.user.api.feign.UserProfileFeign;
import com.globalsources.user.api.feign.UserSecurityFeign;
import com.globalsources.user.api.vo.ProfileInfoVO;
import com.globalsources.user.api.vo.TotpInfoVO;
import com.globalsources.user.api.vo.UserHomeInfoVO;
import com.globalsources.user.api.vo.UserPhoneInfoVO;
import com.globalsources.user.api.vo.security.BindingTotpResultVO;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.mica.qrcode.QrCode;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/22
 */
@Slf4j
@Service
public class UserProfileServiceImpl implements UserProfileService {
    @Resource
    private UserProfileFeign userProfileFeign;
    @Resource
    private VerifyCodeRedis verifyCodeRedis;

    @Resource
    private UserSecurityFeign userSecurityFeign;

    @Override
    public Result<WebUserHomeInfoVO> getUserHomeInfo() {
        try{
            Result<UserHomeInfoVO> result = userProfileFeign.getHomeInfo();
            if (!result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("get user home info occur fail,return:{}", JSON.toJSONString(result));
                return Result.error();
            }

            return Result.success(OrikaMapperUtil.coverObject(result.getData(),WebUserHomeInfoVO.class));
        }catch (Exception e){
            log.error("get user home info occur exception", e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> editPassword(String token, WebEditPasswordDTO dto) {
        try{
            String[] ulCookies= TokenUtil.getData(token);
            EditPasswordDTO editPassword=OrikaMapperUtil.coverObject(dto, EditPasswordDTO.class);
            editPassword.setUlCookie(ulCookies[0]);
            editPassword.setUl2Cookie(ulCookies[1]);

            Result<Void> result = userProfileFeign.editPassword(editPassword);
            if (result.getCode().equals(ResultCode.CommonResultCode.SYSTEM_ERROR.getCode()) || result.getCode().equals(ResultCode.CommonResultCode.FAILED.getCode())) {
                log.error("edit password return error,editPassword:{}, return:{}",editPassword, JSON.toJSONString(result));
                return Result.error();
            }

            return result;
        }catch (Exception e){
            log.error("edit password occur exception,token:{}, dto:{}, detail:{}",token,dto,e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> editEmail(String token,String email, WebEditEmailDTO dto) {
        try{
            String[] ulCookies= TokenUtil.getData(token);
            EditEmailDTO editEmail=OrikaMapperUtil.coverObject(dto, EditEmailDTO.class);
            editEmail.setUlCookie(ulCookies[0]);
            editEmail.setUl2Cookie(ulCookies[1]);
            editEmail.setOldEmail(email);

            //校验验图形证码
            /*String rawJson = verifyCodeRedis.getVerifyCode(dto.getToken());
            verifyCodeRedis.remove(dto.getToken());
            if (StringUtils.isEmpty(rawJson)) {
                return Result.failed(ResultCode.CommonResultCode.SYSTEM_ERROR.getCode(),"verify code info empty!");
            }

            CommonVerifyCodeVO verifyCode = JSON.parseObject(rawJson, CommonVerifyCodeVO.class);
            if (verifyCode.getValid() == null || !verifyCode.getValid()) {
                return Result.failed(ResultCode.CommonResultCode.SYSTEM_ERROR.getCode(),"token verify error!");
            }*/

            Result<Void> result = userProfileFeign.editEmail(editEmail);
            if (result.getCode().equals(ResultCode.CommonResultCode.SYSTEM_ERROR.getCode()) || result.getCode().equals(ResultCode.CommonResultCode.FAILED.getCode())) {
                log.error("edit mail return error,editMail:{}, return:{}",editEmail, JSON.toJSONString(result));
                return Result.error();
            }

            return result;
        }catch (Exception e){
            log.error("edit mail occur exception,token:{},dto:{},detail:{}",token,dto,e.getMessage(), e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> agreePrivacyPolicyFlag(Long userId) {
        return userProfileFeign.agreePrivacyPolicyFlag(userId);
    }

    @Override
    public Result<WebProfileInfoVO> getDetail() {
        try {
            Result<ProfileInfoVO> result = userProfileFeign.getProfileInfo();
            if (!result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("获取用户profile信息失败,返回:{}", JSON.toJSONString(result));
                return Result.error();
            }

            WebProfileInfoVO data=null;
            if(result.getData()!=null){
                data=getWebProfileInfoVO(result.getData());
            }

            return Result.success(data);
        }catch (Exception e){
            log.error("获取用户profile信息发生异常,详情:{}",e.getMessage(),e);
            return Result.error();
        }
    }

    private WebProfileInfoVO getWebProfileInfoVO(ProfileInfoVO profileInfo){
        final String DELIM_FLAG=",";

        WebProfileInfoVO data=new WebProfileInfoVO();
        WebProfileInfoVO.CompanyInfo companyInfo=new WebProfileInfoVO.CompanyInfo();
        data.setCompanyInfo(companyInfo);
        data.setContactInfo(new WebProfileInfoVO.ContactInfo());
        data.setBehaviourInfo(new WebProfileInfoVO.BehaviourInfo());

        BeanUtils.copyProperties(profileInfo.getContactInfo(),data.getContactInfo());
        BeanUtils.copyProperties(profileInfo.getCompanyInfo(),companyInfo);
        BeanUtils.copyProperties(profileInfo.getBehaviourInfo(),data.getBehaviourInfo());

        ProfileInfoVO.CompanyInfo companyVO= profileInfo.getCompanyInfo();
        if(!StringUtils.isEmpty(companyVO.getLookingForProductType())) {
            companyInfo.setLookingForProductType(Arrays.asList(companyVO.getLookingForProductType().split(DELIM_FLAG)));
        }
        if(!StringUtils.isEmpty(companyVO.getLookingForSupplierType())){
            companyInfo.setLookingForSupplierType(Arrays.asList(companyVO.getLookingForSupplierType().split(DELIM_FLAG)));
        }
        if(!StringUtils.isEmpty(companyVO.getSalesChannel())){
            companyInfo.setSalesChannel(Arrays.asList(companyVO.getSalesChannel().split(DELIM_FLAG)));
        }

        //新字段
        if(!StringUtils.isEmpty(companyVO.getBusinessType())) {
            companyInfo.setBusinessType(Arrays.asList(companyVO.getBusinessType().split(DELIM_FLAG)));
        }
        if(!StringUtils.isEmpty(companyVO.getLookingForSupplierTypeV1())){
            companyInfo.setLookingForSupplierTypeV1(Arrays.asList(companyVO.getLookingForSupplierTypeV1().split(DELIM_FLAG)));
        }
        if(!StringUtils.isEmpty(companyVO.getSalesChannelV1())){
            companyInfo.setSalesChannelV1(Arrays.asList(companyVO.getSalesChannelV1().split(DELIM_FLAG)));
        }
        if(!StringUtils.isEmpty(companyVO.getMarketImportZonesV1())) {
            companyInfo.setMarketImportZonesV1(Arrays.asList(companyVO.getMarketImportZonesV1().split(DELIM_FLAG)));
        }
        if(!StringUtils.isEmpty(companyVO.getMarketSellZonesV1())){
            companyInfo.setMarketSellZonesV1(Arrays.asList(companyVO.getMarketSellZonesV1().split(DELIM_FLAG)));
        }


        return data;
    }

    @Override
    public Result<Boolean> modifyCompany(WebProfileCompanyInfoDTO companyInfo) {
        try{
            final String DELIM_FLAG=",";

            ProfileCompanyInfoDTO dto= OrikaMapperUtil.coverObject(companyInfo, ProfileCompanyInfoDTO.class);
            if(!CollectionUtils.isEmpty(companyInfo.getLookingForSupplierType())) {
                dto.setLookingForSupplierType(StringUtils.collectionToDelimitedString(companyInfo.getLookingForSupplierType(), DELIM_FLAG));
            }
            if(!CollectionUtils.isEmpty(companyInfo.getSalesChannel())){
                dto.setSalesChannel(StringUtils.collectionToDelimitedString(companyInfo.getSalesChannel(),DELIM_FLAG));
            }
            if(!CollectionUtils.isEmpty(companyInfo.getLookingForProductType())){
                dto.setLookingForProductType(StringUtils.collectionToDelimitedString(companyInfo.getLookingForProductType(),DELIM_FLAG));
            }

            //使用新字段更新
            if(!CollectionUtils.isEmpty(companyInfo.getSalesChannelV1())){
                dto.setSalesChannelV1(StringUtils.collectionToDelimitedString(companyInfo.getSalesChannelV1(),DELIM_FLAG));
            }
            if(!CollectionUtils.isEmpty(companyInfo.getLookingForSupplierTypeV1())){
                dto.setLookingForSupplierTypeV1(StringUtils.collectionToDelimitedString(companyInfo.getLookingForSupplierTypeV1(),DELIM_FLAG));
            }
            if(!CollectionUtils.isEmpty(companyInfo.getBusinessType())){
                dto.setBusinessType(StringUtils.collectionToDelimitedString(companyInfo.getBusinessType(),DELIM_FLAG));
            }
            if(!CollectionUtils.isEmpty(companyInfo.getMarketImportZonesV1())){
                dto.setMarketImportZonesV1(StringUtils.collectionToDelimitedString(companyInfo.getMarketImportZonesV1(),DELIM_FLAG));
            }
            if(!CollectionUtils.isEmpty(companyInfo.getMarketSellZonesV1())){
                dto.setMarketSellZonesV1(StringUtils.collectionToDelimitedString(companyInfo.getMarketSellZonesV1(),DELIM_FLAG));
            }


            Result<Boolean> result = userProfileFeign.editCompanyInfo(dto);

            if(result.getCode().equals(ResultCode.UserResultCode.NOT_LOGIN.getCode())){
                return Result.failed(ResultCode.UserResultCode.NOT_LOGIN);
            }

            if(!result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())){
                log.error("修改用户profile公司信息发生错误,companyInfo:{},result:{}",JSON.toJSONString(companyInfo),JSON.toJSONString(result));
                return Result.error();
            }

            return result;
        }catch (Exception e){
            log.error("修改用户profile公司信息发生异常,companyInfo:{}, 详情:{}",JSON.toJSONString(companyInfo) ,e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<Boolean> modifyContact(WebProfileContactInfoDTO contactInfo) {
        try{
            ProfileContactInfoDTO dto= OrikaMapperUtil.coverObject(contactInfo, ProfileContactInfoDTO.class);
            return userProfileFeign.editContactInfo(dto);
        }catch (Exception e){
            log.error("修改用户profile联系信息发生异常,contactInfo:{}, 详情:{}",JSON.toJSONString(contactInfo) ,e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<String> userShareLinksToken() {
        return userProfileFeign.genUserShareLinks();
    }

    @Override
    public Result<Void> sendDOI() {
        try {
            Result<Void> result = userProfileFeign.sendDoiReq();
            if(result.getCode().equals(ResultCode.UserResultCode.HAS_BEEN_SENT_EMAIL_DOI.getCode())){
                return result;
            }

            if (!result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("发送doi认证请求发送错误,返回:{}", JSON.toJSONString(result));
                return Result.error();
            }

            return Result.success();
        }catch (Exception e){
            log.error("发送doi认证请求发生异常,详情:{}",e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> bindingPhone(WebBindingPhoneDTO bindingPhone) {
        try{
            BindingPhoneDTO dto=OrikaMapperUtil.coverObject(bindingPhone,BindingPhoneDTO.class);

            Result<Void> result = userProfileFeign.bindingPhone(dto);
            if (!result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("binding phone number from user occur error bindingPhone:{},返回:{}",
                        JSON.toJSONString(bindingPhone), JSON.toJSONString(result));
                String msg= result.getMsg();
                if(result.getCode().equals(ResultCode.UserResultCode.USERNAME_OR_PASSWORD_ERROR.getCode())){
                    msg="Pls input the correct password";
                }

                return Result.failed(result.getCode(),msg);
            }

            return Result.success();
        }catch (Exception e){
            log.error("binding phone number from user occur exception bindingPhone:{}",
                    JSON.toJSONString(bindingPhone), e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> sendVerifyCode(String telCountryCode, String phone, Boolean activate) {
        try{
            log.info("1st sendVerifyCode telCountryCode={}, phone={}, activate={}", telCountryCode, phone, activate);
            //get phone from login user for activate=false
            if (BooleanUtils.isFalse(activate)) {
                Result<UserPhoneInfoVO> userPhoneInfo = userProfileFeign.getUserPhoneInfo();
                if (ResultCode.CommonResultCode.SUCCESS.getCode().equals(userPhoneInfo.getCode())) {
                    telCountryCode = userPhoneInfo.getData().getTelCountryCode();
                    phone = userPhoneInfo.getData().getPhoneNumber();
                    log.info("2nd sendVerifyCode telCountryCode={}, phone={}, activate={}", telCountryCode, phone, activate);
                }
            }

            Result<Void> result = userProfileFeign.sendVerifyCodeV2(telCountryCode,phone,activate, SmsRoleTypeAggEnum.SMS_BUYER.getName());
            if (!result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("send phone verify code to complete security settings occur fail, phone:{} ,activate:{},返回:{}",
                        phone,activate, JSON.toJSONString(result));
                return Result.failed(result.getCode(),result.getMsg());
            }

            return Result.success();
        }catch (Exception e){
            log.error("send phone verify code to complete security settings occur exception, phone:{} ,activate:{}",
                    phone,activate, e);
            return Result.error();
        }
    }

    @Override
    public Result<WebUserPhoneInfoVO> getUserPhoneInfo() {
        try{
            Result<UserPhoneInfoVO> result = userProfileFeign.getUserPhoneInfo();
            if (!result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("get phone info from user occur fail, 返回:{}", JSON.toJSONString(result));
                return Result.failed(result.getCode(),result.getMsg());
            }

            WebUserPhoneInfoVO data=OrikaMapperUtil.coverObject(result.getData(),WebUserPhoneInfoVO.class);


            return Result.success(data);
        }catch (Exception e){
            log.error("get phone info from user occur exception", e);
            return Result.error();
        }
    }

    @Override
    public Result<Void> editShowProfileFlag(Boolean showProfileFlag) {
        try{
            Result<Void> result = userProfileFeign.updateShowAllProfile(showProfileFlag);
            if (!result.getCode().equals(ResultCode.CommonResultCode.SUCCESS.getCode())) {
                log.error("update user profile info occur fail, showProfileFlag:{} ,return:{}",showProfileFlag, JSON.toJSONString(result));
                return Result.failed(result.getCode(),result.getMsg());
            }

            return Result.success();
        }catch (Exception e){
            log.error("update user profile info occur exception, showProfileFlag:{} ",showProfileFlag, e);
            return Result.error();
        }
    }

    @Override
    public Result<String> getTotpToken(String email, String password) {
        return userSecurityFeign.getTotpToken(email,password);
    }

    @Override
    public Result<TotpSecretBffVO> refreshTotpInfo(String token) {
        Result<TotpInfoVO> resp=userSecurityFeign.refreshTotpInfo(token);
        if(resp.getData()==null){
            log.error("refresh totp info return empty, token:{},resp:{}",token,resp);
            return Result.failed(resp.getCode(), resp.getMsg());
        }

        try {
            //转换totp连接为图形二维码
            String base64Img = QrCode.form(resp.getData().getUrl()).toBase64();
            TotpSecretBffVO data = new TotpSecretBffVO();
            data.setQrcodeBase64Img(base64Img);
            data.setSecret(resp.getData().getSecret());

            return Result.success(data);
        }catch (Exception e){
            log.error("get qrcode image occur exception, token:{},totpInfo:{},detail:{}",token,resp.getData(),e.getMessage(),e);
            return Result.error();
        }
    }

    @Override
    public Result<BindingTotpResultVO> bindingTotpInfo(BindingTotpBffDTO dto) {
        return userSecurityFeign.bindingTotpInfoV2(dto.getToken(), dto.getSecret(), dto.getVerifyCode(), UserAppTypeEnum.BUYER.getCode());
    }

    @Override
    public Result<Void> unbindingTotpInfo(Long userId, Integer verifyCode) {
        return userSecurityFeign.unbindingTotpInfoV2(userId,verifyCode, UserAppTypeEnum.BUYER.getCode());
    }

}
