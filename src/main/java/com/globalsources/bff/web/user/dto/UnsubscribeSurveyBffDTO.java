package com.globalsources.bff.web.user.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="取消订阅调查表对象", description="")
public class UnsubscribeSurveyBffDTO implements Serializable {

    private String token;

    @ApiModelProperty("邮件类型:SA,PA,HPA")
    private String type;

    @ApiModelProperty("取消订阅原因类型:ETF=邮件太频繁,EQD=邮件质量下降,NI=不感兴趣,DR=不记得注册过,OTHER=其他原因")
    private List<String> reasonTypeList;

    @ApiModelProperty("其他原因详情")
    private String otherReason;

    @ApiModelProperty("沟通渠道类型:APP,SMS,SM-F,SM-I,SM-L,SM-T,MB,OTHER")
    private List<String> channelTypeList;

    @ApiModelProperty("其他沟通渠道详情")
    private String otherChannel;

}
