package com.globalsources.bff.web.rfi.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.globalsources.rfi.agg.response.product.InquiryProductCoreVO;
import com.globalsources.rfi.agg.response.product.ProductCategoryAttributeDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/1 21:15
 */
@ApiModel(description = "InquiryProductDetail")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class InquiryProductDetailBffVO implements Serializable {

    private static final long serialVersionUID = -1569966580481104818L;
    @ApiModelProperty("商品id")
    private Long productId;
    @ApiModelProperty("商品标题 product name")
    private String productTitle;
    @ApiModelProperty("商品描述")
    private String productDesc;
    @ApiModelProperty("商品图片")
    private String productImage;
    @ApiModelProperty("公司名称")
    private String companyName;
    @ApiModelProperty("categoryId L4")
    private Long categoryId;
    @ApiModelProperty("categoryId L1 数据追踪")
    private Long l1CategoryId;
    @ApiModelProperty("categoryId L2 数据追踪")
    private Long l2CategoryId;
    @ApiModelProperty("categoryId L3 数据追踪")
    private Long l3CategoryId;
    @ApiModelProperty("categoryId L4 数据追踪")
    private Long l4CategoryId;
    /** @deprecated */
    @Deprecated
    @ApiModelProperty("orgId")
    private Long orgId;
    @ApiModelProperty("supplierId 数据追踪")
    private Long supplierId;
    @ApiModelProperty("supplierType: ADV AGG FL 数据追踪")
    private String supplierType;
    @ApiModelProperty("SEO, 供应商自定义域名")
    private String supplierShortName;
    @ApiModelProperty("categoryName")
    private String categoryName;
    @ApiModelProperty("供应商最大等级, supplier package, 为null/-1/-2 标识不存在, , -2无合同，-1 agg免费合同， 0-6: p0-p6")
    private Integer maxContractLevel;
    @ApiModelProperty("是否有o2o标志")
    private Boolean o2oFlag;
    @ApiModelProperty("与GS合作年限")
    private Integer memberSince;
    @ApiModelProperty("是否已认证制造商")
    private Boolean verifiedManufacturerFlag;
    @ApiModelProperty("是否为已认证供应商")
    private Boolean verifiedSupplierFlag;
    @ApiModelProperty("quantity")
    private Integer quantity;
    @ApiModelProperty("unit")
    private String unit;
    @ApiModelProperty("产品属性")
    private List<ProductCategoryAttributeDetailVO> productCategoryAttrInfos;
    @ApiModelProperty("供应商未审核成功显示产品")
    private List<InquiryProductCoreVO> inquiryProductCoreList;
}
