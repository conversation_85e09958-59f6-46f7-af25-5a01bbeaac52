/**
 * <a>Title: InquiryFromDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/20-19:30
 */
package com.globalsources.bff.web.rfi.bean;

import com.globalsources.rfi.agg.request.product.ProductCategoryAttributeRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value="PPage询盘提交对象", description="")
public class ProductPageInquiryDTO implements Serializable {

    @ApiModelProperty(value = "商品编号")
    @NotNull(message = "productId is not null")
    private Long inquiryProductId;

    @ApiModelProperty(value = "message")
    @NotNull(message = "message is not null")
    @Size(max = 1500)
    private String message;

    @ApiModelProperty(value = "sessionId")
    @NotNull(message = "sessionId is not null")
    private Long sessionId;

    @ApiModelProperty(value = "产品类别属性信息")
    private List<ProductCategoryAttributeRequestDTO> productCategoryAttrInfos;

    @ApiModelProperty(value = "inquiryPath")
    private String inquiryPath;
}
