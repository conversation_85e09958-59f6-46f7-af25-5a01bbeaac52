/**
 * <a>Title: InquiryFromDTO </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/4/20-19:30
 */
package com.globalsources.bff.web.rfi.bean;

import com.globalsources.rfi.agg.request.AttachmentAggDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value="供应商询盘提交对象", description="")
public class SupplierInquireFromDTO implements Serializable {

    @ApiModelProperty(value = "公司编号")
    @NotNull(message = "supplierId is not null")
    private Long supplierId;

    @ApiModelProperty(value = "templateId")
    @NotNull(message = "templateId is not null")
    private Integer templateId;

    @NotNull(message = "message is not null")
    @Size(max = 1500)
    @ApiModelProperty(value = "message")
    private String message;

    @ApiModelProperty(value = "附件")
    private List<AttachmentAggDTO> attachmentList;

    @ApiModelProperty(value = "sessionId")
    @NotNull(message = "sessionId is not null")
    private Long sessionId;

    @ApiModelProperty(value = "recommend matching suppliers and send this inquiry to them")
    public Boolean flagRecommend;

    @ApiModelProperty(value = "inquiryPath")
    private String inquiryPath;
}
