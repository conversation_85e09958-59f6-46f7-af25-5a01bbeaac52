/**
 * <a>Title: RFxProductBean </a>
 * <a>Author: <PERSON><PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/5/28-15:35
 */
package com.globalsources.bff.web.rfi.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RFxProductBean implements Serializable {

    /** category_id*/
    private String catId;

    /** product_description*/
    private String desc;

    /** keyspecification*/
    private String keySpec;

    /** model_name*/
    private String model;

    /** product_category*/
    private String prodCat;

    /** supplier_id*/
    private String suppId;
}
