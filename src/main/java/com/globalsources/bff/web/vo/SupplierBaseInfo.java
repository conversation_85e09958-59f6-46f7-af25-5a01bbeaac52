package com.globalsources.bff.web.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2021/8/09 09:30
 */
@Data
@Builder
@ApiModel(description = "供应商基础信息",value = "SupplierBaseInfo")
@AllArgsConstructor
@NoArgsConstructor
public class SupplierBaseInfo implements Serializable {


    private static final long serialVersionUID = -9087182658018594873L;

    @ApiModelProperty("供应商Id")
    private Long supplierId;

    @ApiModelProperty("供应商类型")
    private String supplierType;

    @ApiModelProperty("公司名")
    private String companyName;

    @ApiModelProperty("供应商自定义域名")
    private String supplierShortName;




}
