package com.globalsources.bff.web.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class UserMessageBannerVO {
    private Date startTime;
    private Date endTime;
    @ApiModelProperty("banner url")
    private String bannerUrl;
    @ApiModelProperty("跳转地址")
    private String jumpUrl;
    @ApiModelProperty("是否有效")
    private Boolean valid;
}
