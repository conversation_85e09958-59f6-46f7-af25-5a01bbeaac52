package com.globalsources.bff.web.helpcenter.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/11/2
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "帮助中心-标题和内容")
public class OnlineContentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "内容ID")
    private Integer ocId;

    @ApiModelProperty(value = "主题ID")
    private Integer topicId;

    @ApiModelProperty(value = "标题")
    private String subject;

    @ApiModelProperty(value = "内容")
    private String contentText;
}
