package com.globalsources.bff.web.helpcenter.controller;

import cn.hutool.core.collection.CollUtil;
import com.globalsources.agg.admin.api.model.dto.TopicContentCommonPageDTO;
import com.globalsources.agg.admin.api.model.vo.helpcenter.BffContentListVO;
import com.globalsources.agg.admin.api.model.vo.helpcenter.BffMenuListVO;
import com.globalsources.agg.admin.api.model.vo.helpcenter.BffOnlineContentVO;
import com.globalsources.agg.admin.api.model.vo.helpcenter.OnlineTopicSimpleVO;
import com.globalsources.bff.web.feign.AdminFeign;
import com.globalsources.bff.web.helpcenter.vo.BffContentListWebVO;
import com.globalsources.bff.web.helpcenter.vo.OnlineContentVO;
import com.globalsources.bff.web.helpcenter.vo.OnlineTopicVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Sun
 * @date 2021/11/3
 */
@Slf4j
@RestController
@RequestMapping("/help-center")
@Api(tags = {"帮助中心接口"})
public class HelpCenterController {

    @Autowired
    private AdminFeign adminFeign;


    @ApiOperation("主题列表")
    @GetMapping("/v1/topic-list")
    public Result<List<OnlineTopicVO>> getOnlineTopic() {
        return adminFeign.getOnlineTopic();
    }

    @ApiOperation("内容列表")
    @GetMapping("/v1/content-list")
    public Result<PageResult<OnlineContentVO>> getOnlineContent(@RequestParam("topicId") Integer topicId, @RequestParam(value = "pageNum", required = false) Integer pageNum, @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        return adminFeign.getOnlineContent(topicId, pageNum, pageSize);
    }

    @ApiOperation("  Help Center 前台内容页左侧菜单")
    @GetMapping("/v1/select-all-menu-bff")
    public Result<List<BffMenuListVO>> selectAllMenuBff(@RequestParam("topicId") Long topicId,
                                                        @ApiParam("zh=中文,en=英文") @RequestParam("lang") String lang) {
        return adminFeign.selectAllMenuBff(topicId,lang,"BC",Boolean.FALSE);
    }

    @ApiOperation("  Help Center 前台内容页所有文章列表")
    @PostMapping("/v1/select-all-content-bff")
    public Result<PageResult<BffContentListWebVO>> selectAllContentBff(@RequestBody TopicContentCommonPageDTO pageDTO) {
        if (Objects.isNull(pageDTO)) {
            return Result.failed(ResultCode.CommonResultCode.REQUIRED_REQUEST_PARAMETERS_ARE_MISSING);
        }
        Result<PageResult<BffContentListVO>> result = adminFeign.selectAllContentBff(pageDTO);
        if (ResultCode.CommonResultCode.SUCCESS.getCode().equals(result.getCode())) {
            PageResult<BffContentListVO> pageResult = result.getData();
            if (Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getList())) {
                return Result.success(PageResult.init());
            }
            List<BffContentListWebVO> listWebVOS = Lists.newArrayList();
            List<BffContentListVO> listVOS = pageResult.getList();
            listVOS.forEach(listVO -> {
                BffContentListWebVO webVO = new BffContentListWebVO();
                webVO.setOcId(listVO.getId());
                webVO.setTopicId(listVO.getPid());
                webVO.setSubject(listVO.getName());
                webVO.setContentTopicVO(listVO.getContentTopicVO());
                listWebVOS.add(webVO);
            });

            return Result.success(PageResult.restPage(pageResult, listWebVOS));
        }
        return Result.failed();
    }

    @ApiOperation("  Help Center 前台内容页文章详情")
    @GetMapping("/v1/content-details-bff")
    public Result<BffOnlineContentVO> contentDetailsBff(@ApiParam("内容id") @RequestParam("ocId") Integer ocId,
                                                        @ApiParam("zh=中文,en=英文") @RequestParam("lang") String lang) {
        return adminFeign.contentDetailsBff(ocId,lang);
    }

    @ApiOperation(value = "获取Simple Topic列表",notes ="获取Simple Topic列表")
    @GetMapping("/v1/get-simple-topic-list")
    public Result<List<OnlineTopicSimpleVO>> getHomepageTopicList(@RequestParam("sourceCode") String sourceCode,
                                                                  @RequestParam("homeFlag") Boolean homeFlag, @RequestParam(value = "lang", required = false) String lang) {
        return adminFeign.getHomepageTopicList(sourceCode, homeFlag, lang);
    }

    @ApiOperation(value = "获取Buyer Center首页文章列表",notes ="获取Buyer Center首页文章列表")
    @GetMapping("/v1/get-home-page-content-list")
    public Result<List<BffContentListVO>> getHomePageContentList(@RequestParam("homepageTopicId") Integer homepageTopicId, @RequestParam(value = "lang", required = false) String lang) {
        return adminFeign.getHomePageContentList(homepageTopicId, lang);
    }

}
