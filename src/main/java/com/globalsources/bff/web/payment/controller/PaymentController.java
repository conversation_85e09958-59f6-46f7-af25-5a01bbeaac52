package com.globalsources.bff.web.payment.controller;

import com.globalsources.bff.web.payment.service.PaymentService;
import com.globalsources.bff.web.payment.vo.DOPaySuccessVO;
import com.globalsources.bff.web.payment.vo.DOPaypalInitVO;
import com.globalsources.framework.enums.LanguageDicEnum;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.LanguageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> liu
 * @date 2021/7/14
 */
@RestController
@Slf4j
@RequestMapping("/payment")
@Api(tags = {"支付相关"})
public class PaymentController {

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private HttpServletRequest request;

    @GetMapping("/paypal-button-init")
    @ApiOperation(value = "订单详情页面支付按钮初始化", notes = "paypalButtonInit", tags = {"PaymentController"})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "supplierId", value = "供应商id", required = true, dataType = "Long"),
    })
    public Result<DOPaypalInitVO> paypalButtonInit(Long supplierId) {
        DOPaypalInitVO doPaypalInitVO = paymentService.paypalButtonInit(supplierId);
        return Result.success(doPaypalInitVO);
    }

    @GetMapping("/paynow")
    @ApiOperation(value = "返回paypal_token", notes = "paynow", tags = {"PaymentController"})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "orderId", value = "订单Id", required = true, dataType = "Long"),
    })
    public Result<String> paynow(Long orderId) {
        String paypalToken = paymentService.paynow(orderId);
        return Result.success(paypalToken);
    }

    @GetMapping("/payment-status")
    @ApiOperation(value = "获取支付状态接口", notes = "getPaymentStatus", tags = {"PaymentController"})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "token", value = "paypal_token", required = true, dataType = "String"),
    })
    public Result<Boolean> getPaymentStatus(String token) {
        Boolean aBoolean = paymentService.getPaymentStatus(token);
        return Result.success(aBoolean);
    }

    @GetMapping("/pay-success")
    @ApiOperation(value = "支付成功页面接口", notes = "paySuccess", tags = {"PaymentController"})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "orderId", value = "订单Id", required = true, dataType = "Long"),
    })
    public Result<DOPaySuccessVO> paySuccess(Long orderId) {
        String language = LanguageUtil.getHeaderLanguage(request, LanguageDicEnum.EN_US);
        DOPaySuccessVO doPaySuccessVO = paymentService.paySuccess(orderId, language);
        return Result.success(doPaySuccessVO);
    }


    @GetMapping("/paypal-component")
    @ApiOperation(value = "获取PayPal组件所需内容", notes = "getPaypalComponent", tags = {"PaymentController"})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "orderId", value = "订单Id", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "supplierId", value = "供应商Id", required = true, dataType = "Long")
    })
    public Result<DOPaypalInitVO> getPaypalComponent(Long orderId, Long supplierId) {
        DOPaypalInitVO doPaypalInitVO = paymentService.getPaypalComponent(orderId, supplierId);
        return Result.success(doPaypalInitVO);
    }

}
