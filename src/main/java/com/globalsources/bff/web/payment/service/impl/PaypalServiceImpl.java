package com.globalsources.bff.web.payment.service.impl;

import com.globalsources.agg.api.feign.PaymentAggPayPalFeign;
import com.globalsources.bff.web.payment.service.PaypalService;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class PaypalServiceImpl implements PaypalService {

    @Autowired
    private PaymentAggPayPalFeign paymentAggPayPalFeign;

    @Override
    public Result<String> createPayment(String orderId) {
        Result<String> result = paymentAggPayPalFeign.createPayment(orderId, null, null);
        if (!Result.success().getCode().equals(result.getCode())) {
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        if (!StringUtils.isEmpty(result.getData())) {
            String[] arr = result.getData().split("token=");
            String returnToken = "";
            if (arr.length > 1) {
                returnToken = arr[1];
            }
            return Result.success(returnToken);
        }
        return Result.failed("get token is null");
    }

    @Override
    public Result<String> getPayStatus(String token) {
        Result<String> result = paymentAggPayPalFeign.getPayStatus(token);
        if (!Result.success().getCode().equals(result.getCode())) {
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        return result;
    }

    @Override
    public Result<Map<String, String>> createPaypalButton(Long supplierId) {
        return paymentAggPayPalFeign.createPaypalButton(supplierId);
    }
}
