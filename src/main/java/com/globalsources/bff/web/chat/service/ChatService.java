package com.globalsources.bff.web.chat.service;

import com.globalsources.bff.web.chat.vo.ProdChatUserInfoDTO;
import com.globalsources.bff.web.chat.vo.ProductPPCardVO;
import com.globalsources.bff.web.chat.vo.SupplierCategoryVO;
import com.globalsources.framework.result.PageResult;
import com.globalsources.framework.result.Result;

import java.util.List;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.bff.mobile.chat.service
 * @date:2021/8/9
 */
public interface ChatService {

    Result<PageResult<ProductPPCardVO>> productListBySupplier(Long categoryId, Long supplierId, String keyWord, Integer pageSize, Integer pageNum);

    Result<List<SupplierCategoryVO>> productCategoryListBySupplierUser(Long supplierId);

    ProdChatUserInfoDTO getProductDetailChatUserInfo(Long productId, Long buyerId);

    Long getSupplierIdByProductId(Long productId);
}
