package com.globalsources.bff.web.chat.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/23 21:05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProdChatUserInfoDTO implements Serializable {
    private static final long serialVersionUID = 6193091311537741734L;

    private String firstName;

    private String lastName;

    @ApiModelProperty(
            value = "用户头像",
            required = false
    )
    private String photo;


}
