package com.globalsources.bff.web.chat.vo;

import com.globalsources.framework.vo.ProductPriceVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: create by <PERSON><PERSON>
 * @version: v1.0
 * @description: com.globalsources.bff.web.chat.vo
 * @date:2021/8/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductPPCardVO {

    @ApiModelProperty(value = "产品id")
    private Long productId;

    @ApiModelProperty(value = "供应商id , 注意 以前的大多数都是 orgId")
    private Long supplierId;

    @ApiModelProperty(value = "产品名称")
    private String productName;
    @ApiModelProperty(value = "产品型号")
    private String modelNumber;

    @ApiModelProperty(value = "环球资源产品类别")
    private Long categoryId;

    @ApiModelProperty(value = "最小订单量")
    private Integer minOrderQuantity;

    @ApiModelProperty(value = "复数单位")
    private String minOrderUnit;

    @ApiModelProperty(value = "单数单位，注意是单数的")
    private String minOrderSingleUnit;

    @ApiModelProperty(value = "是否开通环球直购")
    private Boolean directOrderFlag;

    @ApiModelProperty(value = "是否是ac产品",required = false)
    private Boolean acFlag;

    @ApiModelProperty(value = "是否为收藏产品",required = false)
    private Boolean collectFlag;

    @ApiModelProperty(value = "是否是新产品",required = false)
    private Boolean newProductFlag;

    @ApiModelProperty(value = "视频标签")
    private Boolean videoFlag;

    @ApiModelProperty(value = "视频地址")
    private String productVideoUrl;

    @ApiModelProperty(value = "产品主图")
    private String primaryImageUrl;

    @ApiModelProperty(value = "1:根据不同订单量设置精确FOB价格，设置后可开通‘环球直购’ (PRICE_BY_ORDER_QUANTITY)" +
            "2:设定一个价格范围（不支持开通环球直购）(PRICE_RANGE)" +
            "3: 我不希望透露FOB价格 (NONE)" +
            "前端依赖此字段做判断，不会移除")
    private String fobPriceShowType;

    @ApiModelProperty(value = "根据不同订单量设置价格的价格List，非必须字段")
    private List<ProductPriceVo> productPriceVOList;

    @ApiModelProperty(value = "给前端显示的价格，通过上面几个参数，使用 ProductPriceUtil 转换")
    private String listVoShowPriceStr;

    @ApiModelProperty(value = "SEO优化使用的产品详情地址")
    private String desktopProductDetailUrl;

    @ApiModelProperty(value = "展会信息")
    private String exhibition;

    @ApiModelProperty(value = "过去参展数量")
    private Integer pastTradeShowCount;

}
