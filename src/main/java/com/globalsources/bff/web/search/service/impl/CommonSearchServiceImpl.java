package com.globalsources.bff.web.search.service.impl;

import com.alibaba.fastjson.JSON;
import com.globalsources.bff.web.search.feign.ISearchFeign;
import com.globalsources.bff.web.search.model.dto.request.ISearchRequest;
import com.globalsources.bff.web.search.model.dto.response.ISearchResponse;
import com.globalsources.bff.web.search.service.CommonSearchService;
import com.globalsources.framework.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/1/17 21:36
 */
@Slf4j
@Service
public class CommonSearchServiceImpl implements CommonSearchService {

    @Autowired
    private ISearchFeign searchFeign;

    @Override
    public Result<ISearchResponse> genericSearchWithObject(ISearchRequest searchRequest) {
        Result<ISearchResponse> iSearchResponseResult = null;
        if (Objects.nonNull(searchRequest)) {
            try {
                iSearchResponseResult = searchFeign.genericSearchWithObject(searchRequest);
            } catch (Exception e) {
                log.error("failed to genericSearchWithObject, errorMsg: " + e.getMessage() + " , searchRequest: " + JSON.toJSONString(searchRequest), e);
                throw e;
            }
        } else {
            log.warn("genericSearchWithObject, searchRequest is null");
        }
        return iSearchResponseResult;
    }
}
