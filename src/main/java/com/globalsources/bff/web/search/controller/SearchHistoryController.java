package com.globalsources.bff.web.search.controller;

import com.globalsources.bff.web.search.service.SearchHistoryService;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * @Title: SearchHistoryController
 * @Author: Johann
 * @Description: SearchHistoryController
 * @date 2021/07/16 - 9:27
 */
@RestController
@RequestMapping
@Api(tags = "搜索历史记录")
@RequiredArgsConstructor
public class SearchHistoryController {

    private final SearchHistoryService searchHistoryService;

    @Login(validLogin = false)
    @GetMapping(value = "v1/search/history/{searchType}")
    @ApiOperation(value = "搜索关键字历史记录,type=( product || supplier)")
    public Result<List<String>> searchHistory(@ApiIgnore UserVO userVO, @PathVariable String searchType) {
        return searchHistoryService.searchHistory(userVO, searchType);
    }

    @Login(validLogin = false)
    @PostMapping(value = "v1/search/clean/history")
    @ApiOperation(value = "清楚搜索历史记录,type=( Product || Supplier)")
    public Result<Boolean> cleanHistory(@ApiIgnore UserVO userVO, @RequestParam String searchType) {
        return searchHistoryService.cleanHistory(userVO, searchType);
    }

}
