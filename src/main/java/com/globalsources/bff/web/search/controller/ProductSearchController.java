package com.globalsources.bff.web.search.controller;

import com.globalsources.bff.web.search.service.ProductSearchService;
import com.globalsources.bff.web.supplier.model.vo.product.search.SeoSearchResult;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.HttpUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.search.api.dto.ProductSearchDTO;
import com.globalsources.search.api.dto.ProductSuggestDTO;
import com.globalsources.search.api.result.SearchResult;
import com.globalsources.search.api.vo.ProductAdvertVo;
import com.globalsources.search.api.vo.ProductSuggestVo;
import com.globalsources.search.api.vo.free.FreeProductResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * @Title: ProductSearchController
 * @Author: Johann
 * @Description: ProductSearchController
 * @date 2021/07/12 - 11:02
 */
@Slf4j
@RestController
@RequestMapping(value = "search")
@RequiredArgsConstructor
@Api(tags = "商品搜索服务")
public class ProductSearchController {

    private final ProductSearchService productSearchService;

    @Login(validLogin = false)
    @PostMapping(value = "product/v1/search")
    @ApiOperation(value = "商品搜索")
    public SearchResult searchQuery(@ApiIgnore UserVO userVO, @RequestBody ProductSearchDTO searchDTO, HttpServletRequest request) {
        if (Objects.nonNull(userVO)) {
            searchDTO.setUserId(userVO.getUserId().toString());
        }
        searchDTO.setIp(request.getHeader(HttpUtil.REMOTE_ADDRESS));
        return productSearchService.search(searchDTO);
    }

    @Login(validLogin = false)
    @PostMapping(value = "seo/product/v1/search")
    @ApiOperation(value = "seo商品搜索")
    public SeoSearchResult seoProductSearchQuery(@ApiIgnore UserVO userVO, @RequestBody ProductSearchDTO searchDTO, HttpServletRequest request) {
        if (Objects.nonNull(userVO)) {
            searchDTO.setUserId(userVO.getUserId().toString());
        }
        searchDTO.setIp(request.getHeader(HttpUtil.REMOTE_ADDRESS));
        return productSearchService.seoProductSearch(searchDTO);
    }

    @PostMapping(value = "product/suggest")
    @ApiOperation(value = "商品搜索自动补全")
    public Result<ProductSuggestVo> productSearchSuggest(@RequestBody ProductSuggestDTO suggest, HttpServletRequest request) {
        suggest.setIp(request.getHeader(HttpUtil.REMOTE_ADDRESS));
        return Result.success(productSearchService.productSuggest(suggest));
    }

    @GetMapping(value = "product/side")
    @ApiOperation(value = "侧边栏广告商品")
    public Result<List<ProductAdvertVo>> sideAdvertProduct(@RequestParam String query, @RequestParam(required = false, value = "language") String language) {
        return Result.success(productSearchService.sideAdvertProduct(query,language));
    }

    @Login(validLogin = false)
    @PostMapping(value = "product/v1/free/list")
    @ApiOperation(value = "免费供应商商品列表")
    public FreeProductResult freeProductResult(@ApiIgnore UserVO userVO,@RequestBody ProductSearchDTO searchDTO, HttpServletRequest request) {
        searchDTO.setIp(request.getHeader(HttpUtil.REMOTE_ADDRESS));
        return productSearchService.freeProductSearch(searchDTO);
    }

}
