package com.globalsources.bff.web.search.service;

import com.globalsources.bff.web.search.model.dto.request.ISearchRequest;
import com.globalsources.bff.web.search.model.dto.response.ISearchResponse;
import com.globalsources.framework.result.Result;

/**
 * <AUTHOR>
 * @date 2022/1/17 21:36
 */
public interface CommonSearchService {
    Result<ISearchResponse> genericSearchWithObject(ISearchRequest searchRequest);
}
