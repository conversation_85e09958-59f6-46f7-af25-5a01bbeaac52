/**
 * 
 */
package com.globalsources.bff.web.supplier.model.vo.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.globalsources.agg.supplier.api.model.dto.file.SimpleSectionImageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
@ApiModel(description = "QualityControlVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class QualityControlDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6977768932044749395L;
	
	/**
	 * Description of Quality Control/Technical Support Department
	 */
	@ApiModelProperty(name = "Description of Quality Control/Technical Support Department")
	private String technicalSupportDepartment;
	
    /**
     * Quality Control Procedures
     */
    @ApiModelProperty(name = "Quality Control Procedures")
    private String qualityControlProcedure;

    /**
     * Origin of Materials and Components
     */
    @ApiModelProperty(name = "Origin of Materials and Components")
    private String materialsAndComponents;

    /**
     * Additional Information about the Quality Control Process
     */
    @ApiModelProperty(name = "Additional Information about the Quality Control Process")
    private String additionalInfo;

    @ApiModelProperty(name = "Images")
    private List<SimpleSectionImageDTO> images;
}
