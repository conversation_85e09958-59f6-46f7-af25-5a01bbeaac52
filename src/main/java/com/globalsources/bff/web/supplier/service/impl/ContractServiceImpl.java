package com.globalsources.bff.web.supplier.service.impl;

import com.globalsources.agg.contract.api.feign.ContractAggFeign;
import com.globalsources.agg.contract.api.model.dto.GsOnlineContractInfoAggDTO;
import com.globalsources.agg.contract.api.model.dto.SupplierContractStatusInfoCoreDTO;
import com.globalsources.bff.web.supplier.model.vo.SupplierContractStatusInfoVO;
import com.globalsources.bff.web.supplier.service.ContractService;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/7/19 13:30
 */
@Service
public class ContractServiceImpl implements ContractService {

    @Autowired
    private ContractAggFeign contractAggFeign;

    @Override
    public SupplierContractStatusInfoVO getSupplierContractStatusInfo(Long supplierId) {
        SupplierContractStatusInfoVO result = null;
        if (Objects.isNull(supplierId)) {
            return null;
        }
        Result<SupplierContractStatusInfoCoreDTO> contractResult = contractAggFeign.getSupplierContractStatusInfo(supplierId);
        SupplierContractStatusInfoCoreDTO data = ResultUtil.getData(contractResult);
        if (Objects.nonNull(data)) {
             result = OrikaMapperUtil.coverObject(data, SupplierContractStatusInfoVO.class);
        }
        return result;
    }

    @Override
    public GsOnlineContractInfoAggDTO getGsOnlineContractInfoDto(Long supplierId) {
        Result<GsOnlineContractInfoAggDTO> gsOnlineContractInfoDtoResult = contractAggFeign.getGsOnlineContractInfoDtoByOrgId(supplierId);
        return ResultUtil.getData(gsOnlineContractInfoDtoResult, "failed to getGsOnlineContractInfoDtoByOrgId, supplierId: " + supplierId);
    }

}
