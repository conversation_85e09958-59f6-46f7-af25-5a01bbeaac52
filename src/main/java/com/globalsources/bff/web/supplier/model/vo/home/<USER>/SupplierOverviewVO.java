package com.globalsources.bff.web.supplier.model.vo.home.overview;

import com.globalsources.bff.web.supplier.model.vo.SupplierCompanyAnnexEditVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/25 13:11
 */
@ApiModel(description = "SupplierOverview")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SupplierOverviewVO implements Serializable{

    /**
	 * 
	 */
	private static final long serialVersionUID = 3746735275234762793L;

	@Valid
    @ApiModelProperty(value = "公司概况详情", required = false)
    private SupplierOverviewDetailVO overViewDetail;

    @Valid
    @ApiModelProperty(value = "公司概况基本信息", required = false)
    private SupplierOverviewBasicInfoVO overViewBasicInfo;
    

    @Valid
    @ApiModelProperty(value = "公司概要供应商公司视频", required = false)
    private SupplierCompanyAnnexEditVO companyVideo;

    @Size(max = 3, message = "{listSizeLimit}")
    @ApiModelProperty(value = "公司概要供应商公司形象展示图", required = false)
    private List<SupplierCompanyAnnexEditVO> companyPhotos;

    @ApiModelProperty("聊天按钮在线状态， 不显示‘invalid’，在线‘Online’or'PushOnline' ,离线 ‘Offline’")
    private String chatOnlineStatus;

}
