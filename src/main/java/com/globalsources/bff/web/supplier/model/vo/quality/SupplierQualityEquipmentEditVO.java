package com.globalsources.bff.web.supplier.model.vo.quality;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <a>Title: SupplierQualituEquipmentVO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/3/15-11:57
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "质量控制设备基本信息", value = "SupplierQualityEquipmentEditVO")
public class SupplierQualityEquipmentEditVO implements Serializable {

    private static final long serialVersionUID = -7503564294921276293L;
    /**
     * 设备id,自增
     */
    @ApiModelProperty(value = "设备id,自增", required = false)
    private Long id;

    /**
     * 设备名称（检测设备）
     */
    @NotBlank(message = "{required}")
    @Size(max = 60, message = "{required}")
    @ApiModelProperty(value = "设备名称(检测设备)(非空,最大长度60)", required = false)
    private String equipmentName;

    /**
     * 设备型号（检测设备）
     */
    @NotBlank(message = "{required}")
    @Size(max = 60, message = "{required}")
    @ApiModelProperty(value = "设备型号(检测设备)(非空,最大长度60)", required = false)
    private String equipmentModel;

    /**
     *  设备数量（检测设备）
     */
    @NotBlank(message = "{required}")
    @ApiModelProperty(value = "设备数量(检测设备)(非空)", required = false)
    private Integer equipmentQuantity;

    /**
     * 排序，最小提最前面
     */
    @ApiModelProperty(value = "排序，最小提最前面", required = false)
    private Integer sort;
}
