package com.globalsources.bff.web.supplier.model.vo.company;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/21 10:50
 */
@ApiModel(description = "company profile 导航栏")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CompanyProfileNavDTO {

    private List<String> navList;
}
