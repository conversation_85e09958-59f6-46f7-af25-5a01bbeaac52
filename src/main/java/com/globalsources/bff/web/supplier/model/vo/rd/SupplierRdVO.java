package com.globalsources.bff.web.supplier.model.vo.rd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <a>Title: SupplierRdVO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/3/15-17:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "SupplierRdVO", value = "SupplierRdVO")
public class SupplierRdVO implements Serializable {

    private static final long serialVersionUID = -7623007372715668483L;

    /**
     * 研发能力id,自增
     */
    @ApiModelProperty(value = "研发能力id,自增", required = false)
    private Long id;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id", required = false)
    private Long sid;

    /**
     * 供应商id(冗余)
     */
    @ApiModelProperty(value = "供应商id(冗余)", required = false)
    private Long orgId;

    /**
     * 是否展示研发流程
     */
    @NotNull(message = "{required}")
    @ApiModelProperty(value = "是否展示研发流程", required = false)
    private Boolean showRdProcess;

    /**
     * 自定义名称1
     */
    @Size(max = 60, message = "{required}")
    @ApiModelProperty(value = "自定义名称1(最大长度60)", required = false)
    private String custom1;

    /**
     * 自定义值1
     */
    @Size(max = 2000, message = "{required}")
    @ApiModelProperty(value = "自定义值1(最大长度2000)", required = false)
    private String content1;

    @ApiModelProperty(value = "表单类型(custom)", required = false)
    private String type1;

    /**
     * 自定义名称2
     */
    @Size(max = 60, message = "{required}")
    @ApiModelProperty(value = "自定义名称2", required = false)
    private String custom2;

    /**
     * 自定义值2
     */
    @Size(max = 2000, message = "{required}")
    @ApiModelProperty(value = "自定义值2", required = false)
    private String content2;

    @ApiModelProperty(value = "表单类型(custom)", required = false)
    private String type2;
    /**
     * 自定义名称3
     */
    @Size(max = 60, message = "{required}")
    @ApiModelProperty(value = "自定义名称3", required = false)
    private String custom3;

    /**
     * 自定义值3
     */
    @Size(max = 2000, message = "{required}")
    @ApiModelProperty(value = "自定义值3", required = false)
    private String content3;

    @ApiModelProperty(value = "表单类型(custom)", required = false)
    private String type3;
    /**
     * 自定义名称4
     */
    @Size(max = 60, message = "{required}")
    @ApiModelProperty(value = "自定义名称4", required = false)
    private String custom4;

    /**
     * 自定义值4
     */
    @Size(max = 2000, message = "{required}")
    @ApiModelProperty(value = "自定义值4", required = false)
    private String content4;

    @ApiModelProperty(value = "表单类型(custom)", required = false)
    private String type4;
    /**
     * 自定义名称5
     */
    @Size(max = 60, message = "{required}")
    @ApiModelProperty(value = "自定义名称5", required = false)
    private String custom5;

    /**
     * 自定义值5
     */
    @Size(max = 2000, message = "{required}")
    @ApiModelProperty(value = "自定义值5", required = false)
    private String content5;

    @ApiModelProperty(value = "表单类型(custom)", required = false)
    private String type5;
}
