package com.globalsources.bff.web.supplier.model.vo.sca;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/6/15 17:59
 */
@Data
@ApiModel(description = "General Info")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScaGeneralInfoVO implements Serializable {

    private static final long serialVersionUID = -8860903818593182690L;
    /**
     * General Information
     */
    @JsonAlias({"Business type"})
    @JsonProperty("Business type")
    @ApiModelProperty("Business type")
    private String businessType;

    @JsonAlias({"Type of ownership"})
    @JsonProperty("Type of ownership")
    @ApiModelProperty("Type of ownership")
    private String typeOfOwnership;

    @JsonAlias({"Bank account number"})
    @JsonProperty("Bank account number")
    @ApiModelProperty("Bank account number")
    private String bankAccountNumber;

    @JsonAlias({"Number of staff"})
    @JsonProperty("Number of staff")
    @ApiModelProperty("Number of staff")
    private String numOfStaff;

    @JsonAlias({"Factory size"})
    @JsonProperty("Factory size")
    @ApiModelProperty("Factory size")
    private String factorySize;

    @JsonAlias({"Brand names"})
    @JsonProperty("Brand names")
    @ApiModelProperty("Brand names")
    private String brandNames;

    @JsonAlias({"OEM capability"})
    @JsonProperty("OEM capability")
    @ApiModelProperty("OEM capability")
    private String oemCapability;

    @JsonAlias({"Year company was established"})
    @JsonProperty("Year company was established")
    @ApiModelProperty("Year company was established")
    private String yearEstablishedOfCompany;

    @JsonAlias({"Total capitalization"})
    @JsonProperty("Total capitalization")
    @ApiModelProperty("Total capitalization")
    private String totalCapitalization;

    @JsonAlias({"Type of products"})
    @JsonProperty("Type of products")
    @ApiModelProperty("Type of products")
    private String typeOfProducts;

    @JsonAlias({"Company building information"})
    @JsonProperty("Company building information")
    @ApiModelProperty("Company building information")
    private String companyBuildingInfo;

    @JsonAlias({"Factory buildings"})
    @JsonProperty("Factory buildings")
    @ApiModelProperty("Factory buildings")
    private String factoryBuildings;

    @JsonAlias({"Patent situation"})
    @JsonProperty("Patent situation")
    @ApiModelProperty("Patent situation")
    private String patentSituation;

    @JsonAlias({"Company development/expansion plans"})
    @JsonProperty("Company development/expansion plans")
    @ApiModelProperty("Company development/expansion plans")
    private String developmentOrExpansionPlans;

}
