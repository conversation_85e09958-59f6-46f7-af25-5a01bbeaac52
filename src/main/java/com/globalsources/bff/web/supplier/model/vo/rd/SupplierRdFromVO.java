package com.globalsources.bff.web.supplier.model.vo.rd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <a>Title: SupplierRdProcessVO </a>
 * <a>Author: <PERSON> <a>
 * <a>Description：<a>
 *
 * <AUTHOR>
 * @date 2021/3/15-17:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "研发能力", value = "SupplierRdFromVO")
public class SupplierRdFromVO implements Serializable {

    private static final long serialVersionUID = 6397434162179096041L;

    @Valid
    @ApiModelProperty(value = "研发能力信息", required = false)
    private SupplierRdVO supplierRd;

    @Size(max = 5, message = "{listSizeLimit}")
    @ApiModelProperty(value = "研发能力附件信息(最多接收5个,超出数量会有校验)", required = false)
    private List<SupplierRdAnnexEditVO> supplierAnnexList;
}
