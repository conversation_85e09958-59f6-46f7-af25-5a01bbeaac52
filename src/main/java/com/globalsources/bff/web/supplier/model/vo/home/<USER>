package com.globalsources.bff.web.supplier.model.vo.home;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.globalsources.bff.web.supplier.model.vo.home.overview.CountryAndRegionVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/26 17:00
 */
@ApiModel(description = "CompanySnapshot")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OnlineCompanySnapshotVO {

    /**
     * table supplier_attribute
     */
    @ApiModelProperty(value = "业务类型", required = false)
    private List<String> businessTypes;

    /**
     * supplier info, table supplier_address
     */
    @ApiModelProperty(value = "国家", required = false)
    private CountryAndRegionVO countryAndRegion;

    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 上一年销售额, supplier_trade
     */
    @NotBlank(message = "{required}")
    @ApiModelProperty(value = "上一年销售额", required = false)
    private String totalAnnualSales;

    /**
     * table supplier
     */
    @ApiModelProperty(value = "成立年份", required = false)
    private String yearEstablished;

    /**
     * 主要出口市场, table supplier_trade
     */
    @NotBlank(message = "{required}")
    @ApiModelProperty(value = "主要出口市场(只显示州，不显示具体国家)", required = false)
    private String mainMarkets;

    /**
     * 出口比例, table supplier_trade
     */
    @NotBlank(message = "{required}")
    @ApiModelProperty(value = "出口比例", required = false)
    private String exportPercentage;

    @ApiModelProperty(value = "图片", required = false)
    private List<CompanySnapshotAnnexVO> images;

    @ApiModelProperty(value = "图片所属栏目名(company profile)(排序)", required = false)
    private List<String> pageTitleOfImages;

    @ApiModelProperty(value = "sca")
    private CompanySnapshotAnnexVO sca;

    @ApiModelProperty(value = "pdf")
    private CompanySnapshotAnnexVO pdf;

    @ApiModelProperty(value = "eMagazine")
    private CompanySnapshotAnnexVO magazine;

    @ApiModelProperty(value = "certifications")
    private List<CompanySnapshotAnnexVO> certifications;

}
