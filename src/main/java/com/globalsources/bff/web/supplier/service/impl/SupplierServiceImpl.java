package com.globalsources.bff.web.supplier.service.impl;

import com.globalsources.agg.contract.api.constant.ContractAggConstant;
import com.globalsources.agg.supplier.api.constant.SeoConstant;
import com.globalsources.agg.supplier.api.feign.OnlineSectionAggFeign;
import com.globalsources.agg.supplier.api.feign.OrganizationAggFeign;
import com.globalsources.agg.supplier.api.feign.OrganizationPreferenceAggFeign;
import com.globalsources.agg.supplier.api.feign.SupplierAggFeign;
import com.globalsources.agg.supplier.api.feign.SupplierContactAggFeign;
import com.globalsources.agg.supplier.api.feign.SupplierProfileAggFeign;
import com.globalsources.agg.supplier.api.model.dto.CompanyProfileDTO;
import com.globalsources.agg.supplier.api.model.dto.ContactInformationDTO;
import com.globalsources.agg.supplier.api.model.dto.ReferenceCodeDTO;
import com.globalsources.agg.supplier.api.model.dto.SupplierCommonInfoDTO;
import com.globalsources.agg.supplier.api.model.dto.SupplierCompareProfileDTO;
import com.globalsources.agg.supplier.api.model.dto.SupplierCreditProfileDTO;
import com.globalsources.agg.supplier.api.model.dto.certificate.OnlineCertificateDTO;
import com.globalsources.agg.supplier.api.model.dto.certificate.OnlineCertificateImageLinkDTO;
import com.globalsources.agg.supplier.api.model.dto.contact.AddressDTO;
import com.globalsources.agg.supplier.api.model.dto.contact.ContactAddressAggDTO;
import com.globalsources.agg.supplier.api.model.dto.contact.ContactDTO;
import com.globalsources.agg.supplier.api.model.dto.contact.ContactPersonAggDTO;
import com.globalsources.agg.supplier.api.model.dto.contact.OtherContactDTO;
import com.globalsources.agg.supplier.api.model.dto.contact.PersonDTO;
import com.globalsources.agg.supplier.api.model.dto.contact.PhoneDTO;
import com.globalsources.agg.supplier.api.model.dto.contact.SuppContactInfoAggDTO;
import com.globalsources.agg.supplier.api.model.dto.organization.CompanyBannerDTO;
import com.globalsources.agg.supplier.api.model.dto.organization.OrganizationDTO;
import com.globalsources.agg.supplier.api.model.dto.organization.OrganizationPreferenceDTO;
import com.globalsources.agg.supplier.api.model.dto.organization.RegistrationInfoDTO;
import com.globalsources.agg.supplier.api.model.dto.request.cert.SuppCertQueryAggDTO;
import com.globalsources.agg.supplier.api.model.dto.request.contact.SuppContactInfoQueryAggDTO;
import com.globalsources.agg.supplier.api.model.dto.request.org.OrgQueryAggDTO;
import com.globalsources.agg.supplier.api.model.dto.request.org.preference.OrgPreferenceInfoQueryAggDTO;
import com.globalsources.agg.supplier.api.model.dto.request.supplier.SuppCommonInfoQueryAggDTO;
import com.globalsources.agg.supplier.api.model.dto.request.supplier.SuppStatusInfoQueryAggDTO;
import com.globalsources.agg.supplier.api.model.dto.sca.ScaCsvInfoDTO;
import com.globalsources.agg.supplier.api.model.dto.section.CodeofConductSectionDTO;
import com.globalsources.agg.supplier.api.model.dto.section.CustomSectionDTO;
import com.globalsources.agg.supplier.api.model.dto.section.CustomSectionLayoutDTO;
import com.globalsources.agg.supplier.api.model.dto.section.ExportMarketDTO;
import com.globalsources.agg.supplier.api.model.dto.section.FactoryTourFragmentDTO;
import com.globalsources.agg.supplier.api.model.dto.section.FactoryTourSectionDTO;
import com.globalsources.agg.supplier.api.model.dto.section.MainSectionDTO;
import com.globalsources.agg.supplier.api.model.dto.section.ManagementSectionDTO;
import com.globalsources.agg.supplier.api.model.dto.section.NewsroomSectionDTO;
import com.globalsources.agg.supplier.api.model.dto.section.NewsroomSectionDTO.NewsroomStoryDTO;
import com.globalsources.agg.supplier.api.model.dto.section.OemOdmMarketDTO;
import com.globalsources.agg.supplier.api.model.dto.section.OemOdmSectionDTO;
import com.globalsources.agg.supplier.api.model.dto.section.OnlineSectionImageLinkDTO;
import com.globalsources.agg.supplier.api.model.dto.section.QualityControlSectionDTO;
import com.globalsources.agg.supplier.api.model.dto.section.ResearchAndDevelopmentSectionDTO;
import com.globalsources.agg.supplier.api.model.dto.section.SectionVerifiableFieldDTO;
import com.globalsources.agg.supplier.api.model.dto.section.ServiceAndSupportSectionDTO;
import com.globalsources.agg.supplier.api.model.dto.section.TradingServiceSectionDTO;
import com.globalsources.agg.supplier.api.model.dto.seo.SuppSeoInfoAggDTO;
import com.globalsources.agg.supplier.api.model.dto.supplier.ChatSupplierInfoAggDTO;
import com.globalsources.agg.supplier.api.model.dto.supplier.SupplierStatusInfoDTO;
import com.globalsources.agg.supplier.api.model.vo.organization.HomepageTemplateSettingAggVO;
import com.globalsources.agg.supplier.api.model.vo.product.ProductSupplierSnapshotAggVO;
import com.globalsources.agg.supplier.api.util.SupplierAggContactUtil;
import com.globalsources.agg.supplier.api.util.SupplierAggRefCodeUtil;
import com.globalsources.bff.web.supplier.constant.BusinessConstant;
import com.globalsources.bff.web.supplier.constant.CustomSectionConstant;
import com.globalsources.bff.web.supplier.constant.DateConstants;
import com.globalsources.bff.web.supplier.model.vo.CompanyProfileAnnexVO;
import com.globalsources.bff.web.supplier.model.vo.ComprehensiveBRProfileVO;
import com.globalsources.bff.web.supplier.model.vo.CustomVO;
import com.globalsources.bff.web.supplier.model.vo.OnlineSupplierHeaderSnapshotVO;
import com.globalsources.bff.web.supplier.model.vo.ProductSupplierSnapshotVO;
import com.globalsources.bff.web.supplier.model.vo.SupplierAnnexEditVO;
import com.globalsources.bff.web.supplier.model.vo.SupplierBannerDTO;
import com.globalsources.bff.web.supplier.model.vo.SupplierCompanyAnnexEditVO;
import com.globalsources.bff.web.supplier.model.vo.SupplierCompareInfoVO;
import com.globalsources.bff.web.supplier.model.vo.SupplierCustomEditVO;
import com.globalsources.bff.web.supplier.model.vo.SupplierStatusInfoVO;
import com.globalsources.bff.web.supplier.model.vo.UnverifiedSupplierHomeVO;
import com.globalsources.bff.web.supplier.model.vo.certificate.CertificateFormVO;
import com.globalsources.bff.web.supplier.model.vo.certificate.CertificationVO;
import com.globalsources.bff.web.supplier.model.vo.company.CompanyProfileNavDTO;
import com.globalsources.bff.web.supplier.model.vo.company.ProfileMarketVO;
import com.globalsources.bff.web.supplier.model.vo.company.ProfileVerifiableAttributeVO;
import com.globalsources.bff.web.supplier.model.vo.company.SimpleExportMarketVO;
import com.globalsources.bff.web.supplier.model.vo.contact.PhoneVO;
import com.globalsources.bff.web.supplier.model.vo.contact.SupplierAddressEditVO;
import com.globalsources.bff.web.supplier.model.vo.contact.SupplierContactNumberVO;
import com.globalsources.bff.web.supplier.model.vo.contact.SupplierContactUsAddressEditVO;
import com.globalsources.bff.web.supplier.model.vo.contact.SupplierContactUsPageVO;
import com.globalsources.bff.web.supplier.model.vo.home.OnlineSupplierProfileInProductVO;
import com.globalsources.bff.web.supplier.model.vo.home.OnlineSupplierProfileVO;
import com.globalsources.bff.web.supplier.model.vo.home.overview.CountryAndRegionVO;
import com.globalsources.bff.web.supplier.model.vo.home.overview.SupplierOverviewBasicInfoInProductVO;
import com.globalsources.bff.web.supplier.model.vo.home.overview.SupplierOverviewBasicInfoVO;
import com.globalsources.bff.web.supplier.model.vo.home.overview.SupplierOverviewDetailVO;
import com.globalsources.bff.web.supplier.model.vo.home.overview.SupplierOverviewInProductVO;
import com.globalsources.bff.web.supplier.model.vo.home.overview.SupplierOverviewVO;
import com.globalsources.bff.web.supplier.model.vo.production.SupplierProductionEditVO;
import com.globalsources.bff.web.supplier.model.vo.production.SupplierProductionFormVO;
import com.globalsources.bff.web.supplier.model.vo.quality.SupplierQualityAnnexEditVO;
import com.globalsources.bff.web.supplier.model.vo.quality.SupplierQualityEditVO;
import com.globalsources.bff.web.supplier.model.vo.quality.SupplierQualityFormVO;
import com.globalsources.bff.web.supplier.model.vo.rd.SupplierRdAnnexEditVO;
import com.globalsources.bff.web.supplier.model.vo.rd.SupplierRdFromVO;
import com.globalsources.bff.web.supplier.model.vo.rd.SupplierRdVO;
import com.globalsources.bff.web.supplier.model.vo.sca.ScaBasicInfoVO;
import com.globalsources.bff.web.supplier.model.vo.sca.ScaGeneralInfoVO;
import com.globalsources.bff.web.supplier.model.vo.sca.ScaInfoVO;
import com.globalsources.bff.web.supplier.model.vo.sca.ScaMachineryCsvVO;
import com.globalsources.bff.web.supplier.model.vo.sca.ScaMarketCsvVO;
import com.globalsources.bff.web.supplier.model.vo.sca.ScaProductionVO;
import com.globalsources.bff.web.supplier.model.vo.sca.ScaQualitySystemVO;
import com.globalsources.bff.web.supplier.model.vo.sca.ScaTradingVO;
import com.globalsources.bff.web.supplier.model.vo.trade.SupplierTradeBaseAddressEditVO;
import com.globalsources.bff.web.supplier.model.vo.trade.SupplierTradeFormVO;
import com.globalsources.bff.web.supplier.model.vo.trade.SupplierTradeOverseasAgentAddressEditVO;
import com.globalsources.bff.web.supplier.model.vo.trade.SupplierTradeVO;
import com.globalsources.bff.web.supplier.service.OnlineSectionService;
import com.globalsources.bff.web.supplier.service.SupplierSeoService;
import com.globalsources.bff.web.supplier.service.SupplierService;
import com.globalsources.bff.web.utils.CustomInfoUtil;
import com.globalsources.bff.web.utils.ImageUtil;
import com.globalsources.bff.web.utils.RefCodeUtil;
import com.globalsources.bff.web.utils.SupplierInfoUtils;
import com.globalsources.bff.web.utils.SupplierUtil;
import com.globalsources.framework.constants.RedisKey;
import com.globalsources.framework.exception.BusinessException;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.BeanUtil;
import com.globalsources.framework.utils.DateUtil;
import com.globalsources.framework.utils.OrikaMapperUtil;
import com.globalsources.framework.utils.ResultUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.supplierconsole.agg.api.constant.HomepageTemplateConstant;
import com.globalsources.supplierconsole.agg.api.supplier.feign.OrganizationPreferenceFeign;
import com.globalsources.user.api.enums.UserFavoriteEnum;
import com.globalsources.user.api.feign.UserFavoriteCollectionFeign;
import com.globalsources.user.api.feign.UserQueryFeign;
import com.globalsources.user.api.vo.UserFavoriteSimpleVO;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Chen
 * @since 2021/7/20 18:07
 */
@Slf4j
@Service
public class SupplierServiceImpl implements SupplierService {

    public static final String THIRD_PARTY = "3ʳᵈ party";
    @Autowired
    private SupplierAggFeign supplierAggFeign;

    @Autowired
    private OnlineSectionService onlineSectionService;

    @Autowired
    private OrganizationPreferenceAggFeign organizationPreferenceAggFeign;

    @Autowired
    private UserFavoriteCollectionFeign userFavoriteCollectionFeign;

    @Autowired
    private OnlineSectionAggFeign onlineSectionAggFeign;

    @Autowired
    private OrganizationAggFeign organizationAggFeign;

    @Autowired
    private SupplierContactAggFeign supplierContactAggFeign;

    @Autowired
    private SupplierSeoService supplierSeoService;

    @Autowired
    private SupplierProfileAggFeign supplierProfileAggFeign;

    @Autowired
    private UserQueryFeign userQueryFeign;

    @Autowired
    private OrganizationPreferenceFeign organizationPreferenceFeign;

    public static final String ADDCREDIT_STR = "Addcredit";
    public static final String EASECREDIT_STR = "Easecredit";
    public static final String SGS_STR = "SGS";
    public static final ImmutableMap<String, String> BR_VENDOR_MAP = ImmutableMap.<String, String>builder()
            .put("Dun & Bradstreet(D & B)", THIRD_PARTY)
            .put(EASECREDIT_STR, THIRD_PARTY)
            .put("TUV SUD", "TÜV SÜD")
            .put(ADDCREDIT_STR, ADDCREDIT_STR)
            .put("Experian", "Experian")
            .put(SGS_STR, SGS_STR)
            .build();

    public static final ImmutableSet<String> SCA_VENDOR_SET = ImmutableSet.<String>builder()
            .add(SGS_STR)
            .add("TÜV SÜD")
            .add(ADDCREDIT_STR)
            .build();

    @Override
    public SupplierStatusInfoVO getSupplierStatusInfos(Long supplierId) {
        SupplierStatusInfoVO supplierStatusInfoVO = null;
        Result<SupplierStatusInfoDTO> supplierStatusInfo = supplierAggFeign.getSupplierStatusInfo(supplierId);
        SupplierStatusInfoDTO data = ResultUtil.getData(supplierStatusInfo);
        if (Objects.nonNull(data)) {
            supplierStatusInfoVO = OrikaMapperUtil.coverObject(data, SupplierStatusInfoVO.class);
        } else {
            log.warn(String.format("getSupplierStatusInfos Method: failed to get supplierStatusInfo by supplierId[%s]", supplierId));
            supplierStatusInfoVO = new SupplierStatusInfoVO();
            supplierStatusInfoVO.setOnlineFlag(false);
            supplierStatusInfoVO.setSupplierType(ContractAggConstant.SupplierType.FL.getType());
        }
        return supplierStatusInfoVO;
    }

    @Override
    public SupplierStatusInfoVO getSupplierHomepageStatusAndSettings(Long supplierId) {
        SupplierStatusInfoVO supplierStatusInfoVO = null;
        Result<SupplierStatusInfoDTO> supplierStatusInfo = supplierAggFeign.getSupplierStatusInfo(supplierId);
        SupplierStatusInfoDTO data = ResultUtil.getData(supplierStatusInfo);
        if (Objects.nonNull(data)) {
            supplierStatusInfoVO = OrikaMapperUtil.coverObject(data, SupplierStatusInfoVO.class);

            // 获取主页模板设置
            Result<HomepageTemplateSettingAggVO> homepageTemplateSettingResult = organizationPreferenceAggFeign.getHomepageTemplateSetting(supplierId);
            HomepageTemplateSettingAggVO homepageTemplateSetting = ResultUtil.getData(homepageTemplateSettingResult, "failed to getHomepageTemplateSetting, supplierId: " + supplierId);

            supplierStatusInfoVO.setTemplateType(Optional.ofNullable(homepageTemplateSetting).map(HomepageTemplateSettingAggVO::getTemplateType).orElse(HomepageTemplateConstant.TemplateType.COMMON.getValue()));
            supplierStatusInfoVO.setTemplateName(Optional.ofNullable(homepageTemplateSetting).map(HomepageTemplateSettingAggVO::getTemplateName).orElse(HomepageTemplateConstant.DEFAULT_COMMON_TEMPLATE_VALUE));

        } else {
            log.warn(String.format("getSupplierHomepageStatusAndSettings Method: failed to get supplierStatusInfo by supplierId[%s]", supplierId));
            supplierStatusInfoVO = new SupplierStatusInfoVO();
            supplierStatusInfoVO.setOnlineFlag(false);
            supplierStatusInfoVO.setSupplierType(ContractAggConstant.SupplierType.FL.getType());
            supplierStatusInfoVO.setTemplateType(HomepageTemplateConstant.TemplateType.COMMON.getValue());
            supplierStatusInfoVO.setTemplateName(HomepageTemplateConstant.DEFAULT_COMMON_TEMPLATE_VALUE);
        }
        return supplierStatusInfoVO;
    }

    @Override
    public UnverifiedSupplierHomeVO getUnverifiedSupplierHomeInfo(Long supplierId) {
        UnverifiedSupplierHomeVO homeVO = null;
        MainSectionDTO mainSection = onlineSectionService.getMainSection(supplierId);
        if (Objects.nonNull(mainSection)) {
            homeVO = new UnverifiedSupplierHomeVO();
            List<SectionVerifiableFieldDTO> fieldDTOs = mainSection.getBusinessTypes();
            homeVO.setBusinessType(convertBusinessTypetoString(fieldDTOs));
            homeVO.setOemServiceFlag(mainSection.getOemServiceFlag());
            homeVO.setStaffCnt(RefCodeUtil.getValue(mainSection.getEmployeeCntItem()));

            List<ContactPersonAggDTO> contactPersons = mainSection.getContactPersons();
            if (CollectionUtils.isNotEmpty(contactPersons)) {
                ContactPersonAggDTO contactPerson = contactPersons.get(0);
                homeVO.setJobTitle(contactPerson.getJobTitle());
                homeVO.setTitle(contactPerson.getTitle());
                homeVO.setFirstName(contactPerson.getFirstName());
                homeVO.setLastName(contactPerson.getLastName());

            }
            ContactAddressAggDTO contactAddress = mainSection.getContactAddress();
            buildAddressInfo(homeVO, contactAddress);
        }
        return homeVO;
    }

    private String convertBusinessTypetoString(List<SectionVerifiableFieldDTO> fieldDTOs) {
        if (CollectionUtils.isNotEmpty(fieldDTOs)) {
            List<String> businessTypes = Lists.newArrayList();
            fieldDTOs.forEach(fieldDTO -> {
                if (StringUtils.isNotBlank(fieldDTO.getDisplayValue())) {
                    businessTypes.add(fieldDTO.getDisplayValue());
                }
            });
            if (CollectionUtils.isNotEmpty(businessTypes)) {
                return StringUtils.join(businessTypes, ", ");
            }
        }
        return null;
    }

    private void buildAddressInfo(UnverifiedSupplierHomeVO homeVO, ContactAddressAggDTO contactAddress) {
        if (Objects.nonNull(contactAddress)) {

            List<String> addresss = Lists.newArrayList();
            if (StringUtils.isNotBlank(contactAddress.getAddr1())) {
                addresss.add(contactAddress.getAddr1());
            }
            if (StringUtils.isNotBlank(contactAddress.getAddr2())) {
                addresss.add(contactAddress.getAddr2());
            }
            if (StringUtils.isNotBlank(contactAddress.getAddr3())) {
                addresss.add(contactAddress.getAddr3());
            }
            if (CollectionUtils.isNotEmpty(addresss)) {
                homeVO.setAddress(StringUtils.join(addresss, ", "));
            }

            homeVO.setCity(contactAddress.getCity());
            homeVO.setRegion(RefCodeUtil.getValue(contactAddress.getCountryCodeItem()));
            homeVO.setState(RefCodeUtil.getValue(contactAddress.getStateCodeItem()));
            homeVO.setZipCode(contactAddress.getZipCode());
        }
    }

    private String convertMarketToString(List<ExportMarketDTO> markets) {
        List<String> exportMarket = Optional.ofNullable(markets).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .map(ExportMarketDTO::getExportMarkets).filter(Objects::nonNull)
                .map(SectionVerifiableFieldDTO::getDisplayValue)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(exportMarket)) {
            return StringUtils.join(exportMarket, ", ");
        }
        return null;
    }

    @Override
    public ProductSupplierSnapshotVO getProductSupplierSnapshotVo(Long supplierId, Long userId) {
        Result<ProductSupplierSnapshotAggVO> productSupplierSnapshotVOResult = supplierProfileAggFeign.getProductSupplierSnapshotVO(supplierId);
        if (!ResultCode.CommonResultCode.SUCCESS.getCode().equals(productSupplierSnapshotVOResult.getCode())) {
            log.error("getProductSupplierSnapshotVo error, supplierId:{}, result: {}", supplierId, productSupplierSnapshotVOResult);
            throw new BusinessException(ResultCode.CommonResultCode.FAILED.getCode(), "failed to get supplier snapshot info");
        }
        ProductSupplierSnapshotVO productSupplierSnapshotVO = null;
        ProductSupplierSnapshotAggVO data = productSupplierSnapshotVOResult.getData();
        if (Objects.nonNull(data)) {
            productSupplierSnapshotVO = OrikaMapperUtil.coverObject(data, ProductSupplierSnapshotVO.class);
            Boolean collectFlag = checkWhetherFavoriteSupplier(userId, supplierId);
            productSupplierSnapshotVO.setCollectFlag(Optional.ofNullable(collectFlag).orElse(false));

        }
        return productSupplierSnapshotVO;
    }

    @Override
    public OnlineSupplierHeaderSnapshotVO getOnlineHeaderSnapshot(Long supplierId, Long userId, String langCode) {
        OnlineSupplierHeaderSnapshotVO snapshot = null;
        Result<List<SupplierCommonInfoDTO>> commonInfosResult = supplierAggFeign.getCommonInfos(SuppCommonInfoQueryAggDTO.builder()
                .supplierIds(Lists.newArrayList(supplierId)).mainProdFlag(true).build());
        // 待优化
        SuppSeoInfoAggDTO seoInfo = supplierSeoService.getSeoInfoBySupplierId(supplierId, false);
        String shortName = Optional.ofNullable(seoInfo).map(SuppSeoInfoAggDTO::getSupplierShortName).orElse(StringUtils.EMPTY);
        List<SupplierCommonInfoDTO> commonInfos = ResultUtil.getData(commonInfosResult);
        SupplierCommonInfoDTO commonInfo = Optional.ofNullable(commonInfos).flatMap(list -> list.stream().filter(Objects::nonNull).findFirst()).orElse(null);
        if (Objects.nonNull(commonInfo)) {
            snapshot = new OnlineSupplierHeaderSnapshotVO();
            Boolean collectFlag = checkWhetherFavoriteSupplier(userId, supplierId);
            snapshot.setCollectFlag(Optional.ofNullable(collectFlag).orElse(false));
            snapshot.setBrFlag(Boolean.TRUE.equals(commonInfo.getBrFlag()));
            snapshot.setOrgId(commonInfo.getSupplierId());
            snapshot.setSupplierId(commonInfo.getSupplierId());
            snapshot.setCompanyName(commonInfo.getCompanyDisplayName());
            snapshot.setMaxContractLevel(commonInfo.getMaxContractLevel());
            snapshot.setContractCode(commonInfo.getContractCode());
            snapshot.setContractGroupCode(commonInfo.getContractGroupCode());
            snapshot.setMemberTypeNum(commonInfo.getMemberTypeNum());
            snapshot.setMemberSince(commonInfo.getMemberSince());
            snapshot.setO2oFlag(commonInfo.getO2oFlag());
            snapshot.setPayIconFlag(commonInfo.getPayIconFlag());
            snapshot.setVerifiedSupplierFlag(commonInfo.getVerifiedSupplierFlag());
            snapshot.setVerifiedManufacturerFlag(commonInfo.getVerifiedManufacturerFlag());
            snapshot.setSupplierType(commonInfo.getSupplierType());
            snapshot.setScaIconFlag(commonInfo.getScaIconFlag());
            snapshot.setAvgResponseTimeRatingEnum(commonInfo.getAvgResponseTimeRatingEnum());
            snapshot.setResponseRateRating(commonInfo.getResponseRateRating());
            snapshot.setCountry(commonInfo.getCountry());

            snapshot.setLogoUrl(commonInfo.getLogoUrl());
            snapshot.setBusinessTypes(convertBusinessTypetoString(commonInfo.getBusinessTypes()));
            snapshot.setMainMarkets(convertMarketToString(commonInfo.getMainMarkets()));
            snapshot.setVrFlag(SupplierInfoUtils.getVrFlag(commonInfo));
            snapshot.setSupplierShortName(shortName);
            snapshot.setOnlineFlag(commonInfo.getOnlineFlag());
            snapshot.setMainProductKeywords(commonInfo.getMainProdKeywords());

            // 获取主页模板设置
            Result<HomepageTemplateSettingAggVO> homepageTemplateSettingResult = organizationPreferenceAggFeign.getHomepageTemplateSetting(supplierId);
            HomepageTemplateSettingAggVO homepageTemplateSetting = ResultUtil.getData(homepageTemplateSettingResult, "failed to getHomepageTemplateSetting, supplierId: " + supplierId);

            snapshot.setTemplateType(Optional.ofNullable(homepageTemplateSetting).map(HomepageTemplateSettingAggVO::getTemplateType).orElse(HomepageTemplateConstant.TemplateType.COMMON.getValue()));
            snapshot.setTemplateName(Optional.ofNullable(homepageTemplateSetting).map(HomepageTemplateSettingAggVO::getTemplateName).orElse(HomepageTemplateConstant.DEFAULT_COMMON_TEMPLATE_VALUE));

        }

        return snapshot;
    }

    private Boolean checkWhetherFavoriteSupplier(Long userId, Long supplierId) {
        Boolean result = false;
        if (Objects.nonNull(userId)) {
            List<UserFavoriteSimpleVO> userFavoriteInfos = userFavoriteCollectionFeign.internalFavoriteList(userId);
            if (!CollectionUtils.isEmpty(userFavoriteInfos) && Objects.nonNull(supplierId)) {
                Set<Long> supplierIds = userFavoriteInfos.stream().filter(dto -> Objects.nonNull(dto) && Objects.nonNull(dto.getSupplierId()) && UserFavoriteEnum.FAVORITE_SUPPLIER.type().equals(dto.getType()))
                        .map(UserFavoriteSimpleVO::getSupplierId).collect(Collectors.toSet());
                result = !CollectionUtils.isEmpty(supplierIds) && supplierIds.contains(supplierId);
            }
        }
        return result;
    }

    @Override
    public Set<Long> getFavoriteSupplierIds(Long userId) {
        Set<Long> supplierIds = Sets.newHashSet();
        if (Objects.nonNull(userId)) {
            List<UserFavoriteSimpleVO> userFavoriteInfos = userFavoriteCollectionFeign.internalFavoriteList(userId);
            if (!CollectionUtils.isEmpty(userFavoriteInfos)) {
                supplierIds = userFavoriteInfos.stream().filter(dto -> Objects.nonNull(dto) && Objects.nonNull(dto.getSupplierId()) && UserFavoriteEnum.FAVORITE_SUPPLIER.type().equals(dto.getType()))
                        .map(UserFavoriteSimpleVO::getSupplierId).collect(Collectors.toSet());
            }
        }
        return supplierIds;
    }

    @Override
    public OnlineSupplierProfileVO getOnlineSupplierProfile(Long orgId, String langCode) {
        OnlineSupplierProfileVO onlineSupplierProfileVO = null;
        if (Objects.isNull(orgId)) {
            return null;
        }

        SupplierCommonInfoDTO commonInfo = getCommonInfo(orgId);
        String supplierType = Optional.ofNullable(commonInfo).map(SupplierCommonInfoDTO::getSupplierType).orElse("FL");
        boolean verifiedSuppFlag = ContractAggConstant.SupplierType.ADV.getType().equals(supplierType);

        CompanyProfileDTO profileDTO = null;
        MainSectionDTO mainSection = null;
        List<OnlineCertificateDTO> certList = null;
        if (verifiedSuppFlag) {
            Result<CompanyProfileDTO> profileDTOResult = supplierAggFeign.getFullCompanyProfile(orgId);
            profileDTO = ResultUtil.getData(profileDTOResult);
            mainSection = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getMainSection).orElse(null);
            certList = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getCertificateInfos).orElse(null);
        } else {
            //agg 不显示cert 不显示verified businessType
            mainSection = onlineSectionService.getMainSection(orgId);
        }

        if (Objects.isNull(mainSection)) {
            return null;
        }
        onlineSupplierProfileVO = new OnlineSupplierProfileVO();
        onlineSupplierProfileVO.setCompanyName(mainSection.getWebsiteDisplayName());
        onlineSupplierProfileVO.setMaxContractLevel(Optional.ofNullable(commonInfo).map(SupplierCommonInfoDTO::getMaxContractLevel).orElse(-2));
        onlineSupplierProfileVO.setContractCode(Optional.ofNullable(commonInfo).map(SupplierCommonInfoDTO::getContractCode).orElse(null));
        onlineSupplierProfileVO.setContractGroupCode(Optional.ofNullable(commonInfo).map(SupplierCommonInfoDTO::getContractGroupCode).orElse(null));
        onlineSupplierProfileVO.setMemberTypeNum(Optional.ofNullable(commonInfo).map(SupplierCommonInfoDTO::getMemberTypeNum).orElse(null));
        onlineSupplierProfileVO.setSupplierType(supplierType);
        SupplierOverviewVO companyOverview = getCompanyOverview(orgId, commonInfo, mainSection, true, certList);
        //overview
        onlineSupplierProfileVO.setSupplierOverviewVO(companyOverview);


        //production capacity
        SupplierProductionFormVO productionFormBySupplierId = getProduction(profileDTO, verifiedSuppFlag);

        if (verifiedSuppFlag) {
            onlineSupplierProfileVO.setProductionCapacity(productionFormBySupplierId);

            // trtrade capabilities
            SupplierTradeFormVO supplierTradeForm = getSupplierTradeFormVO(mainSection);
            onlineSupplierProfileVO.setTrTradeCapabilities(supplierTradeForm);

//        // quality control
            SupplierQualityFormVO companyQualityForm = getQcFrom(profileDTO);
            onlineSupplierProfileVO.setQualityControl(companyQualityForm);
            //R&D CAPACITY
            SupplierRdFromVO supplierRdForm = getSupplierRdInfo(profileDTO);
            onlineSupplierProfileVO.setRdCapacity(supplierRdForm);

            //Supplier creadit info
            ComprehensiveBRProfileVO comprehensiveBRProfile = getComprehensiveBRProfile(profileDTO);
            onlineSupplierProfileVO.setComprehensiveBRProfile(comprehensiveBRProfile);

//        supplier custom info
            List<SupplierCustomEditVO> customInfos = getSupplierCustomInfos(profileDTO);
            onlineSupplierProfileVO.setSupplierCustomDetails(customInfos);

            //supplier certificate
            onlineSupplierProfileVO.setCertificationForm(getCertificateFromInfo(profileDTO));

            onlineSupplierProfileVO.setScaInfo(getScaInfo(profileDTO));

            OrganizationPreferenceDTO preferenceDTO = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getPreferenceInfo).orElse(null);
            if (Objects.nonNull(preferenceDTO)) {
                onlineSupplierProfileVO.setBannerVRUrl(preferenceDTO.getBannerVRUrl());
                onlineSupplierProfileVO.setCompleteTourVRURL(preferenceDTO.getCompleteTourVRURL());
            }
        }
        return onlineSupplierProfileVO;
    }

    @Override
    @Cacheable(value = RedisKey.WEB_BFF_PP_SUPP_PROFILE, key = "'orgId_'+#orgId+'_langCode_'+#langCode")
    public OnlineSupplierProfileInProductVO getOnlineSupplierProfileProduct(Long orgId, String langCode) {
        OnlineSupplierProfileInProductVO onlineSupplierProfileInProductVO = null;
        if (Objects.isNull(orgId)) {
            return null;
        }

        SupplierCommonInfoDTO commonInfo = getCommonInfo(orgId);
        String supplierType = Optional.ofNullable(commonInfo).map(SupplierCommonInfoDTO::getSupplierType).orElse("FL");
        boolean verifiedSuppFlag = ContractAggConstant.SupplierType.ADV.getType().equals(supplierType);

        CompanyProfileDTO profileDTO = null;
        MainSectionDTO mainSection = null;
        List<OnlineCertificateDTO> certList = null;
        if (verifiedSuppFlag) {
            Result<CompanyProfileDTO> profileDTOResult = supplierAggFeign.getFullCompanyProfile(orgId);
            profileDTO = ResultUtil.getData(profileDTOResult);
            mainSection = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getMainSection).orElse(null);
            certList = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getCertificateInfos).orElse(null);
        } else {
            //agg 不显示cert 不显示verified businessType
            mainSection = onlineSectionService.getMainSection(orgId);
        }

        if (Objects.isNull(mainSection)) {
            return null;
        }
        onlineSupplierProfileInProductVO = new OnlineSupplierProfileInProductVO();
        SupplierOverviewInProductVO companyOverview = getCompanyOverviewForProduct(commonInfo,
                mainSection, certList);
        //overview
        onlineSupplierProfileInProductVO.setSupplierOverviewVO(companyOverview);

        //production capacity
        SupplierProductionFormVO productionFormBySupplierId = getProduction(profileDTO, verifiedSuppFlag);

        if (verifiedSuppFlag) {
            onlineSupplierProfileInProductVO.setProductionCapacity(productionFormBySupplierId);


            // quality control
            SupplierQualityFormVO companyQualityForm = getQcFrom(profileDTO);
            onlineSupplierProfileInProductVO.setQualityControl(companyQualityForm);
            //R&D CAPACITY
            SupplierRdFromVO supplierRdForm = getSupplierRdInfo(profileDTO);
            onlineSupplierProfileInProductVO.setRdCapacity(supplierRdForm);

        }
        return onlineSupplierProfileInProductVO;
    }

    private SupplierCommonInfoDTO getCommonInfo(Long supplierId) {
        Result<SupplierCommonInfoDTO> commonInfoResult = supplierAggFeign.getCommonInfo(supplierId);
        return ResultUtil.getData(commonInfoResult);
    }


    @Override
    public SupplierOverviewVO getCompanyOverview(Long supplierId, MainSectionDTO mainSection, boolean needBasicInfo) {
        Result<List<OnlineCertificateDTO>> certificatesResult = supplierAggFeign.getCertificates(SuppCertQueryAggDTO.builder().supplierId(supplierId).build());
        List<OnlineCertificateDTO> certificates = ResultUtil.getData(certificatesResult, "getCertificates");
        return getCompanyOverview(supplierId, mainSection, needBasicInfo, certificates);
    }

    private SupplierOverviewVO getCompanyOverview(Long supplierId, SupplierCommonInfoDTO commonInfo,
                                                  MainSectionDTO mainSection, boolean needBasicInfo,
                                                  List<OnlineCertificateDTO> certificates) {
        needBasicInfo = Optional.ofNullable(needBasicInfo).orElse(Boolean.FALSE);
        SupplierOverviewDetailVO overviewDetailVO = new SupplierOverviewDetailVO();
        SupplierOverviewVO overviewVO = null;
        SupplierOverviewBasicInfoVO overviewBasicInfoVO = new SupplierOverviewBasicInfoVO();
        if (Objects.isNull(mainSection) || Objects.isNull(commonInfo)) {
            return null;
        }

        overviewVO = new SupplierOverviewVO();
        boolean verifiedSuppFlag = ContractAggConstant.SupplierType.ADV.getType().equals(commonInfo.getSupplierType());

        overviewDetailVO.setCompanyName(commonInfo.getCompanyDisplayName());
        overviewDetailVO.setResponseRateRating(commonInfo.getResponseRateRating());
        overviewDetailVO.setAvgResponseTimeRatingEnum(commonInfo.getAvgResponseTimeRatingEnum());
        overviewDetailVO.setWebsiteSub(commonInfo.getDisplayMessage());
        overviewDetailVO.setWebsiteSubUsp(commonInfo.getSubDisplayMessage());

        overviewDetailVO.setDescription(Optional.ofNullable(mainSection.getOrgDesc()).orElse(""));
        Integer level = commonInfo.getMaxContractLevel();
        //agg 供应商 隐藏视频, 1星以上并且有video封面和video URL
        boolean videoFlag = checkDisplayVideo(mainSection, level);
        if (videoFlag) {
            SupplierCompanyAnnexEditVO video = new SupplierCompanyAnnexEditVO();
            video.setUrl(mainSection.getVideoUrl());
            video.setCoverImageUrl(mainSection.getVideoCoverUrl());
            video.setUploadDate(mainSection.getVideoUploadDate());
            overviewVO.setCompanyVideo(video);
        }
        if (needBasicInfo) {
            String finalVendorName = Optional.ofNullable(commonInfo.getBrVendorName()).orElse("");
            buildBusinessType(commonInfo.getBusinessTypes(), overviewBasicInfoVO, finalVendorName, verifiedSuppFlag);
            buildExportMarkets(commonInfo.getMainMarkets(), overviewBasicInfoVO, finalVendorName);
            overviewBasicInfoVO.setBusinessTypes(SupplierUtil.getFieldValues(commonInfo.getBusinessTypes()));
            overviewBasicInfoVO.setTotalNoEmployees(commonInfo.getTotalNoEmployees());
            overviewBasicInfoVO.setTotalAnnualSales(commonInfo.getTotalAnnualSales());
            overviewBasicInfoVO.setYearEstablished(Objects.nonNull(commonInfo.getYearEstablished()) ? commonInfo.getYearEstablished() : "");
            overviewBasicInfoVO.setTotalCapitalization(RefCodeUtil.getValue(mainSection.getTotOrgCapitalItem()));
            overviewBasicInfoVO.setMajorCustomerNames(SupplierUtil.getFieldValues(mainSection.getCustomers()));
            overviewBasicInfoVO.setMainMarkets(String.join(",", convertEsMarketToString(commonInfo.getMainMarkets())));
        }


        OrgPreferenceInfoQueryAggDTO orgPreQuery = OrgPreferenceInfoQueryAggDTO.builder().supplierId(supplierId).scaDetailFlag(false).build();
        Result<List<OrganizationPreferenceDTO>> orgPreferenceInfos = organizationPreferenceAggFeign.getOrgPreferenceInfos(orgPreQuery);
        List<OrganizationPreferenceDTO> orgPreferences = ResultUtil.getData(orgPreferenceInfos, "failed to getOrgPreferenceInfos");
        OrganizationPreferenceDTO preferenceDTO = Optional.ofNullable(orgPreferences).flatMap(list -> list.stream().filter(Objects::nonNull).findFirst()).orElse(null);
        if (needBasicInfo) {
            if (Objects.nonNull(preferenceDTO)) {
                RegistrationInfoDTO registrationInfo = preferenceDTO.getRegistrationInfo();
                if (Objects.nonNull(registrationInfo)) {
                    overviewBasicInfoVO.setRegAddress(registrationInfo.getRegAddress());
                    overviewBasicInfoVO.setRegCompanyName(registrationInfo.getRegCompanyName());
                    overviewBasicInfoVO.setRegNumber(registrationInfo.getRegNumber());
                    overviewBasicInfoVO.setRegURL(registrationInfo.getRegURL());
                }
            }
            overviewBasicInfoVO.setCountry(commonInfo.getCountry());
            CountryAndRegionVO countryAndRegion = new CountryAndRegionVO();
            countryAndRegion.setCountry(overviewBasicInfoVO.getCountry());
            overviewBasicInfoVO.setCountryAndRegion(countryAndRegion);
            buildCertificateStandards(overviewBasicInfoVO, certificates);
        }

        overviewVO.setOverViewDetail(overviewDetailVO);
        if (needBasicInfo) {
            overviewVO.setOverViewBasicInfo(overviewBasicInfoVO);
        }
        overviewVO.setCompanyPhotos(new ArrayList<>());
        return overviewVO;
    }

    private SupplierOverviewInProductVO getCompanyOverviewForProduct(SupplierCommonInfoDTO commonInfo,
                                                                     MainSectionDTO mainSection,
                                                                     List<OnlineCertificateDTO> certificates) {
        if (Objects.isNull(mainSection) || Objects.isNull(commonInfo)) {
            return null;
        }

        SupplierOverviewBasicInfoInProductVO overviewBasicInfoVO = new SupplierOverviewBasicInfoInProductVO();
        SupplierOverviewInProductVO overviewVO = new SupplierOverviewInProductVO();
        boolean verifiedSuppFlag = ContractAggConstant.SupplierType.ADV.getType().equals(commonInfo.getSupplierType());

        String finalVendorName = Optional.ofNullable(commonInfo.getBrVendorName()).orElse("");
        buildBusinessType2(commonInfo.getBusinessTypes(), overviewBasicInfoVO, finalVendorName, verifiedSuppFlag);
        buildExportMarkets2(commonInfo.getMainMarkets(), overviewBasicInfoVO, finalVendorName);
        overviewBasicInfoVO.setBusinessTypes(SupplierUtil.getFieldValues(commonInfo.getBusinessTypes()));
        overviewBasicInfoVO.setTotalNoEmployees(commonInfo.getTotalNoEmployees());
        overviewBasicInfoVO.setTotalAnnualSales(commonInfo.getTotalAnnualSales());
        overviewBasicInfoVO.setYearEstablished(Objects.nonNull(commonInfo.getYearEstablished()) ? commonInfo.getYearEstablished() : "");
        overviewBasicInfoVO.setTotalCapitalization(RefCodeUtil.getValue(mainSection.getTotOrgCapitalItem()));
        overviewBasicInfoVO.setMajorCustomerNames(SupplierUtil.getFieldValues(mainSection.getCustomers()));
        overviewBasicInfoVO.setMainMarkets(String.join(",", convertEsMarketToString(commonInfo.getMainMarkets())));
        overviewBasicInfoVO.setExportPercentage(SupplierAggRefCodeUtil.getValue(mainSection.getProductExportPercentageItem()));
        overviewBasicInfoVO.setProductExportPercentage(mainSection.getProductExportPercentage());
        overviewBasicInfoVO.setOemServiceFlag(mainSection.getOemServiceFlag());
        overviewBasicInfoVO.setSmallOrdersAcceptedFlag(mainSection.getSmallOrdersAcceptedFlag());
        overviewBasicInfoVO.setBrandNameDesc(mainSection.getBrandNameDesc());
        overviewBasicInfoVO.setPaymentTerm(mainSection.getPaymentTerm());
        overviewBasicInfoVO.setMainAdvantages(SupplierAggRefCodeUtil.getValues(mainSection.getMainAdvantagesItem()));
        overviewBasicInfoVO.setMainAdvantagesItem(mainSection.getMainAdvantagesItem());
        overviewBasicInfoVO.setOtherAdvantage(mainSection.getOtherAdvantage());
        overviewBasicInfoVO.setCustomers(mainSection.getCustomers());

        overviewBasicInfoVO.setCountry(commonInfo.getCountry());
        CountryAndRegionVO countryAndRegion = new CountryAndRegionVO();
        countryAndRegion.setCountry(overviewBasicInfoVO.getCountry());
        overviewBasicInfoVO.setCountryAndRegion(countryAndRegion);
        buildCertificateStandards2(overviewBasicInfoVO, certificates);

        overviewVO.setOverViewBasicInfo(overviewBasicInfoVO);


        return overviewVO;
    }

    private void buildExportMarkets(List<ExportMarketDTO> exportMarketInfos, SupplierOverviewBasicInfoVO overviewBasicInfoVO, String finalVendorName) {
        if (!CollectionUtils.isEmpty(exportMarketInfos)) {
            List<ProfileMarketVO> exportMarketInfoVos = exportMarketInfos.stream().filter(v -> Objects.nonNull(v) && Objects.nonNull(v.getExportMarkets()) && StringUtils.isNotEmpty(v.getExportMarkets().getDisplayValue())).map(v -> {
                ProfileMarketVO profileMarketVo = new ProfileMarketVO();
                SupplierInfoUtils.convertProfileVerifiedAttributeVo(v.getExportMarkets(), profileMarketVo, finalVendorName, null);
                if (!CollectionUtils.isEmpty(v.getExportCountries())) {
                    List<String> exportCountryValueList = v.getExportCountries().stream().map(SectionVerifiableFieldDTO::getDisplayValue).collect(Collectors.toList());
                    profileMarketVo.setExportCountries(exportCountryValueList);
                }
                return profileMarketVo;
            }).collect(Collectors.toList());
            overviewBasicInfoVO.setExportMarketInfos(exportMarketInfoVos);
        }
    }

    private void buildExportMarkets2(List<ExportMarketDTO> exportMarketInfos,
                                     SupplierOverviewBasicInfoInProductVO overviewBasicInfoVO, String finalVendorName) {
        if (!CollectionUtils.isEmpty(exportMarketInfos)) {
            List<ProfileMarketVO> exportMarketInfoVos = exportMarketInfos.stream().filter(v -> Objects.nonNull(v) && Objects.nonNull(v.getExportMarkets()) && StringUtils.isNotEmpty(v.getExportMarkets().getDisplayValue())).map(v -> {
                ProfileMarketVO profileMarketVo = new ProfileMarketVO();
                SupplierInfoUtils.convertProfileVerifiedAttributeVo(v.getExportMarkets(), profileMarketVo, finalVendorName, null);
                if (!CollectionUtils.isEmpty(v.getExportCountries())) {
                    List<String> exportCountryValueList = v.getExportCountries().stream().map(SectionVerifiableFieldDTO::getDisplayValue).collect(Collectors.toList());
                    profileMarketVo.setExportCountries(exportCountryValueList);
                }
                return profileMarketVo;
            }).collect(Collectors.toList());
            overviewBasicInfoVO.setExportMarketInfos(exportMarketInfoVos);
        }
    }

    private void buildBusinessType(List<SectionVerifiableFieldDTO> businessTypes, SupplierOverviewBasicInfoVO overviewBasicInfoVO, String finalVendorName, Boolean verifiedSuppFlag) {
        if (!CollectionUtils.isEmpty(businessTypes)) {
            List<ProfileVerifiableAttributeVO> businessVoList = SupplierInfoUtils.convertBusinessTypeToProfileAttributeVoBatch(businessTypes, finalVendorName, verifiedSuppFlag);
            overviewBasicInfoVO.setBusinessTypeInfos(businessVoList);
        }
    }


    private void buildBusinessType2(List<SectionVerifiableFieldDTO> businessTypes,
                                    SupplierOverviewBasicInfoInProductVO overviewBasicInfoVO, String finalVendorName, Boolean verifiedSuppFlag) {
        if (!CollectionUtils.isEmpty(businessTypes)) {
            List<ProfileVerifiableAttributeVO> businessVoList = SupplierInfoUtils.convertBusinessTypeToProfileAttributeVoBatch(businessTypes, finalVendorName, verifiedSuppFlag);
            overviewBasicInfoVO.setBusinessTypeInfos(businessVoList);
        }
    }

    private boolean checkDisplayVideo(MainSectionDTO mainSection, Integer level) {
        return Objects.nonNull(mainSection.getVideoId())
                && StringUtils.isNotEmpty(mainSection.getVideoCoverUrl()) && Optional.ofNullable(level).orElse(-2) >= BigInteger.ONE.intValue();
    }

    @Override
    public SupplierOverviewVO getCompanyOverview(Long supplierId, MainSectionDTO mainSection, boolean needBasicInfo, List<OnlineCertificateDTO> certificates) {
        SupplierCommonInfoDTO commonInfo = getCommonInfo(supplierId);
        return getCompanyOverview(supplierId, commonInfo, mainSection, needBasicInfo, certificates);
    }

    public void buildCertificateStandards(CompanyProfileDTO profileDTO, SupplierOverviewBasicInfoVO overviewBasicInfoVO) {
        List<OnlineCertificateDTO> certificateInfos = profileDTO.getCertificateInfos();

        buildCertificateStandards(overviewBasicInfoVO, certificateInfos);
    }

    private void buildCertificateStandards(SupplierOverviewBasicInfoVO overviewBasicInfoVO, List<OnlineCertificateDTO> certificateInfos) {
        if (CollectionUtils.isEmpty(certificateInfos)) {
            return;
        }
        List<String> companyCertStandards = new ArrayList<>();
        List<String> productCertStandards = new ArrayList<>();
        overviewBasicInfoVO.setProductCertStandards(productCertStandards);
        certificateInfos.stream().filter(Objects::nonNull)
                //order by update time desc
                .sorted(Comparator.comparingLong((OnlineCertificateDTO cert) -> Objects.nonNull(cert.getLUpdDate()) ? cert.getLUpdDate().getTime() : 0).reversed())
                .forEach(cert -> {
                    String standard = null;
                    if (!StringUtils.isEmpty(cert.getCertificateType())) {
                        standard = RefCodeUtil.getValue(cert.getCertificateStandardItem());
                        if (StringUtils.isEmpty(standard)) {
                            standard = cert.getOtherCertificateStandard();
                        }
                    }
                    if (!StringUtils.isEmpty(standard)) {
                        if (BusinessConstant.CertType.PRODUCT.getType().equals(cert.getCertificateType())) {
                            productCertStandards.add(standard);
                        } else if (BusinessConstant.CertType.COMPANY.getType().equals(cert.getCertificateType())) {
                            companyCertStandards.add(standard);
                        }
                    }
                });
        overviewBasicInfoVO.setProductCertStandards(productCertStandards.stream().filter(c -> !StringUtils.isEmpty(c)).distinct().collect(Collectors.toList()));
        overviewBasicInfoVO.setCompanyCertStandards(companyCertStandards.stream().filter(c -> !StringUtils.isEmpty(c)).distinct().collect(Collectors.toList()));
    }

    private void buildCertificateStandards2(SupplierOverviewBasicInfoInProductVO overviewBasicInfoVO,
                                            List<OnlineCertificateDTO> certificateInfos) {
        if (CollectionUtils.isEmpty(certificateInfos)) {
            return;
        }
        List<String> companyCertStandards = new ArrayList<>();
        List<String> productCertStandards = new ArrayList<>();
        overviewBasicInfoVO.setProductCertStandards(productCertStandards);
        certificateInfos.stream().filter(Objects::nonNull)
                //order by update time desc
                .sorted(Comparator.comparingLong((OnlineCertificateDTO cert) -> Objects.nonNull(cert.getLUpdDate()) ? cert.getLUpdDate().getTime() : 0).reversed())
                .forEach(cert -> {
                    String standard = null;
                    if (!StringUtils.isEmpty(cert.getCertificateType())) {
                        standard = RefCodeUtil.getValue(cert.getCertificateStandardItem());
                        if (StringUtils.isEmpty(standard)) {
                            standard = cert.getOtherCertificateStandard();
                        }
                    }
                    if (!StringUtils.isEmpty(standard)) {
                        if (BusinessConstant.CertType.PRODUCT.getType().equals(cert.getCertificateType())) {
                            productCertStandards.add(standard);
                        } else if (BusinessConstant.CertType.COMPANY.getType().equals(cert.getCertificateType())) {
                            companyCertStandards.add(standard);
                        }
                    }
                });
        overviewBasicInfoVO.setProductCertStandards(productCertStandards.stream().filter(c -> !StringUtils.isEmpty(c)).distinct().collect(Collectors.toList()));
        overviewBasicInfoVO.setCompanyCertStandards(companyCertStandards.stream().filter(c -> !StringUtils.isEmpty(c)).distinct().collect(Collectors.toList()));
    }

    @Override
    public List<String> convertEsMarketToString(List<ExportMarketDTO> markets) {
        return Optional.ofNullable(markets).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .map(ExportMarketDTO::getExportMarkets).filter(Objects::nonNull)
                .map(SectionVerifiableFieldDTO::getDisplayValue)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
    }

    private SupplierTradeFormVO getSupplierTradeFormVO(MainSectionDTO mainSection) {
        SupplierTradeFormVO supplierTradeForm = null;
        if (Objects.isNull(mainSection)) {
            return null;
        }
        supplierTradeForm = new SupplierTradeFormVO();
        SupplierTradeVO supplierTradeVO = new SupplierTradeVO();
        supplierTradeVO.setTotalAnnualSales(RefCodeUtil.getValue(mainSection.getTotAnnualSalesItem()));
        supplierTradeVO.setExportPercentage(RefCodeUtil.getValue(mainSection.getProductExportPercentageItem()));
        supplierTradeVO.setPaymentMethod(mainSection.getPaymentTerm());
        supplierTradeVO.setHasOemServiceFlag(mainSection.getOemServiceFlag());
        supplierTradeVO.setBrandName(mainSection.getBrandNameDesc());
        supplierTradeVO.setHasSmallOrder(mainSection.getSmallOrdersAcceptedFlag());
        supplierTradeVO.setMainAdvantage(mainSection.getMainAdvantagesItem() != null ? String.join(", ", RefCodeUtil.getValues(mainSection.getMainAdvantagesItem())) : null);
        supplierTradeVO.setOtherAdvantage(mainSection.getOtherAdvantage());
        List<SectionVerifiableFieldDTO> customerDtos = mainSection.getCustomers();
        List<String> customerNames = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(customerDtos)) {
            customerDtos.forEach(customer -> customerNames.add(customer.getValue()));
            supplierTradeVO.setMajorCustomers(String.join(",", customerNames));
        }
        supplierTradeVO.setMainMarkets(String.join(",", convertEsMarketToString(mainSection.getExportMarkets())));
        supplierTradeVO.setHasOverseasWarehouse(false);
        List<SectionVerifiableFieldDTO> customers = mainSection.getCustomers();
        List<SupplierTradeBaseAddressEditVO> customerVos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(customers)) {
            supplierTradeVO.setAddMainCustomer(true);
            customers.stream().map(c -> RefCodeUtil.getValue(c.getValueRefCodeInfo())).forEach(name -> {
                SupplierTradeBaseAddressEditVO majorCustomer = new SupplierTradeBaseAddressEditVO();
                majorCustomer.setCustomerName(name);
                customerVos.add(majorCustomer);
            });
        } else {
            supplierTradeVO.setAddMainCustomer(false);
        }
        supplierTradeForm.setMainCustomerAddress(customerVos);

        List<SupplierTradeOverseasAgentAddressEditVO> agentList = new ArrayList<>();
        List<ContactInformationDTO> contactInfos = mainSection.getContactInformations();
        if (!CollectionUtils.isEmpty(contactInfos)) {
            supplierTradeVO.setHasOverseasAgent(true);
            for (ContactInformationDTO contactInfo : contactInfos) {
                ContactAddressAggDTO contactAddress = contactInfo.getContactAddress();
                SupplierTradeOverseasAgentAddressEditVO supplierTradeOverseasAgentAddressEditVO = buildSupplierTradeOverseasAgentAddressEditVO(SupplierAggContactUtil.transferOtherContact(contactAddress));
                if (Objects.nonNull(supplierTradeOverseasAgentAddressEditVO)) {
                    agentList.add(supplierTradeOverseasAgentAddressEditVO);
                }
            }
        } else {
            supplierTradeVO.setHasOverseasAgent(false);
        }
        supplierTradeForm.setOverseasAgentAddress(agentList);
        supplierTradeForm.setSupplierTradeVO(supplierTradeVO);
        return supplierTradeForm;
    }

    private SupplierTradeOverseasAgentAddressEditVO buildSupplierTradeOverseasAgentAddressEditVO(OtherContactDTO otherContactDTO) {
        SupplierTradeOverseasAgentAddressEditVO overseasAgentAddress = null;
        if (Objects.nonNull(otherContactDTO)) {
            overseasAgentAddress = new SupplierTradeOverseasAgentAddressEditVO();
            AddressDTO address = otherContactDTO.getAddress();
            if (address != null) {
                overseasAgentAddress.setAddress(address.getAddress());
                overseasAgentAddress.setCountry(RefCodeUtil.getValue(address.getCountryRefCodeInfo()));
                overseasAgentAddress.setState(RefCodeUtil.getValue(address.getStateRefCodeInfo()));
            }
            List<PhoneDTO> phoneList = otherContactDTO.getPhoneList();
            if (!CollectionUtils.isEmpty(phoneList)) {
                PhoneDTO phoneDto = phoneList.get(0);
                if (phoneDto != null) {
                    overseasAgentAddress.setTelCountryCode(phoneDto.getCountryCode());
                    overseasAgentAddress.setTelAreaCode(phoneDto.getAreaCode());
                    overseasAgentAddress.setTelephone(phoneDto.getTelNum());
                    overseasAgentAddress.setTelExtensionNumber(phoneDto.getExtension());
                }
            }
        }
        return overseasAgentAddress;
    }

    private SupplierProductionFormVO getProduction(CompanyProfileDTO profileDTO, Boolean verifiedSuppFlag) {
        SupplierProductionFormVO result = new SupplierProductionFormVO();
        //默认true 前端可以不改逻辑
        result.setHasBuType(true);
        result.setAddCooperateFactory(false);
        List<SupplierProductionEditVO> supplierProductionList = new ArrayList<>();
        if (Objects.isNull(profileDTO) || !Boolean.TRUE.equals(verifiedSuppFlag)) {
            return result;
        }
        FactoryTourSectionDTO factoryTourSection = profileDTO.getFactoryToursInfo();
        if (Objects.isNull(factoryTourSection) || CollectionUtils.isEmpty(factoryTourSection.getFragments())) {
            return result;
        }
        List<FactoryTourFragmentDTO> factoryTourInfos = factoryTourSection.getFragments();
        for (FactoryTourFragmentDTO factoryTourDto : factoryTourInfos) {
            SupplierProductionEditVO productionVo = new SupplierProductionEditVO();
            productionVo.setAnnexes(ImageUtil.convertAnnexImages(factoryTourDto.getImages()));
            String country = RefCodeUtil.getValue(factoryTourDto.getCountryCodeItem());
            String state = RefCodeUtil.getValue(factoryTourDto.getStateCodeItem());
            String city = factoryTourDto.getCity();
            SupplierAddressEditVO address = buildFactoryAddress(buildAddressList(factoryTourDto), country, state, city);
            productionVo.setAddress(address);
            productionVo.setNumberQcStaff(RefCodeUtil.getValue(factoryTourDto.getQcStaffCntItem()));
            productionVo.setNumberRdStaff(RefCodeUtil.getValue(factoryTourDto.getRdStaffCntItem()));
            productionVo.setNumberProductionStaff(RefCodeUtil.getValue(factoryTourDto.getProductionStaffCntItem()));
            productionVo.setYearEstablished(factoryTourDto.getEstablishedYear() != null ? String.valueOf(factoryTourDto.getEstablishedYear().intValue()) : null);
            Long factorySize = factoryTourDto.getFactorySize();
            if (factorySize != null && factorySize.intValue() > 0) {
                String unit = RefCodeUtil.getValue(factoryTourDto.getFactorySizeUnitItem());
                productionVo.setFactorySize(factorySize.intValue() + " " + unit);
            }
            MainSectionDTO mainSection = profileDTO.getMainSection();
            productionVo.setNoProductionLines(mainSection.getProductionLineCnt());
            supplierProductionList.add(productionVo);
            setFactoryTourCustomInfo(factoryTourDto, productionVo);

        }
        result.setSupplierProductionList(supplierProductionList);
        return result;
    }

    private void setFactoryTourCustomInfo(FactoryTourFragmentDTO factoryTourDto, SupplierProductionEditVO productionVo) {
        Map<String, String> map = new LinkedHashMap<>();
        String materialsUsed = factoryTourDto.getMaterialUsedDesc();
        if (!StringUtils.isEmpty(materialsUsed)) {
            map.put("materialUsedDesc", materialsUsed);
        }
        String productionProcess = factoryTourDto.getProdProcessDesc();
        if (!StringUtils.isEmpty(productionProcess)) {
            map.put("prodProcessDesc", productionProcess);
        }
        String siteAdvantages = factoryTourDto.getSiteAdvantageDesc();
        if (!StringUtils.isEmpty(siteAdvantages)) {
            map.put("siteAdvantages", siteAdvantages);
        }
        String addlInfoHeading = factoryTourDto.getAddlInfoHeading();
        String addlInfo = factoryTourDto.getAddlInfo();
        if (!StringUtils.isEmpty(addlInfoHeading) && !StringUtils.isEmpty(addlInfo)) {
            map.put(addlInfoHeading, addlInfo);
        }
        if (map.size() > 0) {
            int i = 0;
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String key = entry.getKey();
                boolean isCustom = Objects.equals(key, addlInfoHeading);

                switch (i) {
                    case 0:
                        productionVo.setCustom1(key);
                        productionVo.setContent1(entry.getValue());
                        Optional.of(isCustom).filter(Boolean.TRUE::equals).ifPresent(b -> productionVo.setType1(CustomSectionConstant.CUSTOM));
                        break;
                    case 1:
                        productionVo.setCustom2(key);
                        productionVo.setContent2(entry.getValue());
                        Optional.of(isCustom).filter(Boolean.TRUE::equals).ifPresent(b -> productionVo.setType2(CustomSectionConstant.CUSTOM));
                        break;
                    case 2:
                        productionVo.setCustom3(key);
                        productionVo.setContent3(entry.getValue());
                        Optional.of(isCustom).filter(Boolean.TRUE::equals).ifPresent(b -> productionVo.setType3(CustomSectionConstant.CUSTOM));
                        break;
                    case 3:
                        productionVo.setCustom4(key);
                        productionVo.setContent4(entry.getValue());
                        Optional.of(isCustom).filter(Boolean.TRUE::equals).ifPresent(b -> productionVo.setType4(CustomSectionConstant.CUSTOM));
                        break;
                    case 4:
                    default:
                        break;
                }
                i++;
            }
        }
    }

    private List<String> buildAddressList(FactoryTourFragmentDTO factoryTourDto) {
        List<String> addrList = new ArrayList<>();
        String addr1 = factoryTourDto.getAddr1();
        String addr2 = factoryTourDto.getAddr2();
        String addr3 = factoryTourDto.getAddr3();
        if (!StringUtils.isEmpty(addr1)) {
            addrList.add(addr1);
        }
        if (!StringUtils.isEmpty(addr2)) {
            addrList.add(addr2);
        }
        if (!StringUtils.isEmpty(addr3)) {
            addrList.add(addr3);
        }
        return addrList;
    }

    private SupplierAddressEditVO buildFactoryAddress(List<String> addrList, String country, String state, String city) {
        SupplierAddressEditVO address = null;
        if (CollectionUtils.isNotEmpty(addrList) || StringUtils.isNotEmpty(country) || StringUtils.isNotEmpty(state) || StringUtils.isNotEmpty(city)) {
            address = new SupplierAddressEditVO();
            address.setAddress(String.join(", ", addrList));
            address.setCountry(country);
            address.setState(state);
            address.setCity(city);
            address.setRegion(country);
            address.setProvince(state);
        }
        return address;
    }

    private SupplierQualityFormVO getQcFrom(CompanyProfileDTO profileDTO) {
        SupplierQualityFormVO supplierQualityFormVO = null;
        QualityControlSectionDTO qualityControlInfos = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getQualityControlInfos).orElse(null);
        if (Objects.nonNull(qualityControlInfos)) {
            supplierQualityFormVO = new SupplierQualityFormVO();
            supplierQualityFormVO.setShowControlProcess(true);
            List<SupplierQualityAnnexEditVO> supplierAnnexList = new ArrayList<>();
            List<OnlineSectionImageLinkDTO> imageInfos = qualityControlInfos.getImages();
            if (!CollectionUtils.isEmpty(imageInfos)) {
                for (OnlineSectionImageLinkDTO imageLinkDto : imageInfos) {
                    if (Objects.nonNull(imageLinkDto)) {
                        SupplierQualityAnnexEditVO image = new SupplierQualityAnnexEditVO();
                        image.setUrl(imageLinkDto.getOriginalImageUrl());
                        image.setName(imageLinkDto.getCaptionText());
                        supplierAnnexList.add(image);
                    }
                }
            }
            supplierQualityFormVO.setSupplierAnnexList(supplierAnnexList);
            supplierQualityFormVO.setSupplierQuality(buildQCCustomInfo(qualityControlInfos));
        }
        return supplierQualityFormVO;
    }

    private SupplierQualityEditVO buildQCCustomInfo(QualityControlSectionDTO qualityControlInfos) {
        Map<String, String> map = new LinkedHashMap<>();
        String technicalSupportDepartment = qualityControlInfos.getTechnicalSupport();
        if (!StringUtils.isEmpty(technicalSupportDepartment)) {
            map.put("technicalSupportDepartment", technicalSupportDepartment);
        }
        String qualityControlProcedure = qualityControlInfos.getProcedure();
        if (!StringUtils.isEmpty(qualityControlProcedure)) {
            map.put("qualityControlProcedure", qualityControlProcedure);
        }
        String materialsAndComponents = qualityControlInfos.getMaterials();
        if (!StringUtils.isEmpty(materialsAndComponents)) {
            map.put("materialsAndComponents", materialsAndComponents);
        }
        String additionalInfo = qualityControlInfos.getOtherProcedure();
        if (!StringUtils.isEmpty(additionalInfo)) {
            map.put("additionalInfo", additionalInfo);
        }
        return buildSupplierQualityEditVO(map);
    }

    private SupplierQualityEditVO buildSupplierQualityEditVO(Map<String, String> map) {
        SupplierQualityEditVO supplierQuality = new SupplierQualityEditVO();
        if (map.size() > 0) {
            int i = 0;
            for (Map.Entry<String, String> entry : map.entrySet()) {
                if (i == 0) {
                    supplierQuality.setCustom1(entry.getKey());
                    supplierQuality.setContent1(entry.getValue());
                }
                if (i == 1) {
                    supplierQuality.setCustom2(entry.getKey());
                    supplierQuality.setContent2(entry.getValue());
                }
                if (i == 2) {
                    supplierQuality.setCustom3(entry.getKey());
                    supplierQuality.setContent3(entry.getValue());
                }
                if (i == 3) {
                    supplierQuality.setCustom4(entry.getKey());
                    supplierQuality.setContent4(entry.getValue());
                }
                i++;
            }
        }
        return supplierQuality;
    }

    private SupplierRdFromVO getSupplierRdInfo(CompanyProfileDTO profileDTO) {
        SupplierRdFromVO supplierRdFromVO = null;
        ResearchAndDevelopmentSectionDTO rdInfos = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getRdInfos).orElse(null);
        if (rdInfos != null) {
            supplierRdFromVO = new SupplierRdFromVO();
            SupplierRdVO supplierRd = new SupplierRdVO();
            supplierRd.setShowRdProcess(true);
            List<SupplierRdAnnexEditVO> supplierAnnexList = new ArrayList<>();
            List<OnlineSectionImageLinkDTO> imageInfos = rdInfos.getImages();
            if (!CollectionUtils.isEmpty(imageInfos)) {
                for (OnlineSectionImageLinkDTO imageLinkDto : imageInfos) {
                    if (Objects.nonNull(imageLinkDto)) {
                        SupplierRdAnnexEditVO image = new SupplierRdAnnexEditVO();
                        image.setName(imageLinkDto.getCaptionText());
                        image.setUrl(imageLinkDto.getOriginalImageUrl());
                        image.setContext(imageLinkDto.getCaptionText());
                        supplierAnnexList.add(image);
                    }
                }
            }
            supplierRdFromVO.setSupplierAnnexList(supplierAnnexList);
            buildRDCustomInfo(supplierRd, rdInfos);
            supplierRdFromVO.setSupplierRd(supplierRd);
        }
        return supplierRdFromVO;
    }

    private void buildRDCustomInfo(SupplierRdVO supplierRd, ResearchAndDevelopmentSectionDTO rdInfos) {
        Map<String, String> map = new LinkedHashMap<>();
        String machineryAndEquipment = rdInfos.getMachinery();
        if (!StringUtils.isEmpty(machineryAndEquipment)) {
            map.put("machineryAndEquipment", machineryAndEquipment);
        }
        String patentsAndCopyrights = rdInfos.getPatents();
        if (!StringUtils.isEmpty(patentsAndCopyrights)) {
            map.put("patentsAndCopyrights", patentsAndCopyrights);
        }
        String awardsAndRecognitions = rdInfos.getAwards();
        if (!StringUtils.isEmpty(awardsAndRecognitions)) {
            map.put("awardsAndRecognitions", awardsAndRecognitions);
        }
        String locationOfFacilities = rdInfos.getLocation();
        if (!StringUtils.isEmpty(locationOfFacilities)) {
            map.put("locationOfFacilities", locationOfFacilities);
        }
        String proceduresAndPractices = rdInfos.getPractices();
        if (!StringUtils.isEmpty(proceduresAndPractices)) {
            map.put("proceduresAndPractices", proceduresAndPractices);
        }
        buildSupplierRd(supplierRd, map);
    }

    private void buildSupplierRd(SupplierRdVO supplierRd, Map<String, String> map) {
        if (map.size() <= 0) {
            return;
        }
        int i = 0;
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (i == 0) {
                supplierRd.setCustom1(entry.getKey());
                supplierRd.setContent1(entry.getValue());
            }
            if (i == 1) {
                supplierRd.setCustom2(entry.getKey());
                supplierRd.setContent2(entry.getValue());
            }
            if (i == 2) {
                supplierRd.setCustom3(entry.getKey());
                supplierRd.setContent3(entry.getValue());
            }
            if (i == 3) {
                supplierRd.setCustom4(entry.getKey());
                supplierRd.setContent4(entry.getValue());
            }
            if (i == 4) {
                supplierRd.setCustom5(entry.getKey());
                supplierRd.setContent5(entry.getValue());
            }
            i++;
        }
    }

    private ComprehensiveBRProfileVO getComprehensiveBRProfile(CompanyProfileDTO profileDTO) {
        ComprehensiveBRProfileVO bRVO = null;
        SupplierCreditProfileDTO bRDto = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getCreditInfo).orElse(null);
        if (bRDto != null) {
            bRVO = new ComprehensiveBRProfileVO();

            bRVO.setReportDate(bRDto.getReportDate());
            if (bRDto.getReportDate() != null) {
                bRVO.setReportDateStr(DateUtil.date2String(bRDto.getReportDate(), DateConstants.DATE_PATTERN_MMMMDDYYYY, Locale.US));
            }


            String vendorName = bRDto.getVendorName();
            if (StringUtils.isNotEmpty(vendorName)) {
                vendorName = vendorName.trim();
                // - 对于选择了 Addcredit,Experian,SGS,TUV SUD直接按照选项显示(TUV SUD 需显示为 TÜV SÜD)
                // - 选择了3rd(Easecredit 和 Dun & Bradstreet(D & B)) 或 Other的,均显示为 3ʳᵈ party
                vendorName = BR_VENDOR_MAP.getOrDefault(vendorName, THIRD_PARTY);
            } else {
                vendorName = THIRD_PARTY;
            }

            bRVO.setVendorName(vendorName);
            bRVO.setAddress(bRDto.getAddress());
            bRVO.setIncorporationDate(bRDto.getIncorporationDate());
            bRVO.setLegalForm(bRDto.getLegalForm());
            bRVO.setCompanyStatus(bRDto.getCompanyStatus());
            bRVO.setRegistrationAgency(bRDto.getRegistrationAgency());
            bRVO.setRegistrationNumber(bRDto.getRegistrationNumber());
            bRVO.setAuthorizedCapital(bRDto.getAuthorizedCapital());
            bRVO.setPaidUpCapital(bRDto.getPaidUpCapital());
            bRVO.setLegalRepresentative(bRDto.getLegalRepresentative());
            bRVO.setImportExportLicenseFlag(bRDto.isImportExportLicenseFlag());
            bRVO.setBusinessScope(bRDto.getBusinessScope());
            bRVO.setBusinessExpireDate(bRDto.getBusinessExpireDate());
            bRVO.setShareholder(bRDto.getShareholder());
        }
        return bRVO;
    }

    private List<SupplierCustomEditVO> getSupplierCustomInfos(CompanyProfileDTO profileDTO) {
        List<SupplierCustomEditVO> customList = new ArrayList<>();
        //codeofconduct
        buildCodeofConductSectionInfo(profileDTO, customList);
        //servicesupport
        buildServiceAndSupportSectionInfo(profileDTO, customList);
        //newsroom
        buildNewsroomSectionInfo(profileDTO, customList);
        //management
        buildManagementSectionInfo(profileDTO, customList);
        //tradingservice
        buildTradingServiceSectionInfo(profileDTO, customList);
        //oems
        buildOemOdmSectionInfo(profileDTO, customList);
        //custom section
        buildCustomSectionInfo(profileDTO, customList);
        return customList;
    }

    private void buildCodeofConductSectionInfo(CompanyProfileDTO profileDTO, List<SupplierCustomEditVO> customList) {
        CodeofConductSectionDTO codesOfConductInfos = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getCodesOfConductInfos).orElse(null);
        if (Objects.nonNull(codesOfConductInfos)) {
            SupplierCustomEditVO customInfo = new SupplierCustomEditVO();
            Map<String, String> map = new LinkedHashMap<>();
            customInfo.setPageTitle(CustomSectionConstant.CODES_OF_CONDUCT);
//            Equal Employment Opportunity/Non-discrimination
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.EQUAL_EMPLOYMENT_OPPORTUNITY_NONDISCRIMINATION.getDesc(), codesOfConductInfos.getEmploymentOpportunity());
//            Policy on Forced Labour
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_FORCED_LABOUR.getDesc(), codesOfConductInfos.getForcedLabour());
//            Policy on Child Labour
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_CHILD_LABOUR.getDesc(), codesOfConductInfos.getChildLabour());
//            Policy on Hours of Labour
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_HOURS_OF_LABOUR.getDesc(), codesOfConductInfos.getHoursOfLabour());
//            Policy on Coercion and Harassment
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_COERCION_AND_HARASSMENT.getDesc(), codesOfConductInfos.getCoercionAndHarassment());
//            Policy on Compensation
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_COMPENSATION.getDesc(), codesOfConductInfos.getCompensation());
//            Policy on Health and Safety
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_HEALTH_AND_SAFETY.getDesc(), codesOfConductInfos.getHealthAndSafety());
//            Policy on Concern for the Environment
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_CONCERN_FOR_THE_ENVIRONMENT.getDesc(), codesOfConductInfos.getConcernForTheEnvironment());
//            Policy on Sensitive Transactions
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_SENSITIVE_TRANSACTIONS.getDesc(), codesOfConductInfos.getSensitiveTransactions());
//            Policy on Commercial Bribery
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_COMMERCIAL_BRIBERY.getDesc(), codesOfConductInfos.getCommercialBribery());
//            Accounting Controls, Procedures and Records
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.ACCOUNTING_CONTROLS_PROCEDURES_AND_RECORDS.getDesc(), codesOfConductInfos.getProceduresAndRecords());
//            Use and Disclosure of Inside Information
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.USE_AND_DISCLOSURE_OF_INSIDE_INFORMATION.getDesc(), codesOfConductInfos.getDisclosureOfInside());
//            Confidential or Proprietary Information
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.CONFIDENTIAL_OR_PROPRIETARY_INFORMATION.getDesc(), codesOfConductInfos.getConfidentialOrProprietaryInfo());
//            Policy on Conflicts of Interest
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_CONFLICTS_OF_INTEREST.getDesc(), codesOfConductInfos.getConflictsOfInterest());
//            Policy on Fraud and Similar Irregularities
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_FRAUD_AND_SIMILAR_IRREGULARITIES.getDesc(), codesOfConductInfos.getFraudAndSimilar());
//            Policy on Monitoring
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_MONITORING.getDesc(), codesOfConductInfos.getMonitoringAndCompliance());
//            Policy on Inspection and Certification
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.CodesOfConduct.POLICY_ON_INSPECTION_AND_CERTIFICATION.getDesc(), codesOfConductInfos.getInspectionAndDocumentation());
//            Statement 1 Heading
            List<CustomVO> customVos = Lists.newArrayList();
            customVos.addAll(CustomInfoUtil.convertMapToCustomVO(map));
            List<CodeofConductSectionDTO.AdditionalInformation> additionalInfos = codesOfConductInfos.getAdditionalInfos();
            if (!CollectionUtils.isEmpty(additionalInfos)) {
                for (CodeofConductSectionDTO.AdditionalInformation additionalInformation : additionalInfos) {
                    if (StringUtils.isNotEmpty(additionalInformation.getMessageTitle()) && StringUtils.isNotEmpty(additionalInformation.getMessage())) {
                        CustomVO customVO = new CustomVO();
                        customVO.setTitle(additionalInformation.getMessageTitle());
                        customVO.setContent(additionalInformation.getMessage());
                        customVO.setType(CustomSectionConstant.CUSTOM);
                        customVos.add(customVO);
                    }
                }
            }
            customInfo.setCustomInfos(customVos);
            customList.add(customInfo);
        }
    }

    private void buildServiceAndSupportSectionInfo(CompanyProfileDTO profileDTO, List<SupplierCustomEditVO> customList) {
        ServiceAndSupportSectionDTO serviceAndSupportInfos = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getServiceAndSupportInfos).orElse(null);
        if (Objects.nonNull(serviceAndSupportInfos)) {
            SupplierCustomEditVO customInfo = new SupplierCustomEditVO();
            Map<String, String> map = new LinkedHashMap<>();
            customInfo.setPageTitle(CustomSectionConstant.SERVICES_AND_SUPPORT);
//            Sample Policy
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.ServiceAndSupport.SAMPLE_POLICY.getDesc(), serviceAndSupportInfos.getSampleAvailabilityAndPolicy());
//            Guarantee
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.ServiceAndSupport.GUARANTEE.getDesc(), serviceAndSupportInfos.getGuaranteeAndConditions());
//            Export/Import Support
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.ServiceAndSupport.EXPORT_IMPORT_SUPPORT.getDesc(), serviceAndSupportInfos.getExportProcessSupport());
//            After Sales Services
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.ServiceAndSupport.AFTER_SALES_SERVICES.getDesc(), serviceAndSupportInfos.getAfterSalesServices());
//            Customer Hotline 1 Customer Hotline 2
            List<ServiceAndSupportSectionDTO.CustomerHotLine> customerHotLineInfos = serviceAndSupportInfos.getCustomerHotLineInfos();
            if (!CollectionUtils.isEmpty(customerHotLineInfos)) {
                for (ServiceAndSupportSectionDTO.CustomerHotLine hotLine : customerHotLineInfos) {
                    if (Objects.nonNull(hotLine)) {
                        PhoneDTO phoneVO = new PhoneDTO();
                        BeanUtils.copyProperties(hotLine, phoneVO);
                        String num = SupplierAggContactUtil.phoneToString(phoneVO);
                        CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.ServiceAndSupport.HOTLINE.getDesc(), num);
                    }
                }
            }
//            Customer Service e-mail
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.ServiceAndSupport.CUSTOMER_SERVICE_EMAIL.getDesc(), serviceAndSupportInfos.getCustomerServiceEmail());
            List<CustomVO> customVos = CustomInfoUtil.convertMapToCustomVO(map);
            customInfo.setCustomInfos(customVos);

            customInfo.setSupplierCustomImages(ImageUtil.convertAnnexImages(serviceAndSupportInfos.getImages()));
            customList.add(customInfo);
        }
    }

    private void buildNewsroomSectionInfo(CompanyProfileDTO profileDTO, List<SupplierCustomEditVO> customList) {
        NewsroomSectionDTO newsroomInfos = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getNewsroomInfos).orElse(null);
        if (Objects.nonNull(newsroomInfos)) {
            SupplierCustomEditVO customInfo = new SupplierCustomEditVO();
            customInfo.setPageTitle(CustomSectionConstant.NEWSROOM);
            List<NewsroomStoryDTO> stories = newsroomInfos.getStories();
            List<CustomVO> customVos = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(stories)) {
                stories.forEach(story -> {
                    CustomVO customVO = new CustomVO();
                    customVO.setTitle(story.getTitle());
                    customVO.setContent(story.getContent());
                    customVO.setType(CustomSectionConstant.CUSTOM);
                    customVos.add(customVO);
                });
                customInfo.setCustomInfos(customVos);
            }
            customInfo.setSupplierCustomImages(ImageUtil.convertAnnexImages(newsroomInfos.getImages()));
            customList.add(customInfo);
        }
    }

    private void buildManagementSectionInfo(CompanyProfileDTO profileDTO, List<SupplierCustomEditVO> customList) {
        ManagementSectionDTO managementInfos = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getManagementInfos).orElse(null);
        if (Objects.nonNull(managementInfos)) {
            SupplierCustomEditVO customInfo = new SupplierCustomEditVO();
            customInfo.setPageTitle(CustomSectionConstant.MANAGEMENT);
            Map<String, String> map = new LinkedHashMap<>();
            List<CustomVO> customVos = Lists.newArrayList();
            // s29 1.移除 PAGE_TITLE 2.MANAGEMENT_PHILOSOPHY_MISSION_STATEMENT_OR_MESSAGE_FROM_THE_CEO 设置为 PageTitle 的值
            CustomVO customVo = new CustomVO();
            customVo.setTitle(managementInfos.getPageTitle());
            customVo.setContent(managementInfos.getMainContext());
            customVo.setType(CustomSectionConstant.CUSTOM);
            customVos.add(customVo);
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.Management.CONTACT_PERSON.getDesc(), SupplierAggContactUtil.generateName(RefCodeUtil.getValue(managementInfos.getTitleItem()), managementInfos.getFirstName(), managementInfos.getLastName()));
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.Management.JOB_TITLE.getDesc(), managementInfos.getJobTitle());
            customInfo.setSupplierCustomImages(ImageUtil.convertAnnexImages(managementInfos.getImages()));
            customVos.addAll(CustomInfoUtil.convertMapToCustomVO(map));
            customInfo.setCustomInfos(customVos);
            customList.add(customInfo);
        }
    }

    private void buildTradingServiceSectionInfo(CompanyProfileDTO profileDTO, List<SupplierCustomEditVO> customList) {
        TradingServiceSectionDTO tradingServiceInfos = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getTradingServiceInfos).orElse(null);
        if (Objects.nonNull(tradingServiceInfos)) {
            SupplierCustomEditVO customInfo = new SupplierCustomEditVO();
            customInfo.setPageTitle(CustomSectionConstant.TRADING_SERVICES);
            Map<String, String> map = new LinkedHashMap<>();
//            Trading Activities
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.TRADING_ACTIVITIES.getDesc(), tradingServiceInfos.getTradingActivities());
//            Product Range
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.PRODUCT_RANGE.getDesc(), tradingServiceInfos.getProductRange());
//            Supply Chain Elements
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.SUPPLY_CHAIN_ELEMENTS.getDesc(), tradingServiceInfos.getSupplyChain());
//            Capital Financing Terms for Buyers
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.CAPITAL_FINANCING_TERMS_FOR_BUYERS.getDesc(), tradingServiceInfos.getCapitalFinance());
//            OEM Services
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.OEM_SERVICES.getDesc(), tradingServiceInfos.getOemServices());
//            Quality Control Measures
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.QUALITY_CONTROL_MEASURES.getDesc(), tradingServiceInfos.getQcMeasures());
//            Product Inspection and Documentation Policies
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.PRODUCT_INSPECTION_AND_DOCUMENTATION_POLICIES.getDesc(), tradingServiceInfos.getProductInspection());
//            Product Shipping Terms for Buyers
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.PRODUCT_SHIPPING_TERMS_FOR_BUYERS.getDesc(), tradingServiceInfos.getProductShippingTerms());
//            Minimum Order and Delivery Terms
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.MINIMUM_ORDER_AND_DELIVERY_TERMS.getDesc(), tradingServiceInfos.getMinOrder());
//            Staff and Management Structure
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.STAFF_AND_MANAGEMENT_STRUCTURE.getDesc(), tradingServiceInfos.getStaffManagement());
//            Staff Verbal and Written Communication Skills
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.STAFF_VERBAL_AND_WRITTEN_COMMUNICATION_SKILLS.getDesc(), tradingServiceInfos.getStaffCommunicationSkill());
//            Major Buyers
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.MAJOR_BUYERS.getDesc(), tradingServiceInfos.getMajorBuyer());
//            Major Suppliers
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.MAJOR_SUPPLIERS.getDesc(), tradingServiceInfos.getMajorSupplier());
//            Major Brands Traded
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.MAJOR_BRANDS_TRADED.getDesc(), tradingServiceInfos.getBrands());
//            Key Areas of Expertise
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.TradingServices.KEY_AREAS_OF_EXPERTISE.getDesc(), tradingServiceInfos.getExpertise());
            List<CustomVO> customVos = CustomInfoUtil.convertMapToCustomVO(map);
            customInfo.setCustomInfos(customVos);

            customInfo.setSupplierCustomImages(ImageUtil.convertAnnexImages(tradingServiceInfos.getImages()));
            customList.add(customInfo);
        }
    }

    private void buildOemOdmSectionInfo(CompanyProfileDTO profileDTO, List<SupplierCustomEditVO> customList) {
        OemOdmSectionDTO oemOdmInfos = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getOemOdmInfos).orElse(null);
        if (Objects.nonNull(oemOdmInfos)) {
            SupplierCustomEditVO customInfo = new SupplierCustomEditVO();
            customInfo.setPageTitle(CustomSectionConstant.OEM_ODM);
            Map<String, String> map = new LinkedHashMap<>();
//            Factory OEM/ODM Capabilities
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.FACTORY_OEM_ODM_CAPABILITIES.getDesc(), oemOdmInfos.getCapability());
//            Number of Production Lines
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.NUMBER_OF_PRODUCTION_LINES.getDesc(), oemOdmInfos.getProductionLinesCount() + "");
            Double factorySize = oemOdmInfos.getFactorySize();
            String factory = Objects.nonNull(factorySize) ? factorySize + " " + Optional.ofNullable(RefCodeUtil.getValue(oemOdmInfos.getFactorySizeUnitItem())).orElse("") : "";
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.FACTORY_SIZE.getDesc(), factory.trim());
//            Number of QC Staff
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.NUMBER_OF_QC_STAFF.getDesc(), RefCodeUtil.getValue(oemOdmInfos.getQcStaffCntItem()));
//            Number of Production Staff
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.NUMBER_OF_PRODUCTION_STAFF.getDesc(), RefCodeUtil.getValue(oemOdmInfos.getProductionStaffCntItem()));
//            Number of R & D Staff
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.NUMBER_OF_RD_STAFF.getDesc(), RefCodeUtil.getValue(oemOdmInfos.getRdStaffCntItem()));
//            Years of OEM/ODM Experience
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.YEARS_OF_OEM_ODM_EXPERIENCE.getDesc(), oemOdmInfos.getExperienceYears() + "");
//            Design Services Offered
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.DESIGN_SERVICES_OFFERED.getDesc(), oemOdmInfos.getDesignService());
//            Buyer Labels Offered
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.BUYER_LABELS_OFFERED.getDesc(), oemOdmInfos.getBuyerLabel());
//            Materials/Components Used
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.MATERIALS_COMPONENTS_USED.getDesc(), oemOdmInfos.getMaterials());
//            Machinery and Equipment Used
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.MACHINERY_AND_EQUIPMENT_USED.getDesc(), oemOdmInfos.getMachinery());
//            Monthly Capacity	Unit of Measure
            String monthlyCapacity = RefCodeUtil.getValue(oemOdmInfos.getMonthlyCapacityItem());
            monthlyCapacity = StringUtils.isNotEmpty(monthlyCapacity) ? monthlyCapacity + " " + RefCodeUtil.getValue(oemOdmInfos.getMonthlyCapacityUnitItem()) : "";
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.MONTHLY_CAPACITY.getDesc(), monthlyCapacity.trim());
//            Monthly Output	Unit of Measure
            String monthlyOutput = RefCodeUtil.getValue(oemOdmInfos.getAverageMonthlyOutputItem());
            monthlyOutput = StringUtils.isNotEmpty(monthlyOutput) ? monthlyOutput + " " + RefCodeUtil.getValue(oemOdmInfos.getAverageMonthlyOutputUnitItem()) : "";
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.MONTHLY_OUTPUT.getDesc(), monthlyOutput.trim());
//            Minimum Order	Unit of Measure
            String minOrder = RefCodeUtil.getValue(oemOdmInfos.getMinOrderItem());
            minOrder = StringUtils.isNotEmpty(minOrder) ? minOrder + " " + RefCodeUtil.getValue(oemOdmInfos.getMinOrderUnitItem()) : "";
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.MINIMUM_ORDER.getDesc(), minOrder.trim());
//            Major Markets Served
            List<OemOdmMarketDTO> mainExportMarket = oemOdmInfos.getMainExportMarkets();
            if (!CollectionUtils.isEmpty(mainExportMarket)) {
                String exportMarket = mainExportMarket.stream().filter(Objects::nonNull).map(m -> RefCodeUtil.getValue(m.getMainExportMarketItem()))
                        .filter(StringUtils::isNotEmpty).collect(Collectors.joining(", ")).trim();
                CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.MAJOR_MARKETS_SERVED.getDesc(), exportMarket);
            }
//          Other Export Markets
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.OTHER_EXPORT_MARKETS.getDesc(), oemOdmInfos.getOtherExportMarket());

//          Main OEM Customers
            CustomInfoUtil.putCustomInfoToMap(map, CustomSectionConstant.OEMODM.MAIN_OEM_CUSTOMERS.getDesc(), oemOdmInfos.getMainCustomer());
            customInfo.setSupplierCustomImages(ImageUtil.convertAnnexImages(oemOdmInfos.getImages()));
            List<CustomVO> customVos = CustomInfoUtil.convertMapToCustomVO(map);
            customInfo.setCustomInfos(customVos);
            customList.add(customInfo);
        }
    }

    private void buildCustomSectionInfo(CompanyProfileDTO profileDTO, List<SupplierCustomEditVO> customList) {
        if (Objects.nonNull(profileDTO.getCustomSectionInfos())) {
            List<CustomSectionDTO> customSectionInfos = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getCustomSectionInfos).orElse(null);
            if (!CollectionUtils.isEmpty(customSectionInfos)) {
                customSectionInfos.stream().filter(Objects::nonNull).forEach(customSectionDto -> {
                    List<CustomSectionLayoutDTO> layoutDTOs = customSectionDto.getLayouts();
                    SupplierCustomEditVO customInfo = new SupplierCustomEditVO();
                    if (!CollectionUtils.isEmpty(layoutDTOs)) {
                        if (StringUtils.isNotBlank(customSectionDto.getPageName())) {
                            customInfo.setPageTitle(customSectionDto.getPageName());
                        } else {
                            customInfo.setPageTitle(layoutDTOs.get(0).getPageTitle());
                        }
                        customInfo.setType(CustomSectionConstant.CUSTOM);
                        List<CustomVO> customVos = Lists.newArrayList();
                        List<SupplierAnnexEditVO> images = Lists.newArrayList();
                        layoutDTOs.stream().filter(Objects::nonNull).forEach(layoutDTO -> {
                            CustomVO customVO = new CustomVO();
                            customVO.setContent(layoutDTO.getContent());
                            customVO.setTitle(layoutDTO.getPageTitle());
                            customVO.setType(CustomSectionConstant.CUSTOM);
                            customVos.add(customVO);
                            images.addAll(ImageUtil.convertAnnexImages(layoutDTO.getImages()));
                        });
                        customInfo.setSupplierCustomImages(images);
                        customInfo.setCustomInfos(customVos);
                    }
                    customList.add(customInfo);
                });
            }
        }
    }

    private CertificateFormVO getCertificateFromInfo(CompanyProfileDTO profileDTO) {
        CertificateFormVO result = null;
        if (Objects.nonNull(profileDTO) && !CollectionUtils.isEmpty(profileDTO.getCertificateInfos())) {
            result = new CertificateFormVO();
            List<CertificationVO> companyCert = new ArrayList<>();
            List<CertificationVO> productCert = new ArrayList<>();
            List<OnlineCertificateDTO> certificateInfos = profileDTO.getCertificateInfos();
            long currentTimeMillis = System.currentTimeMillis();
            certificateInfos.stream().filter(Objects::nonNull).filter(cert -> Objects.nonNull(cert.getLUpdDate()))
                    //先按更新时间倒序 再按standard字母顺序
                    .sorted(Comparator.comparingLong((OnlineCertificateDTO cert) -> cert.getLUpdDate().getTime()).reversed()
                            .thenComparing(certificateDto -> {
                                String certStandard = RefCodeUtil.getValue(certificateDto.getCertificateStandardItem());
                                return StringUtils.isEmpty(certStandard) ? certificateDto.getOtherCertificateStandard() : certStandard;
                            })
                    )
                    .forEachOrdered(certificateDto -> buildCertificateInfo(certificateDto, companyCert, productCert, currentTimeMillis));

            //公司认证取三个standard不重复的顶置
            Set<String> standardSet = new HashSet<>();
            List<CertificationVO> distinctCompanyCertList = new ArrayList<>();
            List<CertificationVO> moreCompanyCertList = new ArrayList<>();
            for (CertificationVO cert : companyCert) {
                if (standardSet.add(cert.getCertificateStandard()) && distinctCompanyCertList.size() < 3) {
                    distinctCompanyCertList.add(cert);
                } else {
                    moreCompanyCertList.add(cert);
                }
            }
            distinctCompanyCertList.addAll(moreCompanyCertList);
            result.setCompanyCertifications(distinctCompanyCertList);

            //distinct 产品认证按standard去重
            Set<String> productCertStandard = new HashSet<>();
            result.setProductCertifications(productCert.stream().filter(c -> productCertStandard.add(c.getCertificateStandard())).collect(Collectors.toList()));

        }
        return result;
    }

    private void buildCertificateInfo(OnlineCertificateDTO certificateDto, List<CertificationVO> companyCert, List<CertificationVO> productCert, long currentTimeMillis) {
        CertificationVO cert = new CertificationVO();
        cert.setCertificateStandardCode(certificateDto.getCertificateStandard());
        String certStandard = RefCodeUtil.getValue(certificateDto.getCertificateStandardItem());
        certStandard = StringUtils.isEmpty(certStandard) ? certificateDto.getOtherCertificateStandard() : certStandard;
        cert.setCertificateStandard(certStandard);
        cert.setCertificateNum(certificateDto.getCertificateNum());
        cert.setIssueBy(certificateDto.getCertificateAgent());
        cert.setCertificateUrl(certificateDto.getCertificateUrl());
        cert.setThumbImageUrl(certificateDto.getThumbImageUrl());
        List<OnlineCertificateImageLinkDTO> imageInfos = certificateDto.getImages();
        if (CollectionUtils.isNotEmpty(imageInfos)) {
            cert.setImageUrl(imageInfos.get(0).getOriginalImageUrl());
        }
        cert.setCertificateScope(certificateDto.getCertificateScope());
        cert.setIssueDate(certificateDto.getIssueDate());
        cert.setExpiryDate(certificateDto.getExpiryDate());
        cert.setLUpdDate(certificateDto.getLUpdDate());
        if (BusinessConstant.CertType.COMPANY.getType().equals(certificateDto.getCertificateType())) {
            companyCert.add(cert);
        } else if (BusinessConstant.CertType.PRODUCT.getType().equals(certificateDto.getCertificateType())
                || BusinessConstant.CertType.PRODUCT_EPR.getType().equals(certificateDto.getCertificateType())) {
            //产品认证 过有效期后不显示
            cert.setCertificateScope(BusinessConstant.CertType.PRODUCT_EPR.getType().equals(certificateDto.getCertificateType())
                    ? certificateDto.getCertificateScope() : certificateDto.getProductName());

            if (Objects.isNull(cert.getExpiryDate()) || cert.getExpiryDate().getTime() > currentTimeMillis) {
                productCert.add(cert);
            }
        }
    }

    private ScaInfoVO getScaInfo(CompanyProfileDTO profileDTO) {
        ScaInfoVO result = null;
        ScaCsvInfoDTO scaInfo = Optional.ofNullable(profileDTO).map(CompanyProfileDTO::getScaInfos).orElse(null);
        if (Objects.nonNull(scaInfo) && Objects.nonNull(scaInfo.getId()) && StringUtils.isNotEmpty(scaInfo.getVendorName())) {
            result = new ScaInfoVO();
            String vendorName = scaInfo.getVendorName();
            vendorName = convertVendorNameOfSca(vendorName);
            result.setVendorName(vendorName);
            ScaBasicInfoVO scaBasicInfoVo = new ScaBasicInfoVO();
            ScaGeneralInfoVO scaGeneralInfoVo = new ScaGeneralInfoVO();
            ScaTradingVO scaTradingVo = new ScaTradingVO();
            ScaProductionVO scaProductionVo = new ScaProductionVO();
            ScaQualitySystemVO scaQualitySystemVo = new ScaQualitySystemVO();
            BeanUtils.copyProperties(scaInfo, scaBasicInfoVo);
            BeanUtils.copyProperties(scaInfo, scaGeneralInfoVo);
            BeanUtils.copyProperties(scaInfo, scaTradingVo);
            BeanUtils.copyProperties(scaInfo, scaProductionVo);
            BeanUtils.copyProperties(scaInfo, scaQualitySystemVo);
            //整个栏目没值返回null；
            if (BeanUtil.checkBeanAllFieldIsEmpty(scaBasicInfoVo, scaBeanFieldIsEmptyFunction)) {
                scaBasicInfoVo = null;
            }
            if (BeanUtil.checkBeanAllFieldIsEmpty(scaGeneralInfoVo, scaBeanFieldIsEmptyFunction)) {
                scaGeneralInfoVo = null;
            }
            if (BeanUtil.checkBeanAllFieldIsEmpty(scaTradingVo, scaBeanFieldIsEmptyFunction)) {
                scaTradingVo = null;
            }
            if (BeanUtil.checkBeanAllFieldIsEmpty(scaProductionVo, scaBeanFieldIsEmptyFunction)) {
                scaProductionVo = null;
            }
            if (BeanUtil.checkBeanAllFieldIsEmpty(scaQualitySystemVo, scaBeanFieldIsEmptyFunction)) {
                scaQualitySystemVo = null;
            }

            result.setBasicInfo(scaBasicInfoVo);
            result.setGeneralInfo(scaGeneralInfoVo);
            result.setTradingCapabilities(scaTradingVo);
            result.setProductionCapabilities(scaProductionVo);
            result.setQualitySystems(scaQualitySystemVo);

            buildScaInfo(scaInfo, result);

            List<SupplierAnnexEditVO> images = ImageUtil.convertAnnexImages(scaInfo.getImageInfos());
            if (!CollectionUtils.isEmpty(images)) {
                result.setImages(OrikaMapperUtil.coverList(images, CompanyProfileAnnexVO.class));
            }
        }
        return result;
    }

    private static String convertVendorNameOfSca(String vendorName) {
        vendorName = vendorName.trim();
        if ("ADD".equals(vendorName)) {
            vendorName = ADDCREDIT_STR;
        }
        if (!SCA_VENDOR_SET.contains(vendorName)) {
            vendorName = THIRD_PARTY;
        }
        return vendorName;
    }

    private void buildScaInfo(ScaCsvInfoDTO scaInfo, ScaInfoVO result) {
        if (Objects.nonNull(scaInfo.getMarketDistributionFlag()) && scaInfo.getMarketDistributionFlag().booleanValue() && !CollectionUtils.isEmpty(scaInfo.getMarketDistributionInfos())) {
            List<ScaMarketCsvVO> scaMarketCsvVos = OrikaMapperUtil.coverList(scaInfo.getMarketDistributionInfos(), ScaMarketCsvVO.class);
            if (!CollectionUtils.isEmpty(scaMarketCsvVos)) {
                scaMarketCsvVos = scaMarketCsvVos.stream()
                        .filter(market -> StringUtils.isNotEmpty(market.getMainProducts()) || StringUtils.isNotEmpty(market.getRevenue())
                                || StringUtils.isNotEmpty(market.getTotalRevenue())).collect(Collectors.toList());
                result.setMarketDistributionInfos(scaMarketCsvVos);
            }
        }
        if (Objects.nonNull(scaInfo.getDescriptionOfMachineryUsedFlag()) && scaInfo.getDescriptionOfMachineryUsedFlag().booleanValue() && !CollectionUtils.isEmpty(scaInfo.getDescriptionOfMachineryUsedInfos())) {
            List<ScaMachineryCsvVO> scaMachineryCsvVos = OrikaMapperUtil.coverList(scaInfo.getDescriptionOfMachineryUsedInfos(), ScaMachineryCsvVO.class);
            if (!CollectionUtils.isEmpty(scaMachineryCsvVos)) {
                scaMachineryCsvVos = scaMachineryCsvVos.stream()
                        .filter(machinery -> StringUtils.isNotEmpty(machinery.getMachineryName()) || StringUtils.isNotEmpty(machinery.getModel())
                                || StringUtils.isNotEmpty(machinery.getSecification()) || StringUtils.isNotEmpty(machinery.getQuantity())
                                || StringUtils.isNotEmpty(machinery.getYearMade()) || StringUtils.isNotEmpty(machinery.getServiceDate())
                                || StringUtils.isNotEmpty(machinery.getCondition())).collect(Collectors.toList());
                result.setDescriptionOfMachineryUsedInfos(scaMachineryCsvVos);
            }
        }
    }


    private final Function<Object, Boolean> scaBeanFieldIsEmptyFunction = (Object fieldValue) -> {
        boolean isEmpty = fieldValue == null;
        if (fieldValue instanceof String) {
            isEmpty = StringUtils.isEmpty((String) fieldValue);
        }
        return isEmpty;
    };

    @Override
    public SupplierOverviewVO getCompanyOverview(Long supplierId, Boolean needBasicInfo, String langCode) {
        needBasicInfo = Optional.ofNullable(needBasicInfo).orElse(Boolean.FALSE);
        MainSectionDTO mainSection = onlineSectionService.getMainSection(supplierId);
        return getCompanyOverview(supplierId, mainSection, needBasicInfo);
    }

    @Override
    public SupplierContactNumberVO getSupplierContactNumber(Long supplierId, Long userId) {
        SupplierContactNumberVO result = new SupplierContactNumberVO();
        if (Objects.isNull(supplierId)) {
            return result;
        }
        Boolean isExchanged = ResultUtil.getData(userQueryFeign.existExchangeInfo(userId, supplierId), "Failed to get whether exists exchange info: " + supplierId + ", " + userId);
        result.setIsExchangeCard(isExchanged);
        if (BooleanUtils.isTrue(isExchanged)) {
            SuppContactInfoQueryAggDTO query = SuppContactInfoQueryAggDTO.builder().supplierIds(Lists.newArrayList(supplierId)).urlFlag(false).personFlag(false).build();
            List<SuppContactInfoAggDTO> data = getSuppContactInfoDtoList(query);
            SuppContactInfoAggDTO suppContactInfoAggDto = Optional.ofNullable(data).flatMap(list -> list.stream().filter(Objects::nonNull).findFirst()).orElse(null);
            if (Objects.nonNull(suppContactInfoAggDto)) {
                //contact address
                ContactDTO contact = suppContactInfoAggDto.getContact();
                if (Objects.nonNull(contact)) {
                    List<PhoneVO> phoneList = OrikaMapperUtil.coverList(contact.getPhoneList(), PhoneVO.class);
                    List<PhoneVO> faxList = OrikaMapperUtil.coverList(contact.getFaxList(), PhoneVO.class);
                    PhoneDTO mobile = contact.getMobile();
                    PhoneVO mobileVO = OrikaMapperUtil.coverObject(mobile, PhoneVO.class);
                    result.setTelephoneList(phoneList);
                    result.setFaxList(faxList);
                    result.setMobile(mobileVO);
                }
            }
        }

        return result;
    }

    @Override
    public List<SuppContactInfoAggDTO> getSuppContactInfoDtoList(SuppContactInfoQueryAggDTO query) {
        List<SuppContactInfoAggDTO> data = null;
        if (Objects.nonNull(query) && CollectionUtils.isNotEmpty(query.getSupplierIds())) {
            Result<List<SuppContactInfoAggDTO>> contactInfo = supplierContactAggFeign.getContactInfo(query);
            data = ResultUtil.getData(contactInfo, "failed to getContactInfo, param: " + query);
        }
        return Optional.ofNullable(data).orElse(Lists.newArrayList());
    }

    @Override
    public SupplierContactUsPageVO getSupplierContactUsInfo(Long supplierId, UserVO userVO) {
        SupplierContactUsPageVO contactUsVO = null;
        List<Long> supplierIds = Lists.newArrayList(supplierId);
        SuppSeoInfoAggDTO seoInfo = supplierSeoService.getSeoInfoBySupplierId(supplierId, true);

        List<SuppContactInfoAggDTO> suppContactInfoDtoList = getSuppContactInfoDtoList(SuppContactInfoQueryAggDTO.builder().supplierIds(supplierIds).personFlag(true).urlFlag(true).build());
        SuppContactInfoAggDTO suppContactInfo = Optional.ofNullable(suppContactInfoDtoList).flatMap(list -> list.stream().filter(Objects::nonNull).findFirst()).orElse(null);


        ContactDTO contact = null;
        if (Objects.nonNull(suppContactInfo) || Objects.nonNull(seoInfo)) {
            contactUsVO = new SupplierContactUsPageVO();
            String companyName = Optional.ofNullable(seoInfo).map(SuppSeoInfoAggDTO::getCompanyName).orElse(StringUtils.EMPTY);
            String homepageUrl = Optional.ofNullable(suppContactInfo).map(SuppContactInfoAggDTO::getGsUrl).orElse(StringUtils.EMPTY);
            String otherHomepageUrl = Optional.ofNullable(suppContactInfo).map(SuppContactInfoAggDTO::getHomepageUrl).orElse(StringUtils.EMPTY);
            contactUsVO.setSupplierType(Optional.ofNullable(seoInfo).map(SuppSeoInfoAggDTO::getSupplierType).orElse("FL"));
            contactUsVO.setWebsite(homepageUrl);
            contactUsVO.setOtherHomepageUrl(otherHomepageUrl);
            contactUsVO.setCompanyName(companyName);
            SupplierContactUsAddressEditVO addressDto = new SupplierContactUsAddressEditVO();
            contact = Optional.ofNullable(suppContactInfo).map(SuppContactInfoAggDTO::getContact).orElse(null);
            if (contact != null) {
                AddressDTO address = contact.getAddress();
                if (address != null) {
                    addressDto.setCountryCode(Optional.ofNullable(address.getCountryRefCodeInfo()).map(ReferenceCodeDTO::getRefCode).orElse(Strings.EMPTY));
                    addressDto.setRegion(RefCodeUtil.getValue(address.getCountryRefCodeInfo()));
                    addressDto.setCity(address.getCity());
                    addressDto.setState(RefCodeUtil.getValue(address.getStateRefCodeInfo()));
                    addressDto.setAddress(address.getAddress());
                    addressDto.setZipCode(address.getZipCode());
                }
                contactUsVO.setSupplierContactUsAddressInfo(addressDto);

                //phone
                PhoneDTO mobile = contact.getMobile();
                contactUsVO.setHasMobileNumber(Objects.nonNull(mobile));

                List<PersonDTO> contactPersonList = contact.getKeyContactPersons();
                PersonDTO person = contactPersonList.stream().filter(Objects::nonNull).findFirst().orElse(null);
                if (Objects.nonNull(person)) {
                    contactUsVO.setJobTitle(person.getJobTitle());
                    contactUsVO.setContactTitle(person.getSalutation());
                    String firstName = Optional.ofNullable(person.getFirstName()).orElse(StringUtils.EMPTY);
                    String lastName = Optional.ofNullable(person.getLastName()).orElse(StringUtils.EMPTY);
                    contactUsVO.setFirstName(firstName);
                    contactUsVO.setLastName(lastName);
                    contactUsVO.setContactPhonePhoto(null);
                }
            }
            contactUsVO.setSupplierContactUsAddressInfo(addressDto);
        }
        return contactUsVO;
    }

    @Override
    public CompanyProfileNavDTO getProfileNavList(Long supplierId) {
        CompanyProfileNavDTO result = new CompanyProfileNavDTO();
        List<String> navList = new ArrayList<>();
        navList.add("Company overview");
        navList.add("Production Capacity");
        navList.add("Quality Control");
        navList.add("R&D Capacity");
        navList.add("TRTRADE CAPABILITIES");
        result.setNavList(navList);
        return result;
    }

    @Override
    public SupplierBannerDTO getSupplierBanners(Long supplierId) {
        SupplierBannerDTO supplierBannerDTO = null;
        Result<SupplierCommonInfoDTO> commonInfoResult = supplierAggFeign.getCommonInfo(supplierId);
        SupplierCommonInfoDTO commonInfo = ResultUtil.getData(commonInfoResult);
        if (commonInfo != null) {
            Integer maxContractLevel = commonInfo.getMaxContractLevel();
            CompanyBannerDTO banner = commonInfo.getCompanyBannerInfo();
            if (Objects.nonNull(banner) && StringUtils.isNotBlank(banner.getCompanyWebsiteBannerUrl()) && Objects.nonNull(maxContractLevel) && maxContractLevel >= 6) {
                supplierBannerDTO = new SupplierBannerDTO();
                supplierBannerDTO.setBannerUrl(banner.getCompanyWebsiteBannerUrl());
            }

            if (StringUtils.isNotEmpty(commonInfo.getBannerVRUrl()) || StringUtils.isNotEmpty(commonInfo.getCompleteTourVRURL())) {
                if (supplierBannerDTO == null) {
                    supplierBannerDTO = new SupplierBannerDTO();
                }
                supplierBannerDTO.setBannerVRUrl(commonInfo.getBannerVRUrl());
                supplierBannerDTO.setCompleteTourVRURL(commonInfo.getCompleteTourVRURL());
            }

        }

        return supplierBannerDTO;
    }

    @Override
    public List<SupplierCompareInfoVO> getSupplierCompareInfo(List<Long> supplierIds) {
        List<SupplierCompareProfileDTO> compareInfoDTOs = Optional.ofNullable(supplierIds).filter(CollectionUtils::isNotEmpty)
                .map(ids -> supplierAggFeign.getCompareInfos(supplierIds)).map(Result::getData).orElse(null);
        List<SupplierCompareInfoVO> supplierCompareInfoVOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(compareInfoDTOs)) {
            compareInfoDTOs.stream().forEach(compareInfoDTO -> {
                SupplierCompareInfoVO supplierCompareInfoVO = new SupplierCompareInfoVO();
                supplierCompareInfoVO.setCompanyName(compareInfoDTO.getCompanyDisplayName());
                supplierCompareInfoVO.setStaffCnt(compareInfoDTO.getTotalNoEmployees());
                supplierCompareInfoVO.setWebsiteUsp(compareInfoDTO.getDisplayMessage());
                supplierCompareInfoVO.setWebsiteSubUsp(compareInfoDTO.getSubDisplayMessage());
                supplierCompareInfoVO.setMajorCustomerNames(Optional.ofNullable(compareInfoDTO.getCustomers()).orElseGet(ArrayList::new).stream().filter(Objects::nonNull).map(SectionVerifiableFieldDTO::getValue).filter(Objects::nonNull).collect(Collectors.toList()));
                supplierCompareInfoVO.setNoProductionLines(compareInfoDTO.getProductionLineCnt());
                OrikaMapperUtil.coverObject(compareInfoDTO, supplierCompareInfoVO);
                supplierCompareInfoVO.setBusinessTypes(SupplierUtil.getFieldValues(compareInfoDTO.getBusinessTypes()));
                List<ExportMarketDTO> exportMarketInfos = compareInfoDTO.getMainMarkets();
                if (!CollectionUtils.isEmpty(exportMarketInfos)) {
                    supplierCompareInfoVO.setMainMarkets(convertMarketToString(exportMarketInfos));
                    List<SimpleExportMarketVO> exportMarketInfoVos = exportMarketInfos.stream().filter(v -> Objects.nonNull(v) && Objects.nonNull(v.getExportMarkets()) && StringUtils.isNotEmpty(v.getExportMarkets().getDisplayValue())).map(v -> {
                        SimpleExportMarketVO simpleExportMarketVO = new SimpleExportMarketVO();
                        if (!CollectionUtils.isEmpty(v.getExportCountries())) {
                            List<String> exportCountryValueList = v.getExportCountries().stream().map(SectionVerifiableFieldDTO::getDisplayValue).collect(Collectors.toList());
                            simpleExportMarketVO.setExportCountries(exportCountryValueList);
                        }
                        return simpleExportMarketVO;
                    }).collect(Collectors.toList());
                    supplierCompareInfoVO.setExportMarketInfos(exportMarketInfoVos);
                }
                supplierCompareInfoVOS.add(supplierCompareInfoVO);
            });
        }
        return supplierCompareInfoVOS;
    }

    @Override
    public Long getOrganizationId(String urlKeyword) {
        String gsURL = "http://www.globalsources.com/" + urlKeyword + ".co";
        List<OrganizationDTO> organizationDTOS = organizationAggFeign.getOrganization(OrgQueryAggDTO.builder().gsURL(gsURL).build()).getData();
        if (CollectionUtils.isNotEmpty(organizationDTOS)) {
            OrganizationDTO organizationDTO = organizationDTOS.get(0);
            return organizationDTO.getOrgId();
        } else {
            log.warn("No organization with url: " + gsURL);
            return null;
        }
    }

    @Override
    public List<SupplierCommonInfoDTO> getSuppCommonInfos(List<Long> supplierIds) {
        List<SupplierCommonInfoDTO> commonInfoList = null;
        List<SupplierCommonInfoDTO> result = Lists.newArrayList();
        List<Long> querySupplierIds = Optional.ofNullable(supplierIds).map(list -> list.stream().filter(Objects::nonNull).distinct().map(com.globalsources.framework.utils.SupplierUtil::convertGsolOrgIdToPscOrgId).collect(Collectors.toList())).orElse(null);
        if (CollectionUtils.isEmpty(querySupplierIds)) {
            return result;
        }
        try {
            Result<List<SupplierCommonInfoDTO>> commonInfos = supplierAggFeign.getCommonInfos(SuppCommonInfoQueryAggDTO.builder().supplierIds(querySupplierIds).requestSource(SeoConstant.RequestSource.DESKTOP).build());
            commonInfoList = ResultUtil.getData(commonInfos, "failed to get supplier commonInfo, param: " + querySupplierIds);
        } catch (Exception e) {
            log.error("failed to getSupplierCommonInfo, supplierIds: " + supplierIds + " , errorMsg: " + e.getMessage(), e);
        }
        return Optional.ofNullable(commonInfoList).orElse(result);
    }

    @Override
    public List<SupplierStatusInfoDTO> getSuppStatusInfos(List<Long> supplierIds) {
        List<Long> querySupplierIds = Optional.ofNullable(supplierIds).map(list -> list.stream().filter(Objects::nonNull).distinct().map(com.globalsources.framework.utils.SupplierUtil::convertGsolOrgIdToPscOrgId).collect(Collectors.toList())).orElse(null);
        if (CollectionUtils.isEmpty(querySupplierIds)) {
            return Lists.newArrayList();
        }
        List<SupplierStatusInfoDTO> data = null;
        SuppStatusInfoQueryAggDTO query = SuppStatusInfoQueryAggDTO.builder().supplierIds(querySupplierIds).build();
        try {
            Result<List<SupplierStatusInfoDTO>> supplierStatusInfoResult = supplierAggFeign.getSupplierStatusInfoBatch(query);
            data = ResultUtil.getData(supplierStatusInfoResult, "failed to getSupplierStatusInfoBatch, query: " + query);
        } catch (Exception e) {
            log.error("failed to getSupplierStatusInfoBatch, query: " + query + ", errorMsg: " + e.getMessage(), e);
        }
        return Optional.ofNullable(data).orElse(Lists.newArrayList());
    }

    @Override
    public ChatSupplierInfoAggDTO getChatSupplierInfoBySupplierId(Long supplierId) {
        Result<ChatSupplierInfoAggDTO> chatSupplierInfoResult = supplierAggFeign.getChatSupplierInfoBySupplierId(supplierId);
        if (!ResultCode.CommonResultCode.SUCCESS.getCode().equals(chatSupplierInfoResult.getCode())) {
            log.error("failed to get chat supplier info, supplierId: {}, result: {}", supplierId, chatSupplierInfoResult);
        }
        return chatSupplierInfoResult.getData();
    }

}
