package com.globalsources.bff.web.supplier.service.impl;

import com.globalsources.agg.supplier.api.constant.SeoConstant;
import com.globalsources.agg.supplier.api.feign.SupplierSeoAggFeign;
import com.globalsources.agg.supplier.api.model.dto.request.seo.SuppSeoInfoQueryAggDTO;
import com.globalsources.agg.supplier.api.model.dto.seo.SuppSeoInfoAggDTO;
import com.globalsources.bff.web.supplier.service.SupplierSeoService;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.utils.ResultUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/19 10:24
 */
@Slf4j
@Service
public class SupplierSeoServiceImpl implements SupplierSeoService {

    @Autowired
    private SupplierSeoAggFeign supplierSeoAggFeign;

    @Override
    public Map<Long, SuppSeoInfoAggDTO> getSeoInfoMapBySupplierIds(List<Long> resultSupplierIds) {
        Map<Long, SuppSeoInfoAggDTO> seoInfoMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(resultSupplierIds)) {
            return seoInfoMap;
        }
        try {
            Result<List<SuppSeoInfoAggDTO>> suppSeoInfoDtoBatch = supplierSeoAggFeign.getSuppSeoInfoDtoBatch(SuppSeoInfoQueryAggDTO.builder().supplierIds(resultSupplierIds).build());
            List<SuppSeoInfoAggDTO> seoInfos = ResultUtil.getData(suppSeoInfoDtoBatch, "failed to get supplier seo info, supplierIds: " + resultSupplierIds);
            seoInfoMap = Optional.ofNullable(seoInfos).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull)
                    .collect(Collectors.toMap(info -> Optional.ofNullable(info.getSupplierId()).orElse(0L), info -> info, (old, newValue) -> old));
        } catch (Exception e) {
            log.error("failed to get supplier seo info, supplierIds: " + resultSupplierIds +", msg: "+ e.getMessage(), e);
        }
        return seoInfoMap;
    }

    @Override
    public SuppSeoInfoAggDTO getSeoInfoBySupplierId(Long supplierId, Boolean homepageUrlFlag) {
        if (Objects.isNull(supplierId)) {
            return null;
        }
        SuppSeoInfoAggDTO suppSeoInfoAggDTO = null;
        try {
            SuppSeoInfoQueryAggDTO query = SuppSeoInfoQueryAggDTO.builder().supplierIds(Lists.newArrayList(supplierId)).homepageUrlFlag(homepageUrlFlag).requestSource(SeoConstant.RequestSource.DESKTOP).build();
            Result<List<SuppSeoInfoAggDTO>> suppSeoInfoDtoResult = supplierSeoAggFeign.getSuppSeoInfoDtoBatch(query);
            List<SuppSeoInfoAggDTO> data = ResultUtil.getData(suppSeoInfoDtoResult, "failed to getSuppSeoInfoDtoBatch, supplierId: " + supplierId + " , homepage flag: " + homepageUrlFlag);
            suppSeoInfoAggDTO = Optional.ofNullable(data).flatMap(list -> list.stream().filter(Objects::nonNull).findFirst()).orElse(null);
        } catch (Exception e) {
            log.error("failed to getSuppSeoInfoDtoBatch, supplierId: " + supplierId + " , homepage flag: " + homepageUrlFlag + ", errorMsg: " + e.getMessage(), e);
        }
        return suppSeoInfoAggDTO;
    }
}
