package com.globalsources.bff.web.supplier.model.vo.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.globalsources.agg.supplier.api.model.dto.file.SupplierAnnexDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/22 10:33
 */
@ApiModel(description = "自定义公司信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SupplierCustomDTO {

    /**
     * 页面名称
     */
    @ApiModelProperty(value = "页面名称",required = false)
    private String pageTitle;

    @ApiModelProperty(value = "自定义信息")
    private List<CustomDTO> customInfos;

    /**
     * 供应商自定义信息 图片
     */
    @ApiModelProperty(value = "供应商自定义信息-图片", required = false)
    private List<SupplierAnnexDTO> images;
    
}
