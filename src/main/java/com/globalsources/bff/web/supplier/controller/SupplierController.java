package com.globalsources.bff.web.supplier.controller;

import com.alibaba.fastjson.JSON;
import com.globalsources.agg.supplier.api.feign.TradeshowAggFeign;
import com.globalsources.agg.supplier.api.model.dto.supplier.ChatSupplierInfoAggDTO;
import com.globalsources.bff.web.product.service.ProductService;
import com.globalsources.bff.web.supplier.constant.SupplierConstant;
import com.globalsources.bff.web.supplier.model.vo.OnlineSupplierHeaderSnapshotVO;
import com.globalsources.bff.web.supplier.model.vo.ProductSupplierSnapshotVO;
import com.globalsources.bff.web.supplier.model.vo.SupplierBannerDTO;
import com.globalsources.bff.web.supplier.model.vo.SupplierCompareInfoVO;
import com.globalsources.bff.web.supplier.model.vo.SupplierContractStatusInfoVO;
import com.globalsources.bff.web.supplier.model.vo.SupplierStatusInfoVO;
import com.globalsources.bff.web.supplier.model.vo.UnverifiedSupplierHomeVO;
import com.globalsources.bff.web.supplier.model.vo.company.CompanyProfileNavDTO;
import com.globalsources.bff.web.supplier.model.vo.contact.SupplierContactNumberVO;
import com.globalsources.bff.web.supplier.model.vo.contact.SupplierContactUsPageVO;
import com.globalsources.bff.web.supplier.model.vo.home.CompanyAboutUsVO;
import com.globalsources.bff.web.supplier.model.vo.home.OnlineCompanySnapshotVO;
import com.globalsources.bff.web.supplier.model.vo.home.OnlineSupplierProfileInProductVO;
import com.globalsources.bff.web.supplier.model.vo.home.OnlineSupplierProfileVO;
import com.globalsources.bff.web.supplier.model.vo.home.overview.SupplierOverviewVO;
import com.globalsources.bff.web.supplier.model.vo.tradeshow.OnlineFutureTradeShowVO;
import com.globalsources.bff.web.supplier.model.vo.tradeshow.OnlineTradeShowVO;
import com.globalsources.bff.web.supplier.service.CompanySnapshotService;
import com.globalsources.bff.web.supplier.service.ContractService;
import com.globalsources.bff.web.supplier.service.SupplierService;
import com.globalsources.bff.web.supplier.service.SupplierTradeshowService;
import com.globalsources.chat.feign.ChatFeign;
import com.globalsources.chat.util.ChatUtil;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.monitor.BizErrorCodeTranslator;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.result.ResultCode;
import com.globalsources.framework.utils.TokenUtil;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.product.agg.api.feign.SupplierProductListFeign;
import com.globalsources.product.agg.api.vo.SupplierSeoVO;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> Chen
 * @since 2021/7/19 13:23
 */
@Api(tags = "supplier供应商服务")
@Slf4j
@RequestMapping("/supplier")
@RestController("supplierServiceController")
public class SupplierController {

    @Autowired
    private ContractService contractService;

    @Autowired
    private SupplierService supplierService;
    
    @Autowired
    private ChatFeign chatFeign;
    
    @Autowired
    private CompanySnapshotService companySnapshotService;
    
    @Autowired
    private SupplierTradeshowService supplierTradeshowService;

    @Autowired
    private BizErrorCodeTranslator bizErrorCodeTranslator;

    @Autowired
    private TradeshowAggFeign tradeshowAggFeign;

    @Autowired
    private ProductService productService;

    @Autowired
    private SupplierProductListFeign supplierProductListFeign;

    /**
     *
     * @param supplierId supplierId
     * @deprecated not used
     * @return SupplierContractStatusInfoVO
     */
    @Deprecated
    @ApiIgnore
    @ApiOperation(value = "供应商合同状态接口")
    @GetMapping("/v1/contract/status-info")
    public Result<SupplierContractStatusInfoVO> getSupplierHomeInfo(@RequestParam("supplierId") Long supplierId){
        try{
            return Result.success(contractService.getSupplierContractStatusInfo(supplierId));
        } catch (RuntimeException e) {
            log.error("getSupplierHomeInfo failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10001", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }
    
    @Login(validLogin = false)
    @ApiOperation(value = "获取公司页面顶部名称/信息栏目(买家端)数据", notes = "买家端，获取公司页面顶部名称/信息栏目数据")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", name = "supplierId", value = "供应商id", required = true, dataType = "Long")
	})
	@GetMapping("/v1/header-snapshot/{supplierId}")
	public Result<OnlineSupplierHeaderSnapshotVO> getOnlineHeaderSnapshot(@ApiIgnore UserVO userVO, @PathVariable(value = "supplierId") Long supplierId) {
        try{
            Long userId = userVO != null && Objects.nonNull(userVO.getUserId()) && userVO.getUserId() != 0 ? userVO.getUserId() : null;
            OnlineSupplierHeaderSnapshotVO onlineHeaderSnapshot = supplierService.getOnlineHeaderSnapshot(supplierId, userId, SupplierConstant.SUPPLIER_LANG_CODE_EN.getStrCode());
            if(Objects.nonNull(onlineHeaderSnapshot)){
                if (BooleanUtils.isTrue(onlineHeaderSnapshot.getOnlineFlag())) {
                    onlineHeaderSnapshot.setChatOnlineStatus(ChatUtil.getChatOnlineStatus(userVO, supplierId, 0L, chatFeign));
                }
                return Result.success(onlineHeaderSnapshot);
            } else {
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
        } catch (RuntimeException e) {
            log.error("getOnlineHeaderSnapshot failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10002", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
	}

    @ApiOperation(value = "供应商状态信息接口")
    @GetMapping("/v1/status-info/{supplierId}")
    public Result<SupplierStatusInfoVO> getSupplierStatusInfo(@PathVariable("supplierId") Long supplierId){
        try{
            SupplierStatusInfoVO supplierStatusInfoVO = supplierService.getSupplierStatusInfos(supplierId);
            if(Objects.nonNull(supplierStatusInfoVO)){
                return Result.success(supplierStatusInfoVO);
            } else {
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
        } catch (RuntimeException e) {
            log.error("getSupplierStatusInfo failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10003", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @ApiOperation(value = "供应商状态和模板设置信息")
    @GetMapping("/v1/homepage/status-and-settings")
    public Result<SupplierStatusInfoVO> getSupplierHomepageStatusAndSettings(@RequestParam("supplierId") Long supplierId) {
        try{
            SupplierStatusInfoVO supplierStatusInfoVO = supplierService.getSupplierHomepageStatusAndSettings(supplierId);
            if(Objects.nonNull(supplierStatusInfoVO)){
                return Result.success(supplierStatusInfoVO);
            } else {
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
        } catch (RuntimeException e) {
            log.error("getSupplierHomepageStatusAndSettings failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10021", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }
    
    @ApiOperation(value = "Unverified supplier home info")
    @GetMapping("/v1/unverified-supplier-home/{supplierId}")
    public Result<UnverifiedSupplierHomeVO> getUnverifiedSupplierHomeInfo(@PathVariable("supplierId") Long supplierId){
        try {
            UnverifiedSupplierHomeVO supplierHomeVO = supplierService.getUnverifiedSupplierHomeInfo(supplierId);
            if(Objects.nonNull(supplierHomeVO)){
                return Result.success(supplierHomeVO);
            } else {
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
        } catch (RuntimeException e) {
            log.error("getUnverifiedSupplierHomeInfo failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10004", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }
    
    @Login(validLogin = false)
    @ApiOperation(value = "获取公司Profile信息(买家端)", notes = "买家端，获取公司Profile信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", name = "supplierId", value = "供应商id", required = true, dataType = "Long")
    })
    @GetMapping("/v1/online/profile/{supplierId}")
    public Result<OnlineSupplierProfileVO> getOnlineCompanyProfile(@ApiIgnore UserVO userVO, @PathVariable(value = "supplierId") Long supplierId) {
        try {
            OnlineSupplierProfileVO onlineSupplierProfile = supplierService.getOnlineSupplierProfile(supplierId, SupplierConstant.SUPPLIER_LANG_CODE_EN.getStrCode());
            if(Objects.nonNull(onlineSupplierProfile)){
                if (onlineSupplierProfile.getMaxContractLevel() > -2) {
                    onlineSupplierProfile.setChatOnlineStatus(ChatUtil.getChatOnlineStatus(userVO, supplierId, 0L, chatFeign));
                }
                return Result.success(onlineSupplierProfile);
            } else {
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
        } catch (RuntimeException e) {
            log.error("getOnlineCompanyProfile failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10005", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @Login(validLogin = false)
    @ApiOperation(value = "获取公司Profile信息(买家端-PP页)", notes = "买家端-PP页，获取公司Profile信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", name = "supplierId", value = "供应商id", required = true, dataType = "Long")
    })
    @GetMapping("/v1/online/profile/pp/{supplierId}")
    public Result<OnlineSupplierProfileInProductVO> getOnlineCompanyProfileForProduct(HttpServletRequest request,
                                                                                      @ApiIgnore UserVO userVO,
                                                                                      @PathVariable(value = "supplierId") Long supplierId) {
        String lang = request.getHeader("lang");
        try {
            OnlineSupplierProfileInProductVO onlineSupplierProfile = supplierService.getOnlineSupplierProfileProduct(supplierId, lang);
            if(Objects.nonNull(onlineSupplierProfile)){

                return Result.success(onlineSupplierProfile);
            } else {
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
        } catch (RuntimeException e) {
            log.error("getOnlineCompanyProfileForProduct failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10017", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }
    
    @Login(validLogin = false)
    @ApiOperation(value = "获取contactUs信息(买家端公司主页)", notes = "获取contactUs页面信息")
    @ApiImplicitParam(paramType = "path", name = "supplierId", value = "supplierId", required = true, dataType = "Long")
    @GetMapping(value = "/v1/contact-us/{supplierId}")
    public Result<SupplierContactUsPageVO> getSupplierContactInfo(@ApiIgnore UserVO userVO, @PathVariable("supplierId") Long supplierId) {
        try {
            SupplierContactUsPageVO contactUsPageVO = supplierService.getSupplierContactUsInfo(supplierId, userVO);
            if(Objects.nonNull(contactUsPageVO)){
                return Result.success(contactUsPageVO);
            } else {
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
        } catch (RuntimeException e) {
            log.error("getSupplierContactInfo failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10006", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @Login
    @ApiOperation(value = "查询供应商联系方式(电话、传真、手机号)", notes = "查询供应商联系方式(电话、传真、手机号)")
    @GetMapping(value = "/v1/contact-number")
    public Result<SupplierContactNumberVO> getSupplierContactNumber(@ApiIgnore UserVO userVO, @RequestParam("supplierId") Long supplierId) {
        try {
            Long userId = Optional.ofNullable(userVO).map(UserVO::getUserId).orElse(0L);
            return Result.success(supplierService.getSupplierContactNumber(supplierId, userId));
        } catch (RuntimeException e) {
            log.error("getSupplierContactNumber failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10007", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @Login(validLogin = false)
    @ApiOperation(value = "获取公司概况信息(买家端)", notes = "买家端获取公司概况信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", name = "supplierId", value = "供应商id", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "needBasicInfo", value = "是否需要basic信息（默认false）",  dataType = "Boolean")
    })
    @GetMapping("/v1/online/overview/{supplierId}")
    public Result<SupplierOverviewVO> getOnlineCompanySurveyInfo(@ApiIgnore UserVO userVO, @PathVariable(value = "supplierId") Long supplierId,
                                                                 @RequestParam(value = "needBasicInfo", required = false, defaultValue = "false") Boolean needBasicInfo) {
        try {
            SupplierOverviewVO companyOverview = supplierService.getCompanyOverview(supplierId, needBasicInfo, SupplierConstant.SUPPLIER_LANG_CODE_EN.getStrCode());
            companyOverview.setChatOnlineStatus(ChatUtil.getChatOnlineStatus(userVO, supplierId, 0L, chatFeign));
            return Result.success(companyOverview);
        } catch (RuntimeException e) {
            log.error("getOnlineCompanySurveyInfo failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10008", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @ApiOperation(value = "获取公司Profile下拉菜单", notes = "获取公司Profile下拉菜单")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", name = "supplierId", value = "供应商id", required = true, dataType = "Long")
    })
    @GetMapping("/v1/profile/nav-list/{supplierId}")
    public Result<CompanyProfileNavDTO> queryCompanyProfileNavList(Long supplierId) {
        try{
            return Result.success(supplierService.getProfileNavList(supplierId));
        } catch (RuntimeException e) {
            log.error("queryCompanyProfileNavList failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10018", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @Login(validLogin = false)
    @ApiOperation(value = "公司主页，获取company snapshot栏目数据(买家端)", notes = "公司主页，获取company snapshot栏目数据")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", name = "supplierId", value = "供应商id", required = true, dataType = "Long")
    })
    @GetMapping("/v1/online/home/<USER>/{supplierId}")
    public Result<OnlineCompanySnapshotVO> getOnlineCompanySnapshot(@ApiIgnore UserVO userVO, @PathVariable(value = "supplierId") Long supplierId) {
        try{
            String token = Optional.ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).map(ServletRequestAttributes::getRequest).map(req -> req.getHeader(TokenUtil.AUTHORIZE_TOKEN)).orElse(StringUtils.EMPTY);
            OnlineCompanySnapshotVO onlineCompanySnapshot = companySnapshotService.getOnlineCompanySnapshot(supplierId, SupplierConstant.SUPPLIER_LANG_CODE_EN.getStrCode(), userVO);
            log.info("getOnlineCompanySnapshot token: {}, supplierId: {}, userVo: {}, result: {}", token, supplierId, userVO, JSON.toJSONString(onlineCompanySnapshot));
            if(Objects.nonNull(onlineCompanySnapshot)){
                return Result.success(onlineCompanySnapshot);
            } else {
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
        } catch (RuntimeException e) {
            log.error("getOnlineCompanySnapshot failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10009", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @Login(validLogin = false)
    @ApiOperation(value = "公司主页，获取About Us栏目数据(买家端)", notes = "公司主页，获取About Us栏目数据")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "supplierId", value = "供应商id", required = true, dataType = "Long")
    })
    @GetMapping("/v1/online/home/<USER>")
    public Result<CompanyAboutUsVO> getCompanyAboutUs(@ApiIgnore UserVO userVO, @RequestParam(value = "supplierId") Long supplierId) {
        try{
            String token = Optional.ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).map(ServletRequestAttributes::getRequest).map(req -> req.getHeader(TokenUtil.AUTHORIZE_TOKEN)).orElse(StringUtils.EMPTY);
            CompanyAboutUsVO companyAboutUs = companySnapshotService.getCompanyAboutUs(supplierId, Objects.nonNull(userVO));
            log.info("getCompanyAboutUs token: {}, supplierId: {}, userVo: {}, result: {}", token, supplierId, userVO, JSON.toJSONString(companyAboutUs));
            if(Objects.nonNull(companyAboutUs)){
                return Result.success(companyAboutUs);
            } else {
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
        } catch (RuntimeException e) {
            log.error("getCompanyAboutUs failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10022", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @ApiOperation(value = "公司主页Banner", notes = "公司主页Banner")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", name = "supplierId", value = "供应商id", required = true, dataType = "Long")
    })
    @GetMapping("/v1/banners/{supplierId}")
    public Result<SupplierBannerDTO> getSupplierBanners(@PathVariable(value = "supplierId") Long supplierId) {
        try {
            return Result.success(supplierService.getSupplierBanners(supplierId));
        } catch (RuntimeException e) {
            log.error("getSupplierBanners failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10010", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }
    
    @Login(validLogin = false)
    @ApiOperation(value = "获取公司将来展会信息(买家端)", notes = "公司将来展会，获取company tradeshows栏目数据")
    @GetMapping("/v1/online/futuretradeshows/{supplierId}")
    public Result<OnlineFutureTradeShowVO> getFutureTradeShows(@ApiIgnore UserVO userVO,@PathVariable Long supplierId) {
        try {
            return Result.success(supplierTradeshowService.getFutureTradeShows(supplierId,userVO));
        } catch (RuntimeException e) {
            log.error("getFutureTradeShows failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10011", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @ApiOperation(value = "获取公司过去GS展会信息(买家端)", notes = "公司过去GS展会，获取company tradeshows栏目数据")
    @GetMapping("/v1/online/pastgstradeshows/{supplierId}")
    public Result<List<OnlineTradeShowVO>> getPastGsTradeShows(@PathVariable Long supplierId) {
        try {
            return Result.success(supplierTradeshowService.getPastGsTradeShows(supplierId));
        } catch (RuntimeException e) {
            log.error("getPastGsTradeShows failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10012", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @ApiOperation(value = "获取公司过去第三方展会信息(买家端)", notes = "公司过去第三方展会，获取company tradeshows栏目数据")
    @GetMapping("/v1/online/pastthirdtradeshows/{supplierId}")
    public Result<List<OnlineTradeShowVO>> getPastThirdTradeShows(@PathVariable Long supplierId) {
        try {
            return Result.success(supplierTradeshowService.getPastThirdTradeShows(supplierId));
        } catch (RuntimeException e) {
            log.error("getPastThirdTradeShows failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10013", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @ApiOperation(value = "公司是否有展会信息(买家端)", notes = "公司是否有展会")
    @GetMapping("/v1/online/hastradeshow/{supplierId}")
    public Result<Boolean> hasTradeShowInfo(@PathVariable Long supplierId) {
        try {
            return Result.success(supplierTradeshowService.hasTradeShowInfo(supplierId));
        } catch (RuntimeException e) {
            log.error("hasTradeShowInfo failed: " + supplierId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10014", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @ApiOperation(value = "Supplier Compare information")
    @GetMapping("/v1/supplier-compare")
    public Result<List<SupplierCompareInfoVO>> getSupplierCompareInfo(@RequestParam("supplierIds") String supplierIds){
        try {
            if(StringUtils.isNotBlank(supplierIds)){
                List<Long> supplierLongIds = Lists.newArrayList();
                String[] supplierIdStrs= supplierIds.split(",");
                for(String supplierIdStr : supplierIdStrs){
                    supplierLongIds.add(Long.valueOf(supplierIdStr));
                }
                List<SupplierCompareInfoVO> supplierCompareInfoVOS = supplierService.getSupplierCompareInfo(supplierLongIds);
                if(CollectionUtils.isNotEmpty(supplierCompareInfoVOS)){
                    return Result.success(supplierCompareInfoVOS);
                }
            }
            return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
        } catch (RuntimeException e) {
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10015", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @ApiOperation(value = "根据url keyword获取公司ID", notes = "根据url keyword获取公司ID")
    @GetMapping("/v1/get-organizationid")
    public Result<Long> getOranizationId(@RequestParam String urlKeyword) {
        try {
            if(Objects.nonNull(supplierService.getOrganizationId(urlKeyword))){
                return Result.success(supplierService.getOrganizationId(urlKeyword));
            } else {
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
        } catch (RuntimeException e) {
            log.error("getOranizationId failed: " + urlKeyword, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10016", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }


    @Login(validLogin = false)
    @ApiOperation(value = "获取产品页面右边公司信息栏目数据", notes = "获取产品页面右边公司信息栏目数据")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "productId", value = "供应商id", required = true, dataType = "Long")
    })
    @GetMapping("/v1/product/supplier-snapshot-info")
    public Result<ProductSupplierSnapshotVO> getProductSupplierSnapshot(@ApiIgnore UserVO userVO, @RequestParam(value = "productId") Long productId) {
        try{
            Long userId = userVO != null && Objects.nonNull(userVO.getUserId()) && userVO.getUserId() != 0 ? userVO.getUserId() : null;
            Long supplierId = productService.getSupplierIdByOnlineProductId(productId);
            if (Objects.isNull(supplierId)) {
                log.warn("getProductSupplierSnapshot failed, supplierId is null, productId: {}", productId);
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
            ProductSupplierSnapshotVO result = supplierService.getProductSupplierSnapshotVo(supplierId, userId);
            if(Objects.nonNull(result)){
                return Result.success(result);
            } else {
                return Result.failed(ResultCode.CommonResultCode.THE_OBJECT_WAS_NOT_FOUND);
            }
        } catch (RuntimeException e) {
            log.error("getProductSupplierSnapshot failed: " + productId, e);
            bizErrorCodeTranslator.translate(e, "SUPPLIER-WEB-BFF-10019", ResultCode.CommonResultCode.SYSTEM_ERROR);
            return Result.failed();
        }
    }

    @ApiOperation(value = "获取供应商SEO信息", notes = "获取供应商SEO信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "supplierId", value = "供应商id", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "categoryId", value = "产品类别ID或产品分组ID",  dataType = "Long"),
            @ApiImplicitParam(paramType = "query", name = "type", value = "category:产品列别, group:产品分组",  dataType = "String")
    })
    @GetMapping("/v1/seo-info")
    public Result<SupplierSeoVO> getSupplierSeoInfo(@RequestParam(value = "supplierId") Long supplierId,
                                                    @RequestParam(value = "categoryId", required = false) Long categoryId,
                                                    @RequestParam(value="type", required = false) String type) {
        try{
            return supplierProductListFeign.getSupplierSeoInfo(supplierId, categoryId, type);
        } catch (Exception e) {
            log.error("getSupplierSeoInfo failed: " + supplierId, e);
            bizErrorCodeTranslator.increaseMeters("SUPPLIER-WEB-BFF-10020");
            throw e;
        }
    }

    @ApiOperation(value = "根据 supplierId 查询聊天供应商信息", notes = "根据 supplierId 查询聊天供应商信息")
    @GetMapping("/v1/get-chat-supplier-info-by-supplier-id")
    public Result<ChatSupplierInfoAggDTO> getChatSupplierInfoBySupplierId(@RequestParam(value = "supplierId") Long supplierId) {
        return Result.success(supplierService.getChatSupplierInfoBySupplierId(supplierId));
    }


}
