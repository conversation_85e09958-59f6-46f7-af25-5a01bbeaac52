package com.globalsources.bff.web.supplier.model.vo.certificate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/6/1 18:10
 */
@Data
@ApiModel(description = "认证信息")
public class CertificationVO implements Serializable {

    private static final long serialVersionUID = -3310469955533281310L;

    @ApiModelProperty(value = "证书url")
    private String certificateUrl;

    @ApiModelProperty(value = "图片url")
    private String imageUrl;

    @ApiModelProperty(value = "认证标准")
    private String certificateStandard;

    @ApiModelProperty(value = "认证编号")
    private String certificateNum;

    @ApiModelProperty(value = "Scope/Range, Product name")
    private String certificateScope;

    @ApiModelProperty(value = "issue by. certificateAgent")
    private String issueBy;

    @ApiModelProperty(value = "颁发时间 Available Date begin")
    private Date issueDate;

    @ApiModelProperty(value = "有效期限 Available Date end")
    private Date expiryDate;

    @ApiModelProperty(value = "更新时间")
    private Date lUpdDate;

    @ApiModelProperty(value = "认证缩略图", required = false)
    private String thumbImageUrl;

    @ApiModelProperty(value = "认证标准Code", required = false)
    private String certificateStandardCode;

}
