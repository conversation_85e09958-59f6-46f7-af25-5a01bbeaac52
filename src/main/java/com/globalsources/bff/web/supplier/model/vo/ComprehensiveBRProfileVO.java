package com.globalsources.bff.web.supplier.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-28
 */
@ApiModel(description = "ComprehensiveBRProfile", value = "ComprehensiveBRProfile")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ComprehensiveBRProfileVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "credit id", required = false)
    private Long creditId;

    @ApiModelProperty(value = "organization id", required = false)
    private Long orgId;

    @ApiModelProperty(value = "vendor name", required = false)
    private String vendorName;

    @ApiModelProperty(value = "Registered Address", required = false)
    private String address;

    @ApiModelProperty(value = "Report Date", required = false)
    private Date reportDate;

    @ApiModelProperty(value = "Report Date String", required = false)
    private String reportDateStr;

    @ApiModelProperty(value = "Incorporation Date", required = false)
    private Date incorporationDate;

    @ApiModelProperty(value = "Legal Form", required = false)
    private String legalForm;

    @ApiModelProperty(value = "Company Status", required = false)
    private String companyStatus;

    @ApiModelProperty(value = "Registration Agency", required = false)
    private String registrationAgency;

    @ApiModelProperty(value = "Registration No.", required = false)
    private String registrationNumber;

    @ApiModelProperty(value = "Authorized Capital", required = false)
    private String authorizedCapital;

    @ApiModelProperty(value = "Paid-up Capital", required = false)
    private String paidUpCapital;
    
    @ApiModelProperty(value = "Legal Representative", required = false)
    private String legalRepresentative;

    @ApiModelProperty(value = "Import & Export License", required = false)
    private boolean importExportLicenseFlag;

    @ApiModelProperty(value = "Business Scope", required = false)
    private String businessScope;

    @ApiModelProperty(value = "Business Permit Expiry", required = false)
    private Date businessExpireDate;

    @ApiModelProperty(value = "Shareholders", required = false)
    private String shareholder;

    @ApiModelProperty(value = "Create date", required = false)
    private Date createDate;

    @ApiModelProperty(value = "Create user id", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "Last update date", required = false)
    private Date lUpdDate;

    @ApiModelProperty(value = "Last update user id", required = false)
    private Long lUpdUserId;
}
