package com.globalsources.bff.web.supplier.model.vo.production;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/3/15 18:07
 */
@ApiModel(description = "SupplierQualityEquipment")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SupplierProductionEquipmentEditVO implements Serializable {

    private static final long serialVersionUID = -2351567181019502854L;
    /**
     * 设备id,自增
     */
    @ApiModelProperty(value = "设备id,自增", required = false)
    private Long id;

    /**
     * 供应商id(冗余)
     */
    @ApiModelProperty(value = "供应商id(冗余)", required = false)
    private Long orgId;

    /**
     * 语种
     */
    @ApiModelProperty(value = "语种", required = false)
    private String langCode;

    /**
     * 设备名称(展示生产设备)
     */
    @ApiModelProperty(value = "设备名称(展示生产设备)", required = false)
    private String equipmentName;

    /**
     * 设备型号(展示生产设备)
     */
    @ApiModelProperty(value = "设备型号(展示生产设备)", required = false)
    private String equipmentModel;

    /**
     * 设备数量(展示生产设备)
     */
    @NotNull
    @ApiModelProperty(value = "设备数量(展示生产设备)", required = false)
    private Integer equipmentQuantity;

    /**
     * 生产机械描述(展示生产设备)
     */
    @Size(max = 2000, message = "{noMoreThan}")
    @ApiModelProperty(value = "生产机械描述(展示生产设备)", required = false)
    private String equipmentDescription;

    /**
     * 排序，最小提最前面
     */
    @ApiModelProperty(value = "排序，最小提最前面", required = false)
    private Integer sort;
    
}
