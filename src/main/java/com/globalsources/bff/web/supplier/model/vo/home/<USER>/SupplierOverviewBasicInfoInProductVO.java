package com.globalsources.bff.web.supplier.model.vo.home.overview;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.globalsources.agg.supplier.api.model.dto.ReferenceCodeDTO;
import com.globalsources.agg.supplier.api.model.dto.section.SectionVerifiableFieldDTO;
import com.globalsources.bff.web.supplier.model.vo.company.ProfileMarketVO;
import com.globalsources.bff.web.supplier.model.vo.company.ProfileVerifiableAttributeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;


@ApiModel(description = "公司概况基本信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SupplierOverviewBasicInfoInProductVO implements Serializable {


    private static final long serialVersionUID = -3060652772101840027L;
    /**
     * table supplier_attribute
     */
    @ApiModelProperty(value = "业务类型")
    private List<String> businessTypes;

    @ApiModelProperty(value = "业务类型,businessType details")
    private List<ProfileVerifiableAttributeVO> businessTypeInfos;

    /**
     * supplier info, table supplier_address
     */
    @ApiModelProperty(value = "国家地区")
    private CountryAndRegionVO countryAndRegion;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 员工总人数, table supplier_info
     */
    @ApiModelProperty(value = "员工总人数")
    private String totalNoEmployees;
    
    /**
     * 上一年销售额, supplier_trade
     */
    @NotBlank(message = "{required}")
    @ApiModelProperty(value = "上一年销售额")
    private String totalAnnualSales;

    /**
     * table supplier
     */
    @ApiModelProperty(value = "成立年份")
    private String yearEstablished;

    /**
     * 总资产. table supplier_info
     */
    @ApiModelProperty(value = "总资产")
    private String totalCapitalization;

    /** The product export percentage. */
    private String productExportPercentage;

    /** The oem service flag. */
    private Boolean oemServiceFlag;

    /** The small order accepted flag. */
    private Boolean smallOrdersAcceptedFlag;

    /** The brand names. */
    private String brandNameDesc;

    /** The payment term. */
    private String paymentTerm;

    /** The other advantage. */
    private String otherAdvantage;

    /** The main advantages. */
    private List<String> mainAdvantages;

    private List<ReferenceCodeDTO> mainAdvantagesItem;

    /** The customers. */
    private List<SectionVerifiableFieldDTO> customers;

    /**
     * table supplier_address, trade 出口主要客户名称 
     */
    @Size(max = 5, message = "{listSizeLimit}")
    @ApiModelProperty(value = "外贸出口能力主要客户名")
    private List<String> majorCustomerNames;

    /**
     * 主要出口市场, table supplier_trade
     */
    @NotBlank(message = "{required}")
    @ApiModelProperty(value = "主要出口市场")
    private String mainMarkets;

    @ApiModelProperty(value = "主要出口市场, market details")
    private List<ProfileMarketVO> exportMarketInfos;

    /**
     * 出口比例, table supplier_trade
     */
    @NotBlank(message = "{required}")
    @ApiModelProperty(value = "出口比例")
    private String exportPercentage;

    @ApiModelProperty(value = "公司认证标准")
    private List<String> companyCertStandards;

    @ApiModelProperty(value = "产品认证标准")
    private List<String> productCertStandards;
    
    @ApiModelProperty(value = "Company Registration Address")
    private String regAddress;

    @ApiModelProperty(value = "Source URL")
    private String regURL;
    
    @ApiModelProperty(value = "Registered Company Name")
    private String regCompanyName;
    
    @ApiModelProperty(value = "Registration Number")
    private String regNumber;
}
