package com.globalsources.bff.web.supplier.model.vo.vr;

import com.globalsources.bff.web.tradeshow.vo.product.OsHighlightProductVO;
import com.globalsources.bff.web.tradeshow.vo.supplier.OsSuppFlagVO;
import com.globalsources.bff.web.tradeshow.vo.supplier.OsSupplierInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Title: SupplierSearchVo
 * @Author: Johann
 * @Description: SupplierSearchVo
 * @date 2021/06/11 - 10:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "supplier vr list")
public class SuppVrSearchVO {

    @ApiModelProperty(value = "供应商id")
    private Long orgId;

    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商的各种flag")
    private OsSuppFlagVO flag;

    @ApiModelProperty(value = "供应商的3个Highlight Product")
    private List<OsHighlightProductVO> products;

    @ApiModelProperty(value = "供应商基础信息")
    private OsSupplierInfoVO supplier;

    @ApiModelProperty(value = "banner vr url")
    private String bannerVrUrl;

    @ApiModelProperty(value = "complete tour vr url")
    private String completeTourVrUrl;

    @ApiModelProperty(value = "vr cover url")
    private String coverUrl;
}
