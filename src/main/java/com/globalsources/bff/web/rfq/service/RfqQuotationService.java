package com.globalsources.bff.web.rfq.service;

import com.globalsources.framework.result.Result;
import com.globalsources.rfq.bff.api.model.dto.req.RfqImportProductSearchRequestDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationChatRepliedRequestDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationCompareRequestDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationCreateRequestDTO;
import com.globalsources.rfq.bff.api.model.vo.RfqCategoryVO;
import com.globalsources.rfq.bff.api.model.vo.RfqQuotationCompareVO;
import com.globalsources.rfq.bff.api.model.vo.RfqQuotationDetailVO;
import com.globalsources.rfq.bff.api.model.vo.RfqQuotationListVO;
import com.globalsources.rfq.bff.api.model.vo.seller.RfqProductImportSearchVO;
import com.globalsources.rfq.bff.api.model.vo.seller.RfqProductImportVO;
import com.globalsources.rfq.bff.api.model.vo.seller.RfqQuotationImportVO;
import com.globalsources.rfq.bff.api.model.vo.seller.SellerQuotationCountVO;

import java.util.List;


/**
 * <a>Title: rfqQuotationService </a>
 * <a>Author: Mike Chen <a>
 * <a>Description:  <a>
 *
 * <AUTHOR> Chen
 * @date 2021/7/16 16:40
 */
public interface RfqQuotationService {

    Result<RfqQuotationListVO> rfqQuotationList(String rfqId);

    Result<RfqQuotationDetailVO> rfqQuotationDetail(String quotationId);

    Result<Boolean> createQuotation(RfqQuotationCreateRequestDTO requestDTO);

    Result<SellerQuotationCountVO> supplierQuotationCount(Long supplierId);

    Result<RfqQuotationCompareVO> rfqQuotationCompare(RfqQuotationCompareRequestDTO requestDTO);

    Result<Boolean> rfqQuotationChatReplied(RfqQuotationChatRepliedRequestDTO build);

    Result<RfqQuotationImportVO> importPreviousQuotation(Long supplierId, Long userId);

    Result<String> importPreviousQuotationCheck(Long supplierId, Long userId);

    Result<List<RfqProductImportSearchVO>> importProductSearch(RfqImportProductSearchRequestDTO requestDTO);

    Result<RfqProductImportVO> importProduct(Long productId);

    Result<List<RfqCategoryVO>> rfqCategoryOwner(RfqImportProductSearchRequestDTO build);

}