package com.globalsources.bff.web.rfq.service.impl;

import com.globalsources.bff.web.rfq.service.RfqMatchEvaluationService;
import com.globalsources.framework.result.Result;
import com.globalsources.rfq.bff.api.feign.RfqAgg;
import com.globalsources.rfq.bff.api.model.dto.req.RfqMatchEvaluationRequestDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <a>Title: RfqMatchEvaluationServiceImpl </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: RfqMatchEvaluationServiceImpl <a>
 *
 * <AUTHOR>
 * @date 2021/11/2 16:55
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RfqMatchEvaluationServiceImpl implements RfqMatchEvaluationService {

    private final RfqAgg.RfqMatchEvaluationAggService rfqMatchEvaluationAggService;

    @Override
    public Result<Boolean> evaluation(RfqMatchEvaluationRequestDTO requestDTO) {
        return rfqMatchEvaluationAggService.evaluation(requestDTO);
    }
}
