package com.globalsources.bff.web.rfq.service;

import com.globalsources.framework.result.Result;
import com.globalsources.rfq.bff.api.model.dto.req.RfqMatchReserveRequestDTO;
import com.globalsources.rfq.bff.api.model.vo.match.RfqMatchReserveVO;

/**
 * <a>Title: RfqMatchMeetingService </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: RfqMatchMeetingService <a>
 *
 * <AUTHOR>
 * @date 2021/10/28 16:51
 */
public interface RfqMatchMeetingService {

    Result<RfqMatchReserveVO> reserve(RfqMatchReserveRequestDTO requestDTO);

}
