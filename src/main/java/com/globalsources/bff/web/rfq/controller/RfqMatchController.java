package com.globalsources.bff.web.rfq.controller;

import com.globalsources.bff.web.rfq.service.RfqMatchService;
import com.globalsources.framework.result.Result;
import com.globalsources.rfq.bff.api.model.dto.req.RfqMatchPreviewRequestDTO;
import com.globalsources.rfq.bff.api.model.vo.match.RfqMatchPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <a>Title: RfqMatchController </a>
 * <a>Author: <PERSON> <a>
 * <a>Description: RfqMatchController <a>
 *
 * <AUTHOR> Chen
 * @date 2021/10/28 16:46
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@Api(tags = {"rfq match"})
@RequestMapping("rfq-bff/match")
public class RfqMatchController {

    private final RfqMatchService rfqMatchService;

    @ApiOperation(value = "rfq--match rfq match page", notes = "rfq--match rfq match page", tags = {"rfq match"})
    @GetMapping("v1/rfq-match/{matchId}")
    Result<RfqMatchPageVO> rfqMatch(@PathVariable("matchId") String matchId) {
        return rfqMatchService.rfqMatch(matchId);
    }

    @ApiOperation(value = "rfq--match预览", notes = "rfq--match预览", tags = {"rfq match"})
    @PostMapping("v1/preview")
    public Result<RfqMatchPageVO> preview(@RequestBody RfqMatchPreviewRequestDTO requestDTO) {
        return rfqMatchService.preview(requestDTO);
    }
}
