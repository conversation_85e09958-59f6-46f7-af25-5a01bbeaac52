package com.globalsources.bff.web.rfq.controller;

import com.globalsources.bff.web.rfq.service.RfqQuotationService;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqImportProductSearchRequestDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationChatRepliedRequestDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationCompareRequestDTO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqQuotationCreateRequestDTO;
import com.globalsources.rfq.bff.api.model.vo.RfqCategoryVO;
import com.globalsources.rfq.bff.api.model.vo.RfqQuotationCompareVO;
import com.globalsources.rfq.bff.api.model.vo.RfqQuotationDetailVO;
import com.globalsources.rfq.bff.api.model.vo.RfqQuotationListVO;
import com.globalsources.rfq.bff.api.model.vo.seller.RfqProductImportSearchVO;
import com.globalsources.rfq.bff.api.model.vo.seller.RfqProductImportVO;
import com.globalsources.rfq.bff.api.model.vo.seller.RfqQuotationImportVO;
import com.globalsources.rfq.bff.api.model.vo.seller.SellerQuotationCountVO;
import com.globalsources.rfq.bff.api.util.SampleTypeUtil;
import com.globalsources.rfq.bff.api.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <a>Title: RfqQuotationController </a>
 * <a>Author: Mike Chen <a>
 * <a>Description:  <a>
 *
 * <AUTHOR> Chen
 * @date 2021/7/16 16:18
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("rfq-bff/quotation")
@Api(tags = {"rfq报价相关接口"})
public class RfqQuotationController {

    private final HttpServletRequest httpServletRequest;
    private final RfqQuotationService rfqQuotationService;

    @Login
    @ApiOperation(value = "买家rfq--报价列表", notes = "买家rfq--报价列表", tags = {"rfq报价相关接口"})
    @GetMapping("v1/rfq-quotation-list/{rfqId}")
    public Result<RfqQuotationListVO> rfqQuotationList(@PathVariable("rfqId") String rfqId) {
        return rfqQuotationService.rfqQuotationList(rfqId);
    }

    @Login
    @ApiOperation(value = "买家rfq--报价详情", notes = "买家rfq--报价详情", tags = {"rfq报价相关接口"})
    @GetMapping("v1/rfq-quotation-detail/{quotationId}")
    public Result<RfqQuotationDetailVO> rfqQuotationDetail(@PathVariable("quotationId") String quotationId) {
        return rfqQuotationService.rfqQuotationDetail(quotationId);
    }

    @Login
    @ApiOperation(value = "买家rfq--报价对比", notes = "买家rfq--报价对比", tags = {"rfq报价相关接口"})
    @PostMapping("v1/rfq-quotation-compare")
    public Result<RfqQuotationCompareVO> rfqQuotationCompare(@ApiIgnore UserVO userVO, @Valid @RequestBody RfqQuotationCompareRequestDTO requestDTO) {
        return rfqQuotationService.rfqQuotationCompare(requestDTO.toBuilder().userId(UserUtil.getUserId(userVO)).build());
    }

    @Login
    @ApiOperation(value = "卖家rfq--报价", notes = "卖家rfq--报价", tags = {"rfq报价相关接口"})
    @PostMapping("v1/rfq-create-quotation")
    public Result<Boolean> createQuotation(@ApiIgnore UserVO userVO, @Valid @RequestBody RfqQuotationCreateRequestDTO requestDTO) {
        return rfqQuotationService.createQuotation(requestDTO.toBuilder().userId(UserUtil.getUserId(userVO)).supplierId(UserUtil.getSupplierId(userVO)).email(UserUtil.getEmail(userVO)).build());
    }

    @Login
    @ApiOperation(value = "卖家rfq--供应商报价数量", notes = "卖家rfq--供应商报价数量", tags = {"rfq报价相关接口"})
    @GetMapping("v1/rfq-quotation-count")
    public Result<SellerQuotationCountVO> supplierQuotationCount(@ApiIgnore UserVO userVO) {
        return rfqQuotationService.supplierQuotationCount(UserUtil.getSupplierId(userVO));
    }

    @ApiOperation(value = "卖家rfq--供应商报价样品政策下拉框", notes = "卖家rfq--供应商报价样品政策下拉框", tags = {"rfq报价相关接口"})
    @GetMapping("v1/rfq-quotation-sample-type-list")
    public Result<List<Map<String, String>>> supplierQuotationSampleTypeList() {
        return Result.success(SampleTypeUtil.getRfqQuotationSampleType(httpServletRequest));
    }

    @Login
    @ApiOperation(value = "买家rfq--聊天修改报价已回复状态", notes = "买家rfq--聊天修改报价已回复状态", tags = {"rfq报价相关接口"})
    @GetMapping("v1/rfq-quotation-chat-replied/{quotationId}")
    public Result<Boolean> rfqQuotationChatReplied(@ApiIgnore UserVO userVO, @PathVariable("quotationId") String quotationId) {
        return rfqQuotationService.rfqQuotationChatReplied(RfqQuotationChatRepliedRequestDTO.builder().quotationId(quotationId).userId(UserUtil.getUserId(userVO)).supplierId(UserUtil.getSupplierId(userVO)).build());
    }

    @Login
    @ApiOperation(value = "卖家rfq--导入上次报价信息", notes = "卖家rfq--导入上次报价信息", tags = {"rfq报价相关接口"})
    @GetMapping("v1/rfq-quotation-import")
    public Result<RfqQuotationImportVO> importPreviousQuotation(@ApiIgnore UserVO userVO) {
        return rfqQuotationService.importPreviousQuotation(UserUtil.getSupplierId(userVO), UserUtil.getUserId(userVO));
    }

    @Login
    @ApiOperation(value = "卖家rfq--导入上次报价信息二次确认", notes = "卖家rfq--导入上次报价信息二次确认", tags = {"rfq报价相关接口"})
    @GetMapping("v1/rfq-quotation-import-check")
    public Result<String> importPreviousQuotationCheck(@ApiIgnore UserVO userVO) {
        return rfqQuotationService.importPreviousQuotationCheck(UserUtil.getSupplierId(userVO), UserUtil.getUserId(userVO));
    }

    @Login
    @ApiOperation(value = "卖家rfq--导入产品信息搜索接口", notes = "卖家rfq--导入产品信息搜索接口", tags = {"rfq报价相关接口"})
    @PostMapping("v1/supplier-product-search")
    public Result<List<RfqProductImportSearchVO>> importProductSearch(@ApiIgnore UserVO userVO, @RequestBody RfqImportProductSearchRequestDTO requestDTO) {
        return rfqQuotationService.importProductSearch(requestDTO.toBuilder().userId(UserUtil.getUserId(userVO)).supplierId(UserUtil.getSupplierId(userVO)).build());
    }

    @Login
    @ApiOperation(value = "卖家rfq--导入产品信息搜索类目", notes = "卖家rfq--导入产品信息搜索类目", tags = {"rfq报价相关接口"})
    @PostMapping({"v1/category-owner"})
    public Result<List<RfqCategoryVO>> rfqCategoryOwner(@ApiIgnore UserVO userVO) {
        return rfqQuotationService.rfqCategoryOwner(RfqImportProductSearchRequestDTO.builder().supplierId(UserUtil.getSupplierId(userVO)).userId(UserUtil.getUserId(userVO)).build());
    }

    @Login
    @ApiOperation(value = "卖家rfq--导入产品信息", notes = "卖家rfq--导入产品信息", tags = {"rfq报价相关接口"})
    @GetMapping("v1/rfq-product-import/{productId}")
    public Result<RfqProductImportVO> importProduct(@PathVariable("productId") Long productId) {
        return rfqQuotationService.importProduct(productId);
    }

}
