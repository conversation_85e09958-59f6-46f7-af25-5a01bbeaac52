package com.globalsources.bff.web.rfq.controller;

import com.globalsources.bff.web.rfq.service.RfqThreadService;
import com.globalsources.framework.annotation.Login;
import com.globalsources.framework.result.Result;
import com.globalsources.framework.vo.UserVO;
import com.globalsources.rfq.bff.api.model.dto.req.RfqReassignRequestDTO;
import com.globalsources.rfq.bff.api.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

/**
 * <a>Title: RefThreadController </a>
 * <a>Author: <PERSON> <a>
 * <a>Description:  <a>
 *
 * <AUTHOR> Chen
 * @date 2021/7/20 18:26
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("rfq-bff/thread")
@Api(tags = {"rfq分配相关接口"})
public class RefThreadController {

    private final RfqThreadService rfqThreadService;

    @Login
    @ApiOperation(value = "卖家rfq--重新分配", notes = "卖家rfq--重新分配", tags = {"rfq分配相关接口"})
    @PostMapping("v1/rfq-reassign")
    public Result<List<String>> reassign(@ApiIgnore UserVO userVO, @Valid @RequestBody RfqReassignRequestDTO requestDTO) {
        return rfqThreadService.reassign(requestDTO.toBuilder().supplierId(UserUtil.getSupplierId(userVO)).currentUserId(UserUtil.getUserId(userVO)).firstName(userVO.getFirstName()).lastName(userVO.getLastName()).build());
    }

    @Login
    @ApiOperation(value = "卖家rfq--重新分配报价校验", notes = "卖家rfq--重新分配报价校验", tags = {"rfq分配相关接口"})
    @PostMapping("v1/rfq-reassign-check")
    public Result<List<String>> reassignCheck(@ApiIgnore UserVO userVO, @Valid @RequestBody RfqReassignRequestDTO requestDTO) {
        return rfqThreadService.reassignCheck(requestDTO.toBuilder().supplierId(UserUtil.getSupplierId(userVO)).currentUserId(UserUtil.getUserId(userVO)).firstName(userVO.getFirstName()).lastName(userVO.getLastName()).build());
    }
}