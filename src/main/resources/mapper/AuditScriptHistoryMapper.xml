<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.eblock.agg.dao.AuditScriptHistoryMapper">

<!--    &lt;!&ndash; 通用查询映射结果 &ndash;&gt;-->
<!--    <resultMap id="BaseResultMap" type="com.globalsources.eblock.agg.entity.AuditScriptHistoryPO">-->
<!--        <id column="script_history_id" property="scriptHistoryId" />-->
<!--        <result column="script_id" property="scriptId" />-->
<!--        <result column="audit_script_data" property="auditScriptData" />-->
<!--        <result column="edit_by" property="editBy" />-->
<!--        <result column="create_date" property="createDate" />-->
<!--    </resultMap>-->

<!--    &lt;!&ndash; 通用查询结果列 &ndash;&gt;-->
<!--    <sql id="Base_Column_List">-->
<!--        script_history_id, script_id, audit_script_data, edit_by, create_date-->
<!--    </sql>-->

</mapper>
