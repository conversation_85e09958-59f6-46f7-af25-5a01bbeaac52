<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.admin.dao.AcUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.globalsources.admin.model.pojo.AcUser">
        <id column="user_id" property="userId" />
        <result column="password" property="password" />
        <result column="gs_mail_announce" property="gsMailAnnounce" />
        <result column="email_addr" property="email" />
        <result column="gs_mail_survey" property="gsMailSurvey" />
        <result column="gender" property="gender" />
        <result column="first_name" property="firstName" />
        <result column="last_name" property="lastName" />
        <result column="tel_country_code" property="telCountryCode" />
        <result column="tel_area" property="telAreaCode" />
        <result column="tel_num" property="phoneNumber" />
        <result column="tel_ext" property="telExtensionNumber" />
        <result column="country_code" property="countryCode" />
        <result column="company_id" property="companyId" />
        <result column="create_date" property="createDate" />
        <result column="l_upd_date" property="updateDate" />
        <result column="l_upd_by" property="updateUser" />
        <result column="create_by" property="createUser" />
        <result column="user_type" property="type" />
        <result column="tel_country_code_2" property="telCountryCode2" />
        <result column="tel_area_2" property="telAreaCode2" />
        <result column="tel_num_2" property="phoneNumber2" />
        <result column="picture_url" property="photo" />
        <result column="doi_flag" property="activateEmail" />
        <result column="admin_flag" property="administratorFlag" />
        <result column="parent_user_id" property="parentId" />
        <result column="user_status" property="status" />
        <result column="delete_flag" property="deleted" />
        <result column="qc_assignable_flag" property="qcAssignableFlag" />
        <result column="qc_view_all_flag" property="qcViewAllFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, password, gs_mail_announce, email_addr, gs_mail_survey, gender, first_name, last_name, tel_country_code, tel_area, tel_num, tel_ext, country_code, company_id, create_date, l_upd_date, l_upd_by, create_by, user_type, tel_country_code_2, tel_area_2, tel_num_2, picture_url, doi_flag, admin_flag, parent_user_id, user_status, delete_flag, qc_assignable_flag, qc_view_all_flag
    </sql>


    <select id="selectUserAccountList" resultType="com.globalsources.admin.model.dto.AcUserDTO">
        SELECT uuu.user_id as userId,
        uuu.first_name as firstName,
        uuu.last_name as lastName,
        uuu.email_addr as email,
        uuu.doi_flag as activateEmail,
        uuu.tel_num as phoneNumber,
        uuu.user_status as status,
        uuu.admin_flag as administratorFlag,
        uuu.tel_country_code as telCountryCode,
        uuu.tel_area as telAreaCode,
        uuu.l_upd_date as updateDate,
        uuu.qc_assignable_flag,
        uuu.qc_view_all_flag,
        array_to_string(array_agg(r.role_name),',') as roleName
        FROM ac_user uuu
        left join ac_user_role ur on uuu.user_id = ur.user_id
        left join  ac_role r on ur.role_id = r.role_id and r.app_name = 'ADMIN'
        WHERE  1=1
        and uuu.delete_flag = false
        <if test="acUserQuery.keyword != null and acUserQuery.keyword != ''"> and (uuu.first_name LIKE concat(#{acUserQuery.keyword},'%') OR uuu.email_addr LIKE concat(#{acUserQuery.keyword},'%')
            OR uuu.last_name LIKE concat(#{acUserQuery.keyword},'%') OR uuu.tel_num LIKE concat(#{acUserQuery.keyword},'%'))</if>
        <if test="acUserQuery.statusAc != null"> and uuu.user_status=#{acUserQuery.statusAc}</if>
        <if test="acUserQuery.role != null"> and ur.role_id=#{acUserQuery.role}</if>
        <if test="acUserQuery.adminFlag != null and acUserQuery.adminFlag != true and acUserQuery.childUserIdList != null and acUserQuery.childUserIdList.size gt 0 ">
            and uuu.user_id IN
            <foreach collection="acUserQuery.childUserIdList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="acUserQuery.type != null">
            and uuu.user_type = #{acUserQuery.type}
        </if>
        <if test="acUserQuery.qcAssignableFlag != null">
            and uuu.qc_assignable_flag = #{acUserQuery.qcAssignableFlag}
        </if>
        group by uuu.user_id, uuu.parent_user_id, uuu.first_name, uuu.last_name, uuu.email_addr, uuu.doi_flag, uuu.tel_num, uuu.user_status, uuu.admin_flag, uuu.tel_country_code, uuu.tel_area, uuu.l_upd_date, uuu.qc_assignable_flag, uuu.qc_view_all_flag
        ORDER BY uuu.l_upd_date DESC
    </select>


    <update id="logicDeleteUserByUserId">
        update ac_user set delete_flag = true
        where user_id = #{userId}
    </update>

    <update id="updateUserPasswordByUserId">
        update ac_user set password = #{password}
        where user_id = #{userId}
    </update>

    <update id="updateUserTypeByUserId">
        update ac_user set user_type = #{userType}
        where user_id = #{userId}
    </update>

    <update id="updateUserEmailByUserId">
        update ac_user set email_addr = #{email}
        where user_id = #{userId}
    </update>



    <select id="selectAssignUserList" resultType="com.globalsources.admin.model.dto.user.AssignUserInfoDTO">
        SELECT uuu.user_id as userId,
        uuu.email_addr as email
        FROM ac_user uuu
        <where>
            uuu.user_status = 1
            and uuu.delete_flag = false
            and uuu.user_type = 1
            and uuu.qc_assignable_flag = true
        </where>
        ORDER BY uuu.l_upd_date DESC
    </select>

    <select id="selectAssignUserListV2" resultType="com.globalsources.admin.model.dto.user.AssignUserInfoDTO">
        SELECT uuu.user_id as userId, uuu.email_addr as email
        FROM ac_user uuu
        join ac_user_review_permission aurp on uuu.user_id = aurp.user_id
        <where>
            uuu.user_status = 1
            and uuu.delete_flag = false
            and uuu.user_type = 1
            and aurp.type = #{type}
            and aurp.qc_assignable_flag = true
        </where>
        ORDER BY uuu.l_upd_date DESC
    </select>

    <select id="selectAssignUserListV3" resultType="com.globalsources.admin.model.vo.user.AssignUserInfoVO">
        SELECT uuu.user_id as assignedUserId, uuu.email_addr as email, uuu.first_name as firstName, uuu.last_name as lastName
        FROM ac_user uuu
        join ac_user_review_permission aurp on uuu.user_id = aurp.user_id
        <where>
            uuu.user_status = 1
            and uuu.delete_flag = false
            and uuu.user_type = 1
            and aurp.type = #{type}
            and aurp.qc_assignable_flag = true
        </where>
        ORDER BY uuu.create_date asc
    </select>
</mapper>