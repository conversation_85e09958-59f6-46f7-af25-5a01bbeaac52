<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.globalsources.admin.dao.FeedBackMapper">
    <select id="selectFeedBackList" resultType="com.globalsources.admin.model.pojo.FeedBackPO">
        SELECT uf.feedback_id,
        uf.image_url,
        uf.issue,
        uf.issue_type,
        uf.device_type,
        uf.email_addr,
        uf."source",
        uf.create_date,
        uf.l_upd_date,
        uf.supplier_id,
        uf.supplier_name,
        uf.function_type
        FROM "user_feedback" uf
        WHERE uf.source = #{queryDTO.source}
        <if test="queryDTO.issueTypeList != null and queryDTO.issueTypeList.size>0">
            and uf.issue_type in
            <foreach collection="queryDTO.issueTypeList" item="issueType" open="(" close=")" separator=",">
                #{issueType}
            </foreach>
        </if>
        <if test="queryDTO.queryImage">
            and length(uf.image_url) > 0
        </if>
        <if test="queryDTO.issueType != null and queryDTO.issueType != ''">
            and uf.issue_type = #{queryDTO.issueType}
        </if>
        <if test="queryDTO.functionType != null and queryDTO.functionType != ''">
            and uf.function_type = #{queryDTO.functionType}
        </if>

        <choose>
            <when test="queryDTO.startDate != null and queryDTO.endDate != null ">
                and uf.create_date between #{queryDTO.startDate} and #{queryDTO.endDate}
            </when>
            <when test="queryDTO.startDate != null and queryDTO.endDate == null ">
                <![CDATA[ and uf.create_date >= #{queryDTO.startDate} ]]>
            </when>
            <when test="queryDTO.startDate == null  and queryDTO.endDate != null ">
                <![CDATA[ and uf.create_date <= #{queryDTO.endDate} ]]>
            </when>

        </choose>
        ORDER BY uf.create_date DESC
    </select>

    <select id="selectNPSFeedBackList" resultType="com.globalsources.admin.model.pojo.FeedBackPO">
        SELECT uf.feedback_id,
        uf.image_url,
        uf.issue,
        uf.issue_type,
        uf.device_type,
        case uf.email_addr
        when '<EMAIL>' then '' else uf.email_addr end,
        uf."source",
        uf.create_date,
        uf.l_upd_date,
        uf.supplier_id,
        uf.supplier_name,
        uf.nps_score,
        uf.nps_location,
        uf.browser_type,
        uf.ip_address
        FROM "user_feedback" uf
        WHERE uf.issue_type = 'NPS Feedback'
        <if test="queryDTO.deviceType !=null and queryDTO.deviceType !=''">
            and uf.device_type = #{queryDTO.deviceType}
        </if>
        <if test="queryDTO.startTime != null and queryDTO.endTime != null">
            and (uf.create_date &gt; #{queryDTO.startTime} and uf.create_date &lt; #{queryDTO.endTime})
        </if>
        ORDER BY uf.create_date DESC
    </select>


    <select id="selectNPSFeedBackAvgScore" resultType="java.util.Map" parameterType="com.globalsources.admin.model.dto.feedback.NPSFeedbackQueryDTO">
        SELECT
        SUM
        ( CASE WHEN issue_type = 'NPS Feedback' AND device_type = #{queryDTO.deviceType}
        <if test="queryDTO.startTime != null and queryDTO.endTime != null">
            and (create_date &gt; #{queryDTO.startTime} and create_date &lt; #{queryDTO.endTime})
        </if>

        THEN 1 ELSE 0 END ) AS total,
        SUM ( CASE WHEN nps_score IN ( 9, 10 ) AND issue_type = 'NPS Feedback' AND device_type = #{queryDTO.deviceType}
        <if test="queryDTO.startTime != null and queryDTO.endTime != null">
            and (create_date &gt; #{queryDTO.startTime} and create_date &lt; #{queryDTO.endTime})
        </if>

        THEN 1 ELSE 0 END ) AS MAX,
        SUM ( CASE WHEN nps_score BETWEEN 0 AND 6 AND issue_type = 'NPS Feedback' AND device_type = #{queryDTO.deviceType}
        <if test="queryDTO.startTime != null and queryDTO.endTime != null">
            and (create_date &gt; #{queryDTO.startTime} and create_date &lt; #{queryDTO.endTime})
        </if>
        THEN 1 ELSE 0 END ) AS MIN
        FROM
        user_feedback
    </select>

    <select id="avgNPS" resultType="java.math.BigDecimal">
        SELECT COALESCE(avg(nps_score), 0) FROM user_feedback WHERE issue_type = 'NPS Feedback' AND device_type = #{deviceType}
    </select>
</mapper>