<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.rfi.data.dao.InquiryCountryDao">

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO inquire_country(inquiry_id, create_date, buyer_ip,country,province,region)
        VALUES
        <foreach collection="list" separator="," item="dto">
            (#{dto.inquiryId}, #{dto.createDate}, #{dto.buyerIp},#{dto.country},#{dto.province},#{dto.region})
        </foreach>
        on conflict (inquiry_id) do update set
        inquiry_id = EXCLUDED.inquiry_id,
        create_date =  EXCLUDED.create_date,
        buyer_ip = EXCLUDED.buyer_ip,
        country = EXCLUDED.country,
        province = EXCLUDED.province,
        region = EXCLUDED.region
    </insert>
</mapper>
