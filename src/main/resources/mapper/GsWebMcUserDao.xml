<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.rfi.data.dao.GsWebMcUserDao">

    <resultMap id="BastResultMap" type="com.globalsources.rfi.data.entity.GsWebMcUserEntity">
        <result property="userId" column="user_id"/>
        <result property="ldapUserId" column="ldap_user_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="appTourStatus" column="app_tour_status"/>
        <result property="prefLang" column="pref_lang"/>
        <result property="lUpdTimestamp" column="l_upd_timestamp"/>
        <result property="mcEmailAddr" column="mc_email_addr"/>
        <result property="cmHelpTourStatus" column="cm_help_tour_status"/>
        <result property="lReplyTimestamp" column="l_reply_timestamp"/>
    </resultMap>

    <select id="queryMcEmailAddr" resultType="java.lang.String">
        select mc_email_addr from bidirefresh_grp.mc_user where ldap_user_id = #{userId} and supplier_id = #{supplierId} ORDER BY l_reply_timestamp limit 1
    </select>

    <select id="quyerMcUserId" resultType="java.lang.String">
        select user_id from bidirefresh_grp.mc_user where ldap_user_id = #{userId} and supplier_id = #{supplierId} ORDER BY l_reply_timestamp LIMIT 1
    </select>

    <select id="gsWebMcUserEntity" resultMap="BastResultMap">
        SELECT
        user_id,
        ldap_user_id,
        supplier_id,
        app_tour_status,
        pref_lang,
        l_upd_timestamp,
        mc_email_addr,
        cm_help_tour_status,
        l_reply_timestamp
        FROM
        bidirefresh_grp.mc_user
        WHERE
        ldap_user_id = #{ userId }
        AND supplier_id = #{ supplierId } ORDER BY l_reply_timestamp limit 1
    </select>

</mapper>
