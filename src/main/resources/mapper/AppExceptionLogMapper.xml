<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.globalsources.admin.dao.AppExceptionLogMapper">
    <select id="queryAppExceptionUrlCount"
            resultType="com.globalsources.admin.model.vo.app.AppExceptionUrlCountVO">
        select
        ael.apiExceptionUrl,
        coalesce(tb.apiExceptionUrlCount, 0) as apiExceptionUrlCount
        from (
        select
        ae.api_exception_url as apiExceptionUrl,
        0 as apiExceptionUrlCount
        from (
        select
        case
        when position('?' in api_exception_url) > 0 then SUBSTRING(api_exception_url from 1 for position('?' in api_exception_url) - 1)
        else api_exception_url
        end
        from
        app_exception_log
        where
        exception_type = 'ApiError'
        and occur_date >= CURRENT_DATE - interval '10 day'
        <if test="businessType != null and businessType != ''">
            and business_type = #{businessType}
        </if>
        ) as ae
        group by ae.api_exception_url
        ) as ael
        left join (
        select
        ae.api_exception_url as apiExceptionUrl,
        count(ae.api_exception_url) as apiExceptionUrlCount
        from (
        select
        case
        when position('?' in api_exception_url) > 0 then SUBSTRING(api_exception_url from 1 for position('?' in api_exception_url) - 1)
        else api_exception_url
        end
        from
        app_exception_log
        where
        exception_type = 'ApiError'
        <if test="startTime != null and endTime != null">
            and occur_date &gt;= #{startTime} and occur_date &lt;= #{endTime}
        </if>
        <if test="businessType != null and businessType != ''">
            and business_type = #{businessType}
        </if>
        ) as ae
        group by ae.api_exception_url
        ) tb on tb.apiExceptionUrl = ael.apiExceptionUrl
        order by
        apiExceptionUrl desc
    </select>

</mapper>