<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.admin.dao.AcRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.globalsources.admin.model.pojo.AcRole">
        <id column="role_id" property="roleId" />
        <result column="role_name" property="roleName" />
        <result column="description" property="description" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="l_upd_by" property="lUpdBy" />
        <result column="l_upd_date" property="lUpdDate" />
        <result column="active_flag" property="activeFlag"/>
        <result column="remark" property="remark"/>
        <result column="sys_role_flag" property="sysRoleFlag"/>
        <result column="view_inherit_supplier_flag" property="supplierFullDataFlag"/>
        <result column="app_name" property="appName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        role_id, role_name, description, delete_flag, create_by, create_date, l_upd_by, l_upd_date, active_flag, remark, sys_role_flag, view_inherit_supplier_flag, app_name
    </sql>

    <update id="batchLogicDelete">
        UPDATE ac_role
        SET
            delete_flag = TRUE,
            l_upd_by = #{userId},
            l_upd_date = #{updateDate}
        WHERE delete_flag = FALSE
        AND role_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="changeAcRoleStatus">
        UPDATE ac_role
        SET
        active_flag = #{status},
        l_upd_by = #{userId},
        l_upd_date = #{updateDate}
        WHERE delete_flag = FALSE
        AND role_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateRole">
        UPDATE ac_role
        SET
        l_upd_by = #{bean.lUpdBy},
        <if test="bean.roleName != null and bean.roleName != ''">
            role_name = #{bean.roleName},
        </if>
        view_inherit_supplier_flag = #{bean.supplierFullDataFlag},
        active_flag = #{bean.activeFlag},
        <if test="bean.remark != null and bean.remark != ''">
            remark = #{bean.remark},
        </if>
        l_upd_date = #{bean.lUpdDate}
        WHERE delete_flag = FALSE
        AND role_id = #{bean.roleId}
    </update>

    <resultMap id="acRoleDetailResultMap" type="com.globalsources.admin.model.pojo.AcRoleDetailSimplePO">
        <result column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
        <result column="active_flag" property="activeFlag"/>
        <result column="remark" property="remark"/>
        <result column="privilege_type" property="privilegeType"/>
    </resultMap>

    <select id="selectAcRoleDetail" resultMap="acRoleDetailResultMap">
        SELECT
            ar.role_id,
            ar.role_name,
            ar.active_flag,
            ar.remark,
            r.resource_code as privilege_type
        FROM
            ac_role ar
                LEFT JOIN ac_role_permission arp ON ar.role_id = arp.role_id AND ar.delete_flag = FALSE
                LEFT JOIN ac_resource r ON arp.permission_id = r.resource_id AND ar.delete_flag = FALSE
        WHERE
            ar.role_id = #{id}
    </select>

    <select id="selectRoleByUserId" resultType="com.globalsources.admin.model.pojo.AcRole">
        select r.* from ac_user_role ur
            join ac_role r on ur.role_id=r.role_id
            where ur.user_id=#{userId} and ur.delete_flag=false
            <if test="appName != null and appName != ''">
                and r.app_name = #{appName}
            </if>
            <if test="supplierId != null">
                and ur.supplier_id = #{supplierId}
            </if>
    </select>


    <select id="selectRoleIdsByUserId" resultType="java.lang.Long">
        select distinct aro.role_id
        from ac_user au
        join ac_user_role aur on aur.user_id = au.user_id and aur.delete_flag = false
        join ac_role aro on aro.role_id = aur.role_id  and aro.delete_flag = false
        <where>
            au.delete_flag = false
            and aur.user_id = #{userId}
            and aro.app_name = #{appName}
            <if test="supplierId != null">
                and aur.supplier_id = #{supplierId}
            </if>
        </where>
    </select>


    <select id="selectCsoRoleIdsByUserIdAndSupplierId" resultType="java.lang.Long">
        select distinct aro.role_id
        from ac_user_role aur
        join ac_role aro on aro.role_id = aur.role_id  and aro.delete_flag = false
        <where>
            and aur.user_id = #{userId}
            and aro.app_name = 'SC'
            and aur.supplier_id = #{supplierId}
            and aur.role_id = 1400000000210
            and aur.delete_flag = false
        </where>
    </select>

</mapper>