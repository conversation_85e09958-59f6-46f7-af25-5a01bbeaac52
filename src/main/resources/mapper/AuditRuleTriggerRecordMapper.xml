<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.eblock.agg.dao.AuditRuleTriggerRecordMapper">

    <select id="forecastResultCount" resultType="java.lang.Integer">
        SELECT COUNT(distinct request_id)
        FROM contentaudit_grp.audit_rule_trigger_record
        where
        draft_flag = true
        and rule_id like CONCAT('%',#{uuid}::text)
        <if test="type != null and type !=''">
            AND execute_result = #{type}
            AND script_id = 'catchCmp'
        </if>
        and request_id not in
        (
        select distinct request_id
        FROM contentaudit_grp.audit_rule_trigger_record
        where draft_flag = true
        and rule_id like CONCAT('%',#{uuid}::text)
        and script_id  in ('tmxResultCmp','whiteListDomainCmp','blackListDomainCmp','blockListDomainCmp','whiteListBuyerCmp','blockListBuyerCmp','blackListBuyerCmp')
        and execute_result is not null
        )
    </select>

</mapper>
