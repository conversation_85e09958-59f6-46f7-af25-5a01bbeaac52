<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.admin.dao.AcResourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.globalsources.admin.model.pojo.AcResource">
        <id column="resource_id" property="resourceId" />
        <result column="resource_code" property="resourceCode" />
        <result column="resource_type" property="resourceType" />
        <result column="par_resource_id" property="parResourceId" />
        <result column="resource_level" property="resourceLevel" />
        <result column="title" property="title" />
        <result column="new_label_flag" property="newLabelFlag" />
        <result column="resource_uri" property="resourceUri" />
        <result column="resource_icon_class" property="resourceIconClass" />
        <result column="component" property="component" />
        <result column="display_sort" property="displaySort" />
        <result column="create_date" property="createDate" />
        <result column="create_by" property="createBy" />
        <result column="l_upd_date" property="lUpdDate" />
        <result column="l_upd_by" property="lUpdBy" />
        <result column="default_action" property="defaultAction" />
        <result column="req_uri" property="reqUri" />
        <result column="app_name" property="appName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        resource_id, resource_code, resource_type, par_resource_id, resource_level, title, resource_uri, resource_icon_class, component, display_sort, create_date, create_by, l_upd_date, l_upd_by, default_action, req_uri, app_name ,new_label_flag
    </sql>

    <sql id="Alias_Base_Column_List">
        ars.resource_id, ars.resource_code, ars.resource_type, ars.par_resource_id, ars.resource_level, ars.title, ars.resource_uri, ars.resource_icon_class, ars.component, ars.display_sort, ars.create_date, ars.create_by, ars.l_upd_date, ars.l_upd_by, ars.default_action, ars.req_uri, ars.app_name, ars.new_label_flag
    </sql>

    <select id="selectUserResource" resultType="com.globalsources.admin.model.pojo.AcResource">
        select distinct r.*
        from ac_user_role ur
            left join ac_role_permission rp on ur.role_id=rp.role_id
            left join ac_resource r on rp.permission_id=r.resource_id
        <if test="contractCode != null">
            join ac_resource_contract_level_link arcll on r.resource_id = arcll.resource_id
        </if>
        <where>
            <if test="userId!=null">
                and ur.user_id=#{userId}
            </if>
            <if test="supplierId != null">
                and ur.supplier_id = #{supplierId}
            </if>
            <if test="resourceType!=null">
                and r.resource_type = #{resourceType}
            </if>
            <if test="appName != null">
                and r.app_name = #{appName}
            </if>
            <if test="contractCode != null">
                and arcll.contract_code = #{contractCode}
            </if>
        </where>
        order by r.display_sort asc
    </select>

    <select id="selectUserScButtonResourceByMenuIdIgnoreContractCode" resultType="com.globalsources.admin.model.pojo.AcResource">
        select distinct r.*
        from ac_user_role ur left join ac_role_permission rp on ur.role_id=rp.role_id
        join (WITH RECURSIVE a AS ( select p.* from ac_resource p where p.par_resource_id = #{menuId} and p.app_name = 'SC' UNION ALL select p1.* from ac_resource p1 JOIN a ON p1.par_resource_id = a.resource_id where p1.app_name = 'SC') SELECT * from a where a.resource_id != #{menuId} and a.app_name = 'SC' and resource_type = 'BUTTON' order by a.resource_level, a.display_sort
        ) r  on rp.permission_id=r.resource_id
        <where>
            <if test="userId!=null">
                and ur.user_id=#{userId}
            </if>
            <if test="supplierId != null">
                and ur.supplier_id = #{supplierId}
            </if>
            and r.resource_type = 'BUTTON' and r.app_name = 'SC'
        </where>
        order by r.display_sort asc
    </select>

    <select id="selectAllResource" resultType="com.globalsources.admin.model.pojo.AcResource">
        select <include refid="Base_Column_List"/>
        from ac_resource
        where
            app_name = #{query.appName}
            and par_resource_id=#{query.parentId} and resource_type IN
            <foreach item="item" collection="query.resourceType" open="(" close=")" separator=",">
                #{item}
            </foreach>
        order by display_sort asc;
    </select>

    <select id="selectAllButtonByLastMenuId" resultMap="BaseResultMap">
        select <include refid="Alias_Base_Column_List"/>
        from ac_resource ars2
                 join ac_resource ars on ars2.resource_id = ars.par_resource_id
        where
            ars2.resource_type='PAGE'
          and ars2.par_resource_id = #{menuResourceId}
        order by ars2.display_sort, ars.display_sort
    </select>


    <select id="selectMaxSortNumByParentId" resultType="java.lang.Integer">
        select max(display_sort) from ac_resource where par_resource_id = #{parentId}
    </select>

    <delete id="deleteAll">
        delete from ac_resource
    </delete>


    <resultMap id="ResourceRoleLinkResultMap" type="com.globalsources.admin.model.dto.resource.AcResourceLinkDTO">
        <id column="resource_id" property="resourceId" />
        <result column="resource_code" property="resourceCode" />
        <result column="req_uri" property="reqUri" />
        <collection property="roleIds" javaType="java.util.ArrayList" ofType="java.lang.Long">
            <result column="role_id"/>
        </collection>
    </resultMap>
    
    
    <select id="selectResourceRoleLink" resultMap="ResourceRoleLinkResultMap">
        select ars.resource_id, ars.resource_code, ars.req_uri, aro.role_id
        from ac_resource ars
        left join ac_role_permission arp on arp.permission_id = ars.resource_id and arp.delete_flag = false
        left join ac_role aro on aro.role_id = arp.role_id and ars.app_name = aro.app_name and aro.delete_flag = false and aro.app_name = #{appName}
        <where>
            ars.resource_type = 'BUTTON'
            <if test="resourceIds != null and resourceIds.size > 0">
                and ars.resource_id in
                <foreach collection="resourceIds" item="resourceId" open="(" separator="," close=")">
                    #{resourceId}
                </foreach>
            </if>
            and ars.app_name = #{appName}
        </where>
    </select>

    <select id="selectResourceByRoleIds" resultMap="BaseResultMap">
        select <include refid="Alias_Base_Column_List"/>
        from ac_resource ars
        join ac_role_permission arp on arp.permission_id = ars.resource_id and arp.delete_flag = false
        join ac_role aro on aro.role_id = arp.role_id and ars.app_name = aro.app_name and aro.delete_flag = false
        where aro.role_id in
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        and ars.app_name = #{appName}
    </select>

    <select id="selectNoButtonPageList" resultMap="BaseResultMap">
        select distinct <include refid="Alias_Base_Column_List"/>
        from ac_resource ars
        left join ac_resource ars2 on ars.resource_id = ars2.par_resource_id
        where ars.app_name = #{appName}
        and ars.resource_type = 'PAGE'
        and ars2.resource_id is null
    </select>

    <select id="selectAllNotButtonResourceList" resultMap="BaseResultMap">
        select distinct <include refid="Alias_Base_Column_List"/>
        from ac_resource ars
        where ars.app_name = #{appName}
        and ars.resource_type != 'BUTTON'
    </select>

    <update id="updateRequestUriByResource">
        update ac_resource
        set req_uri = #{reqUri},
            l_upd_by = #{userId},
            l_upd_date = #{updateDate}
        where resource_code = #{resourceCode}
        and app_name = #{appName}
    </update>

    <select id="selectDefaultButtonResourceList" resultMap="BaseResultMap">
        select distinct <include refid="Alias_Base_Column_List"/>
        from ac_resource ars
        where ars.app_name = #{appName}
        and ars.resource_type = 'BUTTON'
        and (
        ars.resource_code like '%DEFAULT_BUTTON_CODE'
        or ars.title = 'DefaultButton'
        )
    </select>

    <select id="selectAllChildrenResourceIdByResourceId" resultType="java.lang.Long">
        select *
        from (
            WITH RECURSIVE a AS (
                select p.resource_id from ac_resource p where p.resource_id = #{resourceId}
                UNION ALL
                select p1.resource_id from ac_resource p1 JOIN a ON p1.par_resource_id = a.resource_id
            )
            SELECT resource_id from a where resource_id != #{resourceId}
        ) temp
    </select>


    <select id="selectAllChildrenResourceByResourceIdAndAppName" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from (
                 WITH RECURSIVE a AS (
                     select p.* from ac_resource p where p.par_resource_id = #{resourceId} and p.app_name = #{appName}
                     UNION ALL
                     select p1.* from ac_resource p1 JOIN a ON p1.par_resource_id = a.resource_id where p1.app_name = #{appName}
                 )
                 SELECT * from a where a.resource_id != #{resourceId} and a.app_name = #{appName}
                 order by a.resource_level, a.display_sort
             ) temp
    </select>

    <select id="selectAllButtonChildrenResourceByResourceIdAndAppName" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from (
        WITH RECURSIVE a AS (
        select p.* from ac_resource p where p.par_resource_id = #{resourceId} and p.app_name = #{appName}
        UNION ALL
        select p1.* from ac_resource p1 JOIN a ON p1.par_resource_id = a.resource_id where p1.app_name = #{appName}
        )
        SELECT * from a where a.resource_id != #{resourceId} and a.app_name = #{appName} and resource_type = 'BUTTON'
        order by a.resource_level, a.display_sort
        ) temp
    </select>

    <select id="selectSponsoredRankingMenuResourceId" resultType="java.lang.Long">
        SELECT resource_id  FROM admin_grp.ac_resource x
        WHERE title = 'sponsoredRanking' and resource_type = 'MENU' and app_name = 'SC' and resource_level=2
          and exists (select 1 from admin_grp.ac_resource where x.par_resource_id = resource_id and resource_type = 'MENU' and app_name = 'SC' and title = 'promotion' and resource_level=1) limit 1
    </select>

</mapper>
