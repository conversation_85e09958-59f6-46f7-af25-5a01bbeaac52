<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.eblock.agg.dao.AuditInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.globalsources.eblock.agg.entity.AuditInfoPO">
        <id column="audit_info_id" property="auditInfoId"/>
        <result column="request_id" property="requestId"/>
        <result column="request_type" property="requestType"/>
        <result column="request_source" property="requestSource"/>
        <result column="send_date" property="sendDate"/>
        <result column="buyer_send_ip" property="buyerSendIp"/>
        <result column="inquiry_type" property="inquiryType"/>
        <result column="tracking_path" property="trackingPath"/>
        <result column="subject" property="subject"/>
        <result column="message" property="message"/>
        <result column="has_attachment_flag" property="hasAttachmentFlag"/>
        <result column="category_id" property="categoryId"/>
        <result column="product_id" property="productId"/>
        <result column="supplier_id_list" property="supplierIdList"/>
        <result column="supplier_cnt" property="supplierCnt"/>
        <result column="buyer_id" property="buyerId"/>
        <result column="buyer_email_addr" property="buyerEmailAddr"/>
        <result column="buyer_first_name" property="buyerFirstName"/>
        <result column="buyer_last_name" property="buyerLastName"/>
        <result column="buyer_company_name" property="buyerCompanyName"/>
        <result column="buyer_company_website" property="buyerCompanyWebsite"/>
        <result column="buyer_country_code" property="buyerCountryCode"/>
        <result column="buyer_country_name" property="buyerCountryName"/>
        <result column="system_audit_status" property="systemAuditStatus"/>
        <result column="system_audit_date" property="systemAuditDate"/>
        <result column="system_audit_rules" property="systemAuditRules"/>
        <result column="manual_audit_status" property="manualAuditStatus"/>
        <result column="manual_audit_date" property="manualAuditDate"/>
        <result column="manual_audit_by" property="manualAuditBy"/>
        <result column="manual_audit_email_addr" property="manualAuditEmailAddr"/>
        <result column="manual_audit_reason_id" property="manualAuditReasonId"/>
        <result column="manual_audit_reason" property="manualAuditReason"/>
        <result column="resend_date" property="resendDate"/>
        <result column="resend_by" property="resendBy"/>
        <result column="final_audit_status" property="finalAuditStatus"/>
        <result column="final_audit_date" property="finalAuditDate"/>
        <result column="tmx_review_status" property="tmxReviewStatus"/>
        <result column="tmx_policy_score" property="tmxPolicyScore"/>
        <result column="tmx_risk_rating" property="tmxRiskRating"/>
        <result column="create_date" property="createDate"/>
        <result column="l_upd_date" property="lUpdDate"/>
        <result column="delete_flag" property="deleteFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        audit_info_id, request_id, request_type, request_source, send_date, buyer_send_ip, inquiry_type, tracking_path,
        subject, message, has_attachment_flag, category_id, product_id, supplier_id_list, supplier_cnt, buyer_id,
        buyer_email_addr, buyer_first_name, buyer_last_name, buyer_company_name, buyer_company_website,
        buyer_contry_code, buyer_contry_name, system_audit_status, system_audit_date, system_audit_rules,
        manual_audit_status, manual_audit_date, manual_audit_by, manual_audit_email_addr, manual_audit_reason_id,
        manual_audit_reason, resend_date, resend_by, final_audit_status, final_audit_date, tmx_review_status,
        tmx_policy_score, tmx_risk_rating, create_date, l_upd_date, delete_flag
    </sql>

    <update id="updateBatch">
        <foreach collection="auditInfoList" item="item" separator=";">
            update ${tableName}
            set buyer_send_ip= #{item.buyerSendIp},
                subject = #{item.subject},
                category_id = #{item.categoryId},
                supplier_id_list = #{item.supplierIdList},
                supplier_cnt = #{item.supplierCnt},
                buyer_email_addr = #{item.buyerEmailAddr},
                buyer_first_name = #{item.buyerFirstName},
                buyer_last_name = #{item.buyerLastName},
                buyer_company_name = #{item.buyerCompanyName},
                final_audit_date = now(),
                tmx_policy_score = #{item.tmxPolicyScore},
                tmx_risk_rating = #{item.tmxRiskRating},
                buyer_company_website = #{item.buyerCompanyWebsite},
                buyer_country_code = #{item.buyerCountryCode},
                buyer_country_name = #{item.buyerCountryName},
                buyer_title = #{item.buyerTitle},
                buyer_job_title = #{item.buyerJobTitle},
                buyer_province = #{item.buyerProvince},
                buyer_city = #{item.buyerCity},
                buyer_zip_code = #{item.buyerZipCode},
                buyer_tel_country_code = #{item.buyerTelCountryCode},
                buyer_tel_area = #{item.buyerTelArea},
                buyer_tel_num = #{item.buyerTelNum},
                buyer_tel_ext = #{item.buyerTelExt},
                buyer_register_date = #{item.buyerRegisterDate},
                buyer_ip_country_code = #{item.buyerIpCountryCode},
                buyer_business_type = #{item.buyerBusinessType},
                buyer_linkedln_url = #{item.buyerLinkedlnUrl},
                category_name = #{item.categoryName},
                product_name = #{item.productName},
                model_number = #{item.modelNumber},
                buyer_job_function = #{item.buyerJobFunction},
                buyer_wechat_id = #{item.buyerWechatId},
                supplier_id = #{item.supplierId},
                supplier_name = #{item.supplierName}
            where audit_info_id = #{item.auditInfoId}
        </foreach>
    </update>

    <select id="selectPendingReviewBuyerList" resultType="com.globalsources.eblock.agg.api.model.vo.audit.rfi.AuditInfoVO">
        SELECT
            buyerId,
            buyerEmailAddr,
            buyerFirstName || ' ' || buyerLastName buyerName,
            buyerCompanyName,
            notReviewedRfiCnt,
            supplierCnt,
            systemAuditDate
        FROM (
            SELECT * from (
                SELECT
                    buyer_id as buyerId,
                    buyer_email_addr as buyerEmailAddr,
                    buyer_first_name as buyerFirstName,
                    buyer_last_name as buyerLastName,
                    buyer_company_name as buyerCompanyName,
                    row_number ( ) over ( partition by buyer_id order by create_date desc) rn
                FROM
                    audit_info
                WHERE
                    delete_flag = false
                    AND manual_audit_status = 'Pending'
            ) ait
            WHERE rn = 1
        ) a
        INNER JOIN
        (
            SELECT
                buyer_id,
                count(request_id) as notReviewedRfiCnt,
                sum(supplier_cnt) as supplierCnt,
                min(system_audit_date) as systemAuditDate
            FROM
                audit_info
            WHERE
                delete_flag = false
                AND manual_audit_status = 'Pending'
            GROUP BY buyer_id
        ) b ON a.buyerId = b.buyer_id
        <choose>
            <when test="dto.sortField == 'buyerEmailAddr' or dto.sortField == 'buyerName' or dto.sortField == 'notReviewedRfiCnt' or dto.sortField == 'supplierCnt' or dto.sortField == 'systemAuditDate'">
                ORDER BY ${dto.sortField} ${dto.sortType}
            </when>
            <otherwise>
                ORDER BY systemAuditStatus
            </otherwise>
        </choose>
    </select>

    <select id="getAuditInfoPageList" resultType="com.globalsources.eblock.agg.entity.AuditInfoPO">
        select
        sum(supplier_cnt) as supplierCnt,
        buyer_id as buyerId,
        buyer_email_addr as buyerEmailAddr,
        buyer_first_name as buyerFirstName,
        buyer_last_name as buyerLastName,
        buyer_company_name as buyerCompanyName,
        min(system_audit_date) as systemAuditDate
        from audit_info
        where delete_flag = false and manual_audit_status = 'Pending'
        group by buyerId, buyerEmailAddr, buyerFirstName, buyerLastName, buyerCompanyName
        <if test="dto.sortField == 'buyerEmailAddr' or dto.sortField == 'systemAuditDate' or dto.sortField == 'supplierCnt'">
            order by ${dto.sortField} ${dto.sortType}
        </if>
    </select>

    <sql id="reviewListSql">
        SELECT
            ai.audit_info_id auditInfoId,
            ai.request_id requestId,
            ai.buyer_email_addr buyerEmailAddr,
            ai.buyer_id buyerId,
            ai.request_source requestSource,
            ai.subject,
            ai.message,
            ai.product_id productId,
            ai.category_id categoryId,
            ai.supplier_cnt supplierCnt,
            ai.supplier_id as supplierId,
            ai.buyer_ip_country_code buyerCountryCode,
            ai.buyer_send_ip buyerSendIP,
            ai.inquiry_type inquiryType,
            ai.tracking_path trackingPath,
            ai.has_attachment_flag hasAttachmentFlag,
            ai.tmx_policy_score tmxPolicyScore,
            ai.tmx_risk_rating tmxRiskRating,
            ai.system_audit_status systemAuditStatus,
            ai.system_audit_date systemAuditDate,
            ai.manual_audit_status manualAuditStatus,
            ai.manual_audit_date manualAuditDate,
            ai.manual_audit_email_addr manualAuditEmailAddr,
            ai.manual_audit_reason manualAuditReason,
            ai.resend_date resendDate,
            ai.resend_by resendBy,
            ai.final_audit_status finalAuditStatus,
            ai.final_audit_date finalAuditDate,
            ai.system_audit_rules as ruleName,
            ai.final_audit_date finalAuditDate,
            ai.buyer_register_date buyerRegisterDate,
            ai.buyer_company_name buyerCompanyName,
            ai.buyer_company_website buyerCompanyWebsite,
            ai.buyer_business_type buyerBusinessType,
            ai.buyer_first_name buyerFirstName,
            ai.buyer_last_name buyerLastName,
            ai.buyer_job_title buyerJobTitle,
            ai.buyer_tel_num buyerTelNum,
            ai.buyer_country_name buyerCountryName,
            ai.tmx_review_status tmxReviewStatus,
            ai.system_audit_rules systemAuditRules,
            ai.tmx_smart_id tmxSmartId
        FROM audit_info ai
        WHERE ai.delete_flag = false
    </sql>

    <sql id="queryKeywordSql">
        <!-- buyer id or buyer email or rfi id -->
        <if test="(dto.buyerId != null and dto.buyerId > 0) or (dto.keyword != null and dto.keyword != '')">
            AND (
            <if test="dto.buyerId != null and dto.buyerId > 0 and dto.keyword != null and dto.keyword != ''">
                 ai.buyer_id = #{dto.buyerId} OR ai.buyer_email_addr = #{dto.keyword}
            </if>
            <if test="dto.buyerId != null and dto.buyerId > 0 and (dto.keyword == null or dto.keyword == '')">
                 ai.buyer_id = #{dto.buyerId}
            </if>
            <if test="(dto.buyerId == null or dto.buyerId == 0) and dto.keyword != null and dto.keyword != ''">
                 ai.buyer_email_addr = #{dto.keyword}
            </if>
            <if test="dto.searchType == 'detail'">
                OR ai.request_id = #{dto.keyword}
            </if>
            )
        </if>
    </sql>

    <!-- 待人工审核列表-->
    <select id="selectPendingReviewList" resultType="com.globalsources.eblock.agg.api.model.vo.audit.rfi.ReviewListVO">
        <include refid="reviewListSql"/>
        <if test="dto.buyerId != null and dto.buyerId > 0">
            AND ai.buyer_id = #{dto.buyerId}
        </if>
        <!-- 待人工审核状态-->
        AND ai.manual_audit_status = 'Pending'
        ORDER BY systemAuditStatus asc, auditInfoId asc
    </select>

    <!-- resend 和 detail 列表 -->
    <select id="selectReviewList" resultType="com.globalsources.eblock.agg.api.model.vo.audit.rfi.ReviewListVO">
        <include refid="reviewListSql"/>
        <include refid="queryKeywordSql"/>
        <!-- 最终审核状态-->
        <if test="dto.auditStatusList != null and dto.auditStatusList.size > 0">
            AND ai.final_audit_status IN
            <foreach collection="dto.auditStatusList" item="auditStatus" open="(" separator="," close=")">
                #{auditStatus}
            </foreach>
        </if>
        <!-- 最终审核时间-->
        <if test="dto.startDate != null and dto.startDate != ''">
            AND to_char( ai.final_audit_date, 'yyyy-mm-dd' ) &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null and dto.endDate != ''">
            AND to_char( ai.final_audit_date, 'yyyy-mm-dd' ) &lt;= #{dto.endDate}
        </if>
        <!-- 审核状态 -->
        <if test="dto.inquiryStatus != null and dto.inquiryStatus != ''">
            <if test="dto.inquiryStatus == 'TMXBlacklisted'">
                and ai.system_audit_rules = 'tmxAccountBlacklistCmp'
            </if>
            <if test="dto.inquiryStatus == 'TMX Rejected'">
                and ai.tmx_review_status = 'reject' and ai.system_audit_rules != 'tmxAccountBlacklistCmp'
            </if>
            <if test="dto.inquiryStatus == 'Blacklisted'">
                and ((ai.system_audit_status = 'Reject' and ai.system_audit_rules in ('blackListBuyerCmp','blackListDomainCmp')) or ai.manual_audit_status = 'Reject')
            </if>
            <if test="dto.inquiryStatus == 'Blocked'">
                and ai.manual_audit_status = 'Block'
            </if>
            <if test="dto.inquiryStatus == 'Release'">
                and ai.final_audit_status = 'Release'
            </if>
        </if>
        ORDER BY finalAuditDate asc, auditInfoId asc
    </select>

    <!-- 待人工审核询盘详情汇总 -->
    <select id="summaryPendingReview" resultType="com.globalsources.eblock.agg.api.model.vo.audit.rfi.SummaryPendingReviewVO">
        SELECT
            COALESCE(COUNT(ai.request_id), 0) as totalReviewedRfiCount,
            COALESCE(SUM(ai.supplier_cnt), 0) as totalAffectedRfiCount,
            COALESCE(SUM(case when ai.inquiry_type = 'CATEGORY' then 1 else 0 end), 0) as categoryRfiCount,
            COALESCE(SUM(case when ai.inquiry_type = 'PRODUCT' then 1 else 0 end), 0) as productRfiCount,
            COALESCE(SUM(case when ai.inquiry_type = 'SUPPLIER' then 1 else 0 end), 0) as supplierRfiCount
        FROM
            audit_info ai
        WHERE
            ai.delete_flag = false
            <if test="dto.buyerId != null and dto.buyerId > 0">
                AND ai.buyer_id = #{dto.buyerId}
            </if>
            <!-- 待人工审核状态-->
            AND ai.manual_audit_status = 'Pending'
    </select>

    <!-- 过去N天(询盘提交时间)人工审核记录汇总 -->
    <select id="summaryReviewHistory" resultType="com.globalsources.eblock.agg.api.model.vo.audit.rfi.SummaryReviewHistoryVO">
        select
            (SUM(total.releaseRfiCount) + SUM(total.blockRfiCount) + SUM(total.rejectRfiCount)) as totalReviewedRfiCount,
            SUM(total.releaseRfiCount) as releaseRfiCount,
            SUM(total.blockRfiCount) as blockRfiCount,
            SUM(total.rejectRfiCount) as rejectRfiCount
        from (
            select
                COALESCE(COUNT(ai.request_id), 0) as releaseRfiCount,
                0 as blockRfiCount,
                0 as rejectRfiCount
            from
                audit_info ai
            where
                ai.delete_flag = false
                and ai.buyer_id = #{buyerId}
                <!-- resend 也是人工审核 -->
                and ai.final_audit_status = 'Release'
                and (ai.manual_audit_status='Release' or resend_by notnull)
                and send_date > CURRENT_TIMESTAMP - INTERVAL '${days} day'
            union all
            select
                0 as releaseRfiCount,
                COALESCE(SUM(case when ai.manual_audit_status = 'Block' then 1 else 0 end), 0) as blockRfiCount,
                COALESCE(SUM(case when ai.manual_audit_status = 'Reject' then 1 else 0 end), 0) as rejectRfiCount
            from
                audit_info ai
            where
                ai.delete_flag = false
                and ai.buyer_id = #{buyerId}
                and ai.final_audit_status in ('Block', 'Reject')
                and ai.manual_audit_status in ('Block', 'Reject')
                and send_date > CURRENT_TIMESTAMP - INTERVAL '${days} day'
        ) total
    </select>

    <!-- 过去N天(询盘提交时间)人工审核拦截询盘记录汇总 -->
    <select id="summaryAuditReason" resultType="com.globalsources.eblock.agg.api.model.vo.audit.rfi.SummaryAuditReasonVO">
        select
            ai.manual_audit_reason_id auditReasonId,
            ar.reason reason,
            count(ai.manual_audit_reason_id) auditCount
        from
            audit_info ai
            left join audit_reason ar on ai.manual_audit_reason_id = ar.audit_reason_id
        where
            ai.delete_flag = false
            and ai.manual_audit_reason_id notnull
            and ai.buyer_id = #{buyerId}
            and ai.final_audit_status = 'Block'
            and ai.manual_audit_status = 'Block'
            and ai.send_date > CURRENT_TIMESTAMP - INTERVAL '${days} day'
        group by ai.manual_audit_reason_id, ar.reason
        order by ar.reason
    </select>

    <select id="getSupplierIdList" resultType="java.lang.String">
        SELECT ai.supplier_id_list
        FROM audit_info ai
        WHERE ai.delete_flag = false
        AND ai.audit_info_id = #{auditInfoId}
    </select>

    <select id="getForecastList" resultType="com.globalsources.eblock.agg.entity.AuditInfoPO">
        SELECT *
        FROM audit_info ai
        WHERE ai.delete_flag = false
        AND send_date &gt; #{startDate}
        AND send_date &lt; #{endDate}
        <if test="inquiryId != null and inquiryId != ''">
            AND request_id &gt; #{inquiryId}
        </if>
        and system_audit_rules not in ('whiteListDomainCmp','whiteListBuyerCmp','blackListDomainCmp','blackListBuyerCmp','blockListDomainCmp','blockListBuyerCmp')
        and tmx_review_status ='review'
        order by request_id
        limit 10
    </select>


    <select id="selectAuditInfoCount" resultType="java.lang.Long">
        select count(1) from ${tableName}
    </select>

    <select id="selectAuditInfo" resultType="com.globalsources.eblock.agg.entity.AuditInfoPO">
        select audit_info_id as auditInfoId, request_id as requestId, buyer_send_ip as buyerSendIp, buyer_id as buyerId,subject as subject, product_id as productId, supplier_id as supplierId, inquiry_type as inquiryType
        from ${tableName}
        where
        supplier_name is null
        <if test="requestId != null and requestId != ''">
            AND request_id &lt; #{requestId}
        </if>
        ORDER BY request_id desc
        limit #{limitNum}
    </select>
    <select id="selectErrorCountryCode" resultType="java.lang.Long">
        select audit_info_id
        from contentaudit_grp.audit_info
        where
        length(buyer_ip_country_code) > 2
    </select>
    <select id="forecastCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM audit_info ai
        WHERE ai.delete_flag = false
        AND send_date &gt; #{startDate}
        AND send_date &lt; #{endDate}
        and tmx_review_status= 'review'
        and system_audit_rules not in ('whiteListDomainCmp','whiteListBuyerCmp','blackListDomainCmp','blackListBuyerCmp','blockListDomainCmp','blockListBuyerCmp')

    </select>

    <select id="inquiryBuyerDetailDownload"  resultType="com.globalsources.eblock.agg.api.model.vo.audit.rfi.InquiryBuyerDetailReportVO">
        select ai.buyer_email_addr as buyerEmail,
        ai.buyer_id,
        -- TMX reject
        sum(case when ai.system_audit_rules :: text  = 'tmxResultCmp' and system_audit_status = 'Reject' then 1 end) tmxRejectCnt,
        -- TMX blacklist reject
        sum(case when ai.system_audit_rules :: text  = 'tmxAccountBlacklistCmp' and system_audit_status = 'Reject' then 1 end) tmxBlackRejectCnt,
        -- TMX  release
        sum(case when ai.system_audit_rules :: text  = 'tmxResultCmp' and system_audit_status = 'Release' then 1 end) tmxReleaseCnt,
        -- admin release
        sum(case when ai.system_audit_rules :: text  != 'tmxResultCmp' and system_audit_status = 'Release' then 1 end) eblockReleaseCnt,
        -- admin reject
        sum(case when ai.system_audit_rules :: text  not in ('tmxResultCmp','tmxAccountBlacklistCmp') and system_audit_status = 'Reject' then 1 end) eblockRejectCnt,
        -- manual release
        sum(case when ai.manual_audit_status :: text = 'Release' then 1 end) manualReleaseCnt,
        -- manual block
        sum(case when ai.manual_audit_status :: text = 'Block' then 1 end) manualBlockCnt,
        -- manual reject
        sum(case when ai.manual_audit_status :: text = 'Reject' then 1 end) manualRejectCnt,
        -- manual resend
        sum(case when ai.resend_date is not null then 1 end) resendCnt
        from contentaudit_grp.audit_info ai
        where ai.send_date &gt;= #{dto.startDate} and ai.send_date &lt;= #{dto.endDate}
        group by ai.buyer_email_addr,ai.buyer_id
        order by buyer_id
    </select>

    <select id="inquiryStatusSummaryDownload"  resultType="com.globalsources.eblock.agg.api.model.vo.audit.rfi.InquiryBuyerDetailReportVO">
        select
        -- TMX original reject
        sum(case when ai.system_audit_rules :: text  = 'tmxResultCmp' and system_audit_status = 'Reject' then 1 else 0 end) tmxRejectOriginalCnt,
        -- TMX final reject
        sum(case when ai.system_audit_rules :: text  = 'tmxResultCmp' and system_audit_status = 'Reject' and ai.resend_date is null then 1 else 0 end) tmxRejectFinalCnt,

        -- TMX blacklist Original reject
        sum(case when ai.system_audit_rules :: text  = 'tmxAccountBlacklistCmp' and system_audit_status = 'Reject' then 1 else 0 end) tmxBlackRejectOriginalCnt,
        -- TMX blacklist Final reject
        sum(case when ai.system_audit_rules :: text  = 'tmxAccountBlacklistCmp' and system_audit_status = 'Reject' and ai.resend_date is null then 1 else 0 end) tmxBlackRejectFinalCnt,

        -- TMX Original release
        sum(case when ai.system_audit_rules :: text  = 'tmxResultCmp' and system_audit_status = 'Release' then 1 else 0 end) tmxReleaseOriginalCnt,
        -- TMX Final release
        sum(case when ai.system_audit_rules :: text  = 'tmxResultCmp' and system_audit_status = 'Release' then 1 else 0 end) tmxReleaseFinalCnt,

        -- admin Original release
        sum(case when ai.system_audit_rules :: text  != 'tmxResultCmp' and system_audit_status = 'Release' then 1 else 0 end) eblockReleaseOriginalCnt,
        -- admin Final release
        sum(case when ai.system_audit_rules :: text  != 'tmxResultCmp' and system_audit_status = 'Release' then 1 else 0 end) eblockReleaseFinalCnt,

        -- admin Original reject
        sum(case when ai.system_audit_rules :: text  not in ('tmxResultCmp','tmxAccountBlacklistCmp') and system_audit_status = 'Reject' then 1 else 0 end) eblockRejectOriginalCnt,
        -- admin Final reject
        sum(case when ai.system_audit_rules :: text  not in ('tmxResultCmp','tmxAccountBlacklistCmp') and system_audit_status = 'Reject' and ai.resend_date is null  then 1 else 0 end) eblockRejectFinalCnt,

        -- manual Original release
        sum(case when ai.manual_audit_status :: text = 'Release' then 1 else 0 end) manualReleaseOriginalCnt,
        -- manual Final release
        sum(case when ai.manual_audit_status :: text = 'Release' then 1 else 0 end) manualReleaseFinalCnt,

        -- manual Original block
        sum(case when ai.manual_audit_status :: text = 'Block' then 1 else 0 end) manualBlockOriginalCnt,
        -- manual Final block
        sum(case when ai.manual_audit_status :: text = 'Block' and ai.resend_date is null then 1 else 0 end) manualBlockFinalCnt,

        -- manual Original reject
        sum(case when ai.manual_audit_status :: text = 'Reject' then 1 else 0 end) manualRejectOriginalCnt,
        -- manual Final reject
        sum(case when ai.manual_audit_status :: text = 'Reject' and ai.resend_date is null then 1 else 0 end) manualRejectFinalCnt,

        -- manual Original resend
        sum(case when ai.resend_date is not null then 1 else 0 end) resendOriginalCnt,
        -- manual Final resend
        sum(case when ai.resend_date is not null then 1 else 0 end) resendFinalCnt

        from contentaudit_grp.audit_info ai
        where ai.send_date &gt;= #{dto.startDate} and ai.send_date &lt;= #{dto.endDate}
    </select>

    <select id="inquiryBuyerSummaryDownload"  resultType="com.globalsources.eblock.agg.api.model.vo.audit.rfi.InquiryBuyerDetailReportVO">
        select
        -- TMX original reject
        sum(case when ai.system_audit_rules :: text = 'tmxResultCmp' and system_audit_status = 'Reject' then 1 else 0 end) tmxRejectOriginalCnt,
        -- TMX final reject
        sum(case when ai.system_audit_rules :: text = 'tmxResultCmp' and system_audit_status = 'Reject' and resendCount &lt; totalCount then 1 else 0 end) tmxRejectFinalCnt,
        -- TMX blacklist Original reject
        sum(case when ai.system_audit_rules :: text = 'tmxAccountBlacklistCmp' and system_audit_status = 'Reject' then 1 else 0 end) tmxBlackRejectOriginalCnt,
        -- TMX blacklist Final reject
        sum(case when ai.system_audit_rules :: text = 'tmxAccountBlacklistCmp' and system_audit_status = 'Reject' and resendCount &lt; totalCount then 1 else 0 end) tmxBlackRejectFinalCnt,
        -- TMX Original release
        sum(case when ai.system_audit_rules :: text = 'tmxResultCmp' and system_audit_status = 'Release' then 1 else 0 end) tmxReleaseOriginalCnt,
        -- TMX Final release
        sum(case when ai.system_audit_rules :: text = 'tmxResultCmp' and system_audit_status = 'Release' then 1 else 0 end) tmxReleaseFinalCnt,
        -- admin Original release
        sum(case when ai.system_audit_rules :: text != 'tmxResultCmp' and system_audit_status = 'Release' then 1 else 0 end) eblockReleaseOriginalCnt,
        -- admin Final release
        sum(case when ai.system_audit_rules :: text != 'tmxResultCmp' and system_audit_status = 'Release' then 1 else 0 end) eblockReleaseFinalCnt,
        -- admin Original reject
        sum(case when ai.system_audit_rules :: text not in ('tmxResultCmp', 'tmxAccountBlacklistCmp') and system_audit_status = 'Reject' then 1 else 0 end) eblockRejectOriginalCnt,
        -- admin Final reject
        sum(case when ai.system_audit_rules :: text not in ('tmxResultCmp', 'tmxAccountBlacklistCmp') and system_audit_status = 'Reject' and resendCount &lt; totalCount then 1 else 0 end) eblockRejectFinalCnt,
        -- manual Original release
        sum(case when ai.manual_audit_status :: text = 'Release' then 1 else 0 end) manualReleaseOriginalCnt,
        -- manual Final release
        sum(case when ai.manual_audit_status :: text = 'Release' then 1 else 0 end) manualReleaseFinalCnt,
        -- manual Original block
        sum(case when ai.manual_audit_status :: text = 'Block' then 1 else 0 end) manualBlockOriginalCnt,
        -- manual Final block
        sum(case when ai.manual_audit_status :: text = 'Block' and resendCount &lt; totalCount then 1 else 0 end) manualBlockFinalCnt,
        -- manual Original reject
        sum(case when ai.manual_audit_status :: text = 'Reject' then 1 else 0 end) manualRejectOriginalCnt,
        -- manual Final reject
        sum(case when ai.manual_audit_status :: text = 'Reject' and resendCount &lt; totalCount then 1 else 0 end) manualRejectFinalCnt,
        -- manual Final resend
        count(distinct (case when resendCount &gt; 0 then ai.buyer_email_addr else null end)) resendFinalCnt
        from
        (
        select
        ai.system_audit_status,
        ai.buyer_email_addr,
        ai.system_audit_rules,
        ai.manual_audit_status,
        sum(case when ai.resend_date is not null then 1 else 0 end) as resendCount,
        count(1) as totalCount
        from
        contentaudit_grp.audit_info ai
        where ai.send_date &gt;= #{dto.startDate} and ai.send_date &lt;= #{dto.endDate}
        group by
        ai.buyer_email_addr,
        ai.system_audit_rules,
        ai.system_audit_status,
        ai.manual_audit_status
        order by
        ai.buyer_email_addr) as ai
    </select>

    <select id="buyerLastSendCount" resultType="com.globalsources.eblock.agg.liteflow.bean.BuyerBean">
        SELECT
        COUNT(*) FILTER (WHERE create_date  >= NOW() - INTERVAL '24 HOUR') AS rfiCount1Day,
        COUNT(*) FILTER (WHERE create_date >= CURRENT_DATE  - INTERVAL '3 DAY') AS rfiCount3Day,
        COUNT(*) FILTER (WHERE create_date >= CURRENT_DATE  - INTERVAL '5 DAY') AS rfiCount5Day,
        COUNT(*) FILTER (WHERE create_date >= CURRENT_DATE  - INTERVAL '7 DAY') AS rfiCount7Day,
        COUNT(*) FILTER (WHERE create_date >= CURRENT_DATE  - INTERVAL '30 DAY') AS rfiCount30Day
        from rfi_grp.inquire_all ia
        where ia.buyer_id  =#{buyerId}
        GROUP BY buyer_id;
    </select>
</mapper>
