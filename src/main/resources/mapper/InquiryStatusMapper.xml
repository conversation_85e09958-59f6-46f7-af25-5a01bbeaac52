<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.rfi.data.dao.InquiryStatusDao">

    <select id="batchSelectStatus" resultType="com.globalsources.rfi.data.entity.InquiryStatusEntity">
        select
        *
        from (select *, row_number() over (partition by thread_id ORDER BY create_date DESC)
        from inquiry_status
        where
        <foreach collection="threadIds" item="threadId" open="(" close=")" separator=" or ">
            thread_id=#{threadId}
        </foreach>
        ) as inq_s
        where row_number=1
        UNION ALL
        select
        *
        from (select *, row_number() over (partition by inquiry_id ORDER BY create_date DESC)
        from inquiry_status
        where
        <foreach collection="inquiryIds" item="inquiryId" open="(" close=")" separator=" or ">
            inquiry_id=#{inquiryId}
        </foreach>
        and thread_id IS NULL
        ) as inq_s
        where row_number=1
    </select>
</mapper>
