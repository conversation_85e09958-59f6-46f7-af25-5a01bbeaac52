<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.globalsources.admin.dao.OnlineTopicMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.globalsources.admin.model.pojo.OnlineTopic">
        <id column="topic_id" property="topicId" />
        <result column="topic" property="topic" />
        <result column="topic_pid" property="topicPid" />
        <result column="topic_zh" property="topicZh" />
        <result column="home_flag" property="homeFlag" />
        <result column="display_seq" property="displaySeq" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="l_upd_by" property="lUpdBy" />
        <result column="l_upd_date" property="lUpdDate" />
        <result column="source_code" property="sourceCode" />
        <result column="quick_navigation_flag" property="quickNavigationFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        topic_id, topic, topic_pid, topic_zh, home_flag, display_seq, create_by, create_date, l_upd_by, l_upd_date, source_code, quick_navigation_flag
    </sql>
    <update id="updateDisplaySeq">
        update online_topic set display_seq=#{displaySeq} where topic_id=#{topicId}
    </update>
    <update id="updateAcrossCategoriesDisplaySeq">
        update online_topic set display_seq=#{displaySeq},topic_pid=#{topicPid} where topic_id=#{topicId}
    </update>

    <select id="getDisplaySeqMax" resultType="java.lang.Integer">
        select max(display_seq) from online_topic
        where topic_pid = #{topicPid} and source_code=#{sourceCode} and home_flag = #{homeFlag};
    </select>


    <select id="getTopicPid" resultType="java.lang.Long">
        select topic_pid from online_topic where topic_id = #{topicId}
    </select>
    <select id="selectAll" resultType="com.globalsources.admin.model.vo.helpcenter.OnlineTopicSCVO">
        select
            a.topic_id,a.topic_pid,b.topic as topic_pid_name,a.topic,a.topic_zh,a.quick_navigation_flag
        from
            online_topic as a
                left join
            online_topic as b
            on
                a.topic_pid = b.topic_id
        where
            a.home_flag = #{homeFlag}
          and a.source_code=#{sourceCode}
        order by a.topic_pid,quick_navigation_flag desc,a.display_seq
    </select>
    <select id="selectAllSCMenu" resultType="com.globalsources.admin.model.vo.helpcenter.OnlineTopicSCVO">
        select
            a.topic_id,a.topic_pid,b.topic as topic_pid_name,a.topic,a.topic_zh,a.quick_navigation_flag,a.home_flag
        from
            online_topic as a
                left join
            online_topic as b
            on
                a.topic_pid = b.topic_id
        where
            a.source_code=#{sourceCode}
          and a.topic_pid=#{topicPid}
        order by a.home_flag desc,a.topic_pid,a.display_seq
    </select>

</mapper>