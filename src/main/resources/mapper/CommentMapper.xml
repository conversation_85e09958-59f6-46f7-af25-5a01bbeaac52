<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.globalsources.comment.core.mapper.CommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.globalsources.comment.po.CommentPO">
        <id column="comment_id" property="commentId"/>
        <result column="biz_type" property="bizType"/>
        <result column="biz_id" property="bizId"/>
        <result column="product_id" property="productId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="publish_status" property="publishStatus"/>
        <result column="score" property="score"/>
        <result column="create_date" property="createDate"/>
        <result column="l_upd_date" property="lastUpdateDate"/>

        <collection property="commentMsgPOList" javaType="java.util.ArrayList"
                    ofType="com.globalsources.comment.po.CommentMessagePO">
            <result column="message_id" property="messageId"/>
            <result column="comment_id" property="commentId"/>
            <result column="m_publish_status" property="publishStatus"/>
            <result column="author_type" property="authorType"/>
            <result column="msg_content" property="msgContent"/>
            <result column="parent_msg_id" property="parentMsgId"/>
            <result column="delete_flag" property="deleteFlag"/>
            <result column="remark" property="remark"/>
            <result column="create_date" property="createDate"/>
            <result column="create_by" property="createBy"/>
            <result column="l_upd_date" property="lastUpdateDate"/>
            <result column="l_upd_by" property="lastUpdateBy"/>
        </collection>

    </resultMap>

    <select id="queryLiteCommentList" resultType="com.globalsources.comment.po.CommentPO">
        SELECT c.comment_id, c.biz_type, c.biz_id, c.product_id, c.supplier_id, c.publish_status, c.score,
        c.create_date, c.l_upd_date as last_update_date
        FROM "comment" c LEFT JOIN comment_msg cm on c.comment_id=cm.comment_id
        WHERE 1 = 1
        <if test="commentSearchDTO.supplierId != null and commentSearchDTO.supplierId != 0">
            and c.supplier_id = #{commentSearchDTO.supplierId}
        </if>
        <if test="commentSearchDTO.productId != null and commentSearchDTO.productId != 0">
            and c.product_id = #{commentSearchDTO.productId}
        </if>
        <if test="commentSearchDTO.publishStatus != null and commentSearchDTO.publishStatus != ''">
            and c.publish_status = #{commentSearchDTO.publishStatus}
        </if>
        <if test="commentSearchDTO.bizId != null and commentSearchDTO.bizId != 0">
            and c.biz_id = #{commentSearchDTO.bizId}
        </if>
        <if test="commentSearchDTO.bizType != null and commentSearchDTO.bizType != ''">
            and c.biz_type = #{commentSearchDTO.bizType}
        </if>
        AND cm.publish_status='Published' GROUP BY c.comment_id
        ORDER BY c.l_upd_date desc
    </select>

    <select id="queryCommentMsgList" resultType="com.globalsources.comment.dto.CommentMsgCoreDTO">
        SELECT m.msg_id as message_id, m.comment_id, m.publish_status, m.author_type, m.msg_content, m.parent_msg_id, m.delete_flag, m.remark,
        m.create_date, m.create_by, m.l_upd_date as last_update_date, m.l_upd_by as last_update_by, c.biz_type, c.biz_id, c.product_id, c.score, c.supplier_id
        FROM comment_msg m left join comment c on m.comment_id = c.comment_id
        WHERE 1 = 1
        <if test="commentSearchDTO.searchId != null and commentSearchDTO.searchId !=0">
            and (c.biz_id=cast(#{commentSearchDTO.searchId} as VARCHAR) or c.product_id=#{commentSearchDTO.searchId})
        </if>
        <if test="commentSearchDTO.publishStatus != null and commentSearchDTO.publishStatus != ''">
            and m.publish_status = #{commentSearchDTO.publishStatus}
        </if>
        <if test="commentSearchDTO.authorType != null and commentSearchDTO.authorType != ''">
            and m.author_type = #{commentSearchDTO.authorType}
        </if>

        <if test="commentSearchDTO.startDate != null and commentSearchDTO.endDate != null">
            and m.create_date &gt;= #{commentSearchDTO.startDate} and m.create_date &lt;= #{commentSearchDTO.endDate}
        </if>
        ORDER BY m.l_upd_date desc
    </select>

    <select id="selectBizIdByUserId" resultType="com.globalsources.comment.vo.CommentInfoVO">
        select c.biz_id as bizId, max(m.l_upd_date) as lastUpdateDate
        FROM comment_msg m LEFT JOIN comment c ON m.comment_id=c.comment_id
        WHERE m.create_by = #{userId}
        <if test="bizType != null and bizType != ''">
            and c.biz_type = #{bizType}
        </if>
        <if test="publishStatus != null and publishStatus != ''">
            and m.publish_status= #{publishStatus}
        </if>
        <if test="authorType != null and authorType != ''">
            and m.author_type= #{authorType}
        </if>
        group by c.biz_id ORDER BY lastUpdateDate desc
    </select>
    <select id="selectBizIdBySupplierId" resultType="com.globalsources.comment.vo.CommentInfoVO">
        select c.biz_id as bizId, max(m.l_upd_date) as lastUpdateDate
        FROM comment_msg m LEFT JOIN comment c ON m.comment_id=c.comment_id
        WHERE c.supplier_id = #{supplierId}
        <if test="bizType != null and bizType != ''">
            and c.biz_type = #{bizType}
        </if>
        <if test="publishStatus != null and publishStatus != ''">
            and m.publish_status= #{publishStatus}
        </if>
        <if test="authorType != null and authorType != ''">
            and m.author_type= #{authorType}
        </if>
        group by c.biz_id ORDER BY lastUpdateDate desc
    </select>
    <select id="selectCommentInfoByBizId"
            resultType="com.globalsources.comment.vo.CommentInfoVO">
        select c.product_id as productId, c.score as score, m.comment_id as commentId, m.msg_id as msgId, m.msg_content as msgContent, m.author_type as authorType,
               m.create_date as  createDate
        from "comment" c LEFT JOIN comment_msg m on c.comment_id=m.comment_id WHERE c.biz_id=#{bizId}
    </select>

    <select id="selectBuyerCommentBySupplierId"
            resultType="com.globalsources.comment.vo.CommentInfoVO">
        select c.product_id as productId,
               c.score as score,
               m.msg_content as msgContent,
               m.msg_id as msgId,
               m.create_date as createDate,
               c.biz_id as bizId,
               c.supplier_id as supplierId
        from "comment" c LEFT JOIN comment_msg m ON c.comment_id=m.comment_id
        WHERE c.supplier_id=#{supplierId}
        <if test="bizType != null and bizType != ''">
            and c.biz_type = #{bizType}
        </if>
        <if test="publishStatus != null and publishStatus != ''">
            and m.publish_status= #{publishStatus}
        </if>
        <if test="authorType != null and authorType != ''">
            and m.author_type= #{authorType}
        </if>
         ORDER BY m.l_upd_date DESC
    </select>
    <!-- 查询卖家端 已回复和未回复列表 -->
    <select id="listSupplierComment" resultType="com.globalsources.comment.vo.CommentInfoVO">
        select c.biz_id as bizId,
               c.product_id as productId,
               c.score as score,
               c.supplier_id as supplierId,
               m.msg_content as msgContent,
               m.publish_status as publishStatus,
               m.comment_id as commentId,
               m.msg_id as msgId,
               m.create_date as createDate
        from "comment" c, comment_msg m where c.comment_id=m.comment_id
        and c.supplier_id=#{supplierId}
        <if test="authorType != null and authorType != ''">
            and m.author_type= #{authorType}
        </if>
        <if test="bizType != null and bizType != ''">
            and c.biz_type = #{bizType}
        </if>
        <if test="publishStatus != null and publishStatus != ''">
            and m.publish_status= #{publishStatus}
        </if>
         and m.reply_flag=#{replyFlag} ORDER BY m.l_upd_date desc
    </select>
    <!-- 根据指定业务id和productId 查询 产品评价 -->
    <select id="selectCommentByBizIdAndProductId"
            resultType="com.globalsources.comment.vo.CommentInfoVO">
        select m.author_type as authorType,
               c.biz_id as bizId,
               c.product_id as productId,
               c.score as score,
               c.supplier_id as supplierId,
               m.msg_content as msgContent,
               m.create_date as createDate
        from "comment" c,comment_msg m
        WHERE c.comment_id=m.comment_id
        and c.biz_id=#{bizId}
        and c.product_id=#{productId}
    </select>

    <select id="selectCommentScoreSummaryByIds" resultType="java.util.Map">
        select
        <choose>
            <when test="dto.groupBy == 'Supplier'">
                com.supplier_id "entityId",
                count(com.supplier_id) "commentCnt",
            </when>
            <otherwise>
                com.product_id "entityId",
                count(com.product_id) "commentCnt",
            </otherwise>
        </choose>
        coalesce(sum(com.score), 0) as "totScore"
        from "comment" com
        where com.publish_status = 'Published'
        <if test="dto.bizType != null and dto.bizType != ''">
            and com.biz_type = #{dto.bizType}
        </if>
        <if test="dto.startDate != null">
            and com.create_date &gt;= #{dto.startDate}
        </if>
        <choose>
            <when test="dto.groupBy == 'Supplier'">
                and com.supplier_id  in
                <foreach collection="dto.entityIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                group by com.supplier_id
            </when>
            <otherwise>
                and com.product_id in
                <foreach collection="dto.entityIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                group by com.product_id
            </otherwise>
        </choose>
    </select>

    <select id="selectCommentProductIdList" resultType="java.lang.Long">
        select distinct
        <choose>
            <when test="dto.groupBy == 'Supplier'">
                com.supplier_id
            </when>
            <otherwise>
                com.product_id
            </otherwise>
        </choose>
        from "comment" com
        where com.publish_status = 'Published'
        <if test="dto.bizType != null and dto.bizType != ''">
            and com.biz_type = #{dto.bizType}
        </if>
        <if test="dto.startDate != null">
            and com.create_date &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null">
            and com.create_date &lt;= #{dto.endDate}
        </if>
    </select>

</mapper>