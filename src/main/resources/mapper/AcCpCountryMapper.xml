<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.admin.dao.AcCpCountryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.globalsources.admin.model.pojo.AcCpCountry">
        <id column="cp_country_id" property="cpCountryId" />
        <result column="country_code" property="countryCode" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_date" property="createDate" />
        <result column="l_upd_date" property="lUpdDate" />
        <result column="l_upd_by" property="lUpdBy" />
        <result column="create_by" property="createBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        cp_country_id, country_code, delete_flag, create_date, l_upd_date, l_upd_by, create_by
    </sql>

</mapper>
