<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.admin.dao.AcAnnouncementMapper">

    <resultMap id="ResultAnnouncement" type="com.globalsources.admin.model.vo.AcAnnouncementVO">
        <result column="announcementId" property="announcementId"/>
        <result column="announcement_type" property="announcementType"/>
        <result column="role_type" property="roleType"/>
        <result column="online_status" property="onlineStatus"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="create_date" property="createDate"/>
        <result column="create_by" property="createBy"/>
        <result column="l_upd_date" property="lUpdDate"/>
        <collection property="children" column="announcementId" javaType="java.util.List"
                    ofType="com.globalsources.admin.model.vo.AcAnnouncementMessageVO" select="com.globalsources.admin.dao.AcAnnouncementMessageMapper.findListById"/>
    </resultMap>

    <select id="findAll" resultMap="ResultAnnouncement">
        SELECT
        an.announcement_id as announcementId,
        an.announcement_type,
        an.role_type,
        an.online_status,
        an.start_date,
        an.end_date,
        an.create_date,
        an.create_by,
        an.l_upd_date
        FROM
        ac_announcement an
        WHERE an.role_type = #{roleType} AND an.announcement_type = #{announcementType} AND an.delete_flag = FALSE
        ORDER BY
        an.display_seq desc
    </select>

    <!--根据sort查询上一条记录主键-->
    <select id="selectPreviousId" resultType="com.globalsources.admin.model.vo.AnnouncementKeyWordVO">
        SELECT announcement_id as announcementId, display_seq as displaySeq  FROM ac_announcement WHERE display_seq &gt; #{displaySeq} AND announcement_type = #{announcementType} AND announcement_id != #{seqId} AND role_type = #{roleType} AND delete_flag = FALSE ORDER BY display_seq ASC LIMIT 1
    </select>
    <!--根据sort查询下一条记录主键-->
    <select id="selectNextId" resultType="com.globalsources.admin.model.vo.AnnouncementKeyWordVO">
        SELECT announcement_id as announcementId, display_seq as displaySeq FROM ac_announcement WHERE display_seq &lt; #{displaySeq} AND announcement_type = #{announcementType} AND announcement_id != #{seqId} AND role_type = #{roleType} AND delete_flag = FALSE ORDER BY display_seq desc LIMIT 1
    </select>

    <select id="notice" resultType="com.globalsources.admin.model.vo.AcAnnouncementMessageVO">
        SELECT
        acm.lang_code,
        acm.description
        FROM
        ac_announcement an
        LEFT JOIN ac_announcement_message acm ON an.announcement_id = acm.announcement_id
        WHERE an.role_type = #{roleType} and an.announcement_type = #{announcementType} and online_status = 'Online' and an.delete_flag = false and (an.start_date &lt; #{currentDate} and an.end_date &gt; #{currentDate}) ORDER BY an.display_seq asc
    </select>
</mapper>
