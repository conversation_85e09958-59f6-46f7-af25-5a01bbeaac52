<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.comment.core.mapper.CommentScoreSummaryMapper">

    <update id="updateCommentScoreSummary" >
        update product_score_summary
        <choose>
            <when test="summaryType == '0.0'">
                set
                tot_score = (select coalesce(sum(c.score), 0) from comment c where c.product_id = #{productId} and c.biz_type = #{bizType} and c.publish_status = 'Published'),
                comment_cnt = (select count(1) from comment c where c.product_id = #{productId} and c.biz_type = #{bizType} and c.publish_status = 'Published')
            </when>
            <otherwise>
                set
                comment_cnt = (select count(1) from comment c where c.product_id = #{productId} and c.biz_type = #{bizType} and c.publish_status = 'Published' and c.score = #{summaryType}::numeric)
            </otherwise>
        </choose>
        where biz_type = #{bizType} and product_id = #{productId} and summary_type = #{summaryType};
    </update>

    <select id="queryCommentScoreSummaryList" resultType="java.util.Map">
        select summary_type "summaryType", sum(tot_score) as "totScore", sum(comment_cnt) "commentCnt" from product_score_summary
        <choose>
            <when test="groupBy == 'Supplier'">
                where supplier_id = #{id} group by supplier_id, summary_type;
            </when>
            <otherwise>
                where product_id = #{id} group by product_id, summary_type;
            </otherwise>
        </choose>


    </select>

</mapper>
