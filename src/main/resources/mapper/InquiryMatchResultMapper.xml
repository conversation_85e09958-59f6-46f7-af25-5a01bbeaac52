<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.rfi.data.dao.InquiryMatchResultDao">


    <resultMap id="resultMap_Match_Result" type="com.globalsources.rfi.agg.core.dto.match.InquiryMatchResultDTO">
        <result column="match_id" property="matchId"/>
        <result column="category_id" property="categoryId"/>
        <result column="inquiry_id" property="inquiryId"/>
        <result column="match_complete_flag" property="matchCompleteFlag"/>
        <result column="match_type" property="matchType"/>
        <result column="product_id" property="productId"/>
        <result column="create_date" property="createDate"/>
        <result column="l_up_date" property="lUpDate"/>
        <result column="match_result_json" property="matchResultJson" typeHandler="com.globalsources.rfi.configuration.JsonTypeHandler"
                javaType="Object"/>
    </resultMap>

    <select id="getMatchResultByMatchId" parameterType="java.lang.String" resultMap="resultMap_Match_Result">
        SELECT
        imr.match_id,
        imr.match_result_json,
        imr.category_id,
        imr.inquiry_id,
        imr.match_complete_flag,
        imr.match_type,
        imr.product_id,
        imr.create_date,
        imr.l_up_date
        FROM inquiry_match_result imr
        WHERE imr.match_id = #{matchId}
    </select>

    <select id="getMatchResultInquiryId" parameterType="java.lang.String" resultMap="resultMap_Match_Result">
        SELECT
        imr.match_id,
        imr.match_result_json,
        imr.category_id,
        imr.inquiry_id,
        imr.match_complete_flag,
        imr.match_type,
        imr.product_id,
        imr.create_date,
        imr.l_up_date
        FROM inquiry_match_result imr
        WHERE
        imr.inquiry_id = #{inquiryId}
    </select>

    <select id="getOldJsonData" parameterType="java.lang.String" resultMap="resultMap_Match_Result">
        SELECT
        imrj.match_result_json
        FROM inquiry_match_result imr
        inner join inquiry_match_result_json imrj on imr.match_result_json_id=imrj.match_result_json_id
        WHERE
        imr.inquiry_id = #{inquiryId}
    </select>

    <select id="getMatchUpsellResult" parameterType="java.lang.String" resultMap="resultMap_Match_Result">
        SELECT
        imr.match_id,
        imr.match_result_json,
        imr.category_id,
        imr.inquiry_id,
        imr.match_complete_flag,
        imr.match_type,
        imr.product_id,
        imr.create_date,
        imr.l_up_date
        FROM inquiry_match_result imr
    </select>

    <select id="getMatchCategoryResult" parameterType="java.lang.String" resultMap="resultMap_Match_Result">
        SELECT
        imr.match_id,
        imr.match_result_json,
        imr.category_id,
        imr.inquiry_id,
        imr.match_complete_flag,
        imr.match_type,
        imr.product_id,
        imr.create_date,
        imr.l_up_date
        FROM
        inquiry_match_result imr
        WHERE
        imr.create_date + INTERVAL '${time} min' > CURRENT_TIMESTAMP
        AND imr.category_id = #{categoryId}
        AND imr.match_type = 'CATEGORY'
        ORDER BY
        imr.create_date DESC
        LIMIT 1
    </select>

    <insert id="saveMatchResultJson" parameterType="com.globalsources.rfi.data.entity.InquiryMatchResultEntity">
        insert into inquiry_match_result
        (
        match_id,
        category_id,
        inquiry_id,
        match_complete_flag,
        match_type,
        product_id,
        create_date,
        l_up_date,
        match_result_json
        )
        values (
        #{matchId},
        #{categoryId},
        #{inquiryId},
        #{matchCompleteFlag},
        #{matchType},
        #{productId},
        now(),
        now(),
        #{matchResultJson, typeHandler=com.globalsources.rfi.configuration.JsonTypeHandler}
        )
    </insert>

</mapper>
