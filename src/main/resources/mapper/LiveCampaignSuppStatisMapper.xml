<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.globalsources.admin.dao.LiveCampaignSuppStatisMapper">
    <select id="batchQuerySupplierName" resultType="com.globalsources.admin.model.dto.report.LiveCampaignSuppReport">
        select s.org_id as supplierId,m.website_display_name as supplierName from supplier_grp.online_section s
            left join supplier_grp.online_section_main m on s.section_id=m.section_id
        where s.section_code='MAIN' and s.target_website_status='Online' and s.website_type='GSOL' and
            s.org_id in <foreach collection="supplierIds" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
    </select>

    <select id="batchGetSupplierName" resultType="com.globalsources.admin.model.vo.live.BaseSupplierInfoVO">
        select s.org_id as supplierId,m.website_display_name as supplierName from supplier_grp.online_section s
        left join supplier_grp.online_section_main m on s.section_id=m.section_id
        where s.section_code='MAIN' and s.target_website_status='Online' and s.website_type='GSOL' and
            s.org_id in
            <foreach collection="supplierIds" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
    </select>

    <select id="batchQueryExchangeCardCount" resultType="com.globalsources.admin.model.dto.report.LiveCampaignSuppReport">
        select c.supplier_id,count(c.user_id) as cardCount from user_grp.user_supplier_bus_card c
        where c.delete_flag=false and
            c.supplier_id in <foreach collection="supplierIds" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
            <if test="startTime != null">
                <![CDATA[ and c.create_date >= #{startTime} ]]>
            </if>
            <if test="endTime != null">
                <![CDATA[ and c.create_date <= #{endTime} ]]>
            </if>
        group by c.supplier_id
    </select>

    <select id="batchQueryFollowCount" resultType="com.globalsources.admin.model.dto.report.LiveCampaignSuppReport">
        select f.supplier_id,count(f.create_by) as followCount from user_grp.user_favorite f
        where f.delete_flag=false and f.product_id is null and
            f.supplier_id in <foreach collection="supplierIds" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
        <if test="startTime != null">
            <![CDATA[ and f.l_upd_date >= #{startTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and f.l_upd_date <= #{endTime} ]]>
        </if>
        group by f.supplier_id
    </select>

    <select id="batchQueryChatCount" resultType="com.globalsources.admin.model.dto.report.LiveCampaignSuppReport">
        select supplier_id,count(user_id) as chatCount from (
            select h.sender_ldap_user_id as user_id,h.supplier_id from chat_grp.user_chat_history h
            where h.supplier_id in
            <foreach collection="supplierIds" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
            <if test="startTime != null">
                <![CDATA[ and h.create_date >= #{startTime} ]]>
            </if>
            <if test="endTime != null">
                <![CDATA[ and h.create_date <= #{endTime} ]]>
            </if>
            group by h.sender_ldap_user_id,h.supplier_id ) t
        group by supplier_id
    </select>

    <select id="batchQueryTsScanCount" resultType="com.globalsources.admin.model.dto.report.LiveCampaignSuppReport">
        select s.supplier_id,count(s.ts_buyer_scan_uv_id) as offlineScanCount from tradeshow_grp.ts_buyer_scan_uv s
        where s.supplier_id in
        <foreach collection="supplierIds" item="supplierId" separator="," open="(" close=")">
            #{supplierId}
        </foreach>
        <if test="startTime != null">
            <![CDATA[ and s.scan_date >= #{startTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and s.scan_date <= #{endTime} ]]>
        </if>
        group by s.supplier_id
    </select>
</mapper>
