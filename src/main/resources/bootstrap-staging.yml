spring:
  application:
    name: gsol-rfx-bff
  cloud:
    nacos:
      username: develop
      password: 2FrDwJnP
      config:
        server-addr: 192.168.117.134:8888
        prefix: ${spring.application.name}
        namespace: d1
        extension-configs[0]:
          data-id: common.properties
          group: DEFAULT_GROUP
          refresh: true
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        service: ${spring.application.name}
        namespace: d1
  main:
    allow-bean-definition-overriding: true