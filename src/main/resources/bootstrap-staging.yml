spring:
  application:
    name: gsol-web-bff
  cloud:
    nacos:
      config:
        server-addr: 192.168.117.134:8888
        file-extension: yml
        extension-configs[0]:
          data-id: common.properties
          group: DEFAULT_GROUP
          refresh: true
        namespace: d1
        username: develop
        password: 2FrDwJnP
      discovery:
        server-addr: 192.168.117.134:8888
        service: gsol-web-bff
        namespace: d1
        username: develop
        password: 2FrDwJnP
  main:
    allow-bean-definition-overriding: true