spring:
  application:
    name: service-admin
  cloud:
    nacos:
      config:
        server-addr: 192.168.117.134:8888
        prefix: gsol-service-admin
        namespace: d1
        group: DEFAULT_GROUP
        extension-configs[0]:
          data-id: common.properties
          group: DEFAULT_GROUP
          refresh: true
      discovery:
        server-addr: 192.168.117.134:8888
        service: service-admin
        namespace: d1
        #register-enabled: false
      username: develop
      password: 2FrDwJnP
  main:
    allow-bean-definition-overriding: true

logging:
  level.root: INFO