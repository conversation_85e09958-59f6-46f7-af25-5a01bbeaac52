server:
  servlet:
    context-path: /gsol-rfi-agg

spring:
  application:
    name: gsol-rfi-agg
  cloud:
    nacos:
      config:
        server-addr: 192.168.117.134:8888
        file-extension: properties
        namespace: szdev
        username: develop
        password: 2FrDwJnP
        extension-configs[0]:
          data-id: common.properties
          group: DEFAULT_GROUP
          refresh: true
      discovery:
        server-addr: 192.168.117.134:8888
        service: gsol-rfi-agg
        namespace: szdev
        username: develop
        password: 2FrDwJnP
#        register-enabled: false
  main:
    allow-bean-definition-overriding: true
