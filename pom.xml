<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.globalsources</groupId>
    <artifactId>gsol-rfi-agg-api</artifactId>
<!--    <version>3.3.15-SNAPSHOT</version>-->
    <version>3.3.25-RELEASE</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <parent>
        <groupId>com.globalsources</groupId>
        <artifactId>gsol-basic</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-framework</artifactId>
            <version>1.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.12.4</version>
        </dependency>

        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-service-search-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <!-- 发布maven私服 -->
    <repositories>
        <repository>
            <id>gsol-public</id>
            <url>http://sz-nexus.qa.globalsources.com/repository/maven-public/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>gsol-public</id>
            <url>http://sz-nexus.qa.globalsources.com/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>

    <!-- 发布maven私服 -->
    <distributionManagement>
        <repository>
            <id>gsol-releases</id>
            <url>http://${nexus-uri}/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>gsol-snapshots</id>
            <url>http://${nexus-uri}/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>