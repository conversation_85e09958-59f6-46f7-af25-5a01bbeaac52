<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.globalsources</groupId>
    <artifactId>gsol-rfi-job-agg</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <main.class>com.globalsources.job.RfiJobApplication</main.class>
    </properties>

    <parent>
        <groupId>com.globalsources</groupId>
        <artifactId>gsol-basic</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--xx job-->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.3.0</version>
        </dependency>

        <!-- open feign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- nacos 服务发现 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!-- nacos 配置中心 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- swagger -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>

        <!--redis-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- gsol-framework -->
        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-framework</artifactId>
            <version>4.20-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.maxmind.geoip2</groupId>
            <artifactId>geoip2</artifactId>
            <version>2.12.0</version>
        </dependency>

        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-rfi-agg-api</artifactId>
            <version>3.3.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.13.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>18.0</version>
        </dependency>

        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-service-user-api</artifactId>
            <version>1.6.5-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>

        <!--神策埋点-->
        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-sensordata-core-api</artifactId>
            <version>1.2.9-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.10.10</version>
        </dependency>

        <!--search -->

        <!--amazon-->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>apache-client</artifactId>
            <version>2.16.60</version>
        </dependency>

        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-email-agg-api</artifactId>
            <version>2.8.19-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-rabbit-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-rfx-common</artifactId>
            <version>2.8.8-RELEASE</version>
        </dependency>

        <!-- spring boot rabbit mq -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-admin-agg-api</artifactId>
            <version>1.1.15-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
        </dependency>

        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-rfq-bff-api</artifactId>
            <version>4.0.10-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-message-agg-api</artifactId>
            <version>1.1.3-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.globalsources</groupId>
            <artifactId>gsol-eblock-agg-api</artifactId>
            <version>1.0.7-RELEASE</version>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>2.16.60</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 仓库地址 -->
    <repositories>
        <repository>
            <id>gsol-public</id>
            <url>http://sz-nexus.qa.globalsources.com/repository/maven-public/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>gsol-public</id>
            <url>http://sz-nexus.qa.globalsources.com/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>

    <!-- 发布maven私服 -->
    <distributionManagement>
        <repository>
            <id>gsol-releases</id>
            <name>maven-releases</name>
            <url>http://${nexus-uri}/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>gsol-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://${nexus-uri}/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <finalName>${project.artifactId}.${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.4.5</version>
                <configuration>
                    <mainClass>${main.class}</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>