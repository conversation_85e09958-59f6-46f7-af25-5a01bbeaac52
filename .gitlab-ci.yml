stages:
    - build-dev
    - deploy-dev
    - build-test
    - deploy-test

build-dev:
    stage: build-dev
    image: gsol/maven:3.8.1-openjdk-8
    script:
        - sed -i "s/test$/d1/" ./src/main/resources/bootstrap.yml
        - mvn clean install -U
        - sed -i "s#CI_PROJECT_NAME#${CI_PROJECT_NAME}#" ./Dockerfile
        - ver=`head pom.xml|grep '<version>.*</version>' |awk -F"[<>]" '{print $3}'`
        - sed -i "s/1.0-SNAPSHOT/$ver/" ./Dockerfile
        - docker build -t gsol/${CI_PROJECT_NAME} -f ./Dockerfile .
    only:
        - dev
        - test
        - double_write
    tags:
        - services-group-dev

deploy-dev:
    stage: deploy-dev
    image: centos:7
    script:
        - docker rm -f ${CI_PROJECT_NAME}
        - docker run -itd --network host --name ${CI_PROJECT_NAME} -p 30412:30412 gsol/${CI_PROJECT_NAME}
    only:
        - dev
        - test
        - double_write
    tags:
        - services-group-dev

        
build-test:
    stage: build-test
    image: gsol/maven:3.8.1-openjdk-8
    script:
        - sed -i "s/develop$/${NACOS_TEST_ACCOUNT}/" ./src/main/resources/bootstrap.yml
        - sed -i "s/2FrDwJnP$/${NACOS_TEST_SECRET}/" ./src/main/resources/bootstrap.yml
        - sed -i "s/d1$/test/" ./src/main/resources/bootstrap.yml
        - mvn clean install -U
        - sed -i "s#CI_PROJECT_NAME#${CI_PROJECT_NAME}#" ./Dockerfile
        - ver=`head pom.xml|grep '<version>.*</version>' |awk -F"[<>]" '{print $3}'`
        - sed -i "s/1.0-SNAPSHOT/$ver/" ./Dockerfile
        - docker build -t gsol/${CI_PROJECT_NAME} -f ./Dockerfile .
    only:
        - dev
        - test
    tags:
        - services-group-test

deploy-test:
    when: manual
    stage: deploy-test
    image: centos:7
    script:
        - docker rm -f ${CI_PROJECT_NAME}
        - docker run -itd --name ${CI_PROJECT_NAME} -p 30412:8080 gsol/${CI_PROJECT_NAME}
    only:
        - dev
        - test
    tags:
        - services-group-test
